#!/usr/bin/env python3
"""Test async database connection and basic operations."""

import sys
import asyncio
from pathlib import Path

# Add project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

from src.database.setup import get_async_engine, async_init_db, get_async_db
from src.core.config import settings
from sqlalchemy import text


async def test_async_connection():
    """Test async database connection."""
    print(f"Testing async connection to: {settings.async_database_url}")
    
    try:
        engine = get_async_engine()
        
        # Test basic connection
        async with engine.begin() as conn:
            result = await conn.execute(text("SELECT 1"))
            print("✅ Async database connection successful!")
            
            # Check PostgreSQL version
            result = await conn.execute(text("SELECT version()"))
            version = result.scalar()
            print(f"✅ PostgreSQL version: {version}")
        
        # Initialize tables
        print("\nInitializing database tables...")
        await async_init_db(engine)
        print("✅ Database tables created successfully!")
        
        # Check tables
        async with engine.begin() as conn:
            result = await conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                ORDER BY table_name
            """))
            tables = [row[0] for row in result]
            print(f"\n✅ Tables created: {tables}")
        
        # Test session factory
        print("\nTesting async session...")
        async for session in get_async_db():
            result = await session.execute(text("SELECT COUNT(*) FROM startups"))
            count = result.scalar()
            print(f"✅ Startup count: {count}")
            break
            
    except Exception as e:
        print(f"❌ Error: {e}")
        print(f"❌ Error type: {type(e).__name__}")
        
        # Provide specific guidance based on error type
        if "asyncpg" in str(e).lower():
            print("\n🔧 Fix: Install asyncpg driver:")
            print("  pip install asyncpg")
        elif "connection" in str(e).lower() or "connect" in str(e).lower():
            print("\n🔧 Fix: Check database connectivity:")
            print("  1. Make sure PostgreSQL is running")
            print("  2. For Docker: docker-compose up -d db")
            print("  3. For local: brew services start postgresql")
            print("  4. Verify connection string in .env file")
        elif "does not exist" in str(e).lower():
            print("\n🔧 Fix: Create database:")
            print("  createdb vc_matching_platform")
        
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(test_async_connection())