# Staging Environment Configuration
# Copy this to .env for staging deployment

# Application
ENVIRONMENT=staging
DEBUG=false
SECRET_KEY=your-staging-secret-key-please-change-this
LOG_LEVEL=INFO

# Database
POSTGRES_USER=vcmatching
POSTGRES_PASSWORD=staging-db-password-change-me
POSTGRES_DB=vc_matching_staging
DATABASE_URL=*************************************************************/vc_matching_staging

# Redis
REDIS_URL=redis://redis:6379/0

# API Configuration
API_V1_PREFIX=/api/v1
CORS_ORIGINS=["http://localhost","http://localhost:80","http://localhost:8000"]
RATE_LIMIT_ENABLED=true
RATE_LIMIT_PER_MINUTE=100
RATE_LIMIT_PER_HOUR=5000

# Authentication
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# OpenAI (Required for AI features)
OPENAI_API_KEY=your-openai-api-key-here

# Async Database
USE_ASYNC_DB=true

# Celery Configuration
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0

# Monitoring
GRAFANA_USER=admin
GRAFANA_PASSWORD=staging-grafana-password-change-me

# Scraping
SCRAPING_USER_AGENT="VC-Matching-Bot/1.0 (Staging)"
SCRAPING_TIMEOUT=30

# Feature Flags
ENABLE_AI_ANALYSIS=true
ENABLE_WEB_SCRAPING=true
ENABLE_EMAIL_NOTIFICATIONS=false