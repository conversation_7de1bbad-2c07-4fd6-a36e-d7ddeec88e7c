#!/usr/bin/env python3
"""Quick script to check test coverage for specific modules."""

import subprocess
import sys

# Modules we've improved
improved_modules = [
    "src/core/services/match_service.py",
    "src/core/services/matching_engine.py", 
    "src/api/v1/endpoints/matches.py",
    "src/api/v1/endpoints/startups.py",
    "src/api/v1/endpoints/vcs.py"
]

# Run tests on specific improved test files
test_files = [
    "tests/unit/test_match_service.py",
    "tests/unit/test_matching_engine.py",
    "tests/unit/api/test_matches_endpoints_comprehensive.py",
    "tests/unit/api/test_matches_dependencies.py",
    "tests/unit/api/test_startups_endpoints_comprehensive.py", 
    "tests/unit/api/test_startups_dependencies.py",
    "tests/unit/api/test_vcs_endpoints_comprehensive.py",
    "tests/unit/api/test_vcs_dependencies.py"
]

# Run pytest with coverage
cmd = [
    sys.executable, "-m", "pytest",
    *test_files,
    "--cov=" + ",".join(improved_modules),
    "--cov-report=term-missing:skip-covered",
    "-q"
]

print("Checking coverage for improved modules...")
print("=" * 60)
result = subprocess.run(cmd, capture_output=True, text=True)
print(result.stdout)
if result.stderr:
    print("STDERR:", result.stderr)