# Implementation Checklist - Use Before Every Change

## 🛑 STOP - Before You Code

### Step 1: Identify the Task
- [ ] What specific issue am I fixing?
- [ ] Which layer(s) will this touch?
- [ ] What's the expected outcome?

### Step 2: Consult the Right Agent
- [ ] Which expert agent should guide this work?
  - Infrastructure fix? → `devops_deployment_expert`
  - Domain logic? → `ddd_expert`
  - Test coverage? → `tdd_specialist`
  - API work? → `fastapi_architect`
  - Performance? → `database_performance_expert`

### Step 3: Check Existing Patterns
- [ ] Search for similar implementations
- [ ] Review relevant base classes
- [ ] Check test patterns for this type

### Step 4: Plan the Tests
- [ ] What test should I write first?
- [ ] Where does it go? (`tests/unit/` or `tests/integration/`)
- [ ] What should it test?

## 📝 Implementation Steps

### For Bug Fixes:
1. [ ] Write failing test that reproduces the bug
2. [ ] Run test - confirm it fails
3. [ ] Fix the bug with minimal code
4. [ ] Run test - confirm it passes
5. [ ] Check no other tests broke
6. [ ] Refactor if needed
7. [ ] Run all tests again

### For New Features:
1. [ ] Write integration test for the feature
2. [ ] Write unit tests for components
3. [ ] Implement domain logic (if any)
4. [ ] Implement service logic (if any)
5. [ ] Implement repository (if any)
6. [ ] Implement API endpoint (if any)
7. [ ] Run all tests
8. [ ] Update documentation

### For Refactoring:
1. [ ] Ensure comprehensive test coverage exists
2. [ ] Run tests - all must be green
3. [ ] Make small, incremental changes
4. [ ] Run tests after each change
5. [ ] Commit working code frequently

## ✅ Quality Checks

### Before Committing:
- [ ] All tests passing?
- [ ] No layer violations?
- [ ] Following existing patterns?
- [ ] No code duplication?
- [ ] Type hints added?
- [ ] Error handling complete?
- [ ] Documentation updated?

### Code Smells to Avoid:
- [ ] Domain models importing infrastructure
- [ ] Services implementing business logic
- [ ] Repositories doing more than persistence
- [ ] API endpoints with business logic
- [ ] Tests that test implementation, not behavior
- [ ] Mocking what you don't own

## 🚀 Ready to Implement?

If you checked all boxes above, proceed with confidence!
If not, stop and address the gaps first.

Remember: **Discipline creates quality**