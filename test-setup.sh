#!/bin/bash

# Test script to demonstrate the setup functionality
# This script tests the environment setup without starting services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_header() {
    echo -e "\n${PURPLE}🚀 $1${NC}"
    echo -e "${PURPLE}$(printf '=%.0s' {1..50})${NC}"
}

print_header "Testing VC Matching Platform Setup"

# Test 1: Check if scripts exist and are executable
print_header "Script Validation"

if [ -f "run.sh" ] && [ -x "run.sh" ]; then
    print_status "run.sh exists and is executable"
else
    print_warning "run.sh missing or not executable"
fi

if [ -f "scripts/setup-and-run.sh" ] && [ -x "scripts/setup-and-run.sh" ]; then
    print_status "scripts/setup-and-run.sh exists and is executable"
else
    print_warning "scripts/setup-and-run.sh missing or not executable"
fi

# Test 2: Check environment templates
print_header "Environment Templates"

if [ -f ".env.staging" ]; then
    print_status ".env.staging template found"
    print_info "Contains $(wc -l < .env.staging) lines of configuration"
else
    print_warning ".env.staging template missing"
fi

if [ -f ".env.example" ]; then
    print_status ".env.example template found"
else
    print_info ".env.example not found (optional)"
fi

# Test 3: Check if .env exists
if [ -f ".env" ]; then
    print_info ".env file already exists"
    if grep -q "your-openai-api-key-here\|your-staging-secret-key-please-change-this" ".env"; then
        print_warning ".env file needs configuration (contains placeholder values)"
    else
        print_status ".env file appears to be configured"
    fi
else
    print_info ".env file not found (will be created by setup script)"
fi

# Test 4: Check Python environment
print_header "Python Environment"

if command -v python3 >/dev/null 2>&1; then
    python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    print_status "Python $python_version found"
else
    print_warning "Python 3 not found"
fi

if command -v pip3 >/dev/null 2>&1; then
    pip_version=$(pip3 --version | cut -d' ' -f2)
    print_status "pip3 $pip_version found"
elif command -v pip >/dev/null 2>&1; then
    pip_version=$(pip --version | cut -d' ' -f2)
    print_status "pip $pip_version found"
else
    print_warning "pip not found"
fi

# Test 5: Check project structure
print_header "Project Structure"

required_dirs=("src" "tests" "scripts" "alembic")
for dir in "${required_dirs[@]}"; do
    if [ -d "$dir" ]; then
        print_status "$dir/ directory exists"
    else
        print_warning "$dir/ directory missing"
    fi
done

required_files=("requirements.txt" "alembic.ini" "docker-compose.yml")
for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        print_status "$file exists"
    else
        print_warning "$file missing"
    fi
done

# Test 6: Test help functionality
print_header "Script Help Test"

if ./run.sh --help >/dev/null 2>&1; then
    print_status "Help command works"
else
    print_warning "Help command failed"
fi

# Test 7: Check for Docker (optional)
print_header "Optional Dependencies"

if command -v docker >/dev/null 2>&1; then
    if docker info >/dev/null 2>&1; then
        print_status "Docker is installed and running"
    else
        print_warning "Docker is installed but not running"
    fi
else
    print_info "Docker not installed (required for docker/hybrid modes)"
fi

# Test 8: Check for local services (optional)
if command -v psql >/dev/null 2>&1; then
    print_status "PostgreSQL client (psql) available"
else
    print_info "PostgreSQL client not installed (required for local mode)"
fi

if command -v redis-cli >/dev/null 2>&1; then
    print_status "Redis client (redis-cli) available"
else
    print_info "Redis client not installed (required for local mode)"
fi

# Test 9: Port availability
print_header "Port Availability"

check_port() {
    local port=$1
    local service=$2
    if nc -z localhost "$port" 2>/dev/null; then
        print_warning "Port $port is in use (may conflict with $service)"
    else
        print_status "Port $port is available for $service"
    fi
}

check_port 8000 "API Server"
check_port 5432 "PostgreSQL"
check_port 6379 "Redis"
check_port 5555 "Flower"

# Summary
print_header "Test Summary"

print_info "Setup script test completed!"
print_info "The setup scripts appear to be properly configured."

echo -e "\n${CYAN}Next Steps:${NC}"
echo -e "1. Run ${YELLOW}./run.sh${NC} to start the interactive setup"
echo -e "2. Choose your preferred mode (docker/hybrid/local)"
echo -e "3. Follow the prompts to configure your environment"

echo -e "\n${CYAN}Recommended Mode:${NC}"
if command -v docker >/dev/null 2>&1 && docker info >/dev/null 2>&1; then
    echo -e "  ${GREEN}Docker mode${NC} - All services in containers"
    echo -e "  ${GREEN}Hybrid mode${NC} - Services in Docker, app locally (best for development)"
elif command -v docker >/dev/null 2>&1; then
    echo -e "  ${YELLOW}Start Docker Desktop first, then use Docker or Hybrid mode${NC}"
else
    echo -e "  ${YELLOW}Install Docker for the best experience${NC}"
    echo -e "  ${BLUE}Or use Local mode if you have PostgreSQL and Redis installed${NC}"
fi

print_status "Test completed successfully! 🎉"
