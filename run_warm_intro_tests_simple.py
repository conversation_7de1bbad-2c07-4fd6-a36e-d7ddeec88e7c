#!/usr/bin/env python3
"""Simple test runner for warm intro functionality."""

import sys
import subprocess
import os

# Set PYTHONPATH
env = os.environ.copy()
env["PYTHONPATH"] = "."

# Run just the connection model tests with coverage for that module only
cmd = [
    "python3", "-m", "pytest",
    "tests/unit/models/test_connection_models.py",
    "-v",
    "--cov=src.core.models.connection",
    "--cov-report=term-missing",
    "--cov-fail-under=80"
]

print("Running connection model tests...")
print(" ".join(cmd))
print()

result = subprocess.run(cmd, env=env)

if result.returncode == 0:
    print("\n✅ Connection model tests passed with 80%+ coverage!")
else:
    print("\n❌ Tests failed!")
    sys.exit(1)