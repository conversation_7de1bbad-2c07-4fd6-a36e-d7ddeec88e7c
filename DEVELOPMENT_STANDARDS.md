# BiLat Development Standards & Discipline Guide

## The Problem We're Solving

We've been adding code without following consistent patterns, creating:
- Test files that don't follow TDD principles
- Quick fixes that violate clean architecture
- Duplicate code instead of using existing patterns
- Infrastructure code mixed with domain logic

## Core Development Principles

### 1. **TDD FIRST - No Exceptions**
```bash
# BEFORE writing any code:
1. Write the test first
2. Run test - watch it fail
3. Write minimal code to pass
4. Refactor
5. Commit
```

### 2. **Clean Architecture Boundaries**
```
Domain Layer (src/core/models/)
  ↓ (depends on nothing)
Application Layer (src/core/services/)
  ↓ (depends only on domain)
Infrastructure Layer (src/database/, src/adapters/)
  ↓ (implements interfaces)
Presentation Layer (src/api/)
```

### 3. **DDD Principles**
- Domain models contain business logic
- Services orchestrate, don't implement business rules
- Repositories handle persistence only
- Value objects are immutable
- Aggregates maintain invariants

## Code Standards Checklist

### Before Writing ANY Code:

#### 1. **Identify the Layer**
- [ ] Is this domain logic? → `src/core/models/`
- [ ] Is this orchestration? → `src/core/services/`
- [ ] Is this infrastructure? → `src/database/` or `src/adapters/`
- [ ] Is this API related? → `src/api/`

#### 2. **Check Existing Patterns**
- [ ] Does a similar pattern exist? Use it.
- [ ] Is there a base class? Extend it.
- [ ] Is there a port/interface? Implement it.

#### 3. **Write Test First**
- [ ] Unit test in `tests/unit/`
- [ ] Integration test in `tests/integration/`
- [ ] Follow existing test patterns
- [ ] Use existing fixtures

### File Structure Standards

#### Domain Model Example:
```python
# src/core/models/investment_criteria.py
from dataclasses import dataclass
from typing import List, Set
from decimal import Decimal

@dataclass(frozen=True)  # Value objects are immutable
class InvestmentCriteria:
    """Domain value object - contains business logic"""
    min_check_size: Decimal
    max_check_size: Decimal
    sectors: Set[str]
    stages: Set[str]
    
    def __post_init__(self):
        # Business invariants
        if self.min_check_size > self.max_check_size:
            raise ValueError("Min check size cannot exceed max")
    
    def matches_startup(self, startup: 'Startup') -> bool:
        """Business logic belongs in domain"""
        return (
            startup.stage in self.stages and
            startup.sector in self.sectors
        )
```

#### Service Example:
```python
# src/core/services/investment_service.py
from src.core.ports.repository_port import RepositoryPort
from src.core.models.investment_criteria import InvestmentCriteria

class InvestmentService:
    """Application service - orchestrates, doesn't implement business logic"""
    
    def __init__(self, repository: RepositoryPort):
        self._repository = repository  # Dependency injection
    
    async def find_matching_investments(
        self, 
        criteria: InvestmentCriteria
    ) -> List[Investment]:
        """Orchestration only - business logic is in domain"""
        startups = await self._repository.find_by_criteria(
            sectors=criteria.sectors,
            stages=criteria.stages
        )
        # Use domain logic
        return [s for s in startups if criteria.matches_startup(s)]
```

#### Repository Example:
```python
# src/database/repositories/investment_repository.py
from src.core.ports.repository_port import RepositoryPort

class InvestmentRepository(RepositoryPort):
    """Infrastructure - implements domain interface"""
    
    async def find_by_criteria(self, **kwargs) -> List[Investment]:
        """Pure persistence logic only"""
        query = select(InvestmentModel)
        if sectors := kwargs.get('sectors'):
            query = query.where(InvestmentModel.sector.in_(sectors))
        # etc...
```

## Workflow Discipline

### 1. **Feature Implementation Process**
```bash
# Step 1: Use the appropriate agent
Task: "Using the ddd_expert agent, design the investment tracking feature"

# Step 2: Write integration test first
tests/integration/test_investment_tracking.py

# Step 3: Write unit tests
tests/unit/test_investment_criteria.py

# Step 4: Implement domain model
src/core/models/investment_criteria.py

# Step 5: Implement service
src/core/services/investment_service.py

# Step 6: Implement repository
src/database/repositories/investment_repository.py

# Step 7: Add API endpoint
src/api/v1/endpoints/investments.py
```

### 2. **Code Review Checklist**
- [ ] Tests written first and passing?
- [ ] Domain logic in domain layer?
- [ ] No infrastructure imports in domain?
- [ ] Following existing patterns?
- [ ] No code duplication?
- [ ] Proper error handling?
- [ ] Type hints everywhere?

### 3. **Refactoring Discipline**
```bash
# NEVER refactor without tests
1. Ensure comprehensive test coverage
2. Run tests - all green
3. Refactor in small steps
4. Run tests after each step
5. Commit working code frequently
```

## Common Violations to Avoid

### ❌ **DON'T: Mix Layers**
```python
# BAD - Domain model with infrastructure
class Startup:
    def save(self):
        db.session.add(self)  # NO! Domain shouldn't know about DB
```

### ❌ **DON'T: Business Logic in Services**
```python
# BAD - Service implementing business rules
class MatchService:
    def calculate_compatibility(self):
        # Complex business logic here  # NO! This belongs in domain
```

### ❌ **DON'T: Skip Tests**
```python
# BAD - Writing code without test
def new_feature():
    # Implementation without test  # NO! Test first
```

### ✅ **DO: Follow Patterns**
```python
# GOOD - Use existing base classes
class StartupRepository(BaseRepository):
    model = StartupModel
```

## Enforcement Strategy

### 1. **Pre-Implementation Checklist**
Before coding, ask:
1. Which agent should guide this work?
2. What tests need to be written?
3. Which layer does this belong to?
4. What existing patterns can I follow?

### 2. **Implementation Guards**
- Run tests before and after changes
- Use type checking: `mypy src/`
- Use linting: `ruff check src/`
- Check coverage: `pytest --cov=src`

### 3. **Post-Implementation Review**
- Did I follow TDD?
- Are layers properly separated?
- Did I reuse existing patterns?
- Is the code testable?

## Quick Reference

### Layer Responsibilities
| Layer | Purpose | Dependencies | Example |
|-------|---------|--------------|---------|
| Domain | Business logic | None | `Startup.is_fundable()` |
| Application | Orchestration | Domain only | `MatchService.create_match()` |
| Infrastructure | External systems | All layers | `PostgresRepository.save()` |
| Presentation | User interface | Application | `POST /api/v1/startups` |

### File Locations
| Type | Location | Example |
|------|----------|---------|
| Domain Models | `src/core/models/` | `startup.py` |
| Services | `src/core/services/` | `match_service.py` |
| Ports | `src/core/ports/` | `repository_port.py` |
| Repositories | `src/database/repositories/` | `startup_repository.py` |
| API Endpoints | `src/api/v1/endpoints/` | `startups.py` |
| Unit Tests | `tests/unit/` | `test_startup.py` |
| Integration Tests | `tests/integration/` | `test_matching_flow.py` |

## The Path Forward

1. **Stop** adding code without tests
2. **Stop** mixing layers
3. **Start** using existing patterns
4. **Start** asking agents for guidance
5. **Always** refactor after making tests pass

Remember: The goal is maintainable, testable code that follows established patterns. When in doubt, check existing code or ask the appropriate agent.