# Python 3.13 compatible requirements
# Core FastAPI and web framework
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
gunicorn>=21.2.0

# Database
sqlalchemy>=2.0.23
asyncpg>=0.29.0
aiosqlite>=0.19.0
alembic>=1.13.0
psycopg2-binary>=2.9.9

# Data validation and settings
pydantic[email]>=2.5.0
pydantic-settings>=2.1.0

# Redis and Celery
redis>=5.0.0
celery>=5.3.0
flower>=2.0.0

# AI and Lang<PERSON>hain (updated versions)
openai>=1.12.0
langchain>=0.1.0
langchain-openai>=0.0.8

# HTTP client
httpx>=0.25.0

# Web scraping
beautifulsoup4>=4.12.0
lxml>=4.9.0
selectolax>=0.3.17
playwright>=1.40.0

# Utilities
python-dotenv>=1.0.0
tenacity>=8.2.0
jinja2>=3.1.0
python-multipart>=0.0.6

# Authentication and security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4

# Email
sendgrid>=6.11.0
email-validator>=2.1.0

# Vector database
pgvector>=0.3.0

# Scientific computing (updated for Python 3.13)
numpy>=1.26.0

# Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
