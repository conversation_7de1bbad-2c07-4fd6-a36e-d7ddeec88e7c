#!/bin/bash

# Quick launcher for VC Matching Platform
# This is a simple wrapper around the main setup script

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 VC Matching Platform - Quick Launcher${NC}"
echo -e "${YELLOW}=========================================${NC}"

# Check if the main script exists
if [ ! -f "scripts/setup-and-run.sh" ]; then
    echo -e "${RED}❌ Main setup script not found!${NC}"
    exit 1
fi

# If no arguments provided, show quick options
if [ $# -eq 0 ]; then
    echo -e "\n${CYAN}Quick Start Options:${NC}"
    echo -e "  ${YELLOW}1.${NC} Docker mode (recommended)     - ./run.sh docker"
    echo -e "  ${YELLOW}2.${NC} Hybrid mode (development)     - ./run.sh hybrid"
    echo -e "  ${YELLOW}3.${NC} Local mode (advanced)         - ./run.sh local"
    echo -e "  ${YELLOW}4.${NC} Reset and run with Docker     - ./run.sh docker --reset"
    echo -e "\n${CYAN}For more options:${NC} ./run.sh --help"
    echo -e "\n${YELLOW}Choose a mode or press Ctrl+C to exit${NC}"
    
    read -p "Enter mode (docker/hybrid/local): " mode
    if [ -z "$mode" ]; then
        mode="docker"
    fi
    
    # Execute the main script
    exec ./scripts/setup-and-run.sh "$mode"
else
    # Pass all arguments to the main script
    exec ./scripts/setup-and-run.sh "$@"
fi
