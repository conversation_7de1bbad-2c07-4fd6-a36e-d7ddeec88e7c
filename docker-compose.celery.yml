version: '3.8'

services:
  # Celery Worker for AI Analysis
  celery-ai:
    build: .
    command: celery -A celery_worker worker -Q ai_analysis --loglevel=info --concurrency=2
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ENVIRONMENT=production
    depends_on:
      - db
      - redis
    volumes:
      - ./src:/app/src
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  # Celery Worker for Data Enrichment
  celery-data:
    build: .
    command: celery -A celery_worker worker -Q data_enrichment --loglevel=info --concurrency=4
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - ENVIRONMENT=production
    depends_on:
      - db
      - redis
    volumes:
      - ./src:/app/src
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # Celery Worker for Notifications
  celery-notifications:
    build: .
    command: celery -A celery_worker worker -Q notifications --loglevel=info --concurrency=2
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - ENVIRONMENT=production
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_PORT=${SMTP_PORT}
      - EMAIL_FROM=${EMAIL_FROM}
    depends_on:
      - db
      - redis
    volumes:
      - ./src:/app/src
    restart: unless-stopped

  # Celery Worker for Default Queue
  celery-default:
    build: .
    command: celery -A celery_worker worker -Q default --loglevel=info --concurrency=2
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - ENVIRONMENT=production
    depends_on:
      - db
      - redis
    volumes:
      - ./src:/app/src
    restart: unless-stopped

  # Celery Beat Scheduler
  celery-beat:
    build: .
    command: celery -A celery_worker beat --loglevel=info
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - ENVIRONMENT=production
    depends_on:
      - db
      - redis
    volumes:
      - ./src:/app/src
    restart: unless-stopped

  # Flower - Celery Monitoring
  flower:
    build: .
    command: celery -A celery_worker flower --port=5555 --url_prefix=flower
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - FLOWER_BASIC_AUTH=${FLOWER_BASIC_AUTH:-admin:password}
    ports:
      - "5555:5555"
    depends_on:
      - redis
    volumes:
      - ./src:/app/src
    restart: unless-stopped

  # Redis (if not already in main docker-compose)
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped

volumes:
  redis_data: