"""Test script for web scrapers."""

import asyncio
import logging
from src.scrapers import CompanyScraper, LinkedInScraper

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_company_scraper():
    """Test the general company scraper."""
    print("\n=== Testing Company Scraper ===")
    
    scraper = CompanyScraper()
    
    # Test with a few company websites
    test_urls = [
        "https://stripe.com",
        "https://openai.com",
    ]
    
    for url in test_urls:
        print(f"\nScraping {url}...")
        result = await scraper.scrape(url)
        
        if result.success:
            print(f"✅ Success!")
            print(f"  Name: {result.data.get('name')}")
            print(f"  Description: {result.data.get('description', 'N/A')[:100]}...")
            print(f"  Industry: {result.data.get('industry', 'N/A')}")
            print(f"  Technologies: {', '.join(result.data.get('technologies', []))}")
            print(f"  Social Links: {list(result.data.get('social_links', {}).keys())}")
        else:
            print(f"❌ Failed: {result.error}")

async def test_linkedin_scraper():
    """Test the LinkedIn scraper."""
    print("\n=== Testing LinkedIn Scraper ===")
    print("Note: LinkedIn scraping requires Playwright browser setup")
    
    # First, install Playwright browsers if not already installed
    try:
        from playwright.async_api import async_playwright
        playwright = await async_playwright().start()
        await playwright.stop()
    except Exception as e:
        print(f"⚠️  Playwright not set up. Run: playwright install chromium")
        return
    
    scraper = LinkedInScraper()
    
    # Test with LinkedIn company URLs
    # Note: These may require login or hit rate limits
    test_urls = [
        "https://www.linkedin.com/company/stripe",
        "https://www.linkedin.com/company/openai",
    ]
    
    for url in test_urls:
        print(f"\nScraping {url}...")
        try:
            result = await scraper.scrape(url)
            
            if result.success:
                print(f"✅ Success!")
                print(f"  Name: {result.data.get('name')}")
                print(f"  Tagline: {result.data.get('tagline', 'N/A')}")
                print(f"  Industry: {result.data.get('industry', 'N/A')}")
                print(f"  Company Size: {result.data.get('company_size', 'N/A')}")
                print(f"  Founded: {result.data.get('founded', 'N/A')}")
                print(f"  Headquarters: {result.data.get('headquarters', 'N/A')}")
                print(f"  Specialties: {', '.join(result.data.get('specialties', []))}")
            else:
                print(f"❌ Failed: {result.error}")
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")

async def main():
    """Run all scraper tests."""
    print("🕷️  Testing Web Scrapers")
    print("=" * 50)
    
    # Test general company scraper
    await test_company_scraper()
    
    # Test LinkedIn scraper
    # Commenting out by default as it requires browser setup
    # await test_linkedin_scraper()
    
    print("\n✅ Scraper tests complete!")
    print("\nTo test LinkedIn scraper:")
    print("1. Install Playwright: pip install playwright")
    print("2. Install browser: playwright install chromium")
    print("3. Uncomment the LinkedIn test in this script")

if __name__ == "__main__":
    asyncio.run(main())