#!/usr/bin/env python3
"""Check test coverage summary for the project."""

import subprocess
import re

# Target modules we've been working on
TARGET_MODULES = [
    "src/core/services/match_service.py",
    "src/core/services/matching_engine.py",
    "src/api/v1/endpoints/matches.py",
    "src/api/v1/endpoints/startups.py", 
    "src/api/v1/endpoints/vcs.py",
    "src/core/services/startup_service.py",
    "src/core/services/vc_service.py",
    "src/database/repositories/match_repository.py",
    "src/database/repositories/startup_repository.py",
    "src/database/repositories/vc_repository.py"
]

# Run pytest with coverage for all unit tests
cmd = ["python3", "-m", "pytest", "tests/unit/", "--cov=src", "--cov-report=term", "-q"]
result = subprocess.run(cmd, capture_output=True, text=True, env={'PYTHONPATH': '.'})

output = result.stdout + result.stderr

# Parse coverage results
print("Test Coverage Summary - Target Modules")
print("=" * 70)
print(f"{'Module':<50} {'Before':<10} {'After':<10} {'Change':<10}")
print("-" * 70)

# Initial coverage values
initial_coverage = {
    "match_service.py": 19,
    "matching_engine.py": 24,
    "matches.py": 33,
    "startups.py": 31,
    "vcs.py": 36,
    "startup_service.py": 34,
    "vc_service.py": 24,
    "match_repository.py": 26,
    "startup_repository.py": 25,
    "vc_repository.py": 24
}

total_before = 0
total_after = 0
count = 0

for line in output.split('\n'):
    for module in TARGET_MODULES:
        if module in line and '%' in line:
            parts = line.split()
            if len(parts) >= 5:
                try:
                    coverage_str = parts[-2] if parts[-1] != '%' else parts[-1].rstrip('%')
                    if coverage_str.endswith('%'):
                        coverage_str = coverage_str[:-1]
                    current_coverage = int(coverage_str)
                    
                    # Get module name for lookup
                    module_name = module.split('/')[-1]
                    before = initial_coverage.get(module_name, 0)
                    
                    print(f"{module_name:<50} {before:<10}% {current_coverage:<10}% +{current_coverage - before:<9}%")
                    
                    total_before += before
                    total_after += current_coverage
                    count += 1
                except (ValueError, IndexError):
                    pass

if count > 0:
    print("-" * 70)
    avg_before = total_before / count
    avg_after = total_after / count
    print(f"{'AVERAGE':<50} {avg_before:<10.1f}% {avg_after:<10.1f}% +{avg_after - avg_before:<9.1f}%")
    print("=" * 70)

# Extract overall coverage
total_match = re.search(r'TOTAL\s+\d+\s+\d+\s+(\d+)%', output)
if total_match:
    total_coverage = int(total_match.group(1))
    print(f"\nOverall Project Coverage: {total_coverage}%")
    print(f"Target Coverage: 80%")
    print(f"Remaining: {80 - total_coverage}%")
    
    if total_coverage >= 80:
        print("\n✅ Congratulations! We've achieved the 80% coverage goal!")
    else:
        print(f"\n📊 Progress: {total_coverage}% / 80%")