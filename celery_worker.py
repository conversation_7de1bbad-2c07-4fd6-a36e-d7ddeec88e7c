#!/usr/bin/env python
"""
Celery worker entry point for the VC Matching Platform.

To run the worker:
    celery -A celery_worker worker --loglevel=info

To run specific queues:
    celery -A celery_worker worker -Q ai_analysis --loglevel=info
    celery -A celery_worker worker -Q data_enrichment --loglevel=info

To run the beat scheduler:
    celery -A celery_worker beat --loglevel=info

To run flower (monitoring):
    celery -A celery_worker flower
"""
import os
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import and configure Celery app
from src.workers import celery_app

# Configure Celery for production
if os.getenv('ENVIRONMENT') == 'production':
    celery_app.conf.update(
        # Production-specific settings
        worker_send_task_events=True,
        task_send_sent_event=True,
        task_track_started=True,
        task_publish_retry=True,
        task_publish_retry_policy={
            'max_retries': 3,
            'interval_start': 0,
            'interval_step': 0.2,
            'interval_max': 0.2,
        },
        # Security settings
        worker_hijack_root_logger=False,
        worker_log_format='[%(asctime)s: %(levelname)s/%(processName)s] %(message)s',
        worker_task_log_format='[%(asctime)s: %(levelname)s/%(processName)s][%(task_name)s(%(task_id)s)] %(message)s',
    )

if __name__ == '__main__':
    celery_app.start()