# Environment configuration
PROJECT_NAME="VC-Startup Matching Platform"
VERSION="1.0.0"
DEBUG=true

# API Configuration
API_V1_STR="/api/v1"
SECRET_KEY="CHANGE-THIS-REQUIRED-generate-with-python3-c-import-secrets-print-secrets.token_urlsafe(32))"

# Database
DATABASE_URL=postgresql://user:password@localhost/vc_matching

# Redis
REDIS_URL=redis://localhost:6379

# AI Configuration
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-4
OPENAI_TEMPERATURE=0.3
OPENAI_MAX_TOKENS=1000

# CORS
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8000"]

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_PER_HOUR=1000