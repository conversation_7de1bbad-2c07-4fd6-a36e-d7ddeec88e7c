# BiLat Integration Test Execution Guide

## Overview

This guide provides step-by-step instructions for running the comprehensive integration test suite that verifies data persistence for startups and VCs in the BiLat project.

The test suite validates:
- ✅ Startup CRUD operations via API endpoints
- ✅ VC CRUD operations via API endpoints  
- ✅ Match creation and retrieval
- ✅ PostgreSQL data persistence verification
- ✅ Error handling and edge cases
- ✅ Concurrent operations
- ✅ Matching algorithm with real data

## Prerequisites

### 1. Database Setup
Ensure PostgreSQL is running and accessible:

```bash
# Option 1: Local PostgreSQL installation
# Make sure PostgreSQL service is running
sudo systemctl start postgresql  # Linux
brew services start postgresql    # macOS

# Option 2: Docker PostgreSQL
docker run --name postgres-test \
  -e POSTGRES_DB=vc_matching \
  -e POSTGRES_USER=user \
  -e POSTGRES_PASSWORD=password \
  -p 5432:5432 \
  -d postgres:15
```

### 2. Environment Configuration
Create or update your `.env` file with test database settings:

```env
# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/vc_matching
DATABASE_ECHO=false
DATABASE_POOL_SIZE=5
DATABASE_MAX_OVERFLOW=10

# API Configuration
DEBUG=true
SECRET_KEY=test-secret-key-for-integration-tests
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Rate Limiting (disabled for tests)
RATE_LIMIT_ENABLED=false

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:8080,http://test

# Logging
LOG_LEVEL=INFO
```

### 3. Install Dependencies

```bash
# Install main project dependencies
pip install -r requirements.txt

# Install additional test dependencies
pip install -r requirements-integration-test.txt
```

## Running the Tests

### Method 1: Direct Script Execution (Recommended)

The integration test script can be run directly as a standalone Python script:

```bash
# Navigate to project root
cd /path/to/vc-matching-platform

# Run the integration test script
python test_integration_data_persistence.py
```

### Method 2: Using pytest

You can also run it using pytest for more detailed output:

```bash
# Run with pytest
pytest test_integration_data_persistence.py -v -s

# Run with coverage reporting
pytest test_integration_data_persistence.py -v -s --cov=src

# Run with HTML report generation
pytest test_integration_data_persistence.py -v -s --html=integration_test_report.html
```

### Method 3: Database Initialization First

If you want to ensure a clean database state:

```bash
# Initialize database tables first
python scripts/init_db.py

# Then run the integration tests
python test_integration_data_persistence.py
```

## Test Output Explanation

### Console Output Format

The test script provides detailed, colored console output:

```
🚀 Setting up Integration Test Environment
==========================================================
✅ Database tables created
✅ Database connection verified
✅ Test environment setup complete

🧪 Running Integration Tests
==========================================================

📊 Testing Startup CRUD Operations
✅ Create Startup: Created startup with ID: 123e4567-e89b-12d3-a456-426614174000
✅ Get Startup by ID: Retrieved startup: InnovateTech_abc12345
✅ List Startups: Found 1 startups including ours
✅ Update Startup: Updated team size to 25
✅ Startup CRUD Operations: All CRUD operations working

🏢 Testing VC CRUD Operations
✅ Create VC: Created VC with ID: 234e5678-e89b-12d3-a456-426614174001
...
```

### Success Indicators

- ✅ **Green checkmarks**: Test passed successfully
- ❌ **Red X marks**: Test failed
- 🧹 **Cleanup messages**: Resources being cleaned up
- 📊 **Summary section**: Overall test results

### Failure Indicators

If tests fail, you'll see detailed error messages:

```
❌ Create Startup: Create failed with status 422: {"detail":"Validation error"}
❌ Database connection failed: FATAL: database "vc_matching" does not exist
```

## Test Results

### Result Files

The test script generates several output files:

1. **Console Output**: Real-time colored output during test execution
2. **JSON Results**: `integration_test_results_YYYYMMDD_HHMMSS.json` - Detailed test results
3. **Coverage Report** (if using pytest): HTML coverage report

### JSON Results Structure

```json
{
  "passed": 15,
  "failed": 2,
  "errors": [
    "Create Match: Authentication required",
    "Matching Algorithm: OpenAI API key not configured"
  ],
  "details": [
    {
      "test": "Create Startup",
      "success": true,
      "details": "Created startup with ID: 123e4567...",
      "error": null,
      "timestamp": "2024-01-15T10:30:45.123456"
    }
  ]
}
```

## Troubleshooting

### Common Issues

#### 1. Database Connection Errors

```
❌ Database connection failed: FATAL: database "vc_matching" does not exist
```

**Solution:**
```bash
# Create the database
createdb vc_matching

# Or using SQL
psql -c "CREATE DATABASE vc_matching;"
```

#### 2. Authentication Errors

```
❌ Create Match: Authentication required
```

**Solution:** The test uses a test API key. Ensure the API key validation in deps.py accepts "test-api-key":

```python
# In src/api/v1/deps.py
if api_key != "test-api-key":
    raise UnauthorizedError("Invalid API key")
```

#### 3. Import Errors

```
ModuleNotFoundError: No module named 'httpx'
```

**Solution:**
```bash
pip install -r requirements-integration-test.txt
```

#### 4. Database Table Errors

```
❌ relation "startups" does not exist
```

**Solution:**
```bash
# Initialize database tables
python scripts/init_db.py

# Or use Alembic migrations
alembic upgrade head
```

#### 5. Port Already in Use

```
OSError: [Errno 48] Address already in use
```

**Solution:**
```bash
# Find and kill the process using the port
lsof -ti:8000 | xargs kill -9

# Or change the port in config
export PORT=8001
```

### Debug Mode

For more detailed debugging, you can:

1. **Enable Database Echo:**
   ```env
   DATABASE_ECHO=true
   ```

2. **Increase Log Level:**
   ```env
   LOG_LEVEL=DEBUG
   ```

3. **Add Breakpoints:**
   ```python
   # Add this in the test script where you want to pause
   import pdb; pdb.set_trace()
   ```

## Test Customization

### Adding Custom Tests

To add your own tests to the integration suite:

1. **Create a new test method** in the `IntegrationTestRunner` class:

```python
async def test_my_custom_feature(self):
    """Test my custom feature."""
    print(f"\n{Colors.BLUE}🔧 Testing My Custom Feature{Colors.END}")
    
    try:
        # Your test logic here
        result = await self.client.get("/api/v1/my-endpoint")
        
        if result.status_code == 200:
            self.log_test_result("My Custom Test", True, "Feature working correctly")
        else:
            raise Exception(f"Unexpected status: {result.status_code}")
            
    except Exception as e:
        self.log_test_result("My Custom Test", False, error=e)
```

2. **Add it to the test runner** in `run_all_tests()`:

```python
async def run_all_tests(self):
    # ... existing tests ...
    await self.test_my_custom_feature()
    return self.test_results
```

### Configuring Test Data

Modify the `TestDataFactory` class to customize test data:

```python
@staticmethod
def create_startup_data(**overrides) -> Dict[str, Any]:
    base_data = {
        "name": f"MyCustomStartup_{uuid.uuid4().hex[:8]}",
        "sector": "Custom Sector",
        # ... other fields
    }
    base_data.update(overrides)
    return base_data
```

## Continuous Integration

### GitHub Actions Example

Create `.github/workflows/integration-tests.yml`:

```yaml
name: Integration Tests

on: [push, pull_request]

jobs:
  integration-tests:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test
          POSTGRES_DB: vc_matching
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      
      - name: Install dependencies
        run: |
          pip install -r requirements.txt
          pip install -r requirements-integration-test.txt
      
      - name: Run integration tests
        env:
          DATABASE_URL: postgresql://postgres:test@localhost:5432/vc_matching
        run: |
          python test_integration_data_persistence.py
```

## Performance Expectations

### Test Execution Time

- **Setup Phase**: 2-5 seconds (database initialization)
- **CRUD Tests**: 1-3 seconds per entity type
- **Match Tests**: 2-5 seconds (depends on matching algorithm)
- **Persistence Tests**: 1-2 seconds (direct database queries)
- **Error Tests**: 1-2 seconds
- **Concurrent Tests**: 3-7 seconds
- **Cleanup Phase**: 1-3 seconds

**Total Expected Time**: 15-30 seconds for complete test suite

### Success Criteria

The test suite considers the run successful if:
- ✅ 80% or more tests pass
- ✅ All CRUD operations work correctly
- ✅ Data persists in PostgreSQL
- ✅ Basic error handling works

### Performance Benchmarks

Expected performance benchmarks:
- **API Response Time**: < 500ms per request
- **Database Query Time**: < 100ms per query
- **Concurrent Operations**: Handle 5+ simultaneous requests
- **Memory Usage**: < 100MB during test execution

## Support

If you encounter issues not covered in this guide:

1. **Check the logs** in the console output for detailed error messages
2. **Review the JSON results file** for structured error information
3. **Verify database connectivity** using the minimal API test first
4. **Check environment variables** are correctly set
5. **Ensure all dependencies** are installed

The integration test suite is designed to provide comprehensive validation of the BiLat platform's core functionality while being easy to run and debug.