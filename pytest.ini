[pytest]
# pytest configuration file

# Test discovery patterns
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Test paths
testpaths = tests

# Output options
addopts = 
    -ra
    --strict-markers
    --cov=src
    --cov-report=term-missing
    --cov-report=html
    --cov-fail-under=80
    --maxfail=5
    --tb=short
    -p no:warnings

# Markers for test categorization
markers =
    unit: Unit tests (no external dependencies)
    integration: Integration tests (may use external services)
    slow: Slow running tests
    api: API endpoint tests
    ai: AI service tests
    auth: Authentication tests
    db: Database tests

# Async configuration
asyncio_mode = auto

# Coverage configuration
[coverage:run]
source = src
omit = 
    */tests/*
    */venv/*
    */__init__.py
    */migrations/*

[coverage:report]
precision = 2
show_missing = True
skip_covered = False

[coverage:html]
directory = htmlcov