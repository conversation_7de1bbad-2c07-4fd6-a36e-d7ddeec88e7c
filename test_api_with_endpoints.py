#!/usr/bin/env python3
"""
API with actual endpoints for testing data persistence.
"""

import sys
import uuid
from pathlib import Path
from typing import List, Optional
from datetime import datetime

# Add project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text

from src.database.setup import get_async_db
from src.database.models.startup import Startup as StartupModel
from src.database.models.vc import VC as VCModel
from src.database.models.match import Match as MatchModel
from src.core.config import settings


# Pydantic models for API
class StartupCreate(BaseModel):
    name: str
    sector: str
    stage: str
    description: Optional[str] = None
    website: Optional[str] = None
    team_size: Optional[int] = None
    monthly_revenue: Optional[float] = None

class StartupResponse(BaseModel):
    id: str
    name: str
    sector: str
    stage: str
    description: Optional[str]
    website: Optional[str]
    team_size: Optional[int]
    monthly_revenue: Optional[float]
    created_at: datetime

    class Config:
        from_attributes = True

class VCCreate(BaseModel):
    firm_name: str
    sectors: List[str]
    stages: List[str]
    thesis: Optional[str] = None
    website: Optional[str] = None
    fund_size: Optional[int] = None
    typical_check_size: Optional[int] = None

class VCResponse(BaseModel):
    id: str
    firm_name: str
    sectors: List[str]
    stages: List[str]
    thesis: Optional[str]
    website: Optional[str]
    fund_size: Optional[int]
    typical_check_size: Optional[int]
    created_at: datetime

    class Config:
        from_attributes = True

class MatchCreate(BaseModel):
    startup_id: str
    vc_id: str
    score: float = Field(ge=0, le=1)
    reasons: List[str] = []

class MatchResponse(BaseModel):
    id: str
    startup_id: str
    vc_id: str
    score: float
    reasons: List[str]
    created_at: datetime

    class Config:
        from_attributes = True


# Create FastAPI app
app = FastAPI(
    title="VC Matching Platform API",
    version="1.0.0",
    description="API with full CRUD endpoints for testing"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "VC Matching Platform API is running!"}


@app.get("/health")
async def health_check(db: AsyncSession = Depends(get_async_db)):
    """Health check endpoint."""
    try:
        result = await db.execute(text("SELECT 1"))
        await db.execute(text("SELECT COUNT(*) FROM startups"))
        return {
            "status": "healthy",
            "database": "connected",
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "database": "error",
            "error": str(e)
        }


# Startup endpoints
@app.post("/api/v1/startups", response_model=StartupResponse, status_code=201)
async def create_startup(
    startup: StartupCreate,
    db: AsyncSession = Depends(get_async_db)
):
    """Create a new startup."""
    db_startup = StartupModel(
        id=uuid.uuid4(),
        name=startup.name,
        sector=startup.sector,
        stage=startup.stage,
        description=startup.description,
        website=startup.website,
        team_size=startup.team_size,
        monthly_revenue=startup.monthly_revenue,
        created_at=datetime.utcnow()
    )
    
    db.add(db_startup)
    await db.commit()
    await db.refresh(db_startup)
    
    # Convert UUID to string for response
    return StartupResponse(
        id=str(db_startup.id),
        name=db_startup.name,
        sector=db_startup.sector,
        stage=db_startup.stage,
        description=db_startup.description,
        website=db_startup.website,
        team_size=db_startup.team_size,
        monthly_revenue=db_startup.monthly_revenue,
        created_at=db_startup.created_at
    )


@app.get("/api/v1/startups/{startup_id}", response_model=StartupResponse)
async def get_startup(
    startup_id: str,
    db: AsyncSession = Depends(get_async_db)
):
    """Get a startup by ID."""
    result = await db.execute(
        select(StartupModel).where(StartupModel.id == startup_id)
    )
    startup = result.scalar_one_or_none()
    
    if not startup:
        raise HTTPException(status_code=404, detail="Startup not found")
    
    return StartupResponse(
        id=str(startup.id),
        name=startup.name,
        sector=startup.sector,
        stage=startup.stage,
        description=startup.description,
        website=startup.website,
        team_size=startup.team_size,
        monthly_revenue=startup.monthly_revenue,
        created_at=startup.created_at
    )


@app.get("/api/v1/startups", response_model=List[StartupResponse])
async def list_startups(
    db: AsyncSession = Depends(get_async_db)
):
    """List all startups."""
    result = await db.execute(select(StartupModel))
    startups = result.scalars().all()
    
    return [
        StartupResponse(
            id=str(s.id),
            name=s.name,
            sector=s.sector,
            stage=s.stage,
            description=s.description,
            website=s.website,
            team_size=s.team_size,
            monthly_revenue=s.monthly_revenue,
            created_at=s.created_at
        ) for s in startups
    ]


@app.put("/api/v1/startups/{startup_id}", response_model=StartupResponse)
async def update_startup(
    startup_id: str,
    update_data: dict,
    db: AsyncSession = Depends(get_async_db)
):
    """Update a startup."""
    result = await db.execute(
        select(StartupModel).where(StartupModel.id == startup_id)
    )
    startup = result.scalar_one_or_none()
    
    if not startup:
        raise HTTPException(status_code=404, detail="Startup not found")
    
    # Update fields
    for key, value in update_data.items():
        if hasattr(startup, key):
            setattr(startup, key, value)
    
    await db.commit()
    await db.refresh(startup)
    
    return StartupResponse(
        id=str(startup.id),
        name=startup.name,
        sector=startup.sector,
        stage=startup.stage,
        description=startup.description,
        website=startup.website,
        team_size=startup.team_size,
        monthly_revenue=startup.monthly_revenue,
        created_at=startup.created_at
    )


# VC endpoints
@app.post("/api/v1/vcs", response_model=VCResponse, status_code=201)
async def create_vc(
    vc: VCCreate,
    db: AsyncSession = Depends(get_async_db)
):
    """Create a new VC."""
    db_vc = VCModel(
        id=uuid.uuid4(),
        firm_name=vc.firm_name,
        sectors=vc.sectors,
        stages=vc.stages,
        thesis=vc.thesis,
        website=vc.website,
        created_at=datetime.utcnow()
    )
    
    db.add(db_vc)
    await db.commit()
    await db.refresh(db_vc)
    
    # Convert UUID to string and handle missing fields
    return VCResponse(
        id=str(db_vc.id),
        firm_name=db_vc.firm_name,
        sectors=db_vc.sectors,
        stages=db_vc.stages,
        thesis=db_vc.thesis,
        website=db_vc.website,
        fund_size=None,  # Field doesn't exist in model
        typical_check_size=None,  # Field doesn't exist in model
        created_at=db_vc.created_at
    )


@app.get("/api/v1/vcs/{vc_id}", response_model=VCResponse)
async def get_vc(
    vc_id: str,
    db: AsyncSession = Depends(get_async_db)
):
    """Get a VC by ID."""
    result = await db.execute(
        select(VCModel).where(VCModel.id == vc_id)
    )
    vc = result.scalar_one_or_none()
    
    if not vc:
        raise HTTPException(status_code=404, detail="VC not found")
    
    return VCResponse(
        id=str(vc.id),
        firm_name=vc.firm_name,
        sectors=vc.sectors,
        stages=vc.stages,
        thesis=vc.thesis,
        website=vc.website,
        fund_size=None,
        typical_check_size=None,
        created_at=vc.created_at
    )


@app.get("/api/v1/vcs", response_model=List[VCResponse])
async def list_vcs(
    db: AsyncSession = Depends(get_async_db)
):
    """List all VCs."""
    result = await db.execute(select(VCModel))
    vcs = result.scalars().all()
    
    return [
        VCResponse(
            id=str(v.id),
            firm_name=v.firm_name,
            sectors=v.sectors,
            stages=v.stages,
            thesis=v.thesis,
            website=v.website,
            fund_size=None,
            typical_check_size=None,
            created_at=v.created_at
        ) for v in vcs
    ]


# Match endpoints
@app.post("/api/v1/matches", response_model=MatchResponse, status_code=201)
async def create_match(
    match: MatchCreate,
    db: AsyncSession = Depends(get_async_db)
):
    """Create a new match."""
    db_match = MatchModel(
        id=uuid.uuid4(),
        startup_id=uuid.UUID(match.startup_id),
        vc_id=uuid.UUID(match.vc_id),
        score=match.score,
        reasons=match.reasons,
        created_at=datetime.utcnow()
    )
    
    db.add(db_match)
    await db.commit()
    await db.refresh(db_match)
    
    return MatchResponse(
        id=str(db_match.id),
        startup_id=str(db_match.startup_id),
        vc_id=str(db_match.vc_id),
        score=db_match.score,
        reasons=db_match.reasons,
        created_at=db_match.created_at
    )


@app.get("/api/v1/matches", response_model=List[MatchResponse])
async def list_matches(
    db: AsyncSession = Depends(get_async_db)
):
    """List all matches."""
    result = await db.execute(select(MatchModel))
    matches = result.scalars().all()
    
    return [
        MatchResponse(
            id=str(m.id),
            startup_id=str(m.startup_id),
            vc_id=str(m.vc_id),
            score=m.score,
            reasons=m.reasons,
            created_at=m.created_at
        ) for m in matches
    ]


# Utility endpoint for counting
@app.get("/startups/count")
async def count_startups(db: AsyncSession = Depends(get_async_db)):
    """Count startups in database."""
    result = await db.execute(text("SELECT COUNT(*) FROM startups"))
    count = result.scalar()
    return {"count": count}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)