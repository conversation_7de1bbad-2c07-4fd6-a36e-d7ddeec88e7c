"""Quick test script to verify async database operations."""

import asyncio
from uuid import uuid4
import logging

from src.database.async_setup import async_engine, AsyncSessionLocal
from src.database.base import Base
from src.database.repositories.async_startup_repository import AsyncStartupRepository
from src.core.models.startup import Startup as StartupDomainModel

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_async_operations():
    """Test basic async database operations."""
    
    # Initialize database
    logger.info("Creating database tables...")
    async with async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    # Test repository operations
    async with AsyncSessionLocal() as session:
        repo = AsyncStartupRepository(session)
        
        # Create a startup
        logger.info("Creating startup...")
        startup = StartupDomainModel(
            id=uuid4(),
            name="Async Test Startup",
            sector="Technology",
            stage="Series A",
            description="Testing async database operations",
            website="https://async-test.com",
            team_size=15,
            monthly_revenue=100000.0
        )
        
        saved = await repo.save(startup)
        logger.info(f"Created startup: {saved.name} (ID: {saved.id})")
        
        # Find by ID
        logger.info("Finding startup by ID...")
        found = await repo.find_by_id(saved.id)
        if found:
            logger.info(f"Found startup: {found.name}")
        
        # Update startup
        logger.info("Updating startup...")
        startup.name = "Updated Async Startup"
        startup.team_size = 25
        updated = await repo.save(startup)
        logger.info(f"Updated startup: {updated.name}, team size: {updated.team_size}")
        
        # Search
        logger.info("Searching startups...")
        results = await repo.search("Async")
        logger.info(f"Found {len(results)} startups matching 'Async'")
        
        # Find by sector
        logger.info("Finding by sector...")
        tech_startups = await repo.find_by_sector("Technology")
        logger.info(f"Found {len(tech_startups)} Technology startups")
        
        # Delete
        logger.info("Deleting startup...")
        deleted = await repo.delete(startup.id)
        logger.info(f"Deleted: {deleted}")
        
        # Verify deletion
        not_found = await repo.find_by_id(startup.id)
        logger.info(f"Startup after deletion: {not_found}")
    
    logger.info("All async operations completed successfully!")
    
    # Cleanup
    await async_engine.dispose()


if __name__ == "__main__":
    asyncio.run(test_async_operations())