# Mocking Strategy for Database and AI Components

## Overview
This guide provides concrete mocking strategies for testing database repositories and AI services without requiring actual database connections or API calls.

## Database Mocking Strategy

### 1. SQLAlchemy Session Mocking

```python
# tests/conftest.py
import pytest
from unittest.mock import Mock, AsyncMock, MagicMock
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Query

@pytest.fixture
def mock_async_session():
    """Create a mock async database session."""
    session = Mock(spec=AsyncSession)
    
    # Mock common session methods
    session.execute = AsyncMock()
    session.commit = AsyncMock()
    session.rollback = AsyncMock()
    session.close = AsyncMock()
    session.refresh = AsyncMock()
    session.add = Mock()
    session.delete = Mock()
    session.query = Mock()
    
    # Mock context manager behavior
    session.__aenter__ = AsyncMock(return_value=session)
    session.__aexit__ = AsyncMock(return_value=None)
    
    return session

@pytest.fixture
def mock_query_result():
    """Mock SQLAlchemy query results."""
    def _mock_result(data=None):
        result = Mock()
        result.scalar_one_or_none = Mock(return_value=data)
        result.scalars = Mock(return_value=Mock(all=Mock(return_value=data if isinstance(data, list) else [data])))
        result.first = Mock(return_value=data if not isinstance(data, list) else data[0] if data else None)
        return result
    return _mock_result
```

### 2. Repository Testing Pattern

```python
# tests/unit/database/test_startup_repository_mocked.py
import pytest
from unittest.mock import Mock, patch
from uuid import uuid4
from datetime import datetime

from src.database.repositories.startup_repository import PostgresStartupRepository
from src.database.models.startup import StartupModel
from src.core.models.startup import Startup

class TestPostgresStartupRepository:
    @pytest.fixture
    def mock_startup_model(self):
        """Create a mock startup database model."""
        return Mock(
            id=uuid4(),
            name="Test Startup",
            industry="Technology",
            stage="Series A",
            location="San Francisco",
            website="https://test.com",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    
    @pytest.fixture
    def repository(self, mock_async_session):
        """Create repository with mocked session."""
        return PostgresStartupRepository(session=mock_async_session)
    
    @pytest.mark.asyncio
    async def test_create_startup(self, repository, mock_async_session, mock_query_result):
        """Test creating a startup."""
        startup_data = {
            "name": "New Startup",
            "industry": "AI",
            "stage": "Seed",
            "location": "New York"
        }
        
        # Mock the database response
        created_model = Mock(spec=StartupModel, **startup_data, id=uuid4())
        mock_async_session.execute.return_value = mock_query_result(created_model)
        
        # Execute
        result = await repository.create(startup_data)
        
        # Assert
        assert result.name == "New Startup"
        assert result.industry == "AI"
        mock_async_session.add.assert_called_once()
        mock_async_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_startup_by_id(self, repository, mock_async_session, mock_query_result, mock_startup_model):
        """Test retrieving a startup by ID."""
        startup_id = uuid4()
        
        # Mock the query result
        mock_async_session.execute.return_value = mock_query_result(mock_startup_model)
        
        # Execute
        result = await repository.get(startup_id)
        
        # Assert
        assert result is not None
        assert result.name == mock_startup_model.name
        mock_async_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_update_startup(self, repository, mock_async_session, mock_query_result, mock_startup_model):
        """Test updating a startup."""
        startup_id = uuid4()
        updates = {"industry": "FinTech", "stage": "Series B"}
        
        # Mock finding the startup
        mock_async_session.execute.return_value = mock_query_result(mock_startup_model)
        
        # Execute
        result = await repository.update(startup_id, updates)
        
        # Assert
        assert result is not None
        mock_async_session.commit.assert_called_once()
```

## AI Service Mocking Strategy

### 1. LangChain Component Mocking

```python
# tests/conftest.py (additions)
@pytest.fixture
def mock_langchain_llm():
    """Mock LangChain LLM."""
    with patch('langchain.chat_models.ChatOpenAI') as mock_llm:
        instance = Mock()
        instance.apredict = AsyncMock()
        instance.agenerate = AsyncMock()
        instance.predict = Mock()
        instance.generate = Mock()
        mock_llm.return_value = instance
        yield instance

@pytest.fixture
def mock_embeddings():
    """Mock embeddings model."""
    with patch('langchain.embeddings.OpenAIEmbeddings') as mock_embeddings:
        instance = Mock()
        instance.embed_documents = Mock(return_value=[[0.1] * 1536])
        instance.embed_query = Mock(return_value=[0.1] * 1536)
        mock_embeddings.return_value = instance
        yield instance
```

### 2. AI Integration Testing

```python
# tests/unit/ai/test_ai_integration_mocked.py
import pytest
from unittest.mock import Mock, patch, AsyncMock
import json

from src.core.ai.integration import AIIntegration
from src.core.ai.models import CompatibilityAnalysis, MatchInsight

class TestAIIntegrationMocked:
    @pytest.fixture
    def ai_integration(self, mock_langchain_llm):
        """Create AI integration with mocked LLM."""
        return AIIntegration(llm=mock_langchain_llm)
    
    @pytest.mark.asyncio
    async def test_analyze_compatibility(self, ai_integration, mock_langchain_llm):
        """Test compatibility analysis with mocked LLM."""
        startup = {
            "name": "AI Analytics Inc",
            "industry": "Artificial Intelligence",
            "stage": "Series A",
            "location": "San Francisco"
        }
        
        vc = {
            "name": "Tech Ventures",
            "investment_focus": ["AI", "ML", "Data"],
            "investment_stage": ["Series A", "Series B"],
            "location": "San Francisco"
        }
        
        # Mock LLM response
        mock_response = {
            "score": 0.92,
            "reasoning": "Excellent alignment in industry focus and stage",
            "strengths": [
                "Both focused on AI/ML technologies",
                "Stage alignment (Series A)",
                "Same geographic location"
            ],
            "concerns": [
                "Competition with portfolio companies"
            ]
        }
        
        mock_langchain_llm.apredict.return_value = json.dumps(mock_response)
        
        # Execute
        result = await ai_integration.analyze_compatibility(startup, vc)
        
        # Assert
        assert result.score == 0.92
        assert "Excellent alignment" in result.reasoning
        assert len(result.strengths) == 3
        assert len(result.concerns) == 1
        mock_langchain_llm.apredict.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_generate_match_insights(self, ai_integration, mock_langchain_llm):
        """Test match insights generation."""
        match_data = {
            "startup_name": "HealthTech AI",
            "vc_name": "Medical Ventures",
            "score": 0.85
        }
        
        # Mock LLM response
        mock_insights = {
            "summary": "Strong potential match",
            "key_synergies": ["Healthcare focus", "AI expertise"],
            "action_items": ["Schedule introduction call", "Share pitch deck"],
            "risk_factors": ["Early stage risk"]
        }
        
        mock_langchain_llm.apredict.return_value = json.dumps(mock_insights)
        
        # Execute
        result = await ai_integration.generate_match_insights(match_data)
        
        # Assert
        assert result["summary"] == "Strong potential match"
        assert len(result["key_synergies"]) == 2
        assert len(result["action_items"]) == 2
```

### 3. Rate Limiter Mocking

```python
# tests/unit/ai/test_rate_limiter_mocked.py
import pytest
from unittest.mock import Mock, patch
from datetime import datetime, timedelta

from src.core.ai.rate_limiter import RateLimiter

class TestRateLimiterMocked:
    @pytest.fixture
    def rate_limiter(self):
        """Create rate limiter with mocked time."""
        return RateLimiter(requests_per_minute=60)
    
    @pytest.mark.asyncio
    async def test_rate_limiting(self, rate_limiter):
        """Test rate limiting behavior."""
        with patch('time.time') as mock_time:
            # Set initial time
            current_time = 1000.0
            mock_time.return_value = current_time
            
            # First request should succeed
            async with rate_limiter:
                pass  # Request 1
            
            # Simulate 60 requests in quick succession
            for i in range(59):
                async with rate_limiter:
                    pass
            
            # 61st request should be rate limited
            mock_time.return_value = current_time + 30  # 30 seconds later
            
            with pytest.raises(Exception):  # Should raise rate limit exception
                async with rate_limiter:
                    pass
```

### 4. Cache Mocking

```python
# tests/unit/ai/test_cache_mocked.py
import pytest
from unittest.mock import Mock, patch, AsyncMock
import json

from src.core.ai.cache import AICache

class TestAICacheMocked:
    @pytest.fixture
    def mock_redis(self):
        """Mock Redis client."""
        redis = Mock()
        redis.get = AsyncMock()
        redis.set = AsyncMock()
        redis.delete = AsyncMock()
        redis.exists = AsyncMock()
        redis.expire = AsyncMock()
        return redis
    
    @pytest.fixture
    def ai_cache(self, mock_redis):
        """Create AI cache with mocked Redis."""
        return AICache(redis_client=mock_redis)
    
    @pytest.mark.asyncio
    async def test_cache_hit(self, ai_cache, mock_redis):
        """Test cache hit scenario."""
        key = "analysis:startup123:vc456"
        cached_data = {
            "score": 0.88,
            "reasoning": "Good match",
            "timestamp": "2024-01-01T00:00:00"
        }
        
        # Mock cache hit
        mock_redis.get.return_value = json.dumps(cached_data)
        
        # Execute
        result = await ai_cache.get(key)
        
        # Assert
        assert result == cached_data
        mock_redis.get.assert_called_once_with(key)
    
    @pytest.mark.asyncio
    async def test_cache_miss(self, ai_cache, mock_redis):
        """Test cache miss scenario."""
        key = "analysis:startup789:vc101"
        
        # Mock cache miss
        mock_redis.get.return_value = None
        
        # Execute
        result = await ai_cache.get(key)
        
        # Assert
        assert result is None
        mock_redis.get.assert_called_once_with(key)
    
    @pytest.mark.asyncio
    async def test_cache_set(self, ai_cache, mock_redis):
        """Test setting cache value."""
        key = "analysis:new"
        value = {"score": 0.75, "reasoning": "Fair match"}
        ttl = 3600
        
        # Execute
        await ai_cache.set(key, value, ttl=ttl)
        
        # Assert
        mock_redis.set.assert_called_once()
        call_args = mock_redis.set.call_args
        assert call_args[0][0] == key
        assert json.loads(call_args[0][1]) == value
        assert call_args[1]["ex"] == ttl
```

## Best Practices for Mocking

### 1. Use Fixtures Liberally
```python
# Create reusable fixtures for common mocks
@pytest.fixture(scope="session")
def mock_startup_data():
    return {
        "id": uuid4(),
        "name": "Test Startup",
        "industry": "Technology",
        # ... more fields
    }
```

### 2. Mock at the Right Level
- Mock external dependencies (database, APIs)
- Don't mock the code you're testing
- Mock at service boundaries

### 3. Use Specific Assertions
```python
# Good - specific assertion
mock_repo.create.assert_called_once_with(expected_data)

# Bad - too general
assert mock_repo.create.called
```

### 4. Test Error Cases
```python
@pytest.mark.asyncio
async def test_database_error_handling(repository, mock_async_session):
    """Test handling of database errors."""
    mock_async_session.execute.side_effect = Exception("Database error")
    
    with pytest.raises(Exception):
        await repository.get(uuid4())
```

### 5. Use Context Managers for Patches
```python
async def test_with_multiple_patches():
    with patch('module.ClassA') as mock_a, \
         patch('module.ClassB') as mock_b:
        # Test code here
        pass
```

## Running Tests with Mocks

```bash
# Run all mocked tests
pytest tests/unit/ -v

# Run with coverage
pytest tests/unit/ --cov=src --cov-report=term-missing

# Run specific test file
pytest tests/unit/database/test_startup_repository_mocked.py -v

# Run tests matching pattern
pytest -k "mocked" -v
```

## Integration with CI/CD

```yaml
# .github/workflows/test.yml
name: Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.9'
      - name: Install dependencies
        run: |
          pip install -r requirements.txt
          pip install -r requirements-test.txt
      - name: Run unit tests with mocks
        run: |
          pytest tests/unit/ --cov=src --cov-report=xml
      - name: Upload coverage
        uses: codecov/codecov-action@v1
```