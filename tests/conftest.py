"""Shared pytest fixtures and configuration."""

import pytest
import asyncio
from typing import <PERSON><PERSON><PERSON><PERSON><PERSON>, Generator
from unittest.mock import <PERSON>ck, As<PERSON><PERSON><PERSON>, MagicMock
from datetime import datetime
import uuid

from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from redis import Redis

from src.core.models.startup import Startup
from src.core.models.vc import VC
from src.core.ai.models_v2 import StartupAnalysis, VCThesisAnalysis, InvestmentFocus
from src.core.ai.analyzer import AIAnalyzerService
from src.core.ai.cache import AICache
from src.api.v1.deps import get_database, get_redis_client, get_ai_analyzer
from src.api.main import app


# Configure pytest-asyncio
pytest_plugins = ('pytest_asyncio',)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


# Mock fixtures for external services
@pytest.fixture
def mock_redis():
    """Mock Redis client."""
    redis_mock = Mock(spec=Redis)
    redis_mock.get = AsyncMock(return_value=None)
    redis_mock.set = AsyncMock(return_value=True)
    redis_mock.setex = AsyncMock(return_value=True)
    redis_mock.incr = Mock(return_value=1)
    redis_mock.expire = Mock(return_value=True)
    redis_mock.delete = AsyncMock(return_value=1)
    return redis_mock


@pytest.fixture
def mock_db_session():
    """Mock database session."""
    session = Mock(spec=Session)
    session.add = Mock()
    session.commit = Mock()
    session.rollback = Mock()
    session.close = Mock()
    
    # Mock query to return empty results by default
    mock_query = Mock()
    mock_query.filter = Mock(return_value=mock_query)
    mock_query.filter_by = Mock(return_value=mock_query)
    mock_query.all = Mock(return_value=[])
    mock_query.first = Mock(return_value=None)
    mock_query.limit = Mock(return_value=mock_query)
    mock_query.offset = Mock(return_value=mock_query)
    session.query = Mock(return_value=mock_query)
    
    return session


@pytest.fixture
def mock_openai():
    """Mock OpenAI responses."""
    mock = Mock()
    mock.ainvoke = AsyncMock()
    return mock


# Domain model fixtures
@pytest.fixture
def sample_startup():
    """Create a sample startup."""
    startup = Startup(
        name="TechVenture AI",
        sector="AI/ML",
        stage="Series A",
        description="We build AI-powered analytics tools for e-commerce businesses. Our platform helps online retailers optimize pricing, inventory, and marketing decisions using machine learning.",
        website="https://techventure.ai",
        team_size=25,
        monthly_revenue=150000
    )
    startup.id = str(uuid.uuid4())
    startup.created_at = datetime.utcnow()
    startup.updated_at = datetime.utcnow()
    return startup


@pytest.fixture
def sample_vc():
    """Create a sample VC."""
    vc = VC(
        firm_name="AI Capital Partners",
        website="https://aicapital.com",
        sectors=["AI/ML", "B2B SaaS", "Analytics"],
        stages=["Series A", "Series B"],
        thesis="We invest in AI-first companies that are transforming traditional industries with machine learning and data analytics.",
        check_size_min=2000000,
        check_size_max=10000000
    )
    vc.id = str(uuid.uuid4())
    vc.created_at = datetime.utcnow()
    vc.updated_at = datetime.utcnow()
    return vc


@pytest.fixture
def sample_startup_analysis():
    """Create a sample startup analysis result."""
    from src.core.ai.models import BusinessModel, BusinessModelType, TechnologyStack
    
    return StartupAnalysis(
        company_summary="AI-powered pricing optimization platform for e-commerce businesses",
        key_technologies=["Python", "TensorFlow", "React", "PostgreSQL"],
        market_opportunity="$10B+ e-commerce pricing optimization market growing at 15% CAGR",
        competitive_advantages=[
            "Proprietary ML algorithms",
            "Real-time data processing",
            "Easy integration with major platforms"
        ],
        team_strengths=[
            "Ex-Google ML engineers",
            "10+ years e-commerce experience",
            "Published research in pricing algorithms"
        ],
        risk_factors=[
            "Competitive market with established players",
            "Customer acquisition costs",
            "Data privacy regulations"
        ],
        growth_potential=8.5,
        innovation_score=9.0,
        market_fit_score=7.5,
        sectors=["AI/ML", "E-commerce", "Analytics"],
        target_customers=["Online retailers", "E-commerce platforms"],
        revenue_model="Tiered SaaS subscription with usage-based pricing"
    )


@pytest.fixture
def sample_vc_thesis_analysis():
    """Create a sample VC thesis analysis result."""
    from src.core.ai.models import InvestmentStage
    
    from src.core.ai.models_v2 import Stage, InvestmentFocus
    
    return VCThesisAnalysis(
        thesis_summary="Focus on AI-first companies transforming traditional industries",
        investment_focus=InvestmentFocus(
            sectors=["AI/ML", "B2B SaaS", "Analytics"],
            stages=[Stage.SERIES_A, Stage.SERIES_B],
            themes=["AI transformation", "Enterprise automation", "Data infrastructure"]
        ),
        check_size_range={"min": 2000000, "max": 10000000},
        portfolio_themes=["AI/ML", "Data Analytics", "Enterprise SaaS"],
        investment_criteria=[
            "Strong technical founding team",
            "Clear AI/ML differentiation",
            "Proven product-market fit",
            "$1M+ ARR"
        ],
        notable_investments=["DataRobot", "Weights & Biases", "Cohere"],
        investment_pace="2-3 deals per quarter"
    )


# AI service fixtures
@pytest.fixture
def mock_ai_cache(mock_redis):
    """Create a mock AI cache."""
    cache = Mock(spec=AICache)
    cache.get_startup_analysis = AsyncMock(return_value=None)
    cache.set_startup_analysis = AsyncMock(return_value=True)
    cache.get_vc_thesis = AsyncMock(return_value=None)
    cache.set_vc_thesis = AsyncMock(return_value=True)
    cache.get_cache_stats = Mock(return_value={
        'total_keys': 0,
        'hits': 0,
        'misses': 0,
        'hit_rate': 0
    })
    cache.clear_cache = Mock(return_value=0)
    return cache


@pytest.fixture
def mock_ai_analyzer(mock_openai, mock_ai_cache):
    """Create a mock AI analyzer service."""
    analyzer = AIAnalyzerService(
        openai_api_key="test-key",
        model_name="gpt-4",
        temperature=0.3,
        cache=mock_ai_cache,
        max_retries=3,
        enable_streaming=False
    )
    # Replace the LLM with our mock
    analyzer.llm = mock_openai
    analyzer.startup_chain.llm = mock_openai
    analyzer.vc_chain.llm = mock_openai
    return analyzer


# FastAPI test client fixtures
@pytest.fixture
def test_client(mock_db_session, mock_redis, mock_ai_analyzer):
    """Create a test client with mocked dependencies."""
    # Import the actual dependency functions to override them
    from src.api.v1.deps import get_database, get_redis_client, get_ai_analyzer
    
    # Override dependencies
    app.dependency_overrides[get_database] = lambda: mock_db_session
    app.dependency_overrides[get_redis_client] = lambda: mock_redis
    app.dependency_overrides[get_ai_analyzer] = lambda: mock_ai_analyzer
    
    client = TestClient(app)
    yield client
    
    # Clean up
    app.dependency_overrides.clear()


@pytest.fixture
def auth_headers():
    """Create authentication headers."""
    return {"Authorization": "Bearer test-token"}


@pytest.fixture
def api_key_headers():
    """Create API key headers."""
    return {"X-API-Key": "test-api-key"}


@pytest.fixture
def expired_credentials():
    """Create expired authentication credentials."""
    from fastapi.security import HTTPAuthorizationCredentials
    return HTTPAuthorizationCredentials(
        scheme="Bearer",
        credentials="expired-token"
    )


# Test data factories
@pytest.fixture
def startup_factory():
    """Factory for creating test startups."""
    def _create_startup(**kwargs):
        defaults = {
            "name": f"TestStartup-{uuid.uuid4().hex[:6]}",
            "sector": "Technology",
            "stage": "Seed",
            "description": "A test startup description",
            "website": "https://example.com",
            "team_size": 10,
            "monthly_revenue": 50000
        }
        defaults.update(kwargs)
        startup = Startup(**defaults)
        startup.id = str(uuid.uuid4())
        startup.created_at = datetime.utcnow()
        startup.updated_at = datetime.utcnow()
        return startup
    
    return _create_startup


@pytest.fixture
def vc_factory():
    """Factory for creating test VCs."""
    def _create_vc(**kwargs):
        defaults = {
            "firm_name": f"TestVC-{uuid.uuid4().hex[:6]}",
            "website": "https://vc-example.com",
            "sectors": ["Technology"],
            "stages": ["Seed", "Series A"],
            "thesis": "We invest in early-stage technology companies",
            "check_size_min": 500000,
            "check_size_max": 5000000
        }
        defaults.update(kwargs)
        vc = VC(**defaults)
        vc.id = str(uuid.uuid4())
        vc.created_at = datetime.utcnow()
        vc.updated_at = datetime.utcnow()
        return vc
    
    return _create_vc


# Async test utilities
@pytest.fixture
async def async_mock_response():
    """Create an async mock response."""
    async def _response(data):
        return data
    return _response