"""Test fixtures specifically for warm intro functionality."""

import pytest
from datetime import datetime, timed<PERSON>ta
from uuid import UUID, uuid4
from unittest.mock import Mock, AsyncMock, MagicMock
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session

from src.core.models.connection import (
    Connection,
    ConnectionId,
    ConnectionMetrics,
    ConnectionStrength,
    RelationshipType,
    ConnectionPath,
    IntroductionRequest,
    IntroductionStatus
)
from src.core.models.user import User
from src.database.models.connection import Connection as ConnectionDB, IntroductionRequest as IntroductionRequestDB
from src.database.repositories.connection_repository import PostgresConnectionRepository, PostgresIntroductionRepository
from src.core.services.warm_intro_service import WarmIntroService


@pytest.fixture
def sample_user_ids():
    """Generate consistent user IDs for testing."""
    return {
        'alice': UUID('550e8400-e29b-41d4-a716-************'),
        'bob': UUID('550e8400-e29b-41d4-a716-************'),
        'charlie': UUID('550e8400-e29b-41d4-a716-************'),
        'diana': UUID('550e8400-e29b-41d4-a716-************'),
        'eve': UUID('550e8400-e29b-41d4-a716-************')
    }


@pytest.fixture
def sample_connection_metrics():
    """Create sample connection metrics."""
    return ConnectionMetrics(
        interaction_frequency=3,
        last_interaction_days=15,
        mutual_connections_count=5,
        trust_score=0.8
    )


@pytest.fixture
def sample_connection(sample_user_ids, sample_connection_metrics):
    """Create a sample connection."""
    return Connection(
        id=ConnectionId(),
        user_a_id=sample_user_ids['alice'],
        user_b_id=sample_user_ids['bob'],
        relationship_type=RelationshipType.COLLEAGUE,
        strength=ConnectionStrength.STRONG,
        metrics=sample_connection_metrics,
        notes="Worked together at previous company",
        tags=["former_colleague", "tech"]
    )


@pytest.fixture
def sample_introduction_request(sample_user_ids):
    """Create a sample introduction request."""
    return IntroductionRequest(
        requester_id=sample_user_ids['alice'],
        target_id=sample_user_ids['charlie'],
        connector_id=sample_user_ids['bob'],
        message="I'd like to be introduced to Charlie to discuss potential collaboration on AI projects.",
        status=IntroductionStatus.PENDING
    )


@pytest.fixture
def sample_connection_path(sample_user_ids):
    """Create a sample connection path."""
    # Create mock connections for the path
    connection1 = Connection(
        id=ConnectionId(),
        user_a_id=sample_user_ids['alice'],
        user_b_id=sample_user_ids['bob'],
        relationship_type=RelationshipType.COLLEAGUE,
        strength=ConnectionStrength.STRONG,
        metrics=ConnectionMetrics(trust_score=0.8)
    )
    
    connection2 = Connection(
        id=ConnectionId(),
        user_a_id=sample_user_ids['bob'],
        user_b_id=sample_user_ids['charlie'],
        relationship_type=RelationshipType.BUSINESS_PARTNER,
        strength=ConnectionStrength.MEDIUM,
        metrics=ConnectionMetrics(trust_score=0.6)
    )
    
    return ConnectionPath(
        source_user_id=sample_user_ids['alice'],
        target_user_id=sample_user_ids['charlie'],
        path=[sample_user_ids['alice'], sample_user_ids['bob'], sample_user_ids['charlie']],
        connections=[connection1, connection2]
    )


@pytest.fixture
def mock_users(sample_user_ids):
    """Create mock users for testing."""
    users = {}
    user_data = [
        ('alice', 'Alice Johnson', '<EMAIL>', 'founder'),
        ('bob', 'Bob Smith', '<EMAIL>', 'investor'),
        ('charlie', 'Charlie Brown', '<EMAIL>', 'founder'),
        ('diana', 'Diana Prince', '<EMAIL>', 'investor'),
        ('eve', 'Eve Wilson', '<EMAIL>', 'founder')
    ]
    
    for key, name, email, role in user_data:
        user = Mock(spec=User)
        user.id = sample_user_ids[key]
        user.name = name
        user.email = email
        user.role = role
        users[key] = user
    
    return users


@pytest.fixture
def mock_db_connections(sample_user_ids):
    """Create mock database connection records."""
    connections = []
    
    # Alice -> Bob (strong)
    conn1 = Mock(spec=ConnectionDB)
    conn1.id = uuid4()
    conn1.user_a_id = sample_user_ids['alice']
    conn1.user_b_id = sample_user_ids['bob']
    conn1.relationship_type = 'colleague'
    conn1.strength = 'strong'
    conn1.metrics = {'trust_score': 0.8, 'interaction_frequency': 5}
    conn1.notes = 'Former colleagues'
    conn1.tags = ['work', 'tech']
    conn1.created_at = datetime.utcnow()
    conn1.updated_at = datetime.utcnow()
    conn1.is_active = True
    connections.append(conn1)
    
    # Bob -> Charlie (medium)
    conn2 = Mock(spec=ConnectionDB)
    conn2.id = uuid4()
    conn2.user_a_id = sample_user_ids['bob']
    conn2.user_b_id = sample_user_ids['charlie']
    conn2.relationship_type = 'business_partner'
    conn2.strength = 'medium'
    conn2.metrics = {'trust_score': 0.6, 'interaction_frequency': 2}
    conn2.notes = 'Business partners'
    conn2.tags = ['business']
    conn2.created_at = datetime.utcnow()
    conn2.updated_at = datetime.utcnow()
    conn2.is_active = True
    connections.append(conn2)
    
    # Charlie -> Diana (weak)
    conn3 = Mock(spec=ConnectionDB)
    conn3.id = uuid4()
    conn3.user_a_id = sample_user_ids['charlie']
    conn3.user_b_id = sample_user_ids['diana']
    conn3.relationship_type = 'industry_peer'
    conn3.strength = 'weak'
    conn3.metrics = {'trust_score': 0.3, 'interaction_frequency': 1}
    conn3.notes = 'Met at conference'
    conn3.tags = ['conference']
    conn3.created_at = datetime.utcnow()
    conn3.updated_at = datetime.utcnow()
    conn3.is_active = True
    connections.append(conn3)
    
    return connections


@pytest.fixture
def mock_db_intro_requests(sample_user_ids):
    """Create mock database introduction request records."""
    requests = []
    
    # Alice requests intro to Charlie via Bob
    req1 = Mock(spec=IntroductionRequestDB)
    req1.id = uuid4()
    req1.requester_id = sample_user_ids['alice']
    req1.target_id = sample_user_ids['charlie']
    req1.connector_id = sample_user_ids['bob']
    req1.status = 'pending'
    req1.message = 'Would like to discuss AI collaboration'
    req1.connector_notes = None
    req1.created_at = datetime.utcnow()
    req1.updated_at = datetime.utcnow()
    req1.expires_at = datetime.utcnow() + timedelta(days=30)
    req1.completed_at = None
    requests.append(req1)
    
    return requests


@pytest.fixture
def mock_connection_repository():
    """Create a mock connection repository."""
    repo = Mock(spec=PostgresConnectionRepository)
    
    # Configure async methods
    repo.create_connection = AsyncMock()
    repo.get_connection = AsyncMock()
    repo.get_user_connections = AsyncMock(return_value=[])
    repo.update_connection = AsyncMock()
    repo.delete_connection = AsyncMock(return_value=True)
    repo.find_shortest_paths = AsyncMock(return_value=[])
    repo.get_mutual_connections = AsyncMock(return_value=[])
    repo.search_connections = AsyncMock(return_value=[])
    
    return repo


@pytest.fixture
def mock_introduction_repository():
    """Create a mock introduction repository."""
    repo = Mock(spec=PostgresIntroductionRepository)
    
    # Configure async methods
    repo.create_request = AsyncMock()
    repo.get_request = AsyncMock()
    repo.get_pending_requests = AsyncMock(return_value=[])
    repo.update_request = AsyncMock()
    repo.get_user_requests = AsyncMock(return_value=[])
    repo.cleanup_expired_requests = AsyncMock(return_value=0)
    
    return repo


@pytest.fixture
def mock_user_repository():
    """Create a mock user repository."""
    from src.core.repositories.user_repository import UserRepository
    
    repo = Mock(spec=UserRepository)
    repo.get = AsyncMock()
    repo.get_users_by_startup = AsyncMock(return_value=[])
    repo.get_users_by_vc = AsyncMock(return_value=[])
    
    return repo


@pytest.fixture
def mock_startup_repository():
    """Create a mock startup repository."""
    from src.core.repositories.startup_repository import StartupRepository
    
    repo = Mock(spec=StartupRepository)
    repo.get = AsyncMock()
    
    return repo


@pytest.fixture
def mock_vc_repository():
    """Create a mock VC repository."""
    from src.core.repositories.vc_repository import VCRepository
    
    repo = Mock(spec=VCRepository)
    repo.get = AsyncMock()
    
    return repo


@pytest.fixture
def warm_intro_service(
    mock_connection_repository,
    mock_introduction_repository,
    mock_user_repository,
    mock_startup_repository,
    mock_vc_repository
):
    """Create a WarmIntroService with mocked dependencies."""
    return WarmIntroService(
        connection_repo=mock_connection_repository,
        introduction_repo=mock_introduction_repository,
        user_repo=mock_user_repository,
        startup_repo=mock_startup_repository,
        vc_repo=mock_vc_repository
    )


@pytest.fixture
def path_finding_test_data():
    """Create test data for path finding scenarios."""
    return {
        'linear_path': [
            {'path': ['user1', 'user2', 'user3'], 'depth': 2, 'strength_score': 0.48},
            {'path': ['user1', 'user4', 'user3'], 'depth': 2, 'strength_score': 0.36}
        ],
        'complex_network': [
            {'path': ['user1', 'user2'], 'depth': 1, 'strength_score': 1.0},
            {'path': ['user1', 'user2', 'user3'], 'depth': 2, 'strength_score': 0.6},
            {'path': ['user1', 'user2', 'user4'], 'depth': 2, 'strength_score': 0.8}
        ],
        'no_path': []
    }


@pytest.fixture
def connection_analytics_data():
    """Create sample connection analytics data."""
    return {
        'total_connections': 15,
        'strength_distribution': {
            'strong': 5,
            'medium': 7,
            'weak': 3
        },
        'relationship_distribution': {
            'colleague': 6,
            'business_partner': 4,
            'industry_peer': 3,
            'investor_founder': 2
        },
        'introduction_stats': {
            'requests_sent': 5,
            'requests_received': 3,
            'introductions_made': 8,
            'successful_intros': 6
        },
        'network_reach': {
            'depth_1': 15,
            'depth_2': 47
        },
        'key_connectors': [
            {
                'user': {
                    'id': 'connector1',
                    'name': 'Super Connector',
                    'email': '<EMAIL>'
                },
                'connector_score': 25.5
            }
        ]
    }


@pytest.fixture
def api_test_data():
    """Create test data for API endpoint testing."""
    return {
        'create_connection_request': {
            'other_user_id': 'user123',
            'relationship_type': 'colleague',
            'notes': 'Met at conference',
            'trust_score': 0.7
        },
        'request_introduction_request': {
            'target_id': 'target123',
            'connector_id': 'connector123',
            'message': 'Would like to discuss potential collaboration opportunities.'
        },
        'respond_to_intro_request': {
            'accept': True,
            'notes': 'Happy to make this introduction'
        }
    }


@pytest.fixture
def mock_database_session():
    """Create a mock database session with connection-specific behavior."""
    session = Mock(spec=Session)
    session.add = Mock()
    session.commit = Mock()
    session.rollback = Mock()
    session.close = Mock()
    session.execute = Mock()
    session.refresh = Mock()
    
    # Mock query behavior
    mock_query = Mock()
    mock_query.filter = Mock(return_value=mock_query)
    mock_query.filter_by = Mock(return_value=mock_query)
    mock_query.all = Mock(return_value=[])
    mock_query.first = Mock(return_value=None)
    mock_query.limit = Mock(return_value=mock_query)
    mock_query.offset = Mock(return_value=mock_query)
    mock_query.order_by = Mock(return_value=mock_query)
    mock_query.update = Mock(return_value=0)
    session.query = Mock(return_value=mock_query)
    
    return session


# Error scenarios for testing
@pytest.fixture
def error_scenarios():
    """Create various error scenarios for testing."""
    return {
        'user_not_found': {
            'error_type': ValueError,
            'message': 'User not found'
        },
        'connection_exists': {
            'error_type': ValueError,
            'message': 'Connection already exists between these users'
        },
        'invalid_connector': {
            'error_type': ValueError,
            'message': 'Connector cannot introduce these users - insufficient connections'
        },
        'request_not_found': {
            'error_type': ValueError,
            'message': 'Introduction request not found'
        },
        'invalid_request_status': {
            'error_type': ValueError,
            'message': 'Request is not pending'
        },
        'self_connection': {
            'error_type': ValueError,
            'message': 'Cannot create connection to self'
        },
        'invalid_trust_score': {
            'error_type': ValueError,
            'message': 'Trust score must be between 0.0 and 1.0'
        }
    }