"""Enhanced pytest configuration with comprehensive fixtures for database and AI testing."""

import pytest
import asyncio
from typing import Generator, AsyncGenerator, List
from unittest.mock import MagicMock, AsyncMock, patch, Mock
from uuid import uuid4
from datetime import datetime
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker

from src.database.setup import Base
from src.core.models.startup import Startup
from src.core.models.vc import VC
from src.core.models.match import Match, MatchScore
from src.core.ports.ai_port import AIPort
from src.core.ai.models import StartupAnalysis, VCThesisAnalysis


# Additional Database Test Fixtures
@pytest.fixture
def mock_query_result():
    """Create a mock query result for SQLAlchemy queries."""
    def _create_mock_query(return_value=None, all_return=None, first_return=None, count_return=0):
        mock_query = MagicMock()
        
        # Chain methods
        mock_query.filter_by = MagicMock(return_value=mock_query)
        mock_query.filter = MagicMock(return_value=mock_query)
        mock_query.order_by = MagicMock(return_value=mock_query)
        mock_query.limit = MagicMock(return_value=mock_query)
        mock_query.offset = MagicMock(return_value=mock_query)
        
        # Terminal methods
        mock_query.all = MagicMock(return_value=all_return or [])
        mock_query.first = MagicMock(return_value=first_return)
        mock_query.count = MagicMock(return_value=count_return)
        mock_query.delete = MagicMock(return_value=count_return)
        
        return mock_query
    
    return _create_mock_query


@pytest.fixture
def mock_db_models():
    """Create mock database model classes."""
    from src.database.models.startup import Startup as StartupDB
    from src.database.models.vc import VC as VCDB
    from src.database.models.match import Match as MatchDB
    
    models = {
        'StartupDB': MagicMock(spec=StartupDB),
        'VCDB': MagicMock(spec=VCDB),
        'MatchDB': MagicMock(spec=MatchDB)
    }
    
    # Add common SQLAlchemy attributes
    for model in models.values():
        model.__table__ = MagicMock()
        model.__tablename__ = 'mock_table'
        
    return models


# Repository Test Helpers
@pytest.fixture
def repository_test_data():
    """Create test data for repository testing."""
    startups = []
    vcs = []
    matches = []
    
    # Create startups
    for i in range(5):
        startup = Startup(
            id=uuid4(),
            name=f"Startup {i}",
            sector=["Technology", "Healthcare", "FinTech"][i % 3],
            stage=["Seed", "Series A", "Series B"][i % 3],
            description=f"Description for startup {i}",
            website=f"https://startup{i}.com",
            team_size=10 + i * 5,
            monthly_revenue=50000.0 * (i + 1)
        )
        startups.append(startup)
    
    # Create VCs
    for i in range(3):
        vc = VC(
            id=uuid4(),
            name=f"VC Fund {i}",
            focus_sectors=["Technology", "Healthcare", "FinTech"],
            investment_stages=["Seed", "Series A", "Series B"],
            thesis=f"Investment thesis for VC {i}",
            aum_millions=100.0 * (i + 1),
            portfolio_size=20 + i * 5
        )
        vcs.append(vc)
    
    # Create matches
    for startup in startups[:3]:
        for vc in vcs:
            match = Match(
                id=uuid4(),
                startup_id=startup.id,
                vc_id=vc.id,
                score=7.0 + (hash(str(startup.id) + str(vc.id)) % 30) / 10,
                status="pending",
                explanation="Test match explanation"
            )
            matches.append(match)
    
    return {
        'startups': startups,
        'vcs': vcs,
        'matches': matches
    }


# Service Layer Test Fixtures
@pytest.fixture
def mock_service_dependencies(mock_startup_repository, mock_vc_repository, 
                             mock_match_repository, mock_ai_port):
    """Bundle all service dependencies."""
    return {
        'startup_repository': mock_startup_repository,
        'vc_repository': mock_vc_repository,
        'match_repository': mock_match_repository,
        'ai_port': mock_ai_port
    }


@pytest.fixture
def match_service_with_mocks(mock_service_dependencies):
    """Create MatchService with all dependencies mocked."""
    from src.core.services.match_service import MatchService
    
    return MatchService(
        startup_repository=mock_service_dependencies['startup_repository'],
        vc_repository=mock_service_dependencies['vc_repository'],
        match_repository=mock_service_dependencies['match_repository'],
        ai_port=mock_service_dependencies['ai_port']
    )


# API Testing Fixtures
@pytest.fixture
def mock_api_dependencies():
    """Mock all API endpoint dependencies."""
    deps = {}
    
    # Mock authentication
    deps['current_user'] = MagicMock()
    deps['current_user'].id = uuid4()
    deps['current_user'].email = "<EMAIL>"
    
    # Mock rate limiting
    deps['rate_limiter'] = AsyncMock(return_value=True)
    
    # Mock pagination
    deps['pagination'] = MagicMock()
    deps['pagination'].limit = 10
    deps['pagination'].offset = 0
    
    return deps


@pytest.fixture
def api_test_client_with_deps(api_client, mock_service_dependencies, mock_api_dependencies):
    """Create API test client with all dependencies mocked."""
    from src.api.v1.deps import (
        get_current_user_optional,
        rate_limit_per_minute,
        PaginationParams
    )
    from src.main import app
    
    # Override service dependencies
    app.dependency_overrides.update({
        'get_startup_repository': lambda: mock_service_dependencies['startup_repository'],
        'get_vc_repository': lambda: mock_service_dependencies['vc_repository'],
        'get_match_repository': lambda: mock_service_dependencies['match_repository'],
        'get_ai_port': lambda: mock_service_dependencies['ai_port'],
        get_current_user_optional: lambda: mock_api_dependencies['current_user'],
        rate_limit_per_minute: lambda: mock_api_dependencies['rate_limiter'],
        PaginationParams: lambda: mock_api_dependencies['pagination']
    })
    
    yield api_client
    
    # Clean up overrides
    app.dependency_overrides.clear()


# AI Testing Fixtures
@pytest.fixture
def mock_langchain_components():
    """Mock LangChain components for AI testing."""
    components = {}
    
    # Mock ChatOpenAI
    with patch('langchain_openai.ChatOpenAI') as mock_chat:
        chat_instance = MagicMock()
        chat_instance.invoke = AsyncMock(
            return_value=MagicMock(content="Mocked response")
        )
        chat_instance.stream = AsyncMock()
        mock_chat.return_value = chat_instance
        components['chat_openai'] = chat_instance
    
    # Mock structured output parser
    with patch('langchain.output_parsers.PydanticOutputParser') as mock_parser:
        parser_instance = MagicMock()
        parser_instance.parse = MagicMock()
        mock_parser.return_value = parser_instance
        components['parser'] = parser_instance
    
    return components


@pytest.fixture
def mock_ai_responses():
    """Predefined AI responses for consistent testing."""
    return {
        'startup_analysis': {
            'market_score': 8.5,
            'team_score': 7.5,
            'product_score': 8.0,
            'traction_score': 7.0,
            'summary': 'Strong product-market fit',
            'strengths': ['Technical team', 'Market opportunity'],
            'weaknesses': ['Limited traction', 'Competition']
        },
        'vc_thesis': {
            'focus_sectors': ['Technology', 'AI'],
            'investment_stages': ['Series A', 'Series B'],
            'check_size_range': '$2M - $10M',
            'key_criteria': ['B2B SaaS', 'AI-first'],
            'thesis_summary': 'B2B SaaS with AI focus'
        },
        'match_score': {
            'overall_score': 8.5,
            'sector_alignment': 9.0,
            'stage_alignment': 8.0,
            'thesis_fit': 8.5,
            'explanation': 'Strong alignment'
        }
    }


# Test Data Generators
@pytest.fixture
def generate_test_startups():
    """Generate test startups with various characteristics."""
    def _generate(count: int = 10) -> List[Startup]:
        sectors = ["Technology", "Healthcare", "FinTech", "EdTech", "CleanTech"]
        stages = ["Pre-seed", "Seed", "Series A", "Series B", "Series C"]
        
        startups = []
        for i in range(count):
            startup = Startup(
                id=uuid4(),
                name=f"TestStartup{i}",
                sector=sectors[i % len(sectors)],
                stage=stages[i % len(stages)],
                description=f"Test startup {i} doing innovative things",
                website=f"https://teststartup{i}.com",
                team_size=5 + (i * 3),
                monthly_revenue=10000.0 * (i + 1) if i > 2 else 0
            )
            startups.append(startup)
        
        return startups
    
    return _generate


@pytest.fixture
def generate_test_vcs():
    """Generate test VCs with various characteristics."""
    def _generate(count: int = 5) -> List[VC]:
        vcs = []
        
        for i in range(count):
            focus_sectors = [
                ["Technology", "AI", "SaaS"],
                ["Healthcare", "BioTech"],
                ["FinTech", "Blockchain"],
                ["CleanTech", "Sustainability"],
                ["EdTech", "Future of Work"]
            ][i % 5]
            
            investment_stages = [
                ["Seed", "Series A"],
                ["Series A", "Series B"],
                ["Series B", "Series C"],
                ["Pre-seed", "Seed"],
                ["Series A", "Series B", "Series C"]
            ][i % 5]
            
            vc = VC(
                id=uuid4(),
                name=f"Test Ventures {i}",
                focus_sectors=focus_sectors,
                investment_stages=investment_stages,
                thesis=f"We invest in {focus_sectors[0]} companies at {investment_stages[0]} stage",
                aum_millions=50.0 * (i + 1),
                portfolio_size=10 + (i * 5)
            )
            vcs.append(vc)
        
        return vcs
    
    return _generate


# Assertion Helpers
@pytest.fixture
def assert_api_response():
    """Helper for asserting API responses."""
    def _assert(response, expected_status: int = 200, 
                expected_fields: List[str] = None):
        assert response.status_code == expected_status
        
        if expected_status < 400 and expected_fields:
            data = response.json()
            for field in expected_fields:
                assert field in data, f"Field '{field}' not in response"
        
        return response.json() if expected_status < 400 else None
    
    return _assert


@pytest.fixture
def assert_repository_call():
    """Helper for asserting repository method calls."""
    def _assert(mock_repo, method_name: str, 
                expected_calls: int = 1, **expected_kwargs):
        method = getattr(mock_repo, method_name)
        assert method.call_count == expected_calls
        
        if expected_kwargs and expected_calls > 0:
            actual_kwargs = method.call_args.kwargs
            for key, value in expected_kwargs.items():
                assert key in actual_kwargs
                assert actual_kwargs[key] == value
    
    return _assert