"""Integration tests for authentication flow."""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from src.api.main import app
from src.database.setup import Base
from src.api.v1.deps import get_database
from tests.test_db_utils import init_test_db_with_migrations


# Test database setup with StaticPool for SQLite threading
engine = create_engine(
    "sqlite:///:memory:",
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)

# Initialize database tables including user table
init_test_db_with_migrations(engine)

TestSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    """Override database dependency for tests."""
    try:
        db = TestSessionLocal()
        yield db
    finally:
        db.close()


# Apply dependency override before creating client
app.dependency_overrides[get_database] = override_get_db

# Create test client
client = TestClient(app)


def login(username: str, password: str):
    """Helper function for login with proper OAuth2 form data."""
    # OAuth2PasswordRequestForm expects form data
    return client.post(
        "/api/v1/auth/login",
        data={
            "username": username,
            "password": password,
            "grant_type": "password"  # OAuth2 requires this
        }
    )


class TestAuthenticationFlow:
    """Test complete authentication flow."""
    
    def test_register_new_user(self):
        """Test user registration."""
        response = client.post(
            "/api/v1/auth/register",
            json={
                "email": "<EMAIL>",
                "username": "testuser",
                "password": "securepass123",
                "full_name": "Test User"
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["email"] == "<EMAIL>"
        assert data["username"] == "testuser"
        assert data["full_name"] == "Test User"
        assert data["is_active"] is True
        assert "user" in data["roles"]
        assert "id" in data
    
    def test_register_duplicate_email(self):
        """Test registration with duplicate email."""
        # First registration
        client.post(
            "/api/v1/auth/register",
            json={
                "email": "<EMAIL>",
                "username": "user1",
                "password": "securepass123"
            }
        )
        
        # Attempt duplicate registration
        response = client.post(
            "/api/v1/auth/register",
            json={
                "email": "<EMAIL>",
                "username": "user2",
                "password": "securepass123"
            }
        )
        
        assert response.status_code == 409
        error_data = response.json()
        assert "Email already registered" in error_data.get("message", error_data.get("detail", ""))
    
    def test_login_success(self):
        """Test successful login."""
        # Register user first
        client.post(
            "/api/v1/auth/register",
            json={
                "email": "<EMAIL>",
                "username": "loginuser",
                "password": "securepass123"
            }
        )
        
        # Login with username (OAuth2 expects form data)
        response = client.post(
            "/api/v1/auth/login",
            data={
                "username": "loginuser",
                "password": "securepass123"
            },
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
        assert data["user"]["username"] == "loginuser"
    
    def test_login_with_email(self):
        """Test login using email instead of username."""
        # Register user
        client.post(
            "/api/v1/auth/register",
            json={
                "email": "<EMAIL>",
                "username": "emailuser",
                "password": "securepass123"
            }
        )
        
        # Login with email
        response = client.post(
            "/api/v1/auth/login",
            data={
                "username": "<EMAIL>",
                "password": "securepass123"
            },
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        assert response.status_code == 200
        assert "access_token" in response.json()
    
    def test_login_invalid_credentials(self):
        """Test login with invalid credentials."""
        response = client.post(
            "/api/v1/auth/login",
            data={
                "username": "nonexistent",
                "password": "wrongpass"
            },
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        assert response.status_code == 401
        error_data = response.json()
        assert "Invalid credentials" in error_data.get("message", error_data.get("detail", ""))
    
    def test_get_current_user(self):
        """Test getting current user profile."""
        # Register and login
        client.post(
            "/api/v1/auth/register",
            json={
                "email": "<EMAIL>",
                "username": "currentuser",
                "password": "securepass123",
                "full_name": "Current User"
            }
        )
        
        login_response = login("currentuser", "securepass123")
        
        token = login_response.json()["access_token"]
        
        # Get current user
        response = client.get(
            "/api/v1/auth/me",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["username"] == "currentuser"
        assert data["email"] == "<EMAIL>"
        assert data["full_name"] == "Current User"
    
    def test_unauthorized_access(self):
        """Test accessing protected endpoint without token."""
        response = client.get("/api/v1/auth/me")
        
        assert response.status_code == 401
    
    def test_change_password(self):
        """Test password change functionality."""
        # Register and login
        client.post(
            "/api/v1/auth/register",
            json={
                "email": "<EMAIL>",
                "username": "passuser",
                "password": "oldpass123"
            }
        )
        
        login_response = login("passuser", "oldpass123")
        
        token = login_response.json()["access_token"]
        
        # Change password
        response = client.put(
            "/api/v1/auth/change-password",
            json={
                "old_password": "oldpass123",
                "new_password": "newpass456"
            },
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 200
        
        # Try logging in with new password
        new_login = login("passuser", "newpass456")
        
        assert new_login.status_code == 200
    
    def test_refresh_token(self):
        """Test token refresh functionality."""
        # Register and login
        client.post(
            "/api/v1/auth/register",
            json={
                "email": "<EMAIL>",
                "username": "refreshuser",
                "password": "securepass123"
            }
        )
        
        login_response = login("refreshuser", "securepass123")
        
        token = login_response.json()["access_token"]
        
        # Refresh token
        response = client.post(
            "/api/v1/auth/refresh",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 200
        assert "access_token" in response.json()
        assert response.json()["token_type"] == "bearer"


class TestProtectedEndpoints:
    """Test authentication on protected endpoints."""
    
    def test_create_startup_requires_auth(self):
        """Test that creating a startup requires authentication."""
        response = client.post(
            "/api/v1/startups",
            json={
                "name": "Test Startup",
                "sector": "Technology",
                "stage": "Seed"
            }
        )
        
        # Should require authentication
        assert response.status_code == 401
    
    def test_create_startup_with_auth(self):
        """Test creating startup with valid authentication."""
        # Register and login
        client.post(
            "/api/v1/auth/register",
            json={
                "email": "<EMAIL>",
                "username": "startupuser",
                "password": "securepass123"
            }
        )
        
        login_response = login("startupuser", "securepass123")
        
        token = login_response.json()["access_token"]
        
        # Create startup with auth
        response = client.post(
            "/api/v1/startups",
            json={
                "name": "Auth Test Startup",
                "sector": "Technology",
                "stage": "Seed",
                "description": "Test startup created with auth"
            },
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "Auth Test Startup"