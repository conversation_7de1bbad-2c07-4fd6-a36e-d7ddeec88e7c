"""Integration tests for async startup repository."""

import pytest
import asyncio
from uuid import uuid4
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker

from src.database.base import Base
from src.database.repositories.async_startup_repository import AsyncStartupRepository
from src.core.models.startup import Startup as StartupDomainModel
from src.core.config import settings


@pytest.mark.asyncio
class TestAsyncStartupRepository:
    """Test async startup repository operations."""
    
    @pytest.fixture
    async def async_engine(self):
        """Create async test engine."""
        # Use in-memory SQLite for tests
        engine = create_async_engine(
            "sqlite+aiosqlite:///:memory:",
            echo=True
        )
        
        # Create tables
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        yield engine
        
        # Cleanup
        await engine.dispose()
    
    @pytest.fixture
    async def async_session(self, async_engine):
        """Create async test session."""
        async_session_maker = async_sessionmaker(
            async_engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
        
        async with async_session_maker() as session:
            yield session
    
    @pytest.fixture
    async def repository(self, async_session):
        """Create repository instance."""
        return AsyncStartupRepository(async_session)
    
    @pytest.fixture
    def sample_startup(self):
        """Create sample startup domain model."""
        return StartupDomainModel(
            id=uuid4(),
            name="Async Test Startup",
            sector="Technology",
            stage="Series A",
            description="Testing async repository",
            website="https://async-test.com",
            team_size=10,
            monthly_revenue=50000.0
        )
    
    async def test_create_startup(self, repository, sample_startup):
        """Test creating a startup."""
        # Create startup
        saved = await repository.save(sample_startup)
        
        # Verify
        assert saved.id == sample_startup.id
        assert saved.name == sample_startup.name
        assert saved.sector == sample_startup.sector
        assert saved.stage == sample_startup.stage
    
    async def test_find_by_id(self, repository, sample_startup):
        """Test finding startup by ID."""
        # Create startup
        await repository.save(sample_startup)
        
        # Find by ID
        found = await repository.find_by_id(sample_startup.id)
        
        # Verify
        assert found is not None
        assert found.id == sample_startup.id
        assert found.name == sample_startup.name
    
    async def test_find_by_id_not_found(self, repository):
        """Test finding non-existent startup."""
        not_found = await repository.find_by_id(uuid4())
        assert not_found is None
    
    async def test_update_startup(self, repository, sample_startup):
        """Test updating a startup."""
        # Create startup
        await repository.save(sample_startup)
        
        # Update
        sample_startup.name = "Updated Async Startup"
        sample_startup.team_size = 20
        updated = await repository.save(sample_startup)
        
        # Verify
        assert updated.name == "Updated Async Startup"
        assert updated.team_size == 20
    
    async def test_delete_startup(self, repository, sample_startup):
        """Test deleting a startup."""
        # Create startup
        await repository.save(sample_startup)
        
        # Delete
        deleted = await repository.delete(sample_startup.id)
        assert deleted is True
        
        # Verify deleted
        found = await repository.find_by_id(sample_startup.id)
        assert found is None
    
    async def test_find_by_sector(self, repository):
        """Test finding startups by sector."""
        # Create startups
        startup1 = StartupDomainModel(
            id=uuid4(),
            name="Tech Startup 1",
            sector="Technology",
            stage="Seed",
            description="Tech startup 1"
        )
        startup2 = StartupDomainModel(
            id=uuid4(),
            name="Tech Startup 2",
            sector="Technology",
            stage="Series A",
            description="Tech startup 2"
        )
        startup3 = StartupDomainModel(
            id=uuid4(),
            name="Finance Startup",
            sector="Finance",
            stage="Series B",
            description="Finance startup"
        )
        
        await repository.save(startup1)
        await repository.save(startup2)
        await repository.save(startup3)
        
        # Find by sector
        tech_startups = await repository.find_by_sector("Technology")
        
        # Verify
        assert len(tech_startups) == 2
        assert all(s.sector == "Technology" for s in tech_startups)
    
    async def test_search_startups(self, repository):
        """Test searching startups."""
        # Create startups
        startup1 = StartupDomainModel(
            id=uuid4(),
            name="AI Analytics Platform",
            sector="Technology",
            stage="Series A",
            description="Advanced AI-powered analytics"
        )
        startup2 = StartupDomainModel(
            id=uuid4(),
            name="FinTech Solutions",
            sector="Finance",
            stage="Series B",
            description="Financial technology platform"
        )
        
        await repository.save(startup1)
        await repository.save(startup2)
        
        # Search by name
        results = await repository.search("AI")
        assert len(results) == 1
        assert results[0].name == "AI Analytics Platform"
        
        # Search by description
        results = await repository.search("platform")
        assert len(results) == 2
    
    async def test_concurrent_operations(self, repository):
        """Test concurrent database operations."""
        # Create multiple startups concurrently
        startups = [
            StartupDomainModel(
                id=uuid4(),
                name=f"Concurrent Startup {i}",
                sector="Technology",
                stage="Seed",
                description=f"Testing concurrent ops {i}"
            )
            for i in range(5)
        ]
        
        # Save concurrently
        tasks = [repository.save(s) for s in startups]
        saved = await asyncio.gather(*tasks)
        
        # Verify all saved
        assert len(saved) == 5
        assert all(s.name.startswith("Concurrent Startup") for s in saved)
        
        # Find all
        all_startups = await repository.find_all()
        assert len(all_startups) >= 5