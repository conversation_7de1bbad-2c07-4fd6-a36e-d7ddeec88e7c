"""Integration tests for health check endpoints."""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock

from src.api.main import app

client = TestClient(app)


class TestHealthEndpoints:
    """Test health check endpoints."""
    
    def test_basic_health_check(self):
        """Test basic health endpoint."""
        response = client.get("/api/v1/health/")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "service" in data
        assert "version" in data
    
    def test_root_health_check(self):
        """Test root health endpoint."""
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
    
    @patch('src.workers.health.check_celery_status')
    @patch('src.workers.health.check_celery_beat_status')
    @patch('src.infrastructure.redis.adapter.RedisAdapter.health_check')
    @patch('src.infrastructure.redis.connection.RedisConnectionFactory.health_check')
    @patch('src.api.v1.endpoints.health.get_db')
    def test_detailed_health_check_all_healthy(self, mock_get_db, mock_redis_health, 
                                             mock_adapter_health, mock_beat_status, 
                                             mock_celery_status):
        """Test detailed health check when all components are healthy."""
        # Mock Redis health
        mock_redis_health.return_value = {
            "healthy": True,
            "connection": "active",
            "ping_time": 0.001
        }
        
        # Mock database
        mock_db = MagicMock()
        mock_db.execute.return_value.fetchone.return_value = (1,)
        mock_get_db.return_value.__next__.return_value = mock_db
        
        # Mock adapter health check
        mock_adapter_health.return_value = True
        
        # Mock Celery Beat status
        mock_beat_status.return_value = {
            "healthy": True,
            "scheduled_tasks": 5
        }
        
        # Mock Celery status
        mock_celery_status.return_value = {
            "healthy": True,
            "worker_count": 2,
            "active_tasks": 0,
            "registered_tasks": 10
        }
        
        # Mock cache operations
        with patch('src.infrastructure.redis.adapter.RedisAdapter.set') as mock_set:
            with patch('src.infrastructure.redis.adapter.RedisAdapter.get') as mock_get:
                with patch('src.infrastructure.redis.adapter.RedisAdapter.delete') as mock_delete:
                    mock_set.return_value = True
                    mock_get.return_value = "test_value"
                    mock_delete.return_value = True
                    
                    response = client.get("/api/v1/health/detailed")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "components" in data
        
        # Check individual components
        assert data["components"]["redis"]["healthy"] is True
        assert data["components"]["database"]["healthy"] is True
        assert data["components"]["celery"]["healthy"] is True
    
    @patch('src.infrastructure.redis.connection.RedisConnectionFactory.health_check')
    def test_detailed_health_check_redis_unhealthy(self, mock_redis_health):
        """Test detailed health check when Redis is unhealthy."""
        # Mock Redis as unhealthy
        mock_redis_health.side_effect = Exception("Connection refused")
        
        response = client.get("/api/v1/health/detailed")
        
        # Should return 503 when a component is unhealthy
        assert response.status_code == 503
        data = response.json()
        assert data["detail"]["status"] == "unhealthy"
        assert data["detail"]["components"]["redis"]["healthy"] is False
    
    @patch('src.infrastructure.redis.connection.RedisConnectionFactory.health_check')
    def test_redis_specific_health_check(self, mock_redis_health):
        """Test Redis-specific health endpoint."""
        mock_redis_health.return_value = {
            "healthy": True,
            "connection": "active",
            "ping_time": 0.002,
            "version": "6.2.6"
        }
        
        with patch('src.infrastructure.redis.connection.RedisConnectionFactory.get_connection_info') as mock_conn_info:
            mock_conn_info.return_value = {
                "host": "localhost",
                "port": 6379,
                "db": 0
            }
            
            response = client.get("/api/v1/health/redis")
        
        assert response.status_code == 200
        data = response.json()
        assert data["healthy"] is True
        assert "connection_info" in data
    
    def test_celery_health_check(self):
        """Test Celery-specific health endpoint."""
        with patch('src.workers.health.check_celery_status') as mock_celery_status:
            with patch('src.workers.health.check_celery_beat_status') as mock_beat_status:
                with patch('src.workers.health.get_queue_info') as mock_queue_info:
                    
                    mock_celery_status.return_value = {
                        "healthy": False,  # No workers in test
                        "worker_count": 0,
                        "error": "No workers found"
                    }
                    
                    mock_beat_status.return_value = {
                        "healthy": True,
                        "scheduled_tasks": 5
                    }
                    
                    mock_queue_info.return_value = {
                        "healthy": False,
                        "error": "No active queues"
                    }
                    
                    response = client.get("/api/v1/health/celery")
        
        # Should return 503 when Celery is unhealthy
        assert response.status_code == 503
        data = response.json()
        assert data["detail"]["overall_healthy"] is False
    
    def test_dependencies_check(self):
        """Test dependencies endpoint."""
        with patch('src.infrastructure.redis.connection.RedisConnectionFactory.health_check') as mock_redis:
            with patch('src.api.v1.endpoints.health.get_db') as mock_get_db:
                with patch('src.workers.health.check_celery_status') as mock_celery:
                    
                    # Mock healthy dependencies
                    mock_redis.return_value = {"healthy": True}
                    
                    mock_db = MagicMock()
                    mock_db.execute.return_value.fetchone.return_value = (1,)
                    mock_get_db.return_value.__next__.return_value = mock_db
                    
                    mock_celery.return_value = {"healthy": True, "worker_count": 1}
                    
                    response = client.get("/api/v1/health/dependencies")
        
        assert response.status_code == 200
        data = response.json()
        assert "dependencies" in data
        assert "redis" in data["dependencies"]
        assert "database" in data["dependencies"]
        assert "celery" in data["dependencies"]
        assert "openai" in data["dependencies"]
    
    def test_ready_endpoint(self):
        """Test readiness endpoint."""
        response = client.get("/ready")
        
        assert response.status_code == 200
        data = response.json()
        assert data["ready"] is True
        assert "service" in data


class TestCacheManagement:
    """Test cache management endpoints."""
    
    @patch('src.infrastructure.redis.adapter.RedisAdapter.get_stats')
    def test_cache_stats(self, mock_get_stats):
        """Test cache statistics endpoint."""
        mock_get_stats.return_value = {
            "hit_rate": 0.85,
            "total_hits": 1500,
            "total_misses": 265,
            "total_operations": 1765
        }
        
        response = client.get("/api/v1/health/cache/stats")
        
        assert response.status_code == 200
        data = response.json()
        assert data["hit_rate"] == 0.85
        assert "cache_config" in data
    
    @patch('src.infrastructure.redis.adapter.RedisAdapter.clear_prefix')
    def test_cache_clear(self, mock_clear_prefix):
        """Test cache clearing endpoint."""
        mock_clear_prefix.return_value = 42  # Number of keys deleted
        
        response = client.post("/api/v1/health/cache/clear?pattern=test:*")
        
        assert response.status_code == 200
        data = response.json()
        assert data["deleted_keys"] == 42
        assert data["status"] == "success"
    
    def test_cache_clear_safety_check(self):
        """Test cache clear safety check prevents clearing all keys."""
        with patch('src.infrastructure.redis.adapter.RedisAdapter') as mock_adapter:
            # Create instance with no key_prefix
            instance = mock_adapter.return_value
            instance.key_prefix = None
            
            response = client.post("/api/v1/health/cache/clear?pattern=*")
        
        # Should reject clearing all keys without prefix
        assert response.status_code == 400
        assert "Cannot clear all Redis keys" in response.json()["detail"]