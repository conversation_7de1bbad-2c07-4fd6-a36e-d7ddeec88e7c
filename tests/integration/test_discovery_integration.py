"""Integration tests for warm intro paths in discovery system."""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime
from uuid import UUID, uuid4
from fastapi.testclient import TestClient

from src.api.main import app
from src.core.models.connection import (
    Connection,
    ConnectionId,
    ConnectionMetrics,
    ConnectionStrength,
    RelationshipType,
    ConnectionPath
)
from src.core.models.user import User
from src.core.models.startup import Startup
from src.core.models.vc import VC


@pytest.fixture
def discovery_test_client():
    """Create test client for discovery integration tests."""
    return TestClient(app)


@pytest.fixture
def mock_discovery_system():
    """Mock the complete discovery system with warm intro integration."""
    # Create mock entities for discovery
    startup1 = Startup(
        id=UUID('660e8400-e29b-41d4-a716-************'),
        name='AI Analytics Corp',
        sector='AI/ML',
        stage='Series A',
        description='AI-powered business analytics platform',
        team_size=25,
        monthly_revenue=150000
    )
    
    startup2 = Startup(
        id=UUID('660e8400-e29b-41d4-a716-************'),
        name='FinTech Innovations',
        sector='FinTech',
        stage='Seed',
        description='Blockchain-based payment solutions',
        team_size=12,
        monthly_revenue=50000
    )
    
    vc1 = VC(
        id=UUID('770e8400-e29b-41d4-a716-************'),
        firm_name='Tech Ventures Capital',
        sectors=['AI/ML', 'B2B SaaS'],
        stages=['Series A', 'Series B'],
        thesis='AI-first enterprise solutions',
        check_size_min=2000000,
        check_size_max=10000000
    )
    
    vc2 = VC(
        id=UUID('770e8400-e29b-41d4-a716-************'),
        firm_name='Blockchain Capital Partners',
        sectors=['FinTech', 'Blockchain'],
        stages=['Seed', 'Series A'],
        thesis='Decentralized finance innovation',
        check_size_min=500000,
        check_size_max=5000000
    )
    
    # Mock users associated with entities
    users = {
        'alice': User(id=UUID('550e8400-e29b-41d4-a716-************'), name='Alice Johnson', email='<EMAIL>', role='founder'),
        'bob': User(id=UUID('550e8400-e29b-41d4-a716-************'), name='Bob Smith', email='<EMAIL>', role='investor'),
        'charlie': User(id=UUID('550e8400-e29b-41d4-a716-************'), name='Charlie Brown', email='<EMAIL>', role='founder'),
        'diana': User(id=UUID('550e8400-e29b-41d4-a716-************'), name='Diana Prince', email='<EMAIL>', role='investor'),
        'eve': User(id=UUID('550e8400-e29b-41d4-a716-************'), name='Eve Wilson', email='<EMAIL>', role='founder'),
        'frank': User(id=UUID('550e8400-e29b-41d4-a716-************'), name='Frank Miller', email='<EMAIL>', role='investor')
    }
    
    # Mock discovery matches (AI startup + AI VC, FinTech startup + FinTech VC)
    matches = [
        {
            'startup': startup1,
            'vc': vc1,
            'match_score': 8.5,
            'startup_users': [users['charlie']],
            'vc_users': [users['diana']]
        },
        {
            'startup': startup2,
            'vc': vc2,
            'match_score': 7.2,
            'startup_users': [users['eve']],
            'vc_users': [users['frank']]
        }
    ]
    
    return {
        'startups': [startup1, startup2],
        'vcs': [vc1, vc2],
        'users': users,
        'matches': matches
    }


class TestDiscoveryWithWarmIntroIntegration:
    """Test discovery system enhanced with warm intro functionality."""
    
    def test_discovery_endpoint_includes_intro_paths(self, discovery_test_client, mock_discovery_system):
        """Test that discovery endpoint includes warm intro path information."""
        users = mock_discovery_system['users']
        matches = mock_discovery_system['matches']
        
        # Mock authentication
        from src.api.v1.deps import get_current_user
        def mock_get_current_user():
            return users['alice'].id
        
        # Mock warm intro service to return paths
        with patch('src.api.v1.endpoints.connections.WarmIntroService') as mock_service_class:
            mock_service = Mock()
            
            # Mock paths for first match (AI startup)
            ai_startup_paths = [
                {
                    'target_user': {
                        'id': str(users['charlie'].id),
                        'name': users['charlie'].name,
                        'email': users['charlie'].email,
                        'role': 'founder'
                    },
                    'path': [
                        {'id': str(users['alice'].id), 'name': 'Alice', 'position': 0},
                        {'id': str(users['bob'].id), 'name': 'Bob', 'position': 1},
                        {'id': str(users['charlie'].id), 'name': 'Charlie', 'position': 2}
                    ],
                    'strength_score': 0.8,
                    'hop_count': 2,
                    'can_request_intro': True
                }
            ]
            
            # Mock paths for VC
            ai_vc_paths = [
                {
                    'target_user': {
                        'id': str(users['diana'].id),
                        'name': users['diana'].name,
                        'email': users['diana'].email,
                        'role': 'investor'
                    },
                    'path': [
                        {'id': str(users['alice'].id), 'name': 'Alice', 'position': 0},
                        {'id': str(users['diana'].id), 'name': 'Diana', 'position': 1}
                    ],
                    'strength_score': 0.9,
                    'hop_count': 1,
                    'can_request_intro': True
                }
            ]
            
            mock_service.find_intro_paths_for_match.side_effect = [
                ai_startup_paths,  # For startup
                ai_vc_paths        # For VC
            ]
            mock_service_class.return_value = mock_service
            
            # Override auth dependency
            app.dependency_overrides[get_current_user] = mock_get_current_user
            
            try:
                # Test paths to startup
                response = discovery_test_client.get(
                    f"/connections/paths/to-startup/{matches[0]['startup'].id}"
                )
                
                assert response.status_code == 200
                startup_paths = response.json()
                
                assert len(startup_paths) == 1
                path = startup_paths[0]
                assert path['target_user']['name'] == users['charlie'].name
                assert path['strength_score'] == 0.8
                assert path['hop_count'] == 2
                assert path['can_request_intro'] is True
                
                # Test paths to VC
                response = discovery_test_client.get(
                    f"/connections/paths/to-vc/{matches[0]['vc'].id}"
                )
                
                assert response.status_code == 200
                vc_paths = response.json()
                
                assert len(vc_paths) == 1
                vc_path = vc_paths[0]
                assert vc_path['target_user']['name'] == users['diana'].name
                assert vc_path['strength_score'] == 0.9
                assert vc_path['hop_count'] == 1
                
            finally:
                app.dependency_overrides.clear()
    
    @pytest.mark.asyncio
    async def test_match_ranking_enhanced_by_intro_availability(self, mock_discovery_system):
        """Test that match ranking considers warm intro path availability and strength."""
        users = mock_discovery_system['users']
        matches = mock_discovery_system['matches']
        
        # Mock warm intro service
        from src.core.services.warm_intro_service import WarmIntroService
        
        mock_connection_repo = Mock()
        mock_introduction_repo = Mock()
        mock_user_repo = Mock()
        mock_startup_repo = Mock()
        mock_vc_repo = Mock()
        
        # Configure mocks
        mock_user_repo.get.side_effect = lambda user_id: next(
            (user for user in users.values() if user.id == user_id), None
        )
        mock_user_repo.get_users_by_startup.side_effect = lambda startup_id: [
            user for match in matches 
            for user in match['startup_users'] 
            if match['startup'].id == startup_id
        ]
        mock_user_repo.get_users_by_vc.side_effect = lambda vc_id: [
            user for match in matches 
            for user in match['vc_users'] 
            if match['vc'].id == vc_id
        ]
        
        mock_startup_repo.get.side_effect = lambda startup_id: next(
            (match['startup'] for match in matches if match['startup'].id == startup_id), None
        )
        mock_vc_repo.get.side_effect = lambda vc_id: next(
            (match['vc'] for match in matches if match['vc'].id == vc_id), None
        )
        
        service = WarmIntroService(
            connection_repo=mock_connection_repo,
            introduction_repo=mock_introduction_repo,
            user_repo=mock_user_repo,
            startup_repo=mock_startup_repo,
            vc_repo=mock_vc_repo
        )
        
        # Match 1: Strong intro path available
        strong_path = ConnectionPath(
            source_user_id=users['alice'].id,
            target_user_id=users['charlie'].id,
            path=[users['alice'].id, users['charlie'].id],
            connections=[],
            total_strength_score=0.9
        )
        
        # Match 2: Weak intro path available
        weak_path = ConnectionPath(
            source_user_id=users['alice'].id,
            target_user_id=users['eve'].id,
            path=[users['alice'].id, users['bob'].id, users['frank'].id, users['eve'].id],
            connections=[],
            total_strength_score=0.3
        )
        
        mock_connection_repo.find_shortest_paths.side_effect = [
            [strong_path],  # For AI startup (strong path)
            [],             # For AI VC (no path)
            [weak_path],    # For FinTech startup (weak path)
            []              # For FinTech VC (no path)
        ]
        
        # Get intro paths for both matches
        match1_startup_paths = await service.find_intro_paths_for_match(
            requester_id=users['alice'].id,
            startup_id=matches[0]['startup'].id
        )
        
        match1_vc_paths = await service.find_intro_paths_for_match(
            requester_id=users['alice'].id,
            vc_id=matches[0]['vc'].id
        )
        
        match2_startup_paths = await service.find_intro_paths_for_match(
            requester_id=users['alice'].id,
            startup_id=matches[1]['startup'].id
        )
        
        match2_vc_paths = await service.find_intro_paths_for_match(
            requester_id=users['alice'].id,
            vc_id=matches[1]['vc'].id
        )
        
        # Simulate enhanced match scoring
        def calculate_enhanced_match_score(base_score, startup_paths, vc_paths):
            """Calculate enhanced match score considering intro paths."""
            intro_bonus = 0
            
            # Bonus for having intro paths
            if startup_paths:
                intro_bonus += 0.5 * startup_paths[0]['strength_score']
            if vc_paths:
                intro_bonus += 0.5 * vc_paths[0]['strength_score']
            
            # Penalty for no intro paths
            if not startup_paths and not vc_paths:
                intro_bonus -= 1.0
            
            return base_score + intro_bonus
        
        # Calculate enhanced scores
        match1_enhanced_score = calculate_enhanced_match_score(
            matches[0]['match_score'], match1_startup_paths, match1_vc_paths
        )
        
        match2_enhanced_score = calculate_enhanced_match_score(
            matches[1]['match_score'], match2_startup_paths, match2_vc_paths
        )
        
        # Match 1 should have higher enhanced score due to strong startup intro path
        assert match1_enhanced_score > matches[0]['match_score']  # Improved by intro path
        assert match2_enhanced_score < matches[1]['match_score']  # Penalized for weak/no paths
        assert match1_enhanced_score > match2_enhanced_score     # Match 1 ranks higher overall
    
    def test_intro_request_from_discovery_context(self, discovery_test_client, mock_discovery_system):
        """Test requesting introduction directly from discovery context."""
        users = mock_discovery_system['users']
        matches = mock_discovery_system['matches']
        
        from src.api.v1.deps import get_current_user
        def mock_get_current_user():
            return users['alice'].id
        
        # Mock the warm intro service
        with patch('src.api.v1.endpoints.connections.WarmIntroService') as mock_service_class:
            mock_service = Mock()
            
            # Mock successful introduction request
            from src.core.models.connection import IntroductionRequest, IntroductionStatus
            mock_request = IntroductionRequest(
                requester_id=users['alice'].id,
                target_id=users['charlie'].id,
                connector_id=users['bob'].id,
                message="Introduction through discovery system"
            )
            mock_service.request_introduction = AsyncMock(return_value=mock_request)
            mock_service_class.return_value = mock_service
            
            app.dependency_overrides[get_current_user] = mock_get_current_user
            
            try:
                # Request introduction to startup founder discovered through matching
                response = discovery_test_client.post(
                    "/connections/introductions/request",
                    json={
                        "target_id": str(users['charlie'].id),
                        "connector_id": str(users['bob'].id),
                        "message": f"Hi! I discovered {matches[0]['startup'].name} through our matching system and would love to connect to discuss potential collaboration."
                    }
                )
                
                assert response.status_code == 200
                data = response.json()
                assert data['status'] == 'pending'
                assert "Introduction request sent successfully" in data['message']
                
                # Verify service was called with discovery context
                mock_service.request_introduction.assert_called_once()
                call_args = mock_service.request_introduction.call_args
                assert call_args.kwargs['requester_id'] == users['alice'].id
                assert call_args.kwargs['target_id'] == users['charlie'].id
                assert call_args.kwargs['connector_id'] == users['bob'].id
                assert "discovered" in call_args.kwargs['message'].lower()
                
            finally:
                app.dependency_overrides.clear()
    
    @pytest.mark.asyncio
    async def test_discovery_ui_data_enrichment(self, mock_discovery_system):
        """Test that discovery data is enriched with intro path information for UI."""
        users = mock_discovery_system['users']
        matches = mock_discovery_system['matches']
        
        # Mock warm intro service
        from src.core.services.warm_intro_service import WarmIntroService
        
        mock_connection_repo = Mock()
        mock_introduction_repo = Mock()
        mock_user_repo = Mock()
        mock_startup_repo = Mock()
        mock_vc_repo = Mock()
        
        # Configure mocks
        mock_user_repo.get.side_effect = lambda user_id: next(
            (user for user in users.values() if user.id == user_id), None
        )
        mock_user_repo.get_users_by_startup.return_value = [users['charlie']]
        mock_startup_repo.get.return_value = matches[0]['startup']
        
        # Mock a realistic intro path
        realistic_path = ConnectionPath(
            source_user_id=users['alice'].id,
            target_user_id=users['charlie'].id,
            path=[users['alice'].id, users['bob'].id, users['charlie'].id],
            connections=[],
            total_strength_score=0.7
        )
        
        mock_connection_repo.find_shortest_paths.return_value = [realistic_path]
        
        service = WarmIntroService(
            connection_repo=mock_connection_repo,
            introduction_repo=mock_introduction_repo,
            user_repo=mock_user_repo,
            startup_repo=mock_startup_repo,
            vc_repo=mock_vc_repo
        )
        
        # Get intro paths for match
        paths = await service.find_intro_paths_for_match(
            requester_id=users['alice'].id,
            startup_id=matches[0]['startup'].id
        )
        
        # Simulate discovery UI data structure enriched with intro info
        enriched_match = {
            'match_id': f"{matches[0]['startup'].id}_{matches[0]['vc'].id}",
            'startup': {
                'id': str(matches[0]['startup'].id),
                'name': matches[0]['startup'].name,
                'sector': matches[0]['startup'].sector,
                'stage': matches[0]['startup'].stage,
                'description': matches[0]['startup'].description
            },
            'vc': {
                'id': str(matches[0]['vc'].id),
                'firm_name': matches[0]['vc'].firm_name,
                'sectors': matches[0]['vc'].sectors,
                'stages': matches[0]['vc'].stages,
                'thesis': matches[0]['vc'].thesis
            },
            'match_score': matches[0]['match_score'],
            'intro_paths': {
                'startup_paths': paths,
                'vc_paths': [],  # No VC paths in this test
                'best_path': paths[0] if paths else None,
                'intro_available': len(paths) > 0,
                'min_hop_count': min(p['hop_count'] for p in paths) if paths else None,
                'max_strength_score': max(p['strength_score'] for p in paths) if paths else None
            }
        }
        
        # Verify enriched data structure
        assert enriched_match['intro_paths']['intro_available'] is True
        assert enriched_match['intro_paths']['min_hop_count'] == 2
        assert enriched_match['intro_paths']['max_strength_score'] == 0.7
        assert enriched_match['intro_paths']['best_path']['target_user']['name'] == users['charlie'].name
        
        # Verify UI can determine what to show
        best_path = enriched_match['intro_paths']['best_path']
        assert best_path['can_request_intro'] is True
        assert len(best_path['path']) == 3  # Alice -> Bob -> Charlie
        
        # UI would show:
        # - "Warm intro available" indicator
        # - "Connect via [connector name]" button
        # - Path visualization: "You -> Bob Smith -> Charlie Brown"
        # - Strength indicator: "Strong connection path"


class TestDiscoveryPerformanceWithIntros:
    """Test performance considerations when integrating discovery with intro paths."""
    
    @pytest.mark.asyncio
    async def test_batch_intro_path_lookup(self, mock_discovery_system):
        """Test efficient batch lookup of intro paths for multiple matches."""
        users = mock_discovery_system['users']
        matches = mock_discovery_system['matches']
        
        # Mock warm intro service
        from src.core.services.warm_intro_service import WarmIntroService
        
        mock_connection_repo = Mock()
        mock_introduction_repo = Mock()
        mock_user_repo = Mock()
        mock_startup_repo = Mock()
        mock_vc_repo = Mock()
        
        # Track how many times path finding is called
        path_find_call_count = 0
        
        def mock_find_paths(*args, **kwargs):
            nonlocal path_find_call_count
            path_find_call_count += 1
            # Return different paths for different targets
            target_user_id = kwargs.get('target_user_id')
            if target_user_id == users['charlie'].id:
                return [ConnectionPath(
                    source_user_id=users['alice'].id,
                    target_user_id=target_user_id,
                    path=[users['alice'].id, target_user_id],
                    connections=[],
                    total_strength_score=0.8
                )]
            return []
        
        mock_connection_repo.find_shortest_paths.side_effect = mock_find_paths
        mock_user_repo.get.side_effect = lambda user_id: next(
            (user for user in users.values() if user.id == user_id), None
        )
        
        service = WarmIntroService(
            connection_repo=mock_connection_repo,
            introduction_repo=mock_introduction_repo,
            user_repo=mock_user_repo,
            startup_repo=mock_startup_repo,
            vc_repo=mock_vc_repo
        )
        
        # Simulate discovery system looking up intro paths for multiple matches
        all_intro_paths = {}
        
        for match in matches:
            # Get startup users
            mock_user_repo.get_users_by_startup.return_value = match['startup_users']
            mock_startup_repo.get.return_value = match['startup']
            
            startup_paths = await service.find_intro_paths_for_match(
                requester_id=users['alice'].id,
                startup_id=match['startup'].id
            )
            
            # Get VC users
            mock_user_repo.get_users_by_vc.return_value = match['vc_users']
            mock_vc_repo.get.return_value = match['vc']
            
            vc_paths = await service.find_intro_paths_for_match(
                requester_id=users['alice'].id,
                vc_id=match['vc'].id
            )
            
            all_intro_paths[match['startup'].id] = {
                'startup_paths': startup_paths,
                'vc_paths': vc_paths
            }
        
        # Verify results
        assert len(all_intro_paths) == 2
        
        # AI Analytics Corp should have a path
        ai_startup_id = matches[0]['startup'].id
        assert len(all_intro_paths[ai_startup_id]['startup_paths']) == 1
        assert all_intro_paths[ai_startup_id]['startup_paths'][0]['strength_score'] == 0.8
        
        # FinTech Innovations should have no paths
        fintech_startup_id = matches[1]['startup'].id
        assert len(all_intro_paths[fintech_startup_id]['startup_paths']) == 0
        
        # Verify reasonable number of path finding calls
        # Should be 4 calls total (2 matches × 2 entities each)
        assert path_find_call_count == 4
    
    @pytest.mark.asyncio
    async def test_intro_path_caching_strategy(self, mock_discovery_system):
        """Test caching strategy for intro paths to improve performance."""
        users = mock_discovery_system['users']
        
        # Mock a simple in-memory cache
        path_cache = {}
        
        def mock_find_paths_with_cache(*args, **kwargs):
            # Create cache key from source and target
            source_id = kwargs.get('source_user_id')
            target_id = kwargs.get('target_user_id')
            cache_key = f"{source_id}_{target_id}"
            
            # Check cache first
            if cache_key in path_cache:
                return path_cache[cache_key]
            
            # Simulate expensive path computation
            result = [ConnectionPath(
                source_user_id=source_id,
                target_user_id=target_id,
                path=[source_id, target_id],
                connections=[],
                total_strength_score=0.5
            )]
            
            # Cache result
            path_cache[cache_key] = result
            return result
        
        # Mock warm intro service with caching
        mock_connection_repo = Mock()
        mock_connection_repo.find_shortest_paths.side_effect = mock_find_paths_with_cache
        
        from src.core.services.warm_intro_service import WarmIntroService
        
        mock_introduction_repo = Mock()
        mock_user_repo = Mock()
        mock_startup_repo = Mock()
        mock_vc_repo = Mock()
        
        mock_user_repo.get.side_effect = lambda user_id: next(
            (user for user in users.values() if user.id == user_id), None
        )
        mock_user_repo.get_users_by_startup.return_value = [users['charlie']]
        mock_startup_repo.get.return_value = mock_discovery_system['matches'][0]['startup']
        
        service = WarmIntroService(
            connection_repo=mock_connection_repo,
            introduction_repo=mock_introduction_repo,
            user_repo=mock_user_repo,
            startup_repo=mock_startup_repo,
            vc_repo=mock_vc_repo
        )
        
        # First call - should compute and cache
        paths1 = await service.find_intro_paths_for_match(
            requester_id=users['alice'].id,
            startup_id=mock_discovery_system['matches'][0]['startup'].id
        )
        
        # Second call to same target - should use cache
        paths2 = await service.find_intro_paths_for_match(
            requester_id=users['alice'].id,  # Same requester
            startup_id=mock_discovery_system['matches'][0]['startup'].id  # Same startup
        )
        
        # Verify results are identical (from cache)
        assert len(paths1) == len(paths2) == 1
        assert paths1[0]['strength_score'] == paths2[0]['strength_score'] == 0.5
        
        # Verify cache was used (only one entry should exist)
        assert len(path_cache) == 1
        
        # The cache key should be for Alice -> Charlie
        expected_key = f"{users['alice'].id}_{users['charlie'].id}"
        assert expected_key in path_cache


class TestDiscoveryUIIntegration:
    """Test UI integration aspects of discovery with warm intros."""
    
    def test_discovery_response_format_for_ui(self, mock_discovery_system):
        """Test that discovery responses are formatted correctly for UI consumption."""
        users = mock_discovery_system['users']
        matches = mock_discovery_system['matches']
        
        # Simulate a discovery API response enhanced with intro data
        def format_match_for_ui(match, intro_paths):
            """Format match data for UI consumption."""
            return {
                'id': f"{match['startup'].id}_{match['vc'].id}",
                'startup': {
                    'id': str(match['startup'].id),
                    'name': match['startup'].name,
                    'logo': f"https://logos.example.com/{match['startup'].name.lower().replace(' ', '-')}.png",
                    'sector': match['startup'].sector,
                    'stage': match['startup'].stage,
                    'description': match['startup'].description,
                    'team_size': match['startup'].team_size,
                    'monthly_revenue': match['startup'].monthly_revenue
                },
                'vc': {
                    'id': str(match['vc'].id),
                    'name': match['vc'].firm_name,
                    'logo': f"https://logos.example.com/{match['vc'].firm_name.lower().replace(' ', '-')}.png",
                    'sectors': match['vc'].sectors,
                    'stages': match['vc'].stages,
                    'thesis': match['vc'].thesis,
                    'check_size': {
                        'min': match['vc'].check_size_min,
                        'max': match['vc'].check_size_max
                    }
                },
                'match_score': match['match_score'],
                'intro_status': {
                    'available': len(intro_paths['startup_paths']) > 0 or len(intro_paths['vc_paths']) > 0,
                    'startup_intro': {
                        'available': len(intro_paths['startup_paths']) > 0,
                        'best_path': intro_paths['startup_paths'][0] if intro_paths['startup_paths'] else None,
                        'hop_count': intro_paths['startup_paths'][0]['hop_count'] if intro_paths['startup_paths'] else None,
                        'strength': intro_paths['startup_paths'][0]['strength_score'] if intro_paths['startup_paths'] else None,
                        'connector_name': intro_paths['startup_paths'][0]['path'][1]['name'] if intro_paths['startup_paths'] and len(intro_paths['startup_paths'][0]['path']) > 1 else None
                    },
                    'vc_intro': {
                        'available': len(intro_paths['vc_paths']) > 0,
                        'best_path': intro_paths['vc_paths'][0] if intro_paths['vc_paths'] else None,
                        'hop_count': intro_paths['vc_paths'][0]['hop_count'] if intro_paths['vc_paths'] else None,
                        'strength': intro_paths['vc_paths'][0]['strength_score'] if intro_paths['vc_paths'] else None,
                        'connector_name': intro_paths['vc_paths'][0]['path'][1]['name'] if intro_paths['vc_paths'] and len(intro_paths['vc_paths'][0]['path']) > 1 else None
                    }
                },
                'actions': {
                    'can_request_startup_intro': intro_paths['startup_paths'][0]['can_request_intro'] if intro_paths['startup_paths'] else False,
                    'can_request_vc_intro': intro_paths['vc_paths'][0]['can_request_intro'] if intro_paths['vc_paths'] else False,
                    'direct_contact_available': False  # Assume no direct contact for this test
                }
            }
        
        # Mock intro paths for first match
        mock_intro_paths = {
            'startup_paths': [
                {
                    'target_user': {
                        'id': str(users['charlie'].id),
                        'name': users['charlie'].name,
                        'email': users['charlie'].email
                    },
                    'path': [
                        {'id': str(users['alice'].id), 'name': 'Alice', 'position': 0},
                        {'id': str(users['bob'].id), 'name': 'Bob', 'position': 1},
                        {'id': str(users['charlie'].id), 'name': 'Charlie', 'position': 2}
                    ],
                    'strength_score': 0.8,
                    'hop_count': 2,
                    'can_request_intro': True
                }
            ],
            'vc_paths': []
        }
        
        # Format match for UI
        ui_match = format_match_for_ui(matches[0], mock_intro_paths)
        
        # Verify UI-specific formatting
        assert 'id' in ui_match
        assert 'logo' in ui_match['startup']
        assert 'logo' in ui_match['vc']
        assert 'check_size' in ui_match['vc']
        assert 'intro_status' in ui_match
        assert 'actions' in ui_match
        
        # Verify intro status for UI
        intro_status = ui_match['intro_status']
        assert intro_status['available'] is True
        assert intro_status['startup_intro']['available'] is True
        assert intro_status['startup_intro']['hop_count'] == 2
        assert intro_status['startup_intro']['strength'] == 0.8
        assert intro_status['startup_intro']['connector_name'] == 'Bob'
        assert intro_status['vc_intro']['available'] is False
        
        # Verify actions for UI
        actions = ui_match['actions']
        assert actions['can_request_startup_intro'] is True
        assert actions['can_request_vc_intro'] is False
        assert actions['direct_contact_available'] is False
        
        # UI would render:
        # - Match card with startup and VC info
        # - "Warm intro available" badge
        # - "Connect via Bob" button for startup
        # - Path visualization: "You → Bob → Charlie"
        # - Strength indicator (0.8/1.0)
    
    def test_intro_request_success_feedback_for_ui(self):
        """Test success feedback format for intro request from UI."""
        # Mock successful intro request response
        intro_response = {
            'request_id': str(uuid4()),
            'status': 'pending',
            'message': 'Introduction request sent successfully',
            'expires_at': (datetime.utcnow() + timedelta(days=30)).isoformat(),
            'next_steps': [
                'Your introduction request has been sent to Bob Smith',
                'Bob will review your request and decide whether to make the introduction',
                'You will be notified when Bob responds to your request',
                'Introduction requests expire after 30 days if not responded to'
            ],
            'timeline': {
                'requested_at': datetime.utcnow().isoformat(),
                'expires_at': (datetime.utcnow() + timedelta(days=30)).isoformat(),
                'expected_response_time': '2-3 business days'
            },
            'involved_parties': {
                'requester': 'You',
                'connector': 'Bob Smith',
                'target': 'Charlie Brown (AI Analytics Corp founder)'
            }
        }
        
        # Verify UI feedback data structure
        assert intro_response['status'] == 'pending'
        assert 'next_steps' in intro_response
        assert len(intro_response['next_steps']) == 4
        assert 'timeline' in intro_response
        assert 'involved_parties' in intro_response
        assert intro_response['timeline']['expected_response_time'] == '2-3 business days'
        
        # UI would show:
        # - Success toast: "Introduction request sent!"
        # - Modal with next steps and timeline
        # - Status tracking UI showing "Pending response from Bob"
        # - Notification settings for status updates