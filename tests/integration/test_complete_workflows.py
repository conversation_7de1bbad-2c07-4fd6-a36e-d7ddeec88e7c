"""Comprehensive integration tests for complete system workflows."""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from fastapi.testclient import TestClient
from src.api.main import app
from src.api.v1.deps import get_database
from src.database.setup import Base
from tests.test_db_utils import init_test_db_with_migrations


# Test database setup
engine = create_engine(
    "sqlite:///:memory:",
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
init_test_db_with_migrations(engine)
TestSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    """Override database dependency for tests."""
    try:
        db = TestSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_database] = override_get_db
client = TestClient(app)


class TestCompleteUserJourneys:
    """Test complete user journeys through the platform."""
    
    def setup_method(self):
        """Set up test data before each test."""
        # Clear database
        Base.metadata.drop_all(bind=engine)
        init_test_db_with_migrations(engine)
        
        # Register test user
        self.user_data = {
            "email": "<EMAIL>",
            "username": "testuser",
            "password": "testpass123",
            "full_name": "Test User"
        }
        response = client.post("/api/v1/auth/register", json=self.user_data)
        assert response.status_code == 200
        
        # Login and get token (OAuth2 requires form-encoded data)
        login_response = client.post(
            "/api/v1/auth/login",
            data={
                "username": "testuser",
                "password": "testpass123",
                "grant_type": "password"
            },
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        assert login_response.status_code == 200
        self.token = login_response.json()["access_token"]
        self.headers = {"Authorization": f"Bearer {self.token}"}
    
    def test_startup_onboarding_to_matches_workflow(self):
        """Test complete startup onboarding and matching workflow."""
        
        # 1. Startup creates profile
        startup_data = {
            "name": "AI Insights Corp",
            "sector": "AI/ML",
            "stage": "Series A",
            "description": "We provide AI-powered customer insights for retail businesses",
            "website": "https://aiinsights.com",
            "team_size": 25,
            "monthly_revenue": 150000,
            "founded_date": "2021-01-15"
        }
        
        response = client.post(
            "/api/v1/startups",
            json=startup_data,
            headers=self.headers
        )
        assert response.status_code == 201
        startup = response.json()
        startup_id = startup["id"]
        
        # 2. System analyzes startup (mock AI)
        with patch('src.core.services.startup_service.StartupService.analyze_startup') as mock_analyze:
            mock_analysis = Mock()
            mock_analysis.sectors = ["AI/ML", "Retail Tech", "Analytics"]
            mock_analysis.value_proposition = "AI-powered customer insights for retail"
            mock_analysis.target_customers = ["Retail businesses", "E-commerce companies"]
            mock_analysis.competitive_advantages = ["Advanced AI", "Real-time insights"]
            mock_analysis.business_model.type.value = "subscription"
            mock_analysis.confidence_score = 0.9
            
            mock_analyze.return_value = mock_analysis
            
            response = client.post(
                f"/api/v1/startups/{startup_id}/analyze",
                headers=self.headers
            )
            assert response.status_code == 200
            analysis = response.json()
            assert analysis["analysis"]["sectors"] == ["AI/ML", "Retail Tech", "Analytics"]
        
        # 3. Create multiple VCs with different focuses
        vcs = []
        vc_profiles = [
            {
                "firm_name": "AI Capital Partners",
                "website": "https://aicapital.com",
                "sectors": ["AI/ML", "Deep Tech", "Enterprise Software"],
                "stages": ["Series A", "Series B"],
                "thesis": "We invest in AI companies transforming enterprise workflows",
                "check_size_min": 5000000,
                "check_size_max": 20000000
            },
            {
                "firm_name": "Retail Tech Ventures",
                "website": "https://retailtech.vc",
                "sectors": ["Retail Tech", "E-commerce", "Consumer"],
                "stages": ["Seed", "Series A"],
                "thesis": "Backing the future of retail technology",
                "check_size_min": 1000000,
                "check_size_max": 10000000
            },
            {
                "firm_name": "General Growth Fund",
                "website": "https://ggf.com",
                "sectors": ["B2B SaaS", "Enterprise", "AI/ML"],
                "stages": ["Series A", "Series B", "Series C"],
                "thesis": "Investing in high-growth B2B software companies",
                "check_size_min": 10000000,
                "check_size_max": 50000000
            }
        ]
        
        for vc_data in vc_profiles:
            response = client.post(
                "/api/v1/vcs",
                json=vc_data,
                headers=self.headers
            )
            assert response.status_code == 201
            vcs.append(response.json())
        
        # 4. Generate matches for the startup
        match_results = []
        for vc in vcs:
            response = client.post(
                "/api/v1/matches",
                json={
                    "startup_id": startup_id,
                    "vc_id": vc["id"]
                },
                headers=self.headers
            )
            if response.status_code != 201:
                print(f"Match creation failed: {response.status_code}")
                print(f"Response: {response.json()}")
            assert response.status_code == 201
            match_results.append(response.json())
        
        # 5. Verify match quality
        # AI Capital Partners should have highest score (AI/ML + Series A match)
        ai_capital_match = next(m for m in match_results if m["vc_id"] == vcs[0]["id"])
        assert ai_capital_match["score"] >= 0.7
        
        # 6. Get recommendations for startup
        response = client.get(
            f"/api/v1/startups/{startup_id}",
            headers=self.headers
        )
        assert response.status_code == 200
        
        # 7. Update match status (startup shows interest)
        response = client.put(
            f"/api/v1/matches/{ai_capital_match['id']}",
            json={"status": "interested"},
            headers=self.headers
        )
        assert response.status_code == 200
        
        # 8. Check match statistics
        response = client.get(
            "/api/v1/matches",
            headers=self.headers
        )
        assert response.status_code == 200
        all_matches = response.json()
        assert len(all_matches["items"]) >= 3
    
    def test_vc_sourcing_workflow(self):
        """Test VC actively sourcing startups workflow."""
        
        # 1. VC creates profile
        vc_data = {
            "firm_name": "Deep Tech Ventures",
            "website": "https://deeptech.vc",
            "sectors": ["AI/ML", "Robotics", "Quantum Computing"],
            "stages": ["Seed", "Series A"],
            "thesis": "Investing in deep tech startups solving hard problems",
            "check_size_min": 2000000,
            "check_size_max": 15000000,
            "portfolio_companies": ["TechCo1", "AIStartup2"]
        }
        
        response = client.post(
            "/api/v1/vcs",
            json=vc_data,
            headers=self.headers
        )
        assert response.status_code == 201
        vc = response.json()
        vc_id = vc["id"]
        
        # 2. Create multiple startups in different sectors
        startups = []
        startup_profiles = [
            {
                "name": "Quantum AI Labs",
                "sector": "AI/ML",
                "stage": "Seed",
                "description": "Quantum computing for AI optimization",
                "team_size": 12,
                "monthly_revenue": 50000
            },
            {
                "name": "RoboVision Inc",
                "sector": "Robotics",
                "stage": "Series A",
                "description": "Computer vision for industrial robotics",
                "team_size": 30,
                "monthly_revenue": 200000
            },
            {
                "name": "FinanceFlow",
                "sector": "FinTech",
                "stage": "Series A",
                "description": "AI-powered financial analytics",
                "team_size": 40,
                "monthly_revenue": 300000
            },
            {
                "name": "HealthAI Solutions",
                "sector": "HealthTech",
                "stage": "Seed",
                "description": "AI diagnostics for healthcare",
                "team_size": 15,
                "monthly_revenue": 80000
            }
        ]
        
        for startup_data in startup_profiles:
            response = client.post(
                "/api/v1/startups",
                json=startup_data,
                headers=self.headers
            )
            assert response.status_code == 201
            startups.append(response.json())
        
        # 3. VC searches for relevant startups
        # Search by sector
        response = client.get(
            "/api/v1/startups?sector=AI/ML",
            headers=self.headers
        )
        assert response.status_code == 200
        ai_startups = response.json()["items"]
        assert len(ai_startups) >= 1
        
        # Search by stage
        response = client.get(
            "/api/v1/startups?stage=Seed",
            headers=self.headers
        )
        assert response.status_code == 200
        seed_startups = response.json()["items"]
        assert len(seed_startups) >= 2
        
        # 4. VC creates matches with relevant startups
        relevant_startups = [s for s in startups if s["sector"] in ["AI/ML", "Robotics"]]
        
        for startup in relevant_startups:
            response = client.post(
                "/api/v1/matches",
                json={
                    "startup_id": startup["id"],
                    "vc_id": vc_id,
                    "initiated_by": "vc"
                },
                headers=self.headers
            )
            assert response.status_code == 201
        
        # 5. VC views their active matches
        response = client.get(
            f"/api/v1/vcs/{vc_id}",
            headers=self.headers
        )
        assert response.status_code == 200
    
    @patch('src.workers.tasks.ai_tasks.get_task_status')
    @patch('src.workers.celery_app.celery_app.send_task')
    def test_async_task_workflow(self, mock_send_task, mock_get_task_status):
        """Test workflow involving async background tasks."""
        
        # Mock Celery task responses
        mock_task = Mock()
        mock_task.id = "test-task-123"
        mock_task.state = "PENDING"
        mock_send_task.return_value = mock_task
        
        # Mock task status response
        mock_get_task_status.return_value = {
            'task_id': 'test-task-123',
            'state': 'PENDING',
            'status': 'Task is pending execution'
        }
        
        # 1. Create startup
        startup_data = {
            "name": "AsyncTech",
            "sector": "Technology",
            "stage": "Series A",
            "description": "Async processing platform"
        }
        
        response = client.post(
            "/api/v1/startups",
            json=startup_data,
            headers=self.headers
        )
        assert response.status_code == 201
        startup_id = response.json()["id"]
        
        # 2. Trigger async enrichment task
        response = client.post(
            f"/api/v1/tasks/enrich/startup/{startup_id}",
            headers=self.headers
        )
        assert response.status_code == 200
        task_info = response.json()
        assert task_info["task_id"] == "test-task-123"
        assert task_info["status"] == "queued"
        
        # 3. Check task status
        response = client.get(
            f"/api/v1/tasks/{task_info['task_id']}",
            headers=self.headers
        )
        assert response.status_code == 200
        status_info = response.json()
        assert status_info["task_id"] == "test-task-123"
        assert status_info["state"] == "PENDING"
    
    def test_data_export_workflow(self):
        """Test data export and reporting workflow."""
        
        # 1. Create test data
        # Create 5 startups
        for i in range(5):
            startup_data = {
                "name": f"ExportTest{i}",
                "sector": ["AI/ML", "FinTech", "HealthTech"][i % 3],
                "stage": ["Seed", "Series A", "Series B"][i % 3],
                "description": f"Test startup {i}",
                "team_size": 10 + i * 5,
                "monthly_revenue": 50000 * (i + 1)
            }
            response = client.post(
                "/api/v1/startups",
                json=startup_data,
                headers=self.headers
            )
            assert response.status_code == 201
        
        # Create 3 VCs
        for i in range(3):
            vc_data = {
                "firm_name": f"ExportVC{i}",
                "website": f"https://vc{i}.com",
                "sectors": [["AI/ML"], ["FinTech"], ["HealthTech"]][i],
                "stages": ["Seed", "Series A"]
            }
            response = client.post(
                "/api/v1/vcs",
                json=vc_data,
                headers=self.headers
            )
            assert response.status_code == 201
        
        # 2. Get aggregate statistics
        response = client.get(
            "/api/v1/startups",
            headers=self.headers
        )
        assert response.status_code == 200
        startup_data = response.json()
        assert startup_data["total"] >= 5
        
        # 3. Filter and export specific data
        response = client.get(
            "/api/v1/startups?sector=AI/ML",
            headers=self.headers
        )
        assert response.status_code == 200
        ai_startups = response.json()["items"]
        assert len(ai_startups) >= 1
        
        # 4. Get summary statistics
        # This endpoint would need to be implemented
        # response = client.get(
        #     "/api/v1/analytics/summary",
        #     headers=self.headers
        # )
        # assert response.status_code == 200
        # summary = response.json()
        # assert summary["total_startups"] >= 5
        # assert summary["total_vcs"] >= 3
    
    def test_error_handling_workflow(self):
        """Test comprehensive error handling across workflows."""
        
        # 1. Test validation errors
        invalid_startup = {
            "name": "",  # Empty name
            "sector": "InvalidSector",  # Invalid sector
            "stage": "Series Z",  # Invalid stage
            "team_size": -5  # Negative team size
        }
        
        response = client.post(
            "/api/v1/startups",
            json=invalid_startup,
            headers=self.headers
        )
        assert response.status_code == 422
        errors = response.json()["detail"]
        assert any(error["loc"] == ["body", "name"] for error in errors)
        
        # 2. Test not found errors
        fake_id = "00000000-0000-0000-0000-000000000000"
        response = client.get(
            f"/api/v1/startups/{fake_id}",
            headers=self.headers
        )
        assert response.status_code == 404
        
        # 3. Test unauthorized access
        response = client.post(
            "/api/v1/startups",
            json={"name": "Unauthorized", "sector": "Tech", "stage": "Seed"}
        )
        assert response.status_code == 401
        
        # 4. Test duplicate creation
        startup_data = {
            "name": "UniqueStartup123",
            "sector": "Technology",
            "stage": "Seed",
            "description": "Test"
        }
        
        # First creation should succeed
        response = client.post(
            "/api/v1/startups",
            json=startup_data,
            headers=self.headers
        )
        assert response.status_code == 201
        
        # Duplicate creation might succeed (no unique constraint on name)
        # This depends on business logic implementation
        response = client.post(
            "/api/v1/startups",
            json=startup_data,
            headers=self.headers
        )
        # Could be 201 or 409 depending on implementation
        assert response.status_code in [201, 409]
    
    def test_concurrent_operations_workflow(self):
        """Test system behavior under concurrent operations."""
        
        # Create initial data
        startup_data = {
            "name": "ConcurrentTest",
            "sector": "Technology", 
            "stage": "Series A",
            "description": "Testing concurrent updates"
        }
        
        response = client.post(
            "/api/v1/startups",
            json=startup_data,
            headers=self.headers
        )
        assert response.status_code == 201
        startup_id = response.json()["id"]
        
        # Simulate concurrent updates
        # In a real scenario, these would be actual concurrent requests
        updates = [
            {"description": "Updated description 1"},
            {"team_size": 25},
            {"monthly_revenue": 100000}
        ]
        
        for update in updates:
            response = client.put(
                f"/api/v1/startups/{startup_id}",
                json=update,
                headers=self.headers
            )
            assert response.status_code == 200
        
        # Verify final state
        response = client.get(
            f"/api/v1/startups/{startup_id}",
            headers=self.headers
        )
        assert response.status_code == 200
        final_startup = response.json()
        
        # All updates should be applied
        assert final_startup["description"] == "Updated description 1"
        assert final_startup["team_size"] == 25
        assert final_startup["monthly_revenue"] == 100000


class TestAPIIntegrationPatterns:
    """Test common API integration patterns."""
    
    def setup_method(self):
        """Set up test data."""
        Base.metadata.drop_all(bind=engine)
        init_test_db_with_migrations(engine)
    
    def test_pagination_pattern(self):
        """Test pagination across endpoints."""
        
        # Create test user and login
        user_data = {
            "email": "<EMAIL>",
            "username": "paginateuser",
            "password": "testpass123"
        }
        client.post("/api/v1/auth/register", json=user_data)
        
        login_response = client.post(
            "/api/v1/auth/login",
            data={
                "username": "paginateuser",
                "password": "testpass123",
                "grant_type": "password"
            }
        )
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        
        # Create 25 startups
        for i in range(25):
            client.post(
                "/api/v1/startups",
                json={
                    "name": f"PageStartup{i}",
                    "sector": "Technology",
                    "stage": "Seed"
                },
                headers=headers
            )
        
        # Test default pagination
        response = client.get("/api/v1/startups", headers=headers)
        assert response.status_code == 200
        data = response.json()
        assert data["page"] == 1
        assert data["size"] == 20  # Default page size
        assert data["total"] >= 25
        assert len(data["items"]) == 20
        
        # Test custom page size
        response = client.get("/api/v1/startups?page=1&size=10", headers=headers)
        assert response.status_code == 200
        data = response.json()
        assert len(data["items"]) == 10
        
        # Test second page
        response = client.get("/api/v1/startups?page=2&size=10", headers=headers)
        assert response.status_code == 200
        data = response.json()
        assert len(data["items"]) == 10
        
        # Test last page
        response = client.get("/api/v1/startups?page=3&size=10", headers=headers)
        assert response.status_code == 200
        data = response.json()
        assert len(data["items"]) >= 5  # At least 5 items on last page
    
    def test_filtering_pattern(self):
        """Test filtering patterns across endpoints."""
        
        # Setup
        user_data = {
            "email": "<EMAIL>",
            "username": "filteruser",
            "password": "testpass123"
        }
        client.post("/api/v1/auth/register", json=user_data)
        
        login_response = client.post(
            "/api/v1/auth/login",
            data={
                "username": "filteruser",
                "password": "testpass123",
                "grant_type": "password"
            }
        )
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        
        # Create diverse test data
        test_startups = [
            {"name": "AI Startup", "sector": "AI/ML", "stage": "Seed", "team_size": 5},
            {"name": "FinTech Co", "sector": "FinTech", "stage": "Series A", "team_size": 20},
            {"name": "Health AI", "sector": "HealthTech", "stage": "Series A", "team_size": 15},
            {"name": "Big AI Corp", "sector": "AI/ML", "stage": "Series B", "team_size": 50},
            {"name": "Small FinTech", "sector": "FinTech", "stage": "Seed", "team_size": 3}
        ]
        
        for startup in test_startups:
            client.post("/api/v1/startups", json=startup, headers=headers)
        
        # Test single filter
        response = client.get("/api/v1/startups?sector=AI/ML", headers=headers)
        assert response.status_code == 200
        items = response.json()["items"]
        assert all(item["sector"] == "AI/ML" for item in items)
        assert len(items) == 2
        
        # Test multiple filters
        response = client.get(
            "/api/v1/startups?sector=FinTech&stage=Seed", 
            headers=headers
        )
        assert response.status_code == 200
        items = response.json()["items"]
        assert len(items) == 1
        assert items[0]["name"] == "Small FinTech"
        
        # Test range filters
        response = client.get(
            "/api/v1/startups?min_team_size=10&max_team_size=30",
            headers=headers
        )
        assert response.status_code == 200
        items = response.json()["items"]
        assert all(10 <= item["team_size"] <= 30 for item in items)


# Run specific workflow tests
if __name__ == "__main__":
    pytest.main([__file__, "-v", "-k", "workflow"])