"""End-to-end integration tests for the complete flow."""

import pytest
from unittest.mock import Mock, AsyncMock, patch
import uuid
from datetime import datetime

from fastapi.testclient import TestClient


class TestEndToEndFlow:
    """Test complete user flows through the system."""
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_complete_startup_to_match_flow(
        self, test_client, auth_headers, mock_ai_analyzer,
        sample_startup_analysis, sample_vc_thesis_analysis
    ):
        """Test the complete flow from creating a startup to finding matches."""
        
        # Step 1: Create a startup
        startup_data = {
            "name": "AI Analytics Pro",
            "sector": "Technology",
            "stage": "Series A",
            "description": "We build AI-powered analytics for e-commerce businesses",
            "website": "https://aianalytics.com",
            "team_size": 20,
            "monthly_revenue": 100000
        }
        
        response = test_client.post(
            "/api/v1/startups",
            json=startup_data,
            headers=auth_headers
        )
        assert response.status_code == 201
        startup = response.json()
        startup_id = startup["id"]
        
        # Step 2: Create a VC
        vc_data = {
            "firm_name": "AI Ventures",
            "website": "https://aiventures.com",
            "sectors": ["AI/ML", "Analytics", "B2B SaaS"],
            "stages": ["Series A", "Series B"],
            "thesis": "We invest in AI companies transforming traditional industries",
            "check_size_min": 2000000,
            "check_size_max": 10000000
        }
        
        response = test_client.post(
            "/api/v1/vcs",
            json=vc_data,
            headers=auth_headers
        )
        assert response.status_code == 201
        vc = response.json()
        vc_id = vc["id"]
        
        # Step 3: Analyze the startup with AI
        mock_ai_analyzer.analyze_startup = AsyncMock(return_value=sample_startup_analysis)
        
        response = test_client.post(
            f"/api/v1/startups/{startup_id}/analyze",
            headers=auth_headers
        )
        assert response.status_code == 200
        analysis = response.json()
        assert analysis["analysis"]["key_sectors"] == ["AI/ML", "E-commerce", "Analytics"]
        
        # Step 4: Extract VC thesis
        mock_ai_analyzer.extract_vc_thesis = AsyncMock(return_value=sample_vc_thesis_analysis)
        
        response = test_client.post(
            f"/api/v1/vcs/{vc_id}/extract-thesis",
            json={"website_content": "Sample VC website content..."},
            headers=auth_headers
        )
        assert response.status_code == 200
        
        # Step 5: Create a match
        response = test_client.post(
            "/api/v1/matches",
            json={"startup_id": startup_id, "vc_id": vc_id},
            headers=auth_headers
        )
        assert response.status_code == 201
        match = response.json()
        assert match["score"] >= 0.7  # Should be a good match
        assert "sector alignment" in match["reasons"]
        
        # Step 6: Get match recommendations
        response = test_client.get(
            f"/api/v1/matches/recommendations/startup/{startup_id}",
            headers=auth_headers
        )
        assert response.status_code == 200
        recommendations = response.json()["recommendations"]
        assert len(recommendations) > 0
        assert recommendations[0]["vc_id"] == vc_id
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_batch_processing_flow(
        self, test_client, auth_headers, mock_ai_analyzer
    ):
        """Test batch processing of multiple startups and VCs."""
        
        # Step 1: Create multiple startups
        startups = []
        for i in range(3):
            startup_data = {
                "name": f"Startup {i}",
                "sector": ["AI/ML", "FinTech", "HealthTech"][i],
                "stage": "Series A",
                "description": f"Description for startup {i}"
            }
            response = test_client.post(
                "/api/v1/startups",
                json=startup_data,
                headers=auth_headers
            )
            assert response.status_code == 201
            startups.append(response.json())
        
        # Step 2: Create multiple VCs
        vcs = []
        for i in range(3):
            vc_data = {
                "firm_name": f"VC Fund {i}",
                "website": f"https://vc{i}.com",
                "sectors": [["AI/ML"], ["FinTech"], ["HealthTech"]][i],
                "stages": ["Series A", "Series B"]
            }
            response = test_client.post(
                "/api/v1/vcs",
                json=vc_data,
                headers=auth_headers
            )
            assert response.status_code == 201
            vcs.append(response.json())
        
        # Step 3: Bulk create matches for first startup
        response = test_client.post(
            f"/api/v1/matches/bulk/startup/{startups[0]['id']}",
            json={"vc_ids": [vc["id"] for vc in vcs]},
            headers=auth_headers
        )
        assert response.status_code == 201
        matches = response.json()["matches"]
        assert len(matches) == 3
        
        # Step 4: Get statistics
        response = test_client.get(
            "/api/v1/matches/statistics",
            headers=auth_headers
        )
        assert response.status_code == 200
        stats = response.json()
        assert stats["total_matches"] >= 3
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_search_and_filter_flow(
        self, test_client, auth_headers
    ):
        """Test searching and filtering across entities."""
        
        # Create diverse test data
        sectors = ["AI/ML", "FinTech", "HealthTech", "EdTech", "B2B SaaS"]
        stages = ["Pre-seed", "Seed", "Series A", "Series B", "Series C"]
        
        # Create startups with different characteristics
        for i in range(10):
            startup_data = {
                "name": f"TestStartup{i}",
                "sector": sectors[i % len(sectors)],
                "stage": stages[i % len(stages)],
                "description": f"Building solutions in {sectors[i % len(sectors)]}",
                "team_size": 5 + i * 2,
                "monthly_revenue": 10000 * (i + 1)
            }
            response = test_client.post(
                "/api/v1/startups",
                json=startup_data,
                headers=auth_headers
            )
            assert response.status_code == 201
        
        # Test various search scenarios
        
        # 1. Filter by sector
        response = test_client.get("/api/v1/startups?sector=AI/ML")
        assert response.status_code == 200
        results = response.json()["items"]
        assert all(s["sector"] == "AI/ML" for s in results)
        
        # 2. Filter by stage
        response = test_client.get("/api/v1/startups?stage=Series A")
        assert response.status_code == 200
        results = response.json()["items"]
        assert all(s["stage"] == "Series A" for s in results)
        
        # 3. Filter by team size range
        response = test_client.get(
            "/api/v1/startups?min_team_size=10&max_team_size=20"
        )
        assert response.status_code == 200
        results = response.json()["items"]
        assert all(10 <= s["team_size"] <= 20 for s in results)
        
        # 4. Search by query
        response = test_client.get("/api/v1/startups?query=AI")
        assert response.status_code == 200
        results = response.json()["items"]
        assert len(results) > 0
        
        # 5. Pagination
        response = test_client.get("/api/v1/startups?page=1&size=5")
        assert response.status_code == 200
        data = response.json()
        assert len(data["items"]) <= 5
        assert data["page"] == 1
        assert data["pages"] >= 2  # We created 10 startups
    
    @pytest.mark.integration
    @pytest.mark.slow
    @pytest.mark.asyncio
    async def test_performance_under_load(
        self, test_client, auth_headers
    ):
        """Test system performance with concurrent requests."""
        import asyncio
        import time
        
        async def create_startup(index):
            startup_data = {
                "name": f"LoadTest{index}",
                "sector": "Technology",
                "stage": "Seed",
                "description": f"Load test startup {index}"
            }
            return test_client.post(
                "/api/v1/startups",
                json=startup_data,
                headers=auth_headers
            )
        
        # Measure time for concurrent requests
        start_time = time.time()
        
        # Create 20 startups concurrently
        tasks = [create_startup(i) for i in range(20)]
        responses = await asyncio.gather(*[asyncio.create_task(t) for t in tasks])
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Verify all succeeded
        success_count = sum(1 for r in responses if r.status_code == 201)
        assert success_count >= 18  # Allow for some rate limiting
        
        # Should complete within reasonable time
        assert duration < 10.0  # 10 seconds for 20 requests
        
        print(f"Created {success_count} startups in {duration:.2f} seconds")
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_error_recovery_flow(
        self, test_client, auth_headers, mock_ai_analyzer
    ):
        """Test system behavior when components fail."""
        
        # Create a startup
        startup_data = {
            "name": "ErrorTest Startup",
            "sector": "Technology",
            "stage": "Seed"
        }
        response = test_client.post(
            "/api/v1/startups",
            json=startup_data,
            headers=auth_headers
        )
        assert response.status_code == 201
        startup_id = response.json()["id"]
        
        # Test AI service failure
        mock_ai_analyzer.analyze_startup = AsyncMock(
            side_effect=Exception("AI service unavailable")
        )
        
        response = test_client.post(
            f"/api/v1/startups/{startup_id}/analyze",
            headers=auth_headers
        )
        assert response.status_code == 500
        assert "AI analysis failed" in response.json()["detail"]
        
        # System should still work for other operations
        response = test_client.get(f"/api/v1/startups/{startup_id}")
        assert response.status_code == 200
        
        # Test recovery - AI service back online
        mock_ai_analyzer.analyze_startup = AsyncMock(
            return_value=Mock(
                key_sectors=["Technology"],
                confidence_score=0.8
            )
        )
        
        response = test_client.post(
            f"/api/v1/startups/{startup_id}/analyze",
            headers=auth_headers
        )
        assert response.status_code == 200