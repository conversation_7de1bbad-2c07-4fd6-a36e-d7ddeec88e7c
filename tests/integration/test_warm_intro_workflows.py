"""Integration tests for end-to-end warm intro workflows."""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta
from uuid import UUID, uuid4
from fastapi.testclient import TestClient

from src.api.main import app
from src.core.models.connection import (
    Connection,
    ConnectionId,
    ConnectionMetrics,
    ConnectionStrength,
    RelationshipType,
    ConnectionPath,
    IntroductionRequest,
    IntroductionStatus
)
from src.core.models.user import User
from src.core.models.startup import Startup
from src.core.models.vc import VC
from src.core.services.warm_intro_service import WarmIntroService
from tests.fixtures.warm_intro_fixtures import sample_user_ids, mock_users


@pytest.fixture
def integration_test_client():
    """Create test client for integration tests."""
    return TestClient(app)


@pytest.fixture
def mock_complete_system():
    """Mock the complete warm intro system for integration testing."""
    # Create mock users
    users = {
        'alice': User(
            id=UUID('550e8400-e29b-41d4-a716-************'),
            name='<PERSON>',
            email='<EMAIL>',
            role='founder'
        ),
        'bob': User(
            id=UUID('550e8400-e29b-41d4-a716-************'),
            name='Bob Smith',
            email='<EMAIL>',
            role='investor'
        ),
        'charlie': User(
            id=UUID('550e8400-e29b-41d4-a716-************'),
            name='Charlie Brown',
            email='<EMAIL>',
            role='founder'
        ),
        'diana': User(
            id=UUID('550e8400-e29b-41d4-a716-************'),
            name='Diana Prince',
            email='<EMAIL>',
            role='investor'
        )
    }
    
    # Create mock startup
    startup = Startup(
        id=UUID('660e8400-e29b-41d4-a716-************'),
        name='TechVenture AI',
        sector='AI/ML',
        stage='Series A',
        description='AI-powered analytics platform'
    )
    
    # Create mock VC
    vc = VC(
        id=UUID('770e8400-e29b-41d4-a716-************'),
        firm_name='AI Capital Partners',
        sectors=['AI/ML', 'B2B SaaS'],
        stages=['Series A', 'Series B'],
        thesis='We invest in AI-first companies'
    )
    
    # Create mock connections
    connections = [
        Connection(
            id=ConnectionId(),
            user_a_id=users['alice'].id,
            user_b_id=users['bob'].id,
            relationship_type=RelationshipType.COLLEAGUE,
            strength=ConnectionStrength.STRONG,
            metrics=ConnectionMetrics(trust_score=0.9, interaction_frequency=5)
        ),
        Connection(
            id=ConnectionId(),
            user_a_id=users['bob'].id,
            user_b_id=users['charlie'].id,
            relationship_type=RelationshipType.BUSINESS_PARTNER,
            strength=ConnectionStrength.MEDIUM,
            metrics=ConnectionMetrics(trust_score=0.7, interaction_frequency=3)
        ),
        Connection(
            id=ConnectionId(),
            user_a_id=users['charlie'].id,
            user_b_id=users['diana'].id,
            relationship_type=RelationshipType.INDUSTRY_PEER,
            strength=ConnectionStrength.WEAK,
            metrics=ConnectionMetrics(trust_score=0.4, interaction_frequency=1)
        )
    ]
    
    return {
        'users': users,
        'startup': startup,
        'vc': vc,
        'connections': connections
    }


class TestCompleteIntroductionWorkflow:
    """Test complete introduction workflow from discovery to completion."""
    
    @pytest.mark.asyncio
    async def test_full_intro_workflow_startup_to_vc(self, mock_complete_system):
        """Test complete workflow: startup founder finds path to VC and requests intro."""
        # Setup
        users = mock_complete_system['users']
        startup = mock_complete_system['startup']
        vc = mock_complete_system['vc']
        connections = mock_complete_system['connections']
        
        # Mock all repositories
        mock_connection_repo = Mock()
        mock_introduction_repo = Mock()
        mock_user_repo = Mock()
        mock_startup_repo = Mock()
        mock_vc_repo = Mock()
        
        # Configure mocks for path finding
        mock_user_repo.get.side_effect = lambda user_id: next(
            (user for user in users.values() if user.id == user_id), None
        )
        mock_user_repo.get_users_by_startup.return_value = [users['charlie']]  # Charlie works at startup
        mock_user_repo.get_users_by_vc.return_value = [users['diana']]  # Diana works at VC
        
        mock_startup_repo.get.return_value = startup
        mock_vc_repo.get.return_value = vc
        
        # Mock path finding - Alice -> Bob -> Charlie (startup)
        mock_path_to_startup = ConnectionPath(
            source_user_id=users['alice'].id,
            target_user_id=users['charlie'].id,
            path=[users['alice'].id, users['bob'].id, users['charlie'].id],
            connections=connections[:2],  # Alice-Bob, Bob-Charlie
            total_strength_score=0.7
        )
        
        mock_connection_repo.find_shortest_paths.return_value = [mock_path_to_startup]
        mock_connection_repo.get_connection.side_effect = lambda a, b: next(
            (conn for conn in connections 
             if (conn.user_a_id == min(a, b) and conn.user_b_id == max(a, b))), None
        )
        
        # Create service
        service = WarmIntroService(
            connection_repo=mock_connection_repo,
            introduction_repo=mock_introduction_repo,
            user_repo=mock_user_repo,
            startup_repo=mock_startup_repo,
            vc_repo=mock_vc_repo
        )
        
        # Step 1: Alice finds intro paths to startup
        paths = await service.find_intro_paths_for_match(
            requester_id=users['alice'].id,
            startup_id=startup.id,
            max_depth=3
        )
        
        # Verify path was found
        assert len(paths) == 1
        path = paths[0]
        assert path['target_user']['id'] == str(users['charlie'].id)
        assert path['hop_count'] == 2
        assert path['can_request_intro'] is True
        
        # Step 2: Alice requests introduction through Bob
        mock_introduction_repo.create_request = AsyncMock()
        mock_intro_request = IntroductionRequest(
            requester_id=users['alice'].id,
            target_id=users['charlie'].id,
            connector_id=users['bob'].id,
            message="I'd like to discuss potential collaboration with TechVenture AI"
        )
        mock_introduction_repo.create_request.return_value = mock_intro_request
        
        # Verify Bob can introduce Alice to Charlie
        intro_request = await service.request_introduction(
            requester_id=users['alice'].id,
            target_id=users['charlie'].id,
            connector_id=users['bob'].id,
            message="I'd like to discuss potential collaboration with TechVenture AI"
        )
        
        assert intro_request.requester_id == users['alice'].id
        assert intro_request.target_id == users['charlie'].id
        assert intro_request.connector_id == users['bob'].id
        assert intro_request.status == IntroductionStatus.PENDING
        
        # Step 3: Bob gets pending requests and accepts
        mock_introduction_repo.get_pending_requests.return_value = [mock_intro_request]
        
        pending_requests = await service.get_pending_intro_requests(users['bob'].id)
        assert len(pending_requests) == 1
        assert pending_requests[0]['requester']['name'] == users['alice'].name
        assert pending_requests[0]['target']['name'] == users['charlie'].name
        
        # Bob accepts the request
        mock_introduction_repo.get_request.return_value = mock_intro_request
        mock_introduction_repo.update_request = AsyncMock()
        
        accepted_request = await service.respond_to_intro_request(
            request_id=mock_intro_request.id,
            connector_id=users['bob'].id,
            accept=True,
            notes="Happy to make this introduction - you'll both benefit from connecting!"
        )
        
        assert accepted_request.status == IntroductionStatus.ACCEPTED
        assert "Happy to make this introduction" in accepted_request.connector_notes
    
    @pytest.mark.asyncio
    async def test_multi_hop_path_discovery_and_filtering(self, mock_complete_system):
        """Test discovery of multiple paths with different strengths and filtering."""
        users = mock_complete_system['users']
        startup = mock_complete_system['startup']
        
        # Setup mocks
        mock_connection_repo = Mock()
        mock_introduction_repo = Mock()
        mock_user_repo = Mock()
        mock_startup_repo = Mock()
        mock_vc_repo = Mock()
        
        mock_user_repo.get.side_effect = lambda user_id: next(
            (user for user in users.values() if user.id == user_id), None
        )
        mock_user_repo.get_users_by_startup.return_value = [users['charlie']]
        mock_startup_repo.get.return_value = startup
        
        # Create multiple paths with different strengths
        strong_direct_path = ConnectionPath(
            source_user_id=users['alice'].id,
            target_user_id=users['charlie'].id,
            path=[users['alice'].id, users['charlie'].id],
            connections=[Connection(
                id=ConnectionId(),
                user_a_id=users['alice'].id,
                user_b_id=users['charlie'].id,
                relationship_type=RelationshipType.COLLEAGUE,
                strength=ConnectionStrength.STRONG,
                metrics=ConnectionMetrics(trust_score=0.9)
            )],
            total_strength_score=0.9
        )
        
        weak_long_path = ConnectionPath(
            source_user_id=users['alice'].id,
            target_user_id=users['charlie'].id,
            path=[users['alice'].id, users['bob'].id, users['diana'].id, users['charlie'].id],
            connections=[],  # Simplified for test
            total_strength_score=0.2
        )
        
        # Return paths in "wrong" order (weak first)
        mock_connection_repo.find_shortest_paths.return_value = [weak_long_path, strong_direct_path]
        
        service = WarmIntroService(
            connection_repo=mock_connection_repo,
            introduction_repo=mock_introduction_repo,
            user_repo=mock_user_repo,
            startup_repo=mock_startup_repo,
            vc_repo=mock_vc_repo
        )
        
        # Find paths
        paths = await service.find_intro_paths_for_match(
            requester_id=users['alice'].id,
            startup_id=startup.id,
            max_depth=4
        )
        
        # Verify sorting by strength score (strong first)
        assert len(paths) == 2
        assert paths[0]['strength_score'] == 0.9  # Strong direct path first
        assert paths[0]['hop_count'] == 1
        assert paths[1]['strength_score'] == 0.2  # Weak long path second
        assert paths[1]['hop_count'] == 3
        
        # Verify path filtering (can request intro for paths ≤ 3 hops)
        assert paths[0]['can_request_intro'] is True   # 1 hop - can request
        assert paths[1]['can_request_intro'] is True   # 3 hops - can request
    
    @pytest.mark.asyncio
    async def test_intro_request_validation_and_error_handling(self, mock_complete_system):
        """Test validation and error handling in introduction requests."""
        users = mock_complete_system['users']
        connections = mock_complete_system['connections']
        
        # Setup mocks
        mock_connection_repo = Mock()
        mock_introduction_repo = Mock()
        mock_user_repo = Mock()
        mock_startup_repo = Mock()
        mock_vc_repo = Mock()
        
        mock_user_repo.get.side_effect = lambda user_id: next(
            (user for user in users.values() if user.id == user_id), None
        )
        
        # Mock connection checking for connector validation
        def mock_get_connection(user_a, user_b):
            sorted_ids = sorted([user_a, user_b])
            for conn in connections:
                if conn.user_a_id == sorted_ids[0] and conn.user_b_id == sorted_ids[1]:
                    return conn
            return None
        
        mock_connection_repo.get_connection.side_effect = mock_get_connection
        
        service = WarmIntroService(
            connection_repo=mock_connection_repo,
            introduction_repo=mock_introduction_repo,
            user_repo=mock_user_repo,
            startup_repo=mock_startup_repo,
            vc_repo=mock_vc_repo
        )
        
        # Test 1: Valid introduction request (Alice -> Bob -> Charlie)
        mock_introduction_repo.create_request = AsyncMock()
        mock_request = IntroductionRequest(
            requester_id=users['alice'].id,
            target_id=users['charlie'].id,
            connector_id=users['bob'].id,
            message="Valid introduction request"
        )
        mock_introduction_repo.create_request.return_value = mock_request
        
        # Should succeed
        result = await service.request_introduction(
            requester_id=users['alice'].id,
            target_id=users['charlie'].id,
            connector_id=users['bob'].id,
            message="Valid introduction request"
        )
        assert result.status == IntroductionStatus.PENDING
        
        # Test 2: Invalid connector (Diana doesn't know Alice)
        with pytest.raises(ValueError, match="Connector cannot introduce these users"):
            await service.request_introduction(
                requester_id=users['alice'].id,
                target_id=users['charlie'].id,
                connector_id=users['diana'].id,  # Diana doesn't know Alice
                message="Invalid connector request"
            )
        
        # Test 3: Non-existent user
        mock_user_repo.get.side_effect = lambda user_id: (
            None if user_id == users['alice'].id 
            else next((user for user in users.values() if user.id == user_id), None)
        )
        
        with pytest.raises(ValueError, match="One or more users not found"):
            await service.request_introduction(
                requester_id=users['alice'].id,  # Will return None
                target_id=users['charlie'].id,
                connector_id=users['bob'].id,
                message="Request with non-existent user"
            )
    
    @pytest.mark.asyncio
    async def test_analytics_and_network_insights(self, mock_complete_system):
        """Test connection analytics and network insights generation."""
        users = mock_complete_system['users']
        connections = mock_complete_system['connections']
        
        # Setup mocks
        mock_connection_repo = Mock()
        mock_introduction_repo = Mock()
        mock_user_repo = Mock()
        mock_startup_repo = Mock()
        mock_vc_repo = Mock()
        
        # Mock Alice's connections
        alice_connections = [conn for conn in connections if conn.involves_user(users['alice'].id)]
        mock_connection_repo.get_user_connections.side_effect = [
            alice_connections,  # Alice's direct connections
            connections,        # Bob's connections (for network reach)
            []                  # Other users' connections
        ]
        
        # Mock introduction requests
        mock_intro_requests = [
            IntroductionRequest(
                requester_id=users['alice'].id,
                target_id=users['charlie'].id,
                connector_id=users['bob'].id,
                message="Test request 1",
                status=IntroductionStatus.COMPLETED
            ),
            IntroductionRequest(
                requester_id=users['bob'].id,
                target_id=users['alice'].id,
                connector_id=users['diana'].id,
                message="Test request 2",
                status=IntroductionStatus.PENDING
            ),
            IntroductionRequest(
                requester_id=users['charlie'].id,
                target_id=users['diana'].id,
                connector_id=users['alice'].id,
                message="Test request 3",
                status=IntroductionStatus.COMPLETED
            )
        ]
        mock_introduction_repo.get_user_requests.return_value = mock_intro_requests
        
        mock_user_repo.get.side_effect = lambda user_id: next(
            (user for user in users.values() if user.id == user_id), None
        )
        
        service = WarmIntroService(
            connection_repo=mock_connection_repo,
            introduction_repo=mock_introduction_repo,
            user_repo=mock_user_repo,
            startup_repo=mock_startup_repo,
            vc_repo=mock_vc_repo
        )
        
        # Get analytics for Alice
        analytics = await service.get_connection_analytics(users['alice'].id)
        
        # Verify analytics structure and content
        assert 'total_connections' in analytics
        assert 'strength_distribution' in analytics
        assert 'relationship_distribution' in analytics
        assert 'introduction_stats' in analytics
        assert 'network_reach' in analytics
        assert 'key_connectors' in analytics
        
        # Verify introduction stats
        intro_stats = analytics['introduction_stats']
        assert intro_stats['requests_sent'] == 1        # Alice sent 1 request
        assert intro_stats['requests_received'] == 1    # Alice received 1 request
        assert intro_stats['introductions_made'] == 1   # Alice made 1 introduction
        assert intro_stats['successful_intros'] == 1    # Alice's intro was successful
        
        # Verify strength distribution
        strength_dist = analytics['strength_distribution']
        assert strength_dist['strong'] == 1  # Alice has 1 strong connection (to Bob)
        
        # Verify relationship distribution
        rel_dist = analytics['relationship_distribution']
        assert rel_dist['colleague'] == 1  # Alice has 1 colleague connection


class TestDiscoveryIntegration:
    """Test integration with discovery system for warm intros."""
    
    @pytest.mark.asyncio
    async def test_discovery_triggers_warm_intro_search(self, mock_complete_system):
        """Test that discovery system can trigger warm intro path finding."""
        users = mock_complete_system['users']
        startup = mock_complete_system['startup']
        vc = mock_complete_system['vc']
        
        # Setup mocks
        mock_connection_repo = Mock()
        mock_introduction_repo = Mock()
        mock_user_repo = Mock()
        mock_startup_repo = Mock()
        mock_vc_repo = Mock()
        
        mock_user_repo.get.side_effect = lambda user_id: next(
            (user for user in users.values() if user.id == user_id), None
        )
        mock_user_repo.get_users_by_startup.return_value = [users['charlie']]
        mock_user_repo.get_users_by_vc.return_value = [users['diana']]
        
        mock_startup_repo.get.return_value = startup
        mock_vc_repo.get.return_value = vc
        
        # Mock path finding for both startup and VC
        startup_path = ConnectionPath(
            source_user_id=users['alice'].id,
            target_user_id=users['charlie'].id,
            path=[users['alice'].id, users['bob'].id, users['charlie'].id],
            connections=[],
            total_strength_score=0.7
        )
        
        vc_path = ConnectionPath(
            source_user_id=users['alice'].id,
            target_user_id=users['diana'].id,
            path=[users['alice'].id, users['bob'].id, users['charlie'].id, users['diana'].id],
            connections=[],
            total_strength_score=0.3
        )
        
        mock_connection_repo.find_shortest_paths.side_effect = [
            [startup_path],  # For startup
            [vc_path]        # For VC
        ]
        
        service = WarmIntroService(
            connection_repo=mock_connection_repo,
            introduction_repo=mock_introduction_repo,
            user_repo=mock_user_repo,
            startup_repo=mock_startup_repo,
            vc_repo=mock_vc_repo
        )
        
        # Simulate discovery finding a match and looking for warm intro paths
        startup_paths = await service.find_intro_paths_for_match(
            requester_id=users['alice'].id,
            startup_id=startup.id,
            max_depth=3
        )
        
        vc_paths = await service.find_intro_paths_for_match(
            requester_id=users['alice'].id,
            vc_id=vc.id,
            max_depth=3
        )
        
        # Verify both path searches worked
        assert len(startup_paths) == 1
        assert len(vc_paths) == 1
        
        # Verify path data includes all necessary info for discovery UI
        startup_path_data = startup_paths[0]
        assert 'target_user' in startup_path_data
        assert 'path' in startup_path_data
        assert 'strength_score' in startup_path_data
        assert 'hop_count' in startup_path_data
        assert 'can_request_intro' in startup_path_data
        
        # Verify target user info is sufficient for discovery display
        target_info = startup_path_data['target_user']
        assert 'id' in target_info
        assert 'name' in target_info
        assert 'email' in target_info
        
        # Verify path info is sufficient for intro flow
        path_info = startup_path_data['path']
        assert len(path_info) > 0
        assert all('id' in user_info for user_info in path_info)
        assert all('name' in user_info for user_info in path_info)
        assert all('position' in user_info for user_info in path_info)
    
    @pytest.mark.asyncio
    async def test_match_quality_enhanced_by_intro_availability(self, mock_complete_system):
        """Test that warm intro availability can enhance match quality scoring."""
        users = mock_complete_system['users']
        startup = mock_complete_system['startup']
        vc = mock_complete_system['vc']
        
        # Setup mocks
        mock_connection_repo = Mock()
        mock_introduction_repo = Mock()
        mock_user_repo = Mock()
        mock_startup_repo = Mock()
        mock_vc_repo = Mock()
        
        mock_user_repo.get.side_effect = lambda user_id: next(
            (user for user in users.values() if user.id == user_id), None
        )
        mock_user_repo.get_users_by_startup.return_value = [users['charlie']]
        mock_user_repo.get_users_by_vc.return_value = [users['diana']]
        
        mock_startup_repo.get.return_value = startup
        mock_vc_repo.get.return_value = vc
        
        service = WarmIntroService(
            connection_repo=mock_connection_repo,
            introduction_repo=mock_introduction_repo,
            user_repo=mock_user_repo,
            startup_repo=mock_startup_repo,
            vc_repo=mock_vc_repo
        )
        
        # Scenario 1: Strong, short path available
        strong_path = ConnectionPath(
            source_user_id=users['alice'].id,
            target_user_id=users['charlie'].id,
            path=[users['alice'].id, users['charlie'].id],
            connections=[],
            total_strength_score=0.9
        )
        mock_connection_repo.find_shortest_paths.return_value = [strong_path]
        
        paths_with_strong_intro = await service.find_intro_paths_for_match(
            requester_id=users['alice'].id,
            startup_id=startup.id
        )
        
        # Scenario 2: No path available
        mock_connection_repo.find_shortest_paths.return_value = []
        
        paths_without_intro = await service.find_intro_paths_for_match(
            requester_id=users['alice'].id,
            startup_id=startup.id
        )
        
        # In a real discovery system, this information would be used to:
        # - Boost match scores for matches with strong intro paths
        # - Lower scores for matches without intro paths
        # - Provide "intro available" indicators in UI
        
        assert len(paths_with_strong_intro) == 1
        assert paths_with_strong_intro[0]['strength_score'] == 0.9
        assert paths_with_strong_intro[0]['can_request_intro'] is True
        
        assert len(paths_without_intro) == 0
        
        # This data would be used by discovery system to enhance match ranking


class TestErrorRecoveryAndEdgeCases:
    """Test error recovery and edge cases in workflows."""
    
    @pytest.mark.asyncio
    async def test_partial_failure_recovery(self, mock_complete_system):
        """Test system behavior when parts of the workflow fail."""
        users = mock_complete_system['users']
        
        # Setup mocks with partial failures
        mock_connection_repo = Mock()
        mock_introduction_repo = Mock()
        mock_user_repo = Mock()
        mock_startup_repo = Mock()
        mock_vc_repo = Mock()
        
        # Mock user lookup that sometimes fails
        def flaky_user_get(user_id):
            if user_id == users['charlie'].id:
                return None  # Simulate user not found
            return next((user for user in users.values() if user.id == user_id), None)
        
        mock_user_repo.get.side_effect = flaky_user_get
        mock_user_repo.get_users_by_startup.return_value = [users['charlie']]
        mock_startup_repo.get.return_value = mock_complete_system['startup']
        
        service = WarmIntroService(
            connection_repo=mock_connection_repo,
            introduction_repo=mock_introduction_repo,
            user_repo=mock_user_repo,
            startup_repo=mock_startup_repo,
            vc_repo=mock_vc_repo
        )
        
        # This should handle the missing user gracefully
        paths = await service.find_intro_paths_for_match(
            requester_id=users['alice'].id,
            startup_id=mock_complete_system['startup'].id
        )
        
        # Should return empty list rather than crashing
        assert paths == []
    
    @pytest.mark.asyncio
    async def test_concurrent_intro_requests(self, mock_complete_system):
        """Test handling of concurrent introduction requests."""
        users = mock_complete_system['users']
        
        # Setup mocks
        mock_connection_repo = Mock()
        mock_introduction_repo = Mock()
        mock_user_repo = Mock()
        mock_startup_repo = Mock()
        mock_vc_repo = Mock()
        
        mock_user_repo.get.side_effect = lambda user_id: next(
            (user for user in users.values() if user.id == user_id), None
        )
        
        # Mock connection exists between all users
        mock_connection_repo.get_connection.return_value = Mock()
        
        # Mock first request succeeds, second fails due to duplicate
        request1 = IntroductionRequest(
            requester_id=users['alice'].id,
            target_id=users['charlie'].id,
            connector_id=users['bob'].id,
            message="First request"
        )
        
        mock_introduction_repo.create_request.side_effect = [
            request1,  # First request succeeds
            ValueError("Pending introduction request already exists")  # Second fails
        ]
        
        service = WarmIntroService(
            connection_repo=mock_connection_repo,
            introduction_repo=mock_introduction_repo,
            user_repo=mock_user_repo,
            startup_repo=mock_startup_repo,
            vc_repo=mock_vc_repo
        )
        
        # First request should succeed
        result1 = await service.request_introduction(
            requester_id=users['alice'].id,
            target_id=users['charlie'].id,
            connector_id=users['bob'].id,
            message="First request"
        )
        assert result1.status == IntroductionStatus.PENDING
        
        # Second identical request should fail
        with pytest.raises(ValueError, match="Pending introduction request already exists"):
            await service.request_introduction(
                requester_id=users['alice'].id,
                target_id=users['charlie'].id,
                connector_id=users['bob'].id,
                message="Duplicate request"
            )