"""Tests for YC scraper."""

import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime
import json

from src.scrapers.yc_scraper import YCCompanyScraper, discover_new_yc_companies


@pytest.fixture
def yc_scraper():
    """Create YC scraper instance."""
    return YCCompanyScraper()


@pytest.fixture
def mock_yc_api_response():
    """Mock YC API response data."""
    return [
        {
            "id": 1,
            "name": "TestCo",
            "slug": "testco",
            "small_logo_thumb_url": "https://example.com/logo.png",
            "website": "https://testco.com",
            "all_locations": "San Francisco, CA, USA",
            "long_description": "Test company description",
            "one_liner": "Test company one liner",
            "team_size": 10,
            "industry": "B2B",
            "subindustry": "B2B -> SaaS",
            "launched_at": **********,  # 2022-01-01
            "tags": ["saas", "b2b", "enterprise"],
            "top_company": False,
            "isHiring": True,
            "nonprofit": False,
            "batch": "W22",
            "status": "Active",
            "stage": "Early",
            "url": "https://www.ycombinator.com/companies/testco",
            "api": "https://yc-oss.github.io/api/batches/w22/testco.json"
        },
        {
            "id": 2,
            "name": "AI Corp",
            "slug": "ai-corp",
            "website": "https://aicorp.ai",
            "all_locations": "Remote",
            "long_description": "AI company building LLMs",
            "team_size": 25,
            "industry": "B2B",
            "subindustry": "B2B -> Engineering, Product and Design",
            "tags": ["artificial-intelligence", "machine-learning"],
            "batch": "S23",
            "status": "Active",
            "stage": "Growth",
            "url": "https://www.ycombinator.com/companies/ai-corp"
        }
    ]


@pytest.mark.asyncio
async def test_scrape_companies_success(yc_scraper, mock_yc_api_response):
    """Test successful YC companies scraping."""
    with patch('aiohttp.ClientSession') as mock_session_class:
        # Mock the session and responses
        mock_session = AsyncMock()
        mock_session_class.return_value.__aenter__.return_value = mock_session
        
        # Mock meta response
        mock_meta_response = AsyncMock()
        mock_meta_response.status = 200
        mock_meta_response.json = AsyncMock(return_value={"last_updated": "2024-01-01"})
        
        # Mock companies response
        mock_companies_response = AsyncMock()
        mock_companies_response.status = 200
        mock_companies_response.json = AsyncMock(return_value=mock_yc_api_response)
        
        # Set up the mock to return different responses for different URLs
        mock_session.get.side_effect = [mock_meta_response, mock_companies_response]
        
        # Run the scraper
        companies = await yc_scraper.scrape_companies(limit=2)
        
        # Verify results
        assert len(companies) == 2
        assert companies[0]["name"] == "TestCo"
        assert companies[0]["sector"] == "B2B SaaS"
        assert companies[0]["stage"] == "Seed"
        assert companies[0]["source"] == "yc_api"
        assert companies[0]["yc_id"] == 1
        
        assert companies[1]["name"] == "AI Corp"
        assert companies[1]["sector"] == "AI/ML"
        assert companies[1]["tags"] == ["artificial-intelligence", "machine-learning"]


@pytest.mark.asyncio
async def test_scrape_companies_api_failure(yc_scraper):
    """Test scraper falls back to sample data on API failure."""
    with patch('aiohttp.ClientSession') as mock_session_class:
        # Mock the session to raise an error
        mock_session = AsyncMock()
        mock_session_class.return_value.__aenter__.return_value = mock_session
        mock_session.get.side_effect = Exception("API Error")
        
        # Run the scraper - should fall back to sample data
        companies = await yc_scraper.scrape_companies(limit=2)
        
        # Verify fallback data is returned
        assert len(companies) == 2
        assert companies[0]["source"] == "yc_fallback"
        assert all(c["name"] in ["Perplexity AI", "Brex", "Retool"] for c in companies)


def test_transform_company_data(yc_scraper):
    """Test company data transformation."""
    api_data = {
        "id": 123,
        "name": "Test Startup",
        "long_description": "A test startup description",
        "website": "https://test.com",
        "batch": "W24",
        "industry": "B2B",
        "subindustry": "B2B -> SaaS",
        "tags": ["saas", "enterprise"],
        "team_size": 15,
        "launched_at": 1672531200,  # 2023-01-01
        "isHiring": True,
        "status": "Active",
        "stage": "Early",
        "url": "https://www.ycombinator.com/companies/test"
    }
    
    transformed = yc_scraper._transform_company_data(api_data)
    
    assert transformed["name"] == "Test Startup"
    assert transformed["sector"] == "B2B SaaS"
    assert transformed["stage"] == "Seed"
    assert transformed["team_size"] == 15
    assert transformed["founded"] == "2023"
    assert transformed["yc_id"] == 123
    assert transformed["is_hiring"] is True


def test_determine_stage(yc_scraper):
    """Test stage determination logic."""
    # Test explicit stage
    assert yc_scraper._determine_stage({"stage": "Early"}) == "Seed"
    assert yc_scraper._determine_stage({"stage": "Growth"}) == "Series A"
    
    # Test status-based
    assert yc_scraper._determine_stage({"status": "Acquired"}) == "Acquired"
    assert yc_scraper._determine_stage({"status": "Public"}) == "Public"
    
    # Test batch-based fallback
    assert yc_scraper._determine_stage({"batch": "W24"}) == "Pre-seed"
    assert yc_scraper._determine_stage({"batch": "W22"}) == "Series A"


def test_map_to_sector(yc_scraper):
    """Test sector mapping logic."""
    # Test AI/ML detection
    assert yc_scraper._map_to_sector("", "", ["artificial-intelligence"]) == "AI/ML"
    assert yc_scraper._map_to_sector("B2B", "AI", []) == "AI/ML"
    
    # Test B2B SaaS detection
    assert yc_scraper._map_to_sector("B2B", "SaaS", []) == "B2B SaaS"
    assert yc_scraper._map_to_sector("", "", ["saas", "software"]) == "B2B SaaS"
    
    # Test Fintech detection
    assert yc_scraper._map_to_sector("Financial Services", "", []) == "Fintech"
    assert yc_scraper._map_to_sector("", "", ["payments", "banking"]) == "Fintech"
    
    # Test Healthcare detection
    assert yc_scraper._map_to_sector("Healthcare", "", []) == "Healthcare"
    assert yc_scraper._map_to_sector("", "", ["medical", "biotech"]) == "Healthcare"
    
    # Test fallback
    assert yc_scraper._map_to_sector("Unknown Industry", "", []) == "Unknown Industry"


def test_extract_founded_year(yc_scraper):
    """Test founded year extraction."""
    # Test from timestamp
    assert yc_scraper._extract_founded_year({"launched_at": **********}) == "2022"
    
    # Test from batch
    assert yc_scraper._extract_founded_year({"batch": "Winter 2023"}) == "2023"
    assert yc_scraper._extract_founded_year({"batch": "S22"}) == "2022"
    
    # Test fallback to current year
    current_year = str(datetime.now().year)
    assert yc_scraper._extract_founded_year({}) == current_year


@pytest.mark.asyncio
async def test_discover_new_yc_companies():
    """Test discover new YC companies function."""
    mock_companies = [
        {"name": "Old Company", "batch": "W20", "status": "Active"},
        {"name": "Recent Company 1", "batch": "W23", "status": "Active"},
        {"name": "Recent Company 2", "batch": "S24", "status": "Active"},
        {"name": "Acquired Company", "batch": "W23", "status": "Acquired"},
    ]
    
    with patch.object(YCCompanyScraper, 'scrape_companies', return_value=mock_companies):
        recent = await discover_new_yc_companies()
        
        # Should only return recent, active companies
        assert len(recent) == 2
        assert all(c["status"] == "Active" for c in recent)
        assert all(any(batch in c["batch"] for batch in ["W23", "S24"]) for c in recent)