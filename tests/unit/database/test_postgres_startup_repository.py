"""Test PostgreSQL implementation of StartupRepository."""

import pytest
import uuid
from datetime import datetime
from unittest.mock import Mock, MagicMock, AsyncMock
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession

from src.core.models.startup import Startup as StartupDomain
from src.database.models.startup import Startup as StartupDB
from src.database.repositories.startup_repository import (
    PostgresStartupRepository,
    AsyncPostgresStartupRepository
)


class TestPostgresStartupRepository:
    """Test PostgreSQL startup repository."""
    
    @pytest.fixture
    def mock_session(self):
        """Create mock database session."""
        session = Mock(spec=Session)
        return session
    
    @pytest.fixture
    def repository(self, mock_session):
        """Create repository instance."""
        return PostgresStartupRepository(mock_session)
    
    @pytest.fixture
    def sample_startup(self):
        """Create sample startup domain model."""
        startup = StartupDomain(
            name="Test Startup",
            sector="AI/ML",
            stage="Series A",
            description="A test startup",
            website="https://test.com",
            team_size=10,
            monthly_revenue=50000.0
        )
        startup.id = uuid.uuid4()
        return startup
    
    @pytest.fixture
    def sample_db_startup(self, sample_startup):
        """Create sample database startup."""
        return StartupDB(
            id=sample_startup.id,
            name=sample_startup.name,
            sector=sample_startup.sector,
            stage=sample_startup.stage,
            description=sample_startup.description,
            website=sample_startup.website,
            team_size=sample_startup.team_size,
            monthly_revenue=sample_startup.monthly_revenue,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
    
    async def test_create_startup(self, repository, mock_session, sample_startup, sample_db_startup):
        """Test creating a new startup."""
        # Mock the database operations
        mock_session.add = Mock()
        mock_session.commit = Mock()
        mock_session.refresh = Mock()
        
        # Configure refresh to populate the created_at/updated_at
        def refresh_side_effect(obj):
            obj.created_at = sample_db_startup.created_at
            obj.updated_at = sample_db_startup.updated_at
        
        mock_session.refresh.side_effect = refresh_side_effect
        
        # Create the startup
        result = await repository.create(sample_startup)
        
        # Verify database operations
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()
        
        # Verify result
        assert result.id == sample_startup.id
        assert result.name == sample_startup.name
        assert result.sector == sample_startup.sector
    
    async def test_get_startup(self, repository, mock_session, sample_startup, sample_db_startup):
        """Test getting a startup by ID."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = sample_db_startup
        
        # Get the startup
        result = await repository.get(sample_startup.id)
        
        # Verify query
        mock_session.query.assert_called_once_with(StartupDB)
        mock_query.filter_by.assert_called_once_with(id=sample_startup.id)
        
        # Verify result
        assert result is not None
        assert result.id == sample_startup.id
        assert result.name == sample_startup.name
    
    async def test_get_startup_not_found(self, repository, mock_session):
        """Test getting a non-existent startup."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = None
        
        # Get the startup
        result = await repository.get(uuid.uuid4())
        
        # Verify result
        assert result is None
    
    async def test_list_startups(self, repository, mock_session, sample_db_startup):
        """Test listing startups."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = [sample_db_startup]
        
        # List startups
        result = await repository.list(sector="AI/ML", stage="Series A")
        
        # Verify query
        mock_session.query.assert_called_once_with(StartupDB)
        assert mock_query.filter.call_count == 2  # For sector and stage
        
        # Verify result
        assert len(result) == 1
        assert result[0].name == sample_db_startup.name
    
    async def test_update_startup(self, repository, mock_session, sample_startup, sample_db_startup):
        """Test updating a startup."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = sample_db_startup
        mock_session.commit = Mock()
        mock_session.refresh = Mock()
        
        # Update the startup
        sample_startup.name = "Updated Name"
        result = await repository.update(sample_startup)
        
        # Verify database operations
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()
        
        # Verify the update
        assert sample_db_startup.name == "Updated Name"
    
    async def test_update_startup_not_found(self, repository, mock_session, sample_startup):
        """Test updating a non-existent startup."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = None
        
        # Try to update
        with pytest.raises(ValueError, match="not found"):
            await repository.update(sample_startup)
    
    async def test_delete_startup(self, repository, mock_session, sample_db_startup):
        """Test deleting a startup."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = sample_db_startup
        mock_session.delete = Mock()
        mock_session.commit = Mock()
        
        # Delete the startup
        result = await repository.delete(sample_db_startup.id)
        
        # Verify database operations
        mock_session.delete.assert_called_once_with(sample_db_startup)
        mock_session.commit.assert_called_once()
        
        # Verify result
        assert result is True
    
    async def test_delete_startup_not_found(self, repository, mock_session):
        """Test deleting a non-existent startup."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = None
        
        # Delete the startup
        result = await repository.delete(uuid.uuid4())
        
        # Verify result
        assert result is False
    
    async def test_search_startups(self, repository, mock_session, sample_db_startup):
        """Test searching startups."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = [sample_db_startup]
        
        # Search startups
        result = await repository.search("Test")
        
        # Verify query
        mock_session.query.assert_called_once_with(StartupDB)
        mock_query.filter.assert_called_once()
        
        # Verify result
        assert len(result) == 1
        assert result[0].name == sample_db_startup.name


class TestAsyncPostgresStartupRepository:
    """Test async PostgreSQL startup repository."""
    
    @pytest.fixture
    def mock_session(self):
        """Create mock async database session."""
        session = AsyncMock(spec=AsyncSession)
        return session
    
    @pytest.fixture
    def repository(self, mock_session):
        """Create repository instance."""
        return AsyncPostgresStartupRepository(mock_session)
    
    @pytest.fixture
    def sample_startup(self):
        """Create sample startup domain model."""
        startup = StartupDomain(
            name="Async Test Startup",
            sector="FinTech",
            stage="Seed",
            description="An async test startup",
            website="https://async-test.com",
            team_size=5,
            monthly_revenue=10000.0
        )
        startup.id = uuid.uuid4()
        return startup
    
    @pytest.fixture
    def sample_db_startup(self, sample_startup):
        """Create sample database startup."""
        return StartupDB(
            id=sample_startup.id,
            name=sample_startup.name,
            sector=sample_startup.sector,
            stage=sample_startup.stage,
            description=sample_startup.description,
            website=sample_startup.website,
            team_size=sample_startup.team_size,
            monthly_revenue=sample_startup.monthly_revenue,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
    
    async def test_async_create_startup(self, repository, mock_session, sample_startup):
        """Test creating a new startup asynchronously."""
        # Mock the database operations
        mock_session.add = Mock()
        mock_session.commit = AsyncMock()
        mock_session.refresh = AsyncMock()
        
        # Create the startup
        result = await repository.create(sample_startup)
        
        # Verify database operations
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()
        
        # Verify result
        assert result.id == sample_startup.id
        assert result.name == sample_startup.name
    
    async def test_async_get_startup(self, repository, mock_session, sample_startup, sample_db_startup):
        """Test getting a startup by ID asynchronously."""
        # Mock the execute result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_db_startup
        mock_session.execute = AsyncMock(return_value=mock_result)
        
        # Get the startup
        result = await repository.get(sample_startup.id)
        
        # Verify query
        mock_session.execute.assert_called_once()
        
        # Verify result
        assert result is not None
        assert result.id == sample_startup.id
        assert result.name == sample_startup.name
    
    async def test_async_list_startups(self, repository, mock_session, sample_db_startup):
        """Test listing startups asynchronously."""
        # Mock the execute result
        mock_result = Mock()
        mock_scalars = Mock()
        mock_scalars.all.return_value = [sample_db_startup]
        mock_result.scalars.return_value = mock_scalars
        mock_session.execute = AsyncMock(return_value=mock_result)
        
        # List startups
        result = await repository.list(sector="FinTech")
        
        # Verify result
        assert len(result) == 1
        assert result[0].name == sample_db_startup.name
    
    async def test_async_update_startup(self, repository, mock_session, sample_startup, sample_db_startup):
        """Test updating a startup asynchronously."""
        # Mock the execute result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_db_startup
        mock_session.execute = AsyncMock(return_value=mock_result)
        mock_session.commit = AsyncMock()
        mock_session.refresh = AsyncMock()
        
        # Update the startup
        sample_startup.name = "Updated Async Name"
        result = await repository.update(sample_startup)
        
        # Verify database operations
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()
        
        # Verify the update
        assert sample_db_startup.name == "Updated Async Name"
    
    async def test_async_delete_startup(self, repository, mock_session, sample_db_startup):
        """Test deleting a startup asynchronously."""
        # Mock the execute result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_db_startup
        mock_session.execute = AsyncMock(return_value=mock_result)
        mock_session.delete = AsyncMock()
        mock_session.commit = AsyncMock()
        
        # Delete the startup
        result = await repository.delete(sample_db_startup.id)
        
        # Verify database operations
        mock_session.delete.assert_called_once()
        mock_session.commit.assert_called_once()
        
        # Verify result
        assert result is True
    
    async def test_async_search_startups(self, repository, mock_session, sample_db_startup):
        """Test searching startups asynchronously."""
        # Mock the execute result
        mock_result = Mock()
        mock_scalars = Mock()
        mock_scalars.all.return_value = [sample_db_startup]
        mock_result.scalars.return_value = mock_scalars
        mock_session.execute = AsyncMock(return_value=mock_result)
        
        # Search startups
        result = await repository.search("Async")
        
        # Verify result
        assert len(result) == 1
        assert result[0].name == sample_db_startup.name