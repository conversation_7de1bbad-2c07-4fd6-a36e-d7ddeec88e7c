"""Comprehensive tests for PostgreSQL Startup Repository implementations."""

import pytest
from unittest.mock import MagicMock, patch, AsyncMock, Mock
from uuid import uuid4, UUID
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import Integ<PERSON><PERSON>rror, DatabaseError

from src.database.repositories.startup_repository import (
    PostgresStartupRepository,
    AsyncPostgresStartupRepository
)
from src.database.models.startup import Startup as StartupDB
from src.core.models.startup import Startup as StartupDomain


class TestPostgresStartupRepository:
    """Test PostgresStartupRepository (sync implementation)."""
    
    @pytest.fixture
    def mock_session(self):
        """Create a mock database session."""
        return MagicMock(spec=Session)
    
    @pytest.fixture
    def repository(self, mock_session):
        """Create repository instance with mock session."""
        return PostgresStartupRepository(mock_session)
    
    @pytest.fixture
    def sample_startup_domain(self):
        """Create a sample startup domain model."""
        startup = StartupDomain(
            name="Test Startup",
            sector="Technology",
            stage="Series A",
            description="A test startup",
            website="https://test.com",
            team_size=10,
            monthly_revenue=50000.0
        )
        startup.id = uuid4()
        return startup
    
    @pytest.fixture
    def sample_startup_db(self, sample_startup_domain):
        """Create a sample startup DB model."""
        return StartupDB(
            id=sample_startup_domain.id,
            name=sample_startup_domain.name,
            sector=sample_startup_domain.sector,
            stage=sample_startup_domain.stage,
            description=sample_startup_domain.description,
            website=sample_startup_domain.website,
            team_size=sample_startup_domain.team_size,
            monthly_revenue=sample_startup_domain.monthly_revenue,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
    
    # CREATE Tests
    def test_create_startup_success(self, repository, mock_session, sample_startup_domain):
        """Test successful startup creation."""
        # Arrange
        mock_session.add.return_value = None
        mock_session.commit.return_value = None
        mock_session.refresh.return_value = None
        
        # Act
        result = repository.create(sample_startup_domain)
        
        # Assert
        assert result.id == sample_startup_domain.id
        assert result.name == sample_startup_domain.name
        assert result.sector == sample_startup_domain.sector
        assert result.stage == sample_startup_domain.stage
        assert result.description == sample_startup_domain.description
        assert result.website == sample_startup_domain.website
        assert result.team_size == sample_startup_domain.team_size
        assert result.monthly_revenue == sample_startup_domain.monthly_revenue
        
        # Verify database operations
        mock_session.add.assert_called_once()
        added_startup = mock_session.add.call_args[0][0]
        assert isinstance(added_startup, StartupDB)
        assert added_startup.name == sample_startup_domain.name
        
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()
    
    def test_create_startup_database_error(self, repository, mock_session, sample_startup_domain):
        """Test handling database errors during creation."""
        # Arrange
        mock_session.commit.side_effect = DatabaseError("Connection lost", None, None)
        
        # Act & Assert
        with pytest.raises(DatabaseError):
            repository.create(sample_startup_domain)
    
    def test_create_startup_with_none_description(self, repository, mock_session):
        """Test creating startup with None description."""
        # Arrange
        startup = StartupDomain(
            name="No Description",
            sector="Tech",
            stage="Seed",
            description=None,
            website=None
        )
        startup.id = uuid4()
        
        mock_session.add.return_value = None
        mock_session.commit.return_value = None
        mock_session.refresh.return_value = None
        
        # Act
        result = repository.create(startup)
        
        # Assert
        added_startup = mock_session.add.call_args[0][0]
        assert added_startup.description is None
        assert added_startup.website is None
    
    # GET Tests
    def test_get_startup_found(self, repository, mock_session, sample_startup_db):
        """Test getting an existing startup."""
        # Arrange
        mock_query = MagicMock()
        mock_query.filter_by.return_value.first.return_value = sample_startup_db
        mock_session.query.return_value = mock_query
        
        # Act
        result = repository.get(sample_startup_db.id)
        
        # Assert
        assert result is not None
        assert result.id == sample_startup_db.id
        assert result.name == sample_startup_db.name
        assert isinstance(result, StartupDomain)
        
        mock_session.query.assert_called_once_with(StartupDB)
        mock_query.filter_by.assert_called_once_with(id=sample_startup_db.id)
    
    def test_get_startup_not_found(self, repository, mock_session):
        """Test getting a non-existent startup."""
        # Arrange
        startup_id = uuid4()
        mock_query = MagicMock()
        mock_query.filter_by.return_value.first.return_value = None
        mock_session.query.return_value = mock_query
        
        # Act
        result = repository.get(startup_id)
        
        # Assert
        assert result is None
        mock_query.filter_by.assert_called_once_with(id=startup_id)
    
    # LIST Tests
    def test_list_all_startups(self, repository, mock_session):
        """Test listing all startups without filters."""
        # Arrange
        db_startups = [
            StartupDB(id=uuid4(), name=f"Startup {i}", sector="Tech", stage="Seed")
            for i in range(3)
        ]
        mock_query = MagicMock()
        mock_query.offset.return_value.limit.return_value.all.return_value = db_startups
        mock_session.query.return_value = mock_query
        
        # Act
        result = repository.list()
        
        # Assert
        assert len(result) == 3
        assert all(isinstance(s, StartupDomain) for s in result)
        mock_session.query.assert_called_once_with(StartupDB)
        mock_query.offset.assert_called_once_with(0)
        mock_query.offset.return_value.limit.assert_called_once_with(100)
    
    def test_list_startups_with_sector_filter(self, repository, mock_session):
        """Test listing startups filtered by sector."""
        # Arrange
        tech_startups = [
            StartupDB(id=uuid4(), name=f"Tech {i}", sector="Technology", stage="Series A")
            for i in range(2)
        ]
        mock_query = MagicMock()
        mock_filter_query = MagicMock()
        mock_query.filter.return_value = mock_filter_query
        mock_filter_query.offset.return_value.limit.return_value.all.return_value = tech_startups
        mock_session.query.return_value = mock_query
        
        # Act
        result = repository.list(sector="Technology")
        
        # Assert
        assert len(result) == 2
        mock_query.filter.assert_called_once()
    
    def test_list_startups_with_stage_filter(self, repository, mock_session):
        """Test listing startups filtered by stage."""
        # Arrange
        series_a_startups = [
            StartupDB(id=uuid4(), name=f"Startup {i}", sector="Tech", stage="Series A")
            for i in range(2)
        ]
        mock_query = MagicMock()
        mock_filter_query = MagicMock()
        mock_query.filter.return_value = mock_filter_query
        mock_filter_query.offset.return_value.limit.return_value.all.return_value = series_a_startups
        mock_session.query.return_value = mock_query
        
        # Act
        result = repository.list(stage="Series A")
        
        # Assert
        assert len(result) == 2
        mock_query.filter.assert_called_once()
    
    def test_list_startups_with_both_filters(self, repository, mock_session):
        """Test listing startups with both sector and stage filters."""
        # Arrange
        filtered_startups = [
            StartupDB(id=uuid4(), name="Filtered", sector="FinTech", stage="Series B")
        ]
        mock_query = MagicMock()
        mock_filter_query1 = MagicMock()
        mock_filter_query2 = MagicMock()
        mock_query.filter.return_value = mock_filter_query1
        mock_filter_query1.filter.return_value = mock_filter_query2
        mock_filter_query2.offset.return_value.limit.return_value.all.return_value = filtered_startups
        mock_session.query.return_value = mock_query
        
        # Act
        result = repository.list(sector="FinTech", stage="Series B")
        
        # Assert
        assert len(result) == 1
        assert mock_query.filter.call_count == 1
        assert mock_filter_query1.filter.call_count == 1
    
    def test_list_with_custom_pagination(self, repository, mock_session):
        """Test list with custom limit and offset."""
        # Arrange
        paginated_startups = [
            StartupDB(id=uuid4(), name=f"Page {i}", sector="Tech", stage="Seed")
            for i in range(5)
        ]
        mock_query = MagicMock()
        mock_query.offset.return_value.limit.return_value.all.return_value = paginated_startups
        mock_session.query.return_value = mock_query
        
        # Act
        result = repository.list(limit=5, offset=10)
        
        # Assert
        assert len(result) == 5
        mock_query.offset.assert_called_once_with(10)
        mock_query.offset.return_value.limit.assert_called_once_with(5)
    
    # UPDATE Tests
    def test_update_startup_success(self, repository, mock_session, sample_startup_domain, sample_startup_db):
        """Test successful startup update."""
        # Arrange
        mock_query = MagicMock()
        mock_query.filter_by.return_value.first.return_value = sample_startup_db
        mock_session.query.return_value = mock_query
        
        updated_startup = StartupDomain(
            name="Updated Startup",
            sector="FinTech",
            stage="Series B",
            description="Updated description",
            website="https://updated.com",
            team_size=20,
            monthly_revenue=100000.0
        )
        updated_startup.id = sample_startup_domain.id
        
        # Act
        result = repository.update(sample_startup_domain.id, updated_startup)
        
        # Assert
        assert result is not None
        assert sample_startup_db.name == "Updated Startup"
        assert sample_startup_db.sector == "FinTech"
        assert sample_startup_db.stage == "Series B"
        assert sample_startup_db.team_size == 20
        assert sample_startup_db.monthly_revenue == 100000.0
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()
    
    def test_update_startup_not_found(self, repository, mock_session, sample_startup_domain):
        """Test updating non-existent startup."""
        # Arrange
        startup_id = uuid4()
        mock_query = MagicMock()
        mock_query.filter_by.return_value.first.return_value = None
        mock_session.query.return_value = mock_query
        
        # Act
        result = repository.update(startup_id, sample_startup_domain)
        
        # Assert
        assert result is None
        mock_session.commit.assert_not_called()
    
    # DELETE Tests
    def test_delete_startup_success(self, repository, mock_session, sample_startup_db):
        """Test successful startup deletion."""
        # Arrange
        mock_query = MagicMock()
        mock_query.filter_by.return_value.first.return_value = sample_startup_db
        mock_session.query.return_value = mock_query
        
        # Act
        result = repository._delete_sync(sample_startup_db.id)
        
        # Assert
        assert result is True
        mock_session.delete.assert_called_once_with(sample_startup_db)
        mock_session.commit.assert_called_once()
    
    def test_delete_startup_not_found(self, repository, mock_session):
        """Test deleting non-existent startup."""
        # Arrange
        startup_id = uuid4()
        mock_query = MagicMock()
        mock_query.filter_by.return_value.first.return_value = None
        mock_session.query.return_value = mock_query
        
        # Act
        result = repository._delete_sync(startup_id)
        
        # Assert
        assert result is False
        mock_session.delete.assert_not_called()
        mock_session.commit.assert_not_called()
    
    # SEARCH Tests
    def test_search_startups(self, repository, mock_session):
        """Test searching startups by name or description."""
        # Arrange
        search_results = [
            StartupDB(id=uuid4(), name="AI Startup", sector="Tech", stage="Seed", description="AI solutions"),
            StartupDB(id=uuid4(), name="Tech Co", sector="Tech", stage="Series A", description="AI-powered tech")
        ]
        mock_query = MagicMock()
        mock_filter_result = MagicMock()
        mock_query.filter.return_value = mock_filter_result
        mock_filter_result.limit.return_value.all.return_value = search_results
        mock_session.query.return_value = mock_query
        
        # Act
        result = repository.search("AI")
        
        # Assert
        assert len(result) == 2
        mock_query.filter.assert_called_once()
        mock_filter_result.limit.assert_called_once_with(50)
    
    def test_search_empty_query(self, repository, mock_session):
        """Test search with empty query string."""
        # Arrange
        mock_query = MagicMock()
        mock_filter_result = MagicMock()
        mock_query.filter.return_value = mock_filter_result
        mock_filter_result.limit.return_value.all.return_value = []
        mock_session.query.return_value = mock_query
        
        # Act
        result = repository.search("")
        
        # Assert
        assert result == []
    
    def test_search_by_sector(self, repository, mock_session):
        """Test searching startups by sector."""
        # Arrange
        tech_startups = [
            StartupDB(id=uuid4(), name=f"Tech {i}", sector="Technology", stage="Seed")
            for i in range(3)
        ]
        mock_query = MagicMock()
        mock_filter_result = MagicMock()
        mock_query.filter.return_value = mock_filter_result
        mock_filter_result.all.return_value = tech_startups
        mock_session.query.return_value = mock_query
        
        # Act
        result = repository.search_by_sector("Technology")
        
        # Assert
        assert len(result) == 3
        assert all(s.sector == "Technology" for s in result)
    
    def test_search_by_stage(self, repository, mock_session):
        """Test searching startups by stage."""
        # Arrange
        seed_startups = [
            StartupDB(id=uuid4(), name=f"Startup {i}", sector="Tech", stage="Seed")
            for i in range(2)
        ]
        mock_query = MagicMock()
        mock_filter_result = MagicMock()
        mock_query.filter.return_value = mock_filter_result
        mock_filter_result.all.return_value = seed_startups
        mock_session.query.return_value = mock_query
        
        # Act
        result = repository.search_by_stage("Seed")
        
        # Assert
        assert len(result) == 2
        assert all(s.stage == "Seed" for s in result)
    
    # ASYNC METHOD Tests (which call sync methods)
    @pytest.mark.asyncio
    async def test_save_calls_create(self, repository, mock_session, sample_startup_domain):
        """Test that save method calls create."""
        # Arrange
        with patch.object(repository, 'create', return_value=sample_startup_domain) as mock_create:
            # Act
            result = await repository.save(sample_startup_domain)
            
            # Assert
            mock_create.assert_called_once_with(sample_startup_domain)
            assert result == sample_startup_domain
    
    @pytest.mark.asyncio
    async def test_find_by_id_calls_get(self, repository, mock_session, sample_startup_domain):
        """Test that find_by_id calls get."""
        # Arrange
        with patch.object(repository, 'get', return_value=sample_startup_domain) as mock_get:
            # Act
            result = await repository.find_by_id(sample_startup_domain.id)
            
            # Assert
            mock_get.assert_called_once_with(sample_startup_domain.id)
            assert result == sample_startup_domain
    
    @pytest.mark.asyncio
    async def test_find_by_sector_calls_search_by_sector(self, repository, mock_session):
        """Test that find_by_sector calls search_by_sector."""
        # Arrange
        sector = "Technology"
        expected_results = [MagicMock()]
        with patch.object(repository, 'search_by_sector', return_value=expected_results) as mock_search:
            # Act
            result = await repository.find_by_sector(sector)
            
            # Assert
            mock_search.assert_called_once_with(sector)
            assert result == expected_results
    
    @pytest.mark.asyncio
    async def test_find_by_stage_calls_search_by_stage(self, repository, mock_session):
        """Test that find_by_stage calls search_by_stage."""
        # Arrange
        stage = "Series A"
        expected_results = [MagicMock()]
        with patch.object(repository, 'search_by_stage', return_value=expected_results) as mock_search:
            # Act
            result = await repository.find_by_stage(stage)
            
            # Assert
            mock_search.assert_called_once_with(stage)
            assert result == expected_results
    
    @pytest.mark.asyncio
    async def test_find_all(self, repository, mock_session):
        """Test find_all method."""
        # Arrange
        db_startups = [
            StartupDB(id=uuid4(), name=f"Startup {i}", sector="Tech", stage="Seed")
            for i in range(3)
        ]
        mock_query = MagicMock()
        mock_query.all.return_value = db_startups
        mock_session.query.return_value = mock_query
        
        # Act
        result = await repository.find_all()
        
        # Assert
        assert len(result) == 3
        assert all(isinstance(s, StartupDomain) for s in result)
        mock_session.query.assert_called_once_with(StartupDB)
    
    @pytest.mark.asyncio
    async def test_delete_calls_delete_sync(self, repository, mock_session):
        """Test that async delete calls _delete_sync."""
        # Arrange
        startup_id = uuid4()
        with patch.object(repository, '_delete_sync', return_value=True) as mock_delete:
            # Act
            result = await repository.delete(startup_id)
            
            # Assert
            mock_delete.assert_called_once_with(startup_id)
            assert result is True
    
    # Helper Method Tests
    def test_to_domain_conversion(self, repository, sample_startup_db):
        """Test conversion from DB model to domain model."""
        # Act
        result = repository._to_domain(sample_startup_db)
        
        # Assert
        assert isinstance(result, StartupDomain)
        assert result.id == sample_startup_db.id
        assert result.name == sample_startup_db.name
        assert result.sector == sample_startup_db.sector
        assert result.stage == sample_startup_db.stage
        assert result.description == sample_startup_db.description
        assert result.website == sample_startup_db.website
        assert result.team_size == sample_startup_db.team_size
        assert result.monthly_revenue == sample_startup_db.monthly_revenue
    
    def test_to_domain_with_none_values(self, repository):
        """Test _to_domain with None values in fields."""
        # Arrange
        db_startup = StartupDB(
            id=uuid4(),
            name="Test",
            sector="Tech",
            stage="Seed",
            description=None,
            website=None,
            team_size=10,
            monthly_revenue=1000.0
        )
        
        # Act
        result = repository._to_domain(db_startup)
        
        # Assert
        assert result.description == ""  # Should convert None to empty string
        assert result.website == ""  # Should convert None to empty string
        assert result.team_size == 10
        assert result.monthly_revenue == 1000.0


class TestAsyncPostgresStartupRepository:
    """Test AsyncPostgresStartupRepository (async implementation)."""
    
    @pytest.fixture
    def mock_async_session(self):
        """Create a mock async database session."""
        session = AsyncMock(spec=AsyncSession)
        return session
    
    @pytest.fixture
    def repository(self, mock_async_session):
        """Create async repository instance with mock session."""
        return AsyncPostgresStartupRepository(mock_async_session)
    
    @pytest.fixture
    def sample_startup_domain(self):
        """Create a sample startup domain model."""
        startup = StartupDomain(
            name="Async Test Startup",
            sector="FinTech",
            stage="Series B",
            description="An async test startup",
            website="https://asynctest.com",
            team_size=15,
            monthly_revenue=75000.0
        )
        startup.id = uuid4()
        return startup
    
    @pytest.fixture
    def sample_startup_db(self, sample_startup_domain):
        """Create a sample startup DB model."""
        return StartupDB(
            id=sample_startup_domain.id,
            name=sample_startup_domain.name,
            sector=sample_startup_domain.sector,
            stage=sample_startup_domain.stage,
            description=sample_startup_domain.description,
            website=sample_startup_domain.website,
            team_size=sample_startup_domain.team_size,
            monthly_revenue=sample_startup_domain.monthly_revenue,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
    
    # CREATE Tests
    @pytest.mark.asyncio
    async def test_create_startup_success(self, repository, mock_async_session, sample_startup_domain):
        """Test successful async startup creation."""
        # Arrange
        mock_async_session.add.return_value = None
        mock_async_session.commit.return_value = None
        mock_async_session.refresh.return_value = None
        
        # Act
        result = await repository.create(sample_startup_domain)
        
        # Assert
        assert result.id == sample_startup_domain.id
        assert result.name == sample_startup_domain.name
        assert result.sector == sample_startup_domain.sector
        
        # Verify database operations
        mock_async_session.add.assert_called_once()
        added_startup = mock_async_session.add.call_args[0][0]
        assert isinstance(added_startup, StartupDB)
        assert added_startup.name == sample_startup_domain.name
        
        mock_async_session.commit.assert_called_once()
        mock_async_session.refresh.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_startup_database_error(self, repository, mock_async_session, sample_startup_domain):
        """Test handling database errors during async creation."""
        # Arrange
        mock_async_session.commit.side_effect = DatabaseError("Connection lost", None, None)
        
        # Act & Assert
        with pytest.raises(DatabaseError):
            await repository.create(sample_startup_domain)
    
    # GET Tests
    @pytest.mark.asyncio
    async def test_get_startup_found(self, repository, mock_async_session, sample_startup_db):
        """Test getting an existing startup asynchronously."""
        # Arrange
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_startup_db
        mock_async_session.execute.return_value = mock_result
        
        # Act
        result = await repository.get(sample_startup_db.id)
        
        # Assert
        assert result is not None
        assert result.id == sample_startup_db.id
        assert result.name == sample_startup_db.name
        assert isinstance(result, StartupDomain)
        
        mock_async_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_startup_not_found(self, repository, mock_async_session):
        """Test getting a non-existent startup asynchronously."""
        # Arrange
        startup_id = uuid4()
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_async_session.execute.return_value = mock_result
        
        # Act
        result = await repository.get(startup_id)
        
        # Assert
        assert result is None
    
    # LIST Tests
    @pytest.mark.asyncio
    async def test_list_all_startups(self, repository, mock_async_session):
        """Test listing all startups asynchronously without filters."""
        # Arrange
        db_startups = [
            StartupDB(id=uuid4(), name=f"Async Startup {i}", sector="Tech", stage="Seed")
            for i in range(3)
        ]
        mock_result = MagicMock()
        mock_scalars = MagicMock()
        mock_scalars.all.return_value = db_startups
        mock_result.scalars.return_value = mock_scalars
        mock_async_session.execute.return_value = mock_result
        
        # Act
        result = await repository.list()
        
        # Assert
        assert len(result) == 3
        assert all(isinstance(s, StartupDomain) for s in result)
        mock_async_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_list_with_filters(self, repository, mock_async_session):
        """Test listing with sector and stage filters."""
        # Arrange
        filtered_startups = [
            StartupDB(id=uuid4(), name="Filtered", sector="FinTech", stage="Series A")
        ]
        mock_result = MagicMock()
        mock_scalars = MagicMock()
        mock_scalars.all.return_value = filtered_startups
        mock_result.scalars.return_value = mock_scalars
        mock_async_session.execute.return_value = mock_result
        
        # Act
        result = await repository.list(sector="FinTech", stage="Series A")
        
        # Assert
        assert len(result) == 1
        assert result[0].sector == "FinTech"
        assert result[0].stage == "Series A"
    
    @pytest.mark.asyncio
    async def test_list_with_pagination(self, repository, mock_async_session):
        """Test list with limit and offset."""
        # Arrange
        paginated_startups = [
            StartupDB(id=uuid4(), name=f"Page {i}", sector="Tech", stage="Seed")
            for i in range(5)
        ]
        mock_result = MagicMock()
        mock_scalars = MagicMock()
        mock_scalars.all.return_value = paginated_startups
        mock_result.scalars.return_value = mock_scalars
        mock_async_session.execute.return_value = mock_result
        
        # Act
        result = await repository.list(limit=5, offset=10)
        
        # Assert
        assert len(result) == 5
    
    # UPDATE Tests
    @pytest.mark.asyncio
    async def test_update_startup_success(self, repository, mock_async_session, sample_startup_domain, sample_startup_db):
        """Test successful async startup update."""
        # Arrange
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_startup_db
        mock_async_session.execute.return_value = mock_result
        mock_async_session.commit.return_value = None
        mock_async_session.refresh.return_value = None
        
        updated_startup = StartupDomain(
            name="Updated Async Startup",
            sector="HealthTech",
            stage="Series C",
            description="Updated async description",
            website="https://updatedasync.com",
            team_size=25,
            monthly_revenue=150000.0
        )
        updated_startup.id = sample_startup_domain.id
        
        # Act
        result = await repository.update(updated_startup)
        
        # Assert
        assert result is not None
        assert sample_startup_db.name == "Updated Async Startup"
        assert sample_startup_db.sector == "HealthTech"
        mock_async_session.commit.assert_called_once()
        mock_async_session.refresh.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_update_startup_not_found(self, repository, mock_async_session, sample_startup_domain):
        """Test updating non-existent startup raises error."""
        # Arrange
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_async_session.execute.return_value = mock_result
        
        # Act & Assert
        with pytest.raises(ValueError, match="not found"):
            await repository.update(sample_startup_domain)
    
    # DELETE Tests
    @pytest.mark.asyncio
    async def test_delete_startup_success(self, repository, mock_async_session, sample_startup_db):
        """Test successful async startup deletion."""
        # Arrange
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_startup_db
        mock_async_session.execute.return_value = mock_result
        mock_async_session.delete.return_value = None
        mock_async_session.commit.return_value = None
        
        # Act
        result = await repository.delete(sample_startup_db.id)
        
        # Assert
        assert result is True
        mock_async_session.delete.assert_called_once_with(sample_startup_db)
        mock_async_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_delete_startup_not_found(self, repository, mock_async_session):
        """Test deleting non-existent startup."""
        # Arrange
        startup_id = uuid4()
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_async_session.execute.return_value = mock_result
        
        # Act
        result = await repository.delete(startup_id)
        
        # Assert
        assert result is False
        mock_async_session.delete.assert_not_called()
        mock_async_session.commit.assert_not_called()
    
    # SEARCH Tests
    @pytest.mark.asyncio
    async def test_search_startups(self, repository, mock_async_session):
        """Test searching startups by name or description."""
        # Arrange
        search_results = [
            StartupDB(id=uuid4(), name="AI Async Startup", sector="Tech", stage="Seed", description="AI solutions"),
            StartupDB(id=uuid4(), name="Tech Async Co", sector="Tech", stage="Series A", description="AI-powered tech")
        ]
        mock_result = MagicMock()
        mock_scalars = MagicMock()
        mock_scalars.all.return_value = search_results
        mock_result.scalars.return_value = mock_scalars
        mock_async_session.execute.return_value = mock_result
        
        # Act
        result = await repository.search("AI")
        
        # Assert
        assert len(result) == 2
        mock_async_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_search_special_characters(self, repository, mock_async_session):
        """Test search with special characters in query."""
        # Arrange
        mock_result = MagicMock()
        mock_scalars = MagicMock()
        mock_scalars.all.return_value = []
        mock_result.scalars.return_value = mock_scalars
        mock_async_session.execute.return_value = mock_result
        
        # Act
        result = await repository.search("test%_special")
        
        # Assert
        assert result == []
        # Verify the search pattern is properly escaped
        mock_async_session.execute.assert_called_once()
    
    
    # Edge cases
    @pytest.mark.asyncio
    async def test_create_with_minimal_data(self, repository, mock_async_session):
        """Test creating startup with only required fields."""
        # Arrange
        minimal_startup = StartupDomain(
            name="Minimal Async Startup",
            sector="Technology",
            stage="Seed"
        )
        minimal_startup.id = uuid4()
        
        mock_async_session.add.return_value = None
        mock_async_session.commit.return_value = None
        mock_async_session.refresh.return_value = None
        
        # Act
        result = await repository.create(minimal_startup)
        
        # Assert
        assert result.name == "Minimal Async Startup"
        assert result.description == ""
        assert result.website == ""
    
    @pytest.mark.asyncio
    async def test_list_empty_results(self, repository, mock_async_session):
        """Test list when no startups exist."""
        # Arrange
        mock_result = MagicMock()
        mock_scalars = MagicMock()
        mock_scalars.all.return_value = []
        mock_result.scalars.return_value = mock_scalars
        mock_async_session.execute.return_value = mock_result
        
        # Act
        result = await repository.list()
        
        # Assert
        assert result == []
    
    # Helper method tests
    def test_to_domain_conversion(self, repository, sample_startup_db):
        """Test conversion from DB model to domain model."""
        # Act
        result = repository._to_domain(sample_startup_db)
        
        # Assert
        assert isinstance(result, StartupDomain)
        assert result.id == sample_startup_db.id
        assert result.name == sample_startup_db.name
        assert result.sector == sample_startup_db.sector
        assert result.stage == sample_startup_db.stage
        assert result.description == sample_startup_db.description
        assert result.website == sample_startup_db.website
        assert result.team_size == sample_startup_db.team_size
        assert result.monthly_revenue == sample_startup_db.monthly_revenue
    
    def test_to_domain_handles_none_values(self, repository):
        """Test _to_domain converts None values properly."""
        # Arrange
        db_startup = StartupDB(
            id=uuid4(),
            name="Test",
            sector="Tech",
            stage="Seed",
            description=None,
            website=None,
            team_size=5,
            monthly_revenue=0.0
        )
        
        # Act
        result = repository._to_domain(db_startup)
        
        # Assert
        assert result.description == ""
        assert result.website == ""
        assert result.team_size == 5
        assert result.monthly_revenue == 0.0


# Integration-style tests (testing method interactions)
class TestRepositoryIntegration:
    """Test interactions between repository methods."""
    
    @pytest.fixture
    def sync_repository(self):
        """Create sync repository with mock session."""
        session = MagicMock(spec=Session)
        return PostgresStartupRepository(session)
    
    @pytest.fixture
    def async_repository(self):
        """Create async repository with mock session."""
        session = AsyncMock(spec=AsyncSession)
        return AsyncPostgresStartupRepository(session)
    
    def test_create_then_get_sync(self, sync_repository):
        """Test creating and then retrieving a startup (sync)."""
        # Arrange
        startup = StartupDomain(
            name="Integration Test",
            sector="Tech",
            stage="Seed"
        )
        startup.id = uuid4()
        
        db_startup = StartupDB(
            id=startup.id,
            name=startup.name,
            sector=startup.sector,
            stage=startup.stage,
            description="",
            website="",
            team_size=0,
            monthly_revenue=0
        )
        
        # Mock create
        sync_repository.session.add.return_value = None
        sync_repository.session.commit.return_value = None
        sync_repository.session.refresh.return_value = None
        
        # Mock get
        mock_query = MagicMock()
        mock_query.filter_by.return_value.first.return_value = db_startup
        sync_repository.session.query.return_value = mock_query
        
        # Act
        created = sync_repository.create(startup)
        retrieved = sync_repository.get(startup.id)
        
        # Assert
        assert created.id == retrieved.id
        assert created.name == retrieved.name
    
    @pytest.mark.asyncio
    async def test_create_update_delete_flow_async(self, async_repository):
        """Test full CRUD flow asynchronously."""
        # Arrange
        startup = StartupDomain(
            name="Full Flow Test",
            sector="Tech",
            stage="Seed"
        )
        startup.id = uuid4()
        
        db_startup = StartupDB(
            id=startup.id,
            name=startup.name,
            sector=startup.sector,
            stage=startup.stage,
            description="",
            website="",
            team_size=0,
            monthly_revenue=0
        )
        
        # Mock create
        async_repository.session.add.return_value = None
        async_repository.session.commit.return_value = None
        async_repository.session.refresh.return_value = None
        
        # Act - Create
        created = await async_repository.create(startup)
        assert created.name == "Full Flow Test"
        
        # Mock update
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = db_startup
        async_repository.session.execute.return_value = mock_result
        
        # Act - Update
        created.name = "Updated Flow Test"
        updated = await async_repository.update(created)
        assert db_startup.name == "Updated Flow Test"
        
        # Mock delete
        mock_result.scalar_one_or_none.return_value = db_startup
        async_repository.session.delete.return_value = None
        
        # Act - Delete
        deleted = await async_repository.delete(created.id)
        assert deleted is True