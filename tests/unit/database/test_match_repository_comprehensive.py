"""Comprehensive unit tests for PostgreSQL match repository.

This test module provides comprehensive coverage for both PostgresMatchRepository 
and AsyncPostgresMatchRepository implementations, including:

1. CRUD Operations:
   - Create (with and without ID)
   - Get (existing and non-existent)
   - List (with pagination)
   - Update (existing and non-existent)
   - Delete (existing and non-existent)

2. Query Operations:
   - get_by_startup_id / list_by_startup
   - get_by_vc_id / list_by_vc
   - get_top_matches_for_startup
   - exists
   - update_score

3. Data Conversions:
   - Domain to DB model conversion
   - DB to domain model conversion
   - Handling of null/None values
   - UUID generation when match.id is None

4. Edge Cases:
   - Empty results
   - Very long reason strings
   - Score boundaries (0.0, 1.0, negative)
   - Zero limit in pagination
   - Concurrent modifications
   - Database errors

5. Relationship Loading:
   - Proper use of joinedload for startup and vc relationships
   - Nested object conversions

Total: 46 test cases covering all repository methods and edge cases.
"""

import pytest
from unittest.mock import Mock, MagicMock, AsyncMock, patch
from uuid import UUID, uuid4
from datetime import datetime

from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError

from src.database.repositories.match_repository import PostgresMatchRepository, AsyncPostgresMatchRepository
from src.database.models.match import Match as MatchDB
from src.database.models.startup import Startup as StartupDB
from src.database.models.vc import VC as VCDB
from src.core.models.match import Match as MatchDomain
from src.core.models.startup import Startup as StartupDomain
from src.core.models.vc import VC as VCDomain


class TestPostgresMatchRepository:
    """Test cases for PostgresMatchRepository."""
    
    @pytest.fixture
    def mock_session(self):
        """Create a mock SQLAlchemy session."""
        session = Mock(spec=Session)
        return session
    
    @pytest.fixture
    def repository(self, mock_session):
        """Create a PostgresMatchRepository instance."""
        return PostgresMatchRepository(mock_session)
    
    @pytest.fixture
    def sample_startup_db(self):
        """Create a sample startup DB model."""
        startup = Mock(spec=StartupDB)
        startup.id = uuid4()
        startup.name = "Test Startup"
        startup.sector = "Technology"
        startup.stage = "Seed"
        startup.description = "A test startup"
        startup.website = "https://test.com"
        startup.team_size = 10
        startup.monthly_revenue = 50000.0
        return startup
    
    @pytest.fixture
    def sample_vc_db(self):
        """Create a sample VC DB model."""
        vc = Mock(spec=VCDB)
        vc.id = uuid4()
        vc.firm_name = "Test VC"
        vc.website = "https://testvc.com"
        vc.thesis = "We invest in tech"
        vc.check_size_min = 100000
        vc.check_size_max = 1000000
        vc.sectors = ["Technology", "AI"]
        vc.stages = ["Seed", "Series A"]
        return vc
    
    @pytest.fixture
    def sample_match_db(self, sample_startup_db, sample_vc_db):
        """Create a sample match DB model."""
        match = Mock(spec=MatchDB)
        match.id = uuid4()
        match.startup_id = sample_startup_db.id
        match.vc_id = sample_vc_db.id
        match.score = 0.85
        match.reasons = ["Sector match", "Stage match"]
        match.startup = sample_startup_db
        match.vc = sample_vc_db
        return match
    
    @pytest.fixture
    def sample_startup_domain(self):
        """Create a sample startup domain model."""
        startup = StartupDomain(
            name="Test Startup",
            sector="Technology",
            stage="Seed",
            description="A test startup",
            website="https://test.com",
            team_size=10,
            monthly_revenue=50000.0
        )
        startup.id = uuid4()
        return startup
    
    @pytest.fixture
    def sample_vc_domain(self):
        """Create a sample VC domain model."""
        vc = VCDomain(
            firm_name="Test VC",
            website="https://testvc.com",
            thesis="We invest in tech",
            check_size_min=100000,
            check_size_max=1000000,
            sectors=["Technology", "AI"],
            stages=["Seed", "Series A"]
        )
        vc.id = uuid4()
        return vc
    
    @pytest.fixture
    def sample_match_domain(self, sample_startup_domain, sample_vc_domain):
        """Create a sample match domain model."""
        match = MatchDomain(
            startup=sample_startup_domain,
            vc=sample_vc_domain,
            score=0.85,
            reasons=["Sector match", "Stage match"]
        )
        match.id = uuid4()
        return match
    
    def test_create_with_id(self, repository, mock_session, sample_match_domain, sample_match_db):
        """Test creating a match with an existing ID."""
        # Setup
        mock_query = Mock()
        mock_query.options.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = sample_match_db
        mock_session.query.return_value = mock_query
        
        # Execute
        result = repository.create(sample_match_domain)
        
        # Verify
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()
        
        # Check the match was created with the correct attributes
        created_match = mock_session.add.call_args[0][0]
        assert created_match.id == sample_match_domain.id
        assert created_match.startup_id == sample_match_domain.startup.id
        assert created_match.vc_id == sample_match_domain.vc.id
        assert created_match.score == sample_match_domain.score
        assert created_match.reasons == sample_match_domain.reasons
        
        assert result.id == sample_match_db.id
        assert result.score == sample_match_db.score
    
    def test_create_without_id(self, repository, mock_session, sample_match_domain, sample_match_db):
        """Test creating a match without an ID (should generate UUID)."""
        # Setup
        sample_match_domain.id = None
        mock_query = Mock()
        mock_query.options.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = sample_match_db
        mock_session.query.return_value = mock_query
        
        # Execute
        with patch('src.database.repositories.match_repository.uuid4', return_value=uuid4()):
            result = repository.create(sample_match_domain)
        
        # Verify
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()
        
        # Check that a UUID was generated
        created_match = mock_session.add.call_args[0][0]
        assert created_match.id is not None
        assert isinstance(created_match.id, UUID)
    
    def test_get_existing(self, repository, mock_session, sample_match_db):
        """Test getting an existing match."""
        # Setup
        match_id = sample_match_db.id
        mock_query = Mock()
        mock_query.options.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = sample_match_db
        mock_session.query.return_value = mock_query
        
        # Execute
        result = repository.get(match_id)
        
        # Verify
        mock_session.query.assert_called_with(MatchDB)
        mock_query.filter_by.assert_called_with(id=match_id)
        
        assert result is not None
        assert result.id == sample_match_db.id
        assert result.score == sample_match_db.score
    
    def test_get_nonexistent(self, repository, mock_session):
        """Test getting a non-existent match."""
        # Setup
        match_id = uuid4()
        mock_query = Mock()
        mock_query.options.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = None
        mock_session.query.return_value = mock_query
        
        # Execute
        result = repository.get(match_id)
        
        # Verify
        assert result is None
    
    def test_list(self, repository, mock_session, sample_match_db):
        """Test listing matches with pagination."""
        # Setup
        matches = [sample_match_db, sample_match_db]
        mock_query = Mock()
        mock_query.options.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = matches
        mock_session.query.return_value = mock_query
        
        # Execute
        result = repository.list(limit=10, offset=5)
        
        # Verify
        mock_query.offset.assert_called_with(5)
        mock_query.limit.assert_called_with(10)
        
        assert len(result) == 2
        assert all(isinstance(m, MatchDomain) for m in result)
    
    def test_update_existing(self, repository, mock_session, sample_match_domain, sample_match_db):
        """Test updating an existing match."""
        # Setup
        match_id = sample_match_db.id
        sample_match_domain.score = 0.95
        sample_match_domain.reasons = ["New reason"]
        
        mock_query = Mock()
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = sample_match_db
        mock_query.options.return_value = mock_query
        mock_session.query.return_value = mock_query
        
        # Execute
        result = repository.update(match_id, sample_match_domain)
        
        # Verify
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()
        
        # Check that fields were updated
        assert sample_match_db.score == 0.95
        assert sample_match_db.reasons == ["New reason"]
        assert result is not None
    
    def test_update_nonexistent(self, repository, mock_session, sample_match_domain):
        """Test updating a non-existent match."""
        # Setup
        match_id = uuid4()
        mock_query = Mock()
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = None
        mock_session.query.return_value = mock_query
        
        # Execute
        result = repository.update(match_id, sample_match_domain)
        
        # Verify
        assert result is None
        mock_session.commit.assert_not_called()
    
    def test_delete_existing(self, repository, mock_session, sample_match_db):
        """Test deleting an existing match."""
        # Setup
        match_id = sample_match_db.id
        mock_query = Mock()
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = sample_match_db
        mock_session.query.return_value = mock_query
        
        # Execute
        result = repository.delete(match_id)
        
        # Verify
        mock_session.delete.assert_called_once_with(sample_match_db)
        mock_session.commit.assert_called_once()
        assert result is True
    
    def test_delete_nonexistent(self, repository, mock_session):
        """Test deleting a non-existent match."""
        # Setup
        match_id = uuid4()
        mock_query = Mock()
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = None
        mock_session.query.return_value = mock_query
        
        # Execute
        result = repository.delete(match_id)
        
        # Verify
        mock_session.delete.assert_not_called()
        assert result is False
    
    def test_get_by_startup_id(self, repository, mock_session, sample_match_db):
        """Test getting matches by startup ID."""
        # Setup
        startup_id = sample_match_db.startup_id
        matches = [sample_match_db, sample_match_db]
        mock_query = Mock()
        mock_query.options.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.all.return_value = matches
        mock_session.query.return_value = mock_query
        
        # Execute
        result = repository.get_by_startup_id(startup_id)
        
        # Verify
        mock_query.filter_by.assert_called_with(startup_id=startup_id)
        assert len(result) == 2
        assert all(isinstance(m, MatchDomain) for m in result)
    
    def test_list_by_startup(self, repository, mock_session, sample_match_db):
        """Test list_by_startup alias method."""
        # Setup
        startup_id = sample_match_db.startup_id
        matches = [sample_match_db]
        mock_query = Mock()
        mock_query.options.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.all.return_value = matches
        mock_session.query.return_value = mock_query
        
        # Execute
        result = repository.list_by_startup(startup_id)
        
        # Verify
        assert len(result) == 1
    
    def test_get_by_vc_id(self, repository, mock_session, sample_match_db):
        """Test getting matches by VC ID."""
        # Setup
        vc_id = sample_match_db.vc_id
        matches = [sample_match_db, sample_match_db]
        mock_query = Mock()
        mock_query.options.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.all.return_value = matches
        mock_session.query.return_value = mock_query
        
        # Execute
        result = repository.get_by_vc_id(vc_id)
        
        # Verify
        mock_query.filter_by.assert_called_with(vc_id=vc_id)
        assert len(result) == 2
        assert all(isinstance(m, MatchDomain) for m in result)
    
    def test_list_by_vc(self, repository, mock_session, sample_match_db):
        """Test list_by_vc alias method."""
        # Setup
        vc_id = sample_match_db.vc_id
        matches = [sample_match_db]
        mock_query = Mock()
        mock_query.options.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.all.return_value = matches
        mock_session.query.return_value = mock_query
        
        # Execute
        result = repository.list_by_vc(vc_id)
        
        # Verify
        assert len(result) == 1
    
    def test_get_top_matches_for_startup(self, repository, mock_session, sample_match_db):
        """Test getting top matches for a startup."""
        # Setup
        startup_id = sample_match_db.startup_id
        matches = [sample_match_db, sample_match_db]
        mock_query = Mock()
        mock_query.options.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = matches
        mock_session.query.return_value = mock_query
        
        # Execute
        result = repository.get_top_matches_for_startup(startup_id, limit=5)
        
        # Verify
        mock_query.filter_by.assert_called_with(startup_id=startup_id)
        mock_query.limit.assert_called_with(5)
        assert len(result) == 2
    
    def test_exists_true(self, repository, mock_session, sample_match_db):
        """Test checking if a match exists (exists)."""
        # Setup
        startup_id = sample_match_db.startup_id
        vc_id = sample_match_db.vc_id
        mock_query = Mock()
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = sample_match_db
        mock_session.query.return_value = mock_query
        
        # Execute
        result = repository.exists(startup_id, vc_id)
        
        # Verify
        mock_query.filter_by.assert_called_with(startup_id=startup_id, vc_id=vc_id)
        assert result is True
    
    def test_exists_false(self, repository, mock_session):
        """Test checking if a match exists (doesn't exist)."""
        # Setup
        startup_id = uuid4()
        vc_id = uuid4()
        mock_query = Mock()
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = None
        mock_session.query.return_value = mock_query
        
        # Execute
        result = repository.exists(startup_id, vc_id)
        
        # Verify
        assert result is False
    
    def test_update_score_existing(self, repository, mock_session, sample_match_db):
        """Test updating score of an existing match."""
        # Setup
        match_id = sample_match_db.id
        new_score = 0.99
        mock_query = Mock()
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = sample_match_db
        mock_query.options.return_value = mock_query
        mock_session.query.return_value = mock_query
        
        # Execute
        result = repository.update_score(match_id, new_score)
        
        # Verify
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()
        assert sample_match_db.score == new_score
        assert result is not None
    
    def test_update_score_nonexistent(self, repository, mock_session):
        """Test updating score of a non-existent match."""
        # Setup
        match_id = uuid4()
        new_score = 0.99
        mock_query = Mock()
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = None
        mock_session.query.return_value = mock_query
        
        # Execute
        result = repository.update_score(match_id, new_score)
        
        # Verify
        assert result is None
        mock_session.commit.assert_not_called()
    
    def test_to_domain_conversion(self, repository, sample_match_db):
        """Test conversion from DB model to domain model."""
        # Execute
        result = repository._to_domain(sample_match_db)
        
        # Verify
        assert isinstance(result, MatchDomain)
        assert result.id == sample_match_db.id
        assert result.score == sample_match_db.score
        assert result.reasons == sample_match_db.reasons
        
        # Verify startup conversion
        assert isinstance(result.startup, StartupDomain)
        assert result.startup.id == sample_match_db.startup.id
        assert result.startup.name == sample_match_db.startup.name
        
        # Verify VC conversion
        assert isinstance(result.vc, VCDomain)
        assert result.vc.id == sample_match_db.vc.id
        assert result.vc.firm_name == sample_match_db.vc.firm_name
    
    def test_to_domain_with_null_values(self, repository):
        """Test conversion with null/None values."""
        # Setup
        startup = Mock(spec=StartupDB)
        startup.id = uuid4()
        startup.name = "Test Startup"
        startup.sector = "Tech"
        startup.stage = "Seed"
        startup.description = None  # Null value
        startup.website = None  # Null value
        startup.team_size = 5
        startup.monthly_revenue = 0
        
        vc = Mock(spec=VCDB)
        vc.id = uuid4()
        vc.firm_name = "Test VC"
        vc.website = None  # Null value
        vc.thesis = None  # Null value
        vc.check_size_min = None  # Null value
        vc.check_size_max = None  # Null value
        vc.sectors = None  # Null value
        vc.stages = None  # Null value
        
        match = Mock(spec=MatchDB)
        match.id = uuid4()
        match.score = 0.75
        match.reasons = None  # Null value
        match.startup = startup
        match.vc = vc
        
        # Execute
        result = repository._to_domain(match)
        
        # Verify null values are handled correctly
        assert result.startup.description == ""
        assert result.startup.website == ""
        assert result.vc.website == ""
        assert result.vc.thesis == ""
        assert result.vc.check_size_min == 0
        assert result.vc.check_size_max == 0
        assert result.vc.sectors == []
        assert result.vc.stages == []
        assert result.reasons == []
    
    def test_database_error_handling(self, repository, mock_session, sample_match_domain):
        """Test handling of database errors."""
        # Setup
        mock_session.add.side_effect = SQLAlchemyError("Database error")
        
        # Execute and verify
        with pytest.raises(SQLAlchemyError):
            repository.create(sample_match_domain)


class TestAsyncPostgresMatchRepository:
    """Test cases for AsyncPostgresMatchRepository."""
    
    @pytest.fixture
    def mock_session(self):
        """Create a mock async SQLAlchemy session."""
        session = AsyncMock(spec=AsyncSession)
        return session
    
    @pytest.fixture
    def repository(self, mock_session):
        """Create an AsyncPostgresMatchRepository instance."""
        return AsyncPostgresMatchRepository(mock_session)
    
    @pytest.fixture
    def sample_startup_db(self):
        """Create a sample startup DB model."""
        startup = Mock(spec=StartupDB)
        startup.id = uuid4()
        startup.name = "Test Startup"
        startup.sector = "Technology"
        startup.stage = "Seed"
        startup.description = "A test startup"
        startup.website = "https://test.com"
        startup.team_size = 10
        startup.monthly_revenue = 50000.0
        return startup
    
    @pytest.fixture
    def sample_vc_db(self):
        """Create a sample VC DB model."""
        vc = Mock(spec=VCDB)
        vc.id = uuid4()
        vc.firm_name = "Test VC"
        vc.website = "https://testvc.com"
        vc.thesis = "We invest in tech"
        vc.check_size_min = 100000
        vc.check_size_max = 1000000
        vc.sectors = ["Technology", "AI"]
        vc.stages = ["Seed", "Series A"]
        return vc
    
    @pytest.fixture
    def sample_match_db(self, sample_startup_db, sample_vc_db):
        """Create a sample match DB model."""
        match = Mock(spec=MatchDB)
        match.id = uuid4()
        match.startup_id = sample_startup_db.id
        match.vc_id = sample_vc_db.id
        match.score = 0.85
        match.reasons = ["Sector match", "Stage match"]
        match.startup = sample_startup_db
        match.vc = sample_vc_db
        return match
    
    @pytest.fixture
    def sample_match_domain(self, sample_startup_db, sample_vc_db):
        """Create a sample match domain model."""
        startup = StartupDomain(
            name="Test Startup",
            sector="Technology",
            stage="Seed",
            description="A test startup",
            website="https://test.com",
            team_size=10,
            monthly_revenue=50000.0
        )
        startup.id = sample_startup_db.id
        
        vc = VCDomain(
            firm_name="Test VC",
            website="https://testvc.com",
            thesis="We invest in tech",
            check_size_min=100000,
            check_size_max=1000000,
            sectors=["Technology", "AI"],
            stages=["Seed", "Series A"]
        )
        vc.id = sample_vc_db.id
        
        match = MatchDomain(
            startup=startup,
            vc=vc,
            score=0.85,
            reasons=["Sector match", "Stage match"]
        )
        match.id = uuid4()
        return match
    
    @pytest.mark.asyncio
    async def test_create_with_id(self, repository, mock_session, sample_match_domain, sample_match_db):
        """Test creating a match with an existing ID."""
        # Setup
        mock_result = Mock()
        mock_result.scalar_one.return_value = sample_match_db
        mock_session.execute.return_value = mock_result
        
        # Execute
        result = await repository.create(sample_match_domain)
        
        # Verify
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()
        
        assert result.id == sample_match_db.id
        assert result.score == sample_match_db.score
    
    @pytest.mark.asyncio
    async def test_create_without_id(self, repository, mock_session, sample_match_domain, sample_match_db):
        """Test creating a match without an ID."""
        # Setup
        sample_match_domain.id = None
        mock_result = Mock()
        mock_result.scalar_one.return_value = sample_match_db
        mock_session.execute.return_value = mock_result
        
        # Execute
        with patch('src.database.repositories.match_repository.uuid4', return_value=uuid4()):
            result = await repository.create(sample_match_domain)
        
        # Verify
        created_match = mock_session.add.call_args[0][0]
        assert created_match.id is not None
        assert isinstance(created_match.id, UUID)
    
    @pytest.mark.asyncio
    async def test_get_existing(self, repository, mock_session, sample_match_db):
        """Test getting an existing match."""
        # Setup
        match_id = sample_match_db.id
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_match_db
        mock_session.execute.return_value = mock_result
        
        # Execute
        result = await repository.get(match_id)
        
        # Verify
        assert result is not None
        assert result.id == sample_match_db.id
    
    @pytest.mark.asyncio
    async def test_get_nonexistent(self, repository, mock_session):
        """Test getting a non-existent match."""
        # Setup
        match_id = uuid4()
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result
        
        # Execute
        result = await repository.get(match_id)
        
        # Verify
        assert result is None
    
    @pytest.mark.asyncio
    async def test_list(self, repository, mock_session, sample_match_db):
        """Test listing matches with pagination."""
        # Setup
        matches = [sample_match_db, sample_match_db]
        mock_scalars = Mock()
        mock_scalars.all.return_value = matches
        mock_result = Mock()
        mock_result.scalars.return_value = mock_scalars
        mock_session.execute.return_value = mock_result
        
        # Execute
        result = await repository.list(limit=10, offset=5)
        
        # Verify
        assert len(result) == 2
        assert all(isinstance(m, MatchDomain) for m in result)
    
    @pytest.mark.asyncio
    async def test_update_existing(self, repository, mock_session, sample_match_domain, sample_match_db):
        """Test updating an existing match."""
        # Setup
        match_id = sample_match_db.id
        sample_match_domain.score = 0.95
        sample_match_domain.reasons = ["New reason"]
        
        mock_result1 = Mock()
        mock_result1.scalar_one_or_none.return_value = sample_match_db
        
        mock_result2 = Mock()
        mock_result2.scalar_one.return_value = sample_match_db
        
        mock_session.execute.side_effect = [mock_result1, mock_result2]
        
        # Execute
        result = await repository.update(match_id, sample_match_domain)
        
        # Verify
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()
        assert sample_match_db.score == 0.95
        assert sample_match_db.reasons == ["New reason"]
        assert result is not None
    
    @pytest.mark.asyncio
    async def test_update_nonexistent(self, repository, mock_session, sample_match_domain):
        """Test updating a non-existent match."""
        # Setup
        match_id = uuid4()
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result
        
        # Execute
        result = await repository.update(match_id, sample_match_domain)
        
        # Verify
        assert result is None
        mock_session.commit.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_delete_existing(self, repository, mock_session, sample_match_db):
        """Test deleting an existing match."""
        # Setup
        match_id = sample_match_db.id
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_match_db
        mock_session.execute.return_value = mock_result
        
        # Execute
        result = await repository.delete(match_id)
        
        # Verify
        mock_session.delete.assert_called_once_with(sample_match_db)
        mock_session.commit.assert_called_once()
        assert result is True
    
    @pytest.mark.asyncio
    async def test_delete_nonexistent(self, repository, mock_session):
        """Test deleting a non-existent match."""
        # Setup
        match_id = uuid4()
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result
        
        # Execute
        result = await repository.delete(match_id)
        
        # Verify
        mock_session.delete.assert_not_called()
        assert result is False
    
    @pytest.mark.asyncio
    async def test_list_by_startup(self, repository, mock_session, sample_match_db):
        """Test listing matches by startup ID."""
        # Setup
        startup_id = sample_match_db.startup_id
        matches = [sample_match_db, sample_match_db]
        mock_scalars = Mock()
        mock_scalars.all.return_value = matches
        mock_result = Mock()
        mock_result.scalars.return_value = mock_scalars
        mock_session.execute.return_value = mock_result
        
        # Execute
        result = await repository.list_by_startup(startup_id)
        
        # Verify
        assert len(result) == 2
        assert all(isinstance(m, MatchDomain) for m in result)
    
    @pytest.mark.asyncio
    async def test_list_by_vc(self, repository, mock_session, sample_match_db):
        """Test listing matches by VC ID."""
        # Setup
        vc_id = sample_match_db.vc_id
        matches = [sample_match_db]
        mock_scalars = Mock()
        mock_scalars.all.return_value = matches
        mock_result = Mock()
        mock_result.scalars.return_value = mock_scalars
        mock_session.execute.return_value = mock_result
        
        # Execute
        result = await repository.list_by_vc(vc_id)
        
        # Verify
        assert len(result) == 1
    
    @pytest.mark.asyncio
    async def test_exists_true(self, repository, mock_session, sample_match_db):
        """Test checking if a match exists (exists)."""
        # Setup
        startup_id = sample_match_db.startup_id
        vc_id = sample_match_db.vc_id
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_match_db
        mock_session.execute.return_value = mock_result
        
        # Execute
        result = await repository.exists(startup_id, vc_id)
        
        # Verify
        assert result is True
    
    @pytest.mark.asyncio
    async def test_exists_false(self, repository, mock_session):
        """Test checking if a match exists (doesn't exist)."""
        # Setup
        startup_id = uuid4()
        vc_id = uuid4()
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result
        
        # Execute
        result = await repository.exists(startup_id, vc_id)
        
        # Verify
        assert result is False
    
    @pytest.mark.asyncio
    async def test_update_score_existing(self, repository, mock_session, sample_match_db):
        """Test updating score of an existing match."""
        # Setup
        match_id = sample_match_db.id
        new_score = 0.99
        
        mock_result1 = Mock()
        mock_result1.scalar_one_or_none.return_value = sample_match_db
        
        mock_result2 = Mock()
        mock_result2.scalar_one.return_value = sample_match_db
        
        mock_session.execute.side_effect = [mock_result1, mock_result2]
        
        # Execute
        result = await repository.update_score(match_id, new_score)
        
        # Verify
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()
        assert sample_match_db.score == new_score
        assert result is not None
    
    @pytest.mark.asyncio
    async def test_update_score_nonexistent(self, repository, mock_session):
        """Test updating score of a non-existent match."""
        # Setup
        match_id = uuid4()
        new_score = 0.99
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result
        
        # Execute
        result = await repository.update_score(match_id, new_score)
        
        # Verify
        assert result is None
        mock_session.commit.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_database_error_handling(self, repository, mock_session, sample_match_domain):
        """Test handling of database errors."""
        # Setup
        mock_session.add.side_effect = SQLAlchemyError("Database error")
        
        # Execute and verify
        with pytest.raises(SQLAlchemyError):
            await repository.create(sample_match_domain)
    
    def test_to_domain_inheritance(self, repository):
        """Test that _to_domain method is inherited correctly."""
        # The async repository should have the same _to_domain method
        assert hasattr(repository, '_to_domain')
        
    @pytest.mark.asyncio
    async def test_joinedload_usage(self, repository, mock_session, sample_match_db):
        """Test that joinedload is used correctly for relationship loading."""
        # Setup
        match_id = sample_match_db.id
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_match_db
        mock_session.execute.return_value = mock_result
        
        # Execute
        await repository.get(match_id)
        
        # Verify that execute was called with a select statement containing joinedload
        execute_call = mock_session.execute.call_args[0][0]
        # The actual verification of joinedload would require inspecting the SQLAlchemy query object
        # which is complex in a unit test. This test mainly ensures the method runs without error.


class TestEdgeCases:
    """Test edge cases and error conditions."""
    
    @pytest.fixture
    def mock_session(self):
        """Create a mock SQLAlchemy session."""
        return Mock(spec=Session)
    
    @pytest.fixture
    def repository(self, mock_session):
        """Create a PostgresMatchRepository instance."""
        return PostgresMatchRepository(mock_session)
    
    def test_empty_reasons_list(self, repository):
        """Test handling of empty reasons list."""
        # Setup
        startup = Mock(spec=StartupDB)
        startup.id = uuid4()
        startup.name = "Test"
        startup.sector = "Tech"
        startup.stage = "Seed"
        startup.description = ""
        startup.website = ""
        startup.team_size = 1
        startup.monthly_revenue = 0
        
        vc = Mock(spec=VCDB)
        vc.id = uuid4()
        vc.firm_name = "VC"
        vc.website = ""
        vc.thesis = ""
        vc.check_size_min = 0
        vc.check_size_max = 0
        vc.sectors = []
        vc.stages = []
        
        match = Mock(spec=MatchDB)
        match.id = uuid4()
        match.score = 0.5
        match.reasons = []  # Empty list
        match.startup = startup
        match.vc = vc
        
        # Execute
        result = repository._to_domain(match)
        
        # Verify
        assert result.reasons == []
    
    def test_very_long_reasons(self, repository):
        """Test handling of very long reason strings."""
        # Setup
        startup = Mock(spec=StartupDB)
        startup.id = uuid4()
        startup.name = "Test"
        startup.sector = "Tech"
        startup.stage = "Seed"
        startup.description = ""
        startup.website = ""
        startup.team_size = 1
        startup.monthly_revenue = 0
        
        vc = Mock(spec=VCDB)
        vc.id = uuid4()
        vc.firm_name = "VC"
        vc.website = ""
        vc.thesis = ""
        vc.check_size_min = 0
        vc.check_size_max = 0
        vc.sectors = []
        vc.stages = []
        
        match = Mock(spec=MatchDB)
        match.id = uuid4()
        match.score = 0.5
        match.reasons = ["A" * 1000]  # Very long reason
        match.startup = startup
        match.vc = vc
        
        # Execute
        result = repository._to_domain(match)
        
        # Verify
        assert result.reasons[0] == "A" * 1000
    
    def test_score_boundaries(self, repository, mock_session):
        """Test score boundary values (0.0 and 1.0)."""
        # Setup for score = 0.0
        match_id = uuid4()
        mock_query = Mock()
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = Mock(spec=MatchDB)
        mock_query.options.return_value = mock_query
        mock_session.query.return_value = mock_query
        
        # Test score = 0.0
        result = repository.update_score(match_id, 0.0)
        assert mock_query.first.return_value.score == 0.0
        
        # Test score = 1.0
        result = repository.update_score(match_id, 1.0)
        assert mock_query.first.return_value.score == 1.0
    
    def test_concurrent_modification(self, repository, mock_session):
        """Test behavior with concurrent modifications."""
        # This is a simplified test - in reality, you'd need proper transaction isolation
        match_id = uuid4()
        
        # Setup mock DB match
        startup = Mock(spec=StartupDB)
        startup.id = uuid4()
        startup.name = "Test"
        startup.sector = "Tech"
        startup.stage = "Seed"
        startup.description = "Test"
        startup.website = "https://test.com"
        startup.team_size = 1
        startup.monthly_revenue = 0
        
        vc = Mock(spec=VCDB)
        vc.id = uuid4()
        vc.firm_name = "VC"
        vc.website = "https://vc.com"
        vc.thesis = "Test"
        vc.check_size_min = 100000
        vc.check_size_max = 1000000
        vc.sectors = ["Tech"]
        vc.stages = ["Seed"]
        
        match_db = Mock(spec=MatchDB)
        match_db.id = match_id
        match_db.score = 0.8
        match_db.reasons = ["Test"]
        match_db.startup = startup
        match_db.vc = vc
        
        # Create two separate mock queries
        mock_query1 = Mock()
        mock_query1.options.return_value = mock_query1
        mock_query1.filter_by.return_value = mock_query1
        mock_query1.first.return_value = match_db
        
        mock_query2 = Mock()
        mock_query2.options.return_value = mock_query2
        mock_query2.filter_by.return_value = mock_query2
        mock_query2.first.return_value = None
        
        # Configure session to return different queries
        mock_session.query.side_effect = [mock_query1, mock_query2]
        
        # First call succeeds
        result1 = repository.get(match_id)
        assert result1 is not None
        assert result1.id == match_id
        
        # Second call fails (match was deleted)
        result2 = repository.get(match_id)
        assert result2 is None
    
    def test_get_top_matches_empty_result(self, repository, mock_session):
        """Test getting top matches when no matches exist."""
        # Setup
        startup_id = uuid4()
        mock_query = Mock()
        mock_query.options.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = []
        mock_session.query.return_value = mock_query
        
        # Execute
        result = repository.get_top_matches_for_startup(startup_id, limit=10)
        
        # Verify
        assert result == []
        
    def test_negative_score_handling(self, repository, mock_session):
        """Test handling of negative scores (edge case)."""
        # Setup
        match_id = uuid4()
        mock_match = Mock(spec=MatchDB)
        mock_query = Mock()
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = mock_match
        mock_query.options.return_value = mock_query
        mock_session.query.return_value = mock_query
        
        # Execute with negative score
        result = repository.update_score(match_id, -0.5)
        
        # Verify the score was set even though it's negative
        assert mock_match.score == -0.5
        mock_session.commit.assert_called_once()
    
    def test_list_with_zero_limit(self, repository, mock_session):
        """Test listing with limit of 0."""
        # Setup
        mock_query = Mock()
        mock_query.options.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = []
        mock_session.query.return_value = mock_query
        
        # Execute
        result = repository.list(limit=0, offset=0)
        
        # Verify
        mock_query.limit.assert_called_with(0)
        assert result == []