"""Comprehensive tests for startup repository."""

import pytest
import uuid
from datetime import datetime
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker
import asyncio

from src.database.setup import Base
from src.database.models.startup import Startup as StartupModel
from src.database.repositories.startup_repository import (
    PostgresStartupRepository,
    AsyncPostgresStartupRepository
)
from src.core.models.startup import Startup
from src.core.repositories.startup_repository import StartupRepository
from tests.test_db_utils import init_test_db_with_migrations


class TestPostgresStartupRepository:
    """Test PostgreSQL startup repository."""
    
    @pytest.fixture
    def engine(self):
        """Create test database engine."""
        engine = create_engine("sqlite:///:memory:")
        init_test_db_with_migrations(engine)
        return engine
    
    @pytest.fixture
    def session(self, engine):
        """Create test database session."""
        SessionLocal = sessionmaker(bind=engine)
        session = SessionLocal()
        yield session
        session.close()
    
    @pytest.fixture
    def repository(self, session):
        """Create repository instance."""
        return PostgresStartupRepository(session)
    
    def test_create_startup(self, repository, session):
        """Test creating a startup."""
        startup = Startup(
            id=uuid.uuid4(),
            name="Test Startup",
            sector="AI/ML",
            stage="Series A",
            description="Test description",
            website="https://test.com",
            team_size=10,
            monthly_revenue=50000.0
        )
        
        created = repository.create(startup)
        
        assert created.id == startup.id
        assert created.name == startup.name
        assert created.sector == startup.sector
        assert created.stage == startup.stage
        assert created.description == startup.description
        assert created.website == startup.website
        assert created.team_size == startup.team_size
        assert created.monthly_revenue == startup.monthly_revenue
        
        # Verify it's in the database
        db_startup = session.query(StartupModel).filter_by(id=startup.id).first()
        assert db_startup is not None
        assert db_startup.name == startup.name
    
    def test_get_startup_by_id(self, repository, session):
        """Test getting a startup by ID."""
        startup_id = uuid.uuid4()
        startup_model = StartupModel(
            id=startup_id,
            name="Get Test",
            sector="FinTech",
            stage="Seed"
        )
        session.add(startup_model)
        session.commit()
        
        startup = repository.get(startup_id)
        
        assert startup is not None
        assert startup.id == startup_id
        assert startup.name == "Get Test"
        assert startup.sector == "FinTech"
        assert startup.stage == "Seed"
    
    def test_get_nonexistent_startup(self, repository):
        """Test getting a startup that doesn't exist."""
        startup = repository.get(uuid.uuid4())
        assert startup is None
    
    def test_list_startups(self, repository, session):
        """Test listing all startups."""
        # Create multiple startups
        startups = []
        for i in range(3):
            startup = StartupModel(
                id=uuid.uuid4(),
                name=f"Startup {i}",
                sector="SaaS",
                stage="Seed"
            )
            startups.append(startup)
            session.add(startup)
        session.commit()
        
        result = repository.list()
        
        assert len(result) == 3
        names = {s.name for s in result}
        assert names == {"Startup 0", "Startup 1", "Startup 2"}
    
    def test_list_with_limit(self, repository, session):
        """Test listing startups with limit."""
        # Create multiple startups
        for i in range(5):
            startup = StartupModel(
                id=uuid.uuid4(),
                name=f"Startup {i}",
                sector="SaaS",
                stage="Seed"
            )
            session.add(startup)
        session.commit()
        
        result = repository.list(limit=3)
        
        assert len(result) == 3
    
    def test_list_with_offset(self, repository, session):
        """Test listing startups with offset."""
        # Create multiple startups
        for i in range(5):
            startup = StartupModel(
                id=uuid.uuid4(),
                name=f"Startup {i}",
                sector="SaaS",
                stage="Seed"
            )
            session.add(startup)
        session.commit()
        
        result = repository.list(offset=2, limit=2)
        
        assert len(result) == 2
    
    def test_update_startup(self, repository, session):
        """Test updating a startup."""
        startup_id = uuid.uuid4()
        startup_model = StartupModel(
            id=startup_id,
            name="Original Name",
            sector="AI/ML",
            stage="Seed"
        )
        session.add(startup_model)
        session.commit()
        
        # Update the startup
        updated = Startup(
            id=startup_id,
            name="Updated Name",
            sector="FinTech",
            stage="Series A",
            description="New description",
            website="https://updated.com",
            team_size=25,
            monthly_revenue=100000.0
        )
        
        result = repository.update(startup_id, updated)
        
        assert result is not None
        assert result.name == "Updated Name"
        assert result.sector == "FinTech"
        assert result.stage == "Series A"
        assert result.description == "New description"
        assert result.website == "https://updated.com"
        assert result.team_size == 25
        assert result.monthly_revenue == 100000.0
        
        # Verify in database
        db_startup = session.query(StartupModel).filter_by(id=startup_id).first()
        assert db_startup.name == "Updated Name"
        assert db_startup.sector == "FinTech"
    
    def test_update_nonexistent_startup(self, repository):
        """Test updating a startup that doesn't exist."""
        startup = Startup(
            id=uuid.uuid4(),
            name="Doesn't Matter",
            sector="AI/ML",
            stage="Seed"
        )
        
        result = repository.update(uuid.uuid4(), startup)
        assert result is None
    
    def test_delete_startup(self, repository, session):
        """Test deleting a startup."""
        startup_id = uuid.uuid4()
        startup_model = StartupModel(
            id=startup_id,
            name="To Delete",
            sector="AI/ML",
            stage="Seed"
        )
        session.add(startup_model)
        session.commit()
        
        # Delete the startup
        result = repository.delete(startup_id)
        
        assert result is True
        
        # Verify it's gone from database
        db_startup = session.query(StartupModel).filter_by(id=startup_id).first()
        assert db_startup is None
    
    def test_delete_nonexistent_startup(self, repository):
        """Test deleting a startup that doesn't exist."""
        result = repository.delete(uuid.uuid4())
        assert result is False
    
    def test_search_by_sector(self, repository, session):
        """Test searching startups by sector."""
        # Create startups with different sectors
        sectors = ["AI/ML", "FinTech", "AI/ML", "SaaS"]
        for i, sector in enumerate(sectors):
            startup = StartupModel(
                id=uuid.uuid4(),
                name=f"Startup {i}",
                sector=sector,
                stage="Seed"
            )
            session.add(startup)
        session.commit()
        
        result = repository.search_by_sector("AI/ML")
        
        assert len(result) == 2
        for startup in result:
            assert startup.sector == "AI/ML"
    
    def test_search_by_stage(self, repository, session):
        """Test searching startups by stage."""
        # Create startups with different stages
        stages = ["Seed", "Series A", "Seed", "Series B"]
        for i, stage in enumerate(stages):
            startup = StartupModel(
                id=uuid.uuid4(),
                name=f"Startup {i}",
                sector="AI/ML",
                stage=stage
            )
            session.add(startup)
        session.commit()
        
        result = repository.search_by_stage("Seed")
        
        assert len(result) == 2
        for startup in result:
            assert startup.stage == "Seed"
    
    def test_search_with_multiple_criteria(self, repository, session):
        """Test searching startups with multiple criteria."""
        # Create diverse startups
        startups_data = [
            ("AI Startup", "AI/ML", "Seed"),
            ("Fintech Startup", "FinTech", "Series A"),
            ("AI Series A", "AI/ML", "Series A"),
            ("SaaS Seed", "SaaS", "Seed")
        ]
        
        for name, sector, stage in startups_data:
            startup = StartupModel(
                id=uuid.uuid4(),
                name=name,
                sector=sector,
                stage=stage
            )
            session.add(startup)
        session.commit()
        
        # Search for AI/ML startups in Series A
        ai_sector_results = repository.search_by_sector("AI/ML")
        series_a_results = repository.search_by_stage("Series A")
        
        # Find intersection manually (in real app, you'd have a combined search method)
        ai_series_a = [s for s in ai_sector_results if s.stage == "Series A"]
        
        assert len(ai_series_a) == 1
        assert ai_series_a[0].name == "AI Series A"


class TestAsyncPostgresStartupRepository:
    """Test async PostgreSQL startup repository."""
    
    @pytest.fixture
    def async_engine(self):
        """Create async test database engine."""
        engine = create_async_engine("sqlite+aiosqlite:///:memory:")
        return engine
    
    @pytest.fixture
    async def async_session(self, async_engine):
        """Create async test database session."""
        from tests.test_db_utils import init_async_test_db_with_migrations
        await init_async_test_db_with_migrations(async_engine)
        
        AsyncSessionLocal = async_sessionmaker(async_engine)
        async with AsyncSessionLocal() as session:
            yield session
    
    @pytest.fixture
    def async_repository(self, async_session):
        """Create async repository instance."""
        return AsyncPostgresStartupRepository(async_session)
    
    @pytest.mark.asyncio
    async def test_async_create_startup(self, async_repository, async_session):
        """Test creating a startup asynchronously."""
        startup = Startup(
            id=uuid.uuid4(),
            name="Async Test Startup",
            sector="AI/ML",
            stage="Series A"
        )
        
        created = await async_repository.create(startup)
        
        assert created.id == startup.id
        assert created.name == startup.name
        assert created.sector == startup.sector
        assert created.stage == startup.stage
    
    @pytest.mark.asyncio
    async def test_async_get_startup(self, async_repository, async_session):
        """Test getting a startup asynchronously."""
        startup_id = uuid.uuid4()
        startup_model = StartupModel(
            id=startup_id,
            name="Async Get Test",
            sector="FinTech",
            stage="Seed"
        )
        async_session.add(startup_model)
        await async_session.commit()
        
        startup = await async_repository.get(startup_id)
        
        assert startup is not None
        assert startup.id == startup_id
        assert startup.name == "Async Get Test"
    
    @pytest.mark.asyncio
    async def test_async_list_startups(self, async_repository, async_session):
        """Test listing startups asynchronously."""
        for i in range(3):
            startup = StartupModel(
                id=uuid.uuid4(),
                name=f"Async Startup {i}",
                sector="SaaS",
                stage="Seed"
            )
            async_session.add(startup)
        await async_session.commit()
        
        result = await async_repository.list()
        
        assert len(result) == 3
    
    @pytest.mark.asyncio
    async def test_async_update_startup(self, async_repository, async_session):
        """Test updating a startup asynchronously."""
        startup_id = uuid.uuid4()
        startup_model = StartupModel(
            id=startup_id,
            name="Original Async",
            sector="AI/ML",
            stage="Seed"
        )
        async_session.add(startup_model)
        await async_session.commit()
        
        updated = Startup(
            id=startup_id,
            name="Updated Async",
            sector="FinTech",
            stage="Series A"
        )
        
        result = await async_repository.update(updated)
        
        assert result is not None
        assert result.name == "Updated Async"
        assert result.sector == "FinTech"
    
    @pytest.mark.asyncio
    async def test_async_delete_startup(self, async_repository, async_session):
        """Test deleting a startup asynchronously."""
        startup_id = uuid.uuid4()
        startup_model = StartupModel(
            id=startup_id,
            name="To Delete Async",
            sector="AI/ML",
            stage="Seed"
        )
        async_session.add(startup_model)
        await async_session.commit()
        
        result = await async_repository.delete(startup_id)
        
        assert result is True