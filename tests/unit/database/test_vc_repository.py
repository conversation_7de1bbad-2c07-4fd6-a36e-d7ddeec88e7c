"""Comprehensive tests for VC repository."""

import pytest
import uuid
from datetime import datetime
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker

from src.database.setup import Base
from tests.test_db_utils import init_test_db_with_migrations
from src.database.models.vc import VC as VCModel
from src.database.repositories.vc_repository import (
    PostgresVCRepository,
    AsyncPostgresVCRepository
)
from src.core.models.vc import VC
from src.core.repositories.vc_repository import VCRepository


class TestPostgresVCRepository:
    """Test PostgreSQL VC repository."""
    
    @pytest.fixture
    def engine(self):
        """Create test database engine."""
        engine = create_engine("sqlite:///:memory:")
        init_test_db_with_migrations(engine)
        return engine
    
    @pytest.fixture
    def session(self, engine):
        """Create test database session."""
        SessionLocal = sessionmaker(bind=engine)
        session = SessionLocal()
        yield session
        session.close()
    
    @pytest.fixture
    def repository(self, session):
        """Create repository instance."""
        return PostgresVCRepository(session)
    
    def test_create_vc(self, repository, session):
        """Test creating a VC."""
        vc = VC(
            id=uuid.uuid4(),
            firm_name="Test Ventures",
            website="https://testventures.com",
            thesis="We invest in AI startups",
            check_size_min=100000.0,
            check_size_max=500000.0,
            sectors=["AI/ML", "SaaS"],
            stages=["Seed", "Series A"]
        )
        
        created = repository.create(vc)
        
        assert created.id == vc.id
        assert created.firm_name == vc.firm_name
        assert created.website == vc.website
        assert created.thesis == vc.thesis
        assert created.check_size_min == vc.check_size_min
        assert created.check_size_max == vc.check_size_max
        assert created.sectors == vc.sectors
        assert created.stages == vc.stages
        
        # Verify it's in the database
        db_vc = session.query(VCModel).filter_by(id=vc.id).first()
        assert db_vc is not None
        assert db_vc.firm_name == vc.firm_name
    
    def test_get_vc_by_id(self, repository, session):
        """Test getting a VC by ID."""
        vc_id = uuid.uuid4()
        vc_model = VCModel(
            id=vc_id,
            firm_name="Get Test VC",
            thesis="Test thesis",
            check_size_min=50000.0,
            check_size_max=200000.0
        )
        session.add(vc_model)
        session.commit()
        
        vc = repository.get(vc_id)
        
        assert vc is not None
        assert vc.id == vc_id
        assert vc.firm_name == "Get Test VC"
        assert vc.thesis == "Test thesis"
        assert vc.check_size_min == 50000.0
        assert vc.check_size_max == 200000.0
    
    def test_get_nonexistent_vc(self, repository):
        """Test getting a VC that doesn't exist."""
        vc = repository.get(uuid.uuid4())
        assert vc is None
    
    def test_list_vcs(self, repository, session):
        """Test listing all VCs."""
        # Create multiple VCs
        vcs = []
        for i in range(3):
            vc = VCModel(
                id=uuid.uuid4(),
                firm_name=f"VC {i}",
                thesis="Invest in tech",
                check_size_min=100000.0,
                check_size_max=500000.0
            )
            vcs.append(vc)
            session.add(vc)
        session.commit()
        
        result = repository.list()
        
        assert len(result) == 3
        names = {v.firm_name for v in result}
        assert names == {"VC 0", "VC 1", "VC 2"}
    
    def test_list_with_limit(self, repository, session):
        """Test listing VCs with limit."""
        # Create multiple VCs
        for i in range(5):
            vc = VCModel(
                id=uuid.uuid4(),
                firm_name=f"VC {i}",
                thesis="Test",
                check_size_min=100000.0,
                check_size_max=500000.0
            )
            session.add(vc)
        session.commit()
        
        result = repository.list(limit=3)
        
        assert len(result) == 3
    
    def test_list_with_offset(self, repository, session):
        """Test listing VCs with offset."""
        # Create multiple VCs
        for i in range(5):
            vc = VCModel(
                id=uuid.uuid4(),
                firm_name=f"VC {i}",
                thesis="Test",
                check_size_min=100000.0,
                check_size_max=500000.0
            )
            session.add(vc)
        session.commit()
        
        result = repository.list(offset=2, limit=2)
        
        assert len(result) == 2
    
    def test_update_vc(self, repository, session):
        """Test updating a VC."""
        vc_id = uuid.uuid4()
        vc_model = VCModel(
            id=vc_id,
            firm_name="Original VC",
            thesis="Original thesis",
            check_size_min=100000.0,
            check_size_max=500000.0
        )
        session.add(vc_model)
        session.commit()
        
        # Update the VC
        updated = VC(
            id=vc_id,
            firm_name="Updated VC",
            website="https://updated.com",
            thesis="Updated thesis",
            check_size_min=200000.0,
            check_size_max=1000000.0,
            sectors=["FinTech", "AI/ML"],
            stages=["Series A", "Series B"]
        )
        
        result = repository.update(vc_id, updated)
        
        assert result is not None
        assert result.firm_name == "Updated VC"
        assert result.website == "https://updated.com"
        assert result.thesis == "Updated thesis"
        assert result.check_size_min == 200000.0
        assert result.check_size_max == 1000000.0
        assert result.sectors == ["FinTech", "AI/ML"]
        assert result.stages == ["Series A", "Series B"]
        
        # Verify in database
        db_vc = session.query(VCModel).filter_by(id=vc_id).first()
        assert db_vc.firm_name == "Updated VC"
        assert db_vc.thesis == "Updated thesis"
    
    def test_update_nonexistent_vc(self, repository):
        """Test updating a VC that doesn't exist."""
        vc = VC(
            id=uuid.uuid4(),
            firm_name="Doesn't Matter",
            thesis="Test",
            check_size_min=100000.0,
            check_size_max=500000.0
        )
        
        result = repository.update(uuid.uuid4(), vc)
        assert result is None
    
    def test_delete_vc(self, repository, session):
        """Test deleting a VC."""
        vc_id = uuid.uuid4()
        vc_model = VCModel(
            id=vc_id,
            firm_name="To Delete",
            thesis="Delete test",
            check_size_min=100000.0,
            check_size_max=500000.0
        )
        session.add(vc_model)
        session.commit()
        
        # Delete the VC
        result = repository.delete(vc_id)
        
        assert result is True
        
        # Verify it's gone from database
        db_vc = session.query(VCModel).filter_by(id=vc_id).first()
        assert db_vc is None
    
    def test_delete_nonexistent_vc(self, repository):
        """Test deleting a VC that doesn't exist."""
        result = repository.delete(uuid.uuid4())
        assert result is False
    
    def test_search_by_sector_focus(self, repository, session):
        """Test searching VCs by sector focus."""
        # Create VCs with different sector focuses
        vcs_data = [
            ("AI Ventures", ["AI/ML", "Data"]),
            ("Fintech Capital", ["FinTech", "Payments"]),
            ("AI & Fintech", ["AI/ML", "FinTech"]),
            ("SaaS Fund", ["SaaS", "Enterprise"])
        ]
        
        for firm_name, sectors in vcs_data:
            vc = VCModel(
                id=uuid.uuid4(),
                firm_name=firm_name,
                thesis="Test",
                check_size_min=100000.0,
                check_size_max=500000.0,
                sectors=sectors
            )
            session.add(vc)
        session.commit()
        
        result = repository.search_by_sector_focus("AI/ML")
        
        assert len(result) == 2
        firm_names = {v.firm_name for v in result}
        assert firm_names == {"AI Ventures", "AI & Fintech"}
    
    def test_search_by_stage_focus(self, repository, session):
        """Test searching VCs by stage focus."""
        # Create VCs with different stage focuses
        vcs_data = [
            ("Early Stage", ["Pre-seed", "Seed"]),
            ("Growth Capital", ["Series B", "Series C"]),
            ("Seed & A", ["Seed", "Series A"]),
            ("Late Stage", ["Series C", "Series D"])
        ]
        
        for firm_name, stages in vcs_data:
            vc = VCModel(
                id=uuid.uuid4(),
                firm_name=firm_name,
                thesis="Test",
                check_size_min=100000.0,
                check_size_max=500000.0,
                stages=stages
            )
            session.add(vc)
        session.commit()
        
        result = repository.search_by_stage_focus("Seed")
        
        assert len(result) == 2
        firm_names = {v.firm_name for v in result}
        assert firm_names == {"Early Stage", "Seed & A"}
    
    def test_search_by_check_size_range(self, repository, session):
        """Test searching VCs by check size range."""
        # Create VCs with different check sizes
        vcs_data = [
            ("Small Checks", 10000.0, 50000.0),
            ("Medium Checks", 100000.0, 500000.0),
            ("Large Checks", 500000.0, 2000000.0),
            ("Flexible Checks", 50000.0, 1000000.0)
        ]
        
        for firm_name, min_check, max_check in vcs_data:
            vc = VCModel(
                id=uuid.uuid4(),
                firm_name=firm_name,
                thesis="Test",
                check_size_min=min_check,
                check_size_max=max_check
            )
            session.add(vc)
        session.commit()
        
        # Find VCs that could write a 250k check
        result = repository.search_by_check_size_range(250000.0)
        
        assert len(result) == 2
        firm_names = {v.firm_name for v in result}
        assert firm_names == {"Medium Checks", "Flexible Checks"}
    
    def test_get_active_vcs(self, repository, session):
        """Test getting active VCs (those with defined thesis and check sizes)."""
        # Create mix of complete and incomplete VCs
        complete_vc = VCModel(
            id=uuid.uuid4(),
            firm_name="Complete VC",
            thesis="We invest in AI",
            check_size_min=100000.0,
            check_size_max=500000.0,
            sectors=["AI/ML"],
            stages=["Seed"]
        )
        
        incomplete_vc1 = VCModel(
            id=uuid.uuid4(),
            firm_name="No Thesis VC",
            thesis=None,
            check_size_min=100000.0,
            check_size_max=500000.0
        )
        
        incomplete_vc2 = VCModel(
            id=uuid.uuid4(),
            firm_name="No Check Size VC",
            thesis="We invest",
            check_size_min=None,
            check_size_max=None
        )
        
        session.add_all([complete_vc, incomplete_vc1, incomplete_vc2])
        session.commit()
        
        result = repository.get_active()
        
        # Only the complete VC should be considered active
        assert len(result) == 1
        assert result[0].firm_name == "Complete VC"


class TestAsyncPostgresVCRepository:
    """Test async PostgreSQL VC repository."""
    
    @pytest.fixture
    def async_engine(self):
        """Create async test database engine."""
        engine = create_async_engine("sqlite+aiosqlite:///:memory:")
        return engine
    
    @pytest.fixture
    async def async_session(self, async_engine):
        """Create async test database session."""
        async with async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        AsyncSessionLocal = async_sessionmaker(async_engine)
        async with AsyncSessionLocal() as session:
            yield session
    
    @pytest.fixture
    def async_repository(self, async_session):
        """Create async repository instance."""
        return AsyncPostgresVCRepository(async_session)
    
    @pytest.mark.asyncio
    async def test_async_create_vc(self, async_repository, async_session):
        """Test creating a VC asynchronously."""
        vc = VC(
            id=uuid.uuid4(),
            firm_name="Async Test VC",
            thesis="Async investing",
            check_size_min=100000.0,
            check_size_max=500000.0
        )
        
        created = await async_repository.create(vc)
        
        assert created.id == vc.id
        assert created.firm_name == vc.firm_name
        assert created.thesis == vc.thesis
    
    @pytest.mark.asyncio
    async def test_async_get_vc(self, async_repository, async_session):
        """Test getting a VC asynchronously."""
        vc_id = uuid.uuid4()
        vc_model = VCModel(
            id=vc_id,
            firm_name="Async Get Test",
            thesis="Test",
            check_size_min=100000.0,
            check_size_max=500000.0
        )
        async_session.add(vc_model)
        await async_session.commit()
        
        vc = await async_repository.get(vc_id)
        
        assert vc is not None
        assert vc.id == vc_id
        assert vc.firm_name == "Async Get Test"
    
    @pytest.mark.asyncio
    async def test_async_list_vcs(self, async_repository, async_session):
        """Test listing VCs asynchronously."""
        for i in range(3):
            vc = VCModel(
                id=uuid.uuid4(),
                firm_name=f"Async VC {i}",
                thesis="Test",
                check_size_min=100000.0,
                check_size_max=500000.0
            )
            async_session.add(vc)
        await async_session.commit()
        
        result = await async_repository.list()
        
        assert len(result) == 3
    
    @pytest.mark.asyncio
    async def test_async_update_vc(self, async_repository, async_session):
        """Test updating a VC asynchronously."""
        vc_id = uuid.uuid4()
        vc_model = VCModel(
            id=vc_id,
            firm_name="Original Async",
            thesis="Original",
            check_size_min=100000.0,
            check_size_max=500000.0
        )
        async_session.add(vc_model)
        await async_session.commit()
        
        updated = VC(
            id=vc_id,
            firm_name="Updated Async",
            thesis="Updated",
            check_size_min=200000.0,
            check_size_max=1000000.0
        )
        
        result = await async_repository.update(vc_id, updated)
        
        assert result is not None
        assert result.firm_name == "Updated Async"
        assert result.check_size_min == 200000.0
    
    @pytest.mark.asyncio
    async def test_async_delete_vc(self, async_repository, async_session):
        """Test deleting a VC asynchronously."""
        vc_id = uuid.uuid4()
        vc_model = VCModel(
            id=vc_id,
            firm_name="To Delete Async",
            thesis="Test",
            check_size_min=100000.0,
            check_size_max=500000.0
        )
        async_session.add(vc_model)
        await async_session.commit()
        
        result = await async_repository.delete(vc_id)
        
        assert result is True