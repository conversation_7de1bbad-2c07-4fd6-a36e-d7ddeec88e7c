"""Test database setup and configuration."""

import pytest
from sqlalchemy import create_engine, inspect
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from src.database.setup import Base, get_db, get_async_db, init_db


class TestDatabaseSetup:
    """Test database configuration and setup."""
    
    def test_base_metadata_exists(self):
        """Test that SQLAlchemy Base is properly configured."""
        assert hasattr(Base, 'metadata')
        assert hasattr(Base, 'registry')
    
    def test_sync_database_url_configuration(self):
        """Test synchronous database URL is properly configured."""
        from src.core.config import settings
        
        # Should have a database URL
        assert hasattr(settings, 'database_url')
        assert settings.database_url is not None
        assert 'postgresql' in settings.database_url or 'sqlite' in settings.database_url
    
    def test_async_database_url_configuration(self):
        """Test async database URL is properly configured."""
        from src.core.config import settings
        
        # Should have an async database URL
        assert hasattr(settings, 'async_database_url')
        if settings.database_url.startswith("postgresql://"):
            assert 'postgresql+asyncpg' in settings.async_database_url
        elif settings.database_url.startswith("sqlite://"):
            assert 'sqlite+aiosqlite' in settings.async_database_url
    
    def test_get_db_returns_session(self):
        """Test get_db dependency returns a database session."""
        db_gen = get_db()
        db = next(db_gen)
        
        # Should be a session
        assert hasattr(db, 'query')
        assert hasattr(db, 'add')
        assert hasattr(db, 'commit')
        assert hasattr(db, 'rollback')
        
        # Cleanup
        try:
            next(db_gen)
        except StopIteration:
            pass
    
    @pytest.mark.asyncio
    async def test_get_async_db_returns_async_session(self):
        """Test get_async_db returns an async session."""
        async for db in get_async_db():
            # Should be an AsyncSession
            assert isinstance(db, AsyncSession)
            assert hasattr(db, 'execute')
            assert hasattr(db, 'add')
            assert hasattr(db, 'commit')
            assert hasattr(db, 'rollback')
            break
    
    def test_init_db_creates_tables(self):
        """Test init_db creates all tables."""
        # Create in-memory SQLite engine for testing
        engine = create_engine("sqlite:///:memory:")
        
        # Initialize database
        init_db(engine)
        
        # Check tables were created
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        # Should have our main tables
        assert 'startups' in tables
        assert 'vcs' in tables
        assert 'matches' in tables
    
    @pytest.mark.asyncio
    async def test_async_init_db_creates_tables(self):
        """Test async init_db creates all tables."""
        from src.database.setup import async_init_db
        
        # Create in-memory SQLite engine for testing
        engine = create_async_engine("sqlite+aiosqlite:///:memory:")
        
        # Initialize database
        await async_init_db(engine)
        
        # Check tables were created
        async with engine.begin() as conn:
            def sync_inspect(connection):
                inspector = inspect(connection)
                return inspector.get_table_names()
            
            tables = await conn.run_sync(sync_inspect)
        
        # Should have our main tables
        assert 'startups' in tables
        assert 'vcs' in tables
        assert 'matches' in tables
        
        await engine.dispose()
    
    def test_database_session_lifecycle(self):
        """Test database session can be created and closed properly."""
        from src.database.setup import get_session_factory
        
        # Get session factory
        SessionLocal = get_session_factory()
        
        # Create session
        session = SessionLocal()
        
        # Verify it's a valid session
        assert hasattr(session, 'query')
        assert hasattr(session, 'close')
        
        # Should be able to close without error
        session.close()
    
    @pytest.mark.asyncio
    async def test_async_database_session_lifecycle(self):
        """Test async database session lifecycle."""
        from src.database.setup import get_async_session_factory
        
        # Get async session factory
        AsyncSessionLocal = get_async_session_factory()
        
        # Create session
        async with AsyncSessionLocal() as session:
            # Verify it's a valid async session
            assert isinstance(session, AsyncSession)
            assert hasattr(session, 'execute')
            
        # Session should be closed after context manager