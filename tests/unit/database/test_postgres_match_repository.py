"""Test PostgreSQL implementation of MatchRepository."""

import pytest
import uuid
from datetime import datetime
from unittest.mock import Mock, MagicMock, AsyncMock
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession

from src.core.models.match import Match as MatchDomain
from src.core.models.startup import Startup as StartupDomain
from src.core.models.vc import VC as VCDomain
from src.database.models.match import Match as MatchDB
from src.database.models.startup import Startup as StartupDB
from src.database.models.vc import VC as VCDB
from src.database.repositories.match_repository import (
    PostgresMatchRepository,
    AsyncPostgresMatchRepository
)


class TestPostgresMatchRepository:
    """Test PostgreSQL match repository."""
    
    @pytest.fixture
    def mock_session(self):
        """Create mock database session."""
        session = Mock(spec=Session)
        return session
    
    @pytest.fixture
    def repository(self, mock_session):
        """Create repository instance."""
        return PostgresMatchRepository(mock_session)
    
    @pytest.fixture
    def sample_startup(self):
        """Create sample startup."""
        startup = StartupDomain(
            name="Test Startup",
            sector="AI/ML",
            stage="Series A",
            description="A test startup",
            website="https://test.com",
            team_size=10,
            monthly_revenue=50000.0
        )
        startup.id = uuid.uuid4()
        return startup
    
    @pytest.fixture
    def sample_vc(self):
        """Create sample VC."""
        vc = VCDomain(
            firm_name="Test Ventures",
            website="https://testventures.com",
            thesis="We invest in AI companies",
            check_size_min=100000.0,
            check_size_max=500000.0,
            sectors=["AI/ML"],
            stages=["Series A"]
        )
        vc.id = uuid.uuid4()
        return vc
    
    @pytest.fixture
    def sample_match(self, sample_startup, sample_vc):
        """Create sample match domain model."""
        match = MatchDomain(
            startup=sample_startup,
            vc=sample_vc,
            score=0.85,
            reasons=["Strong AI focus", "Right stage"]
        )
        match.id = uuid.uuid4()
        return match
    
    @pytest.fixture
    def sample_db_match(self, sample_match, sample_startup, sample_vc):
        """Create sample database match."""
        db_match = MatchDB(
            id=sample_match.id,
            startup_id=sample_startup.id,
            vc_id=sample_vc.id,
            score=sample_match.score,
            reasons=sample_match.reasons,
            created_at=datetime.utcnow()
        )
        # Mock the relationships
        db_match.startup = Mock(spec=StartupDB)
        db_match.startup.id = sample_startup.id
        db_match.startup.name = sample_startup.name
        db_match.startup.sector = sample_startup.sector
        db_match.startup.stage = sample_startup.stage
        db_match.startup.description = sample_startup.description
        db_match.startup.website = sample_startup.website
        db_match.startup.team_size = sample_startup.team_size
        db_match.startup.monthly_revenue = sample_startup.monthly_revenue
        
        db_match.vc = Mock(spec=VCDB)
        db_match.vc.id = sample_vc.id
        db_match.vc.firm_name = sample_vc.firm_name
        db_match.vc.website = sample_vc.website
        db_match.vc.thesis = sample_vc.thesis
        db_match.vc.check_size_min = sample_vc.check_size_min
        db_match.vc.check_size_max = sample_vc.check_size_max
        db_match.vc.sectors = sample_vc.sectors
        db_match.vc.stages = sample_vc.stages
        
        return db_match
    
    async def test_create_match(self, repository, mock_session, sample_match, sample_db_match):
        """Test creating a new match."""
        # Mock the database operations
        mock_session.add = Mock()
        mock_session.commit = Mock()
        mock_session.refresh = Mock()
        
        # Configure refresh to populate the created_at and relationships
        def refresh_side_effect(obj):
            obj.created_at = sample_db_match.created_at
            obj.startup = sample_db_match.startup
            obj.vc = sample_db_match.vc
        
        mock_session.refresh.side_effect = refresh_side_effect
        
        # Create the match
        result = await repository.create(sample_match)
        
        # Verify database operations
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()
        
        # Verify result
        assert result.id == sample_match.id
        assert result.score == sample_match.score
        assert result.reasons == sample_match.reasons
    
    async def test_get_match(self, repository, mock_session, sample_match, sample_db_match):
        """Test getting a match by ID."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.options.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = sample_db_match
        
        # Get the match
        result = await repository.get(sample_match.id)
        
        # Verify query
        mock_session.query.assert_called_once_with(MatchDB)
        mock_query.filter_by.assert_called_once_with(id=sample_match.id)
        
        # Verify result
        assert result is not None
        assert result.id == sample_match.id
        assert result.score == sample_match.score
    
    async def test_get_match_not_found(self, repository, mock_session):
        """Test getting a non-existent match."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.options.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = None
        
        # Get the match
        result = await repository.get(uuid.uuid4())
        
        # Verify result
        assert result is None
    
    async def test_list_matches_by_startup(self, repository, mock_session, sample_startup, sample_db_match):
        """Test listing matches for a startup."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.options.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.all.return_value = [sample_db_match]
        
        # List matches
        result = await repository.list_by_startup(sample_startup.id)
        
        # Verify result
        assert len(result) == 1
        assert result[0].score == sample_db_match.score
    
    async def test_list_matches_by_vc(self, repository, mock_session, sample_vc, sample_db_match):
        """Test listing matches for a VC."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.options.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.all.return_value = [sample_db_match]
        
        # List matches
        result = await repository.list_by_vc(sample_vc.id)
        
        # Verify result
        assert len(result) == 1
        assert result[0].score == sample_db_match.score
    
    async def test_delete_match(self, repository, mock_session, sample_db_match):
        """Test deleting a match."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = sample_db_match
        mock_session.delete = Mock()
        mock_session.commit = Mock()
        
        # Delete the match
        result = await repository.delete(sample_db_match.id)
        
        # Verify database operations
        mock_session.delete.assert_called_once_with(sample_db_match)
        mock_session.commit.assert_called_once()
        
        # Verify result
        assert result is True
    
    async def test_delete_match_not_found(self, repository, mock_session):
        """Test deleting a non-existent match."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = None
        
        # Delete the match
        result = await repository.delete(uuid.uuid4())
        
        # Verify result
        assert result is False
    
    async def test_exists(self, repository, mock_session, sample_startup, sample_vc):
        """Test checking if a match exists."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = Mock()  # Any object means it exists
        
        # Check existence
        result = await repository.exists(sample_startup.id, sample_vc.id)
        
        # Verify result
        assert result is True
    
    async def test_update_score(self, repository, mock_session, sample_match, sample_db_match):
        """Test updating match score."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = sample_db_match
        mock_session.commit = Mock()
        mock_session.refresh = Mock()
        
        # Update score
        new_score = 0.95
        result = await repository.update_score(sample_match.id, new_score)
        
        # Verify database operations
        assert sample_db_match.score == new_score
        mock_session.commit.assert_called_once()
        
        # Verify result
        assert result is not None
        assert result.score == new_score


class TestAsyncPostgresMatchRepository:
    """Test async PostgreSQL match repository."""
    
    @pytest.fixture
    def mock_session(self):
        """Create mock async database session."""
        session = AsyncMock(spec=AsyncSession)
        return session
    
    @pytest.fixture
    def repository(self, mock_session):
        """Create repository instance."""
        return AsyncPostgresMatchRepository(mock_session)
    
    @pytest.fixture
    def sample_startup(self):
        """Create sample startup."""
        startup = StartupDomain(
            name="Async Startup",
            sector="SaaS",
            stage="Seed",
            description="An async startup",
            website="https://async.com",
            team_size=5,
            monthly_revenue=10000.0
        )
        startup.id = uuid.uuid4()
        return startup
    
    @pytest.fixture
    def sample_vc(self):
        """Create sample VC."""
        vc = VCDomain(
            firm_name="Async Capital",
            website="https://asynccapital.com",
            thesis="We invest in SaaS companies",
            check_size_min=50000.0,
            check_size_max=200000.0,
            sectors=["SaaS"],
            stages=["Seed"]
        )
        vc.id = uuid.uuid4()
        return vc
    
    @pytest.fixture
    def sample_match(self, sample_startup, sample_vc):
        """Create sample match domain model."""
        match = MatchDomain(
            startup=sample_startup,
            vc=sample_vc,
            score=0.75,
            reasons=["Good fit", "Right timing"]
        )
        match.id = uuid.uuid4()
        return match
    
    @pytest.fixture
    def sample_db_match(self, sample_match, sample_startup, sample_vc):
        """Create sample database match."""
        db_match = MatchDB(
            id=sample_match.id,
            startup_id=sample_startup.id,
            vc_id=sample_vc.id,
            score=sample_match.score,
            reasons=sample_match.reasons,
            created_at=datetime.utcnow()
        )
        # Mock the relationships
        db_match.startup = Mock(spec=StartupDB)
        db_match.startup.id = sample_startup.id
        db_match.startup.name = sample_startup.name
        db_match.startup.sector = sample_startup.sector
        db_match.startup.stage = sample_startup.stage
        db_match.startup.description = sample_startup.description
        db_match.startup.website = sample_startup.website
        db_match.startup.team_size = sample_startup.team_size
        db_match.startup.monthly_revenue = sample_startup.monthly_revenue
        
        db_match.vc = Mock(spec=VCDB)
        db_match.vc.id = sample_vc.id
        db_match.vc.firm_name = sample_vc.firm_name
        db_match.vc.website = sample_vc.website
        db_match.vc.thesis = sample_vc.thesis
        db_match.vc.check_size_min = sample_vc.check_size_min
        db_match.vc.check_size_max = sample_vc.check_size_max
        db_match.vc.sectors = sample_vc.sectors
        db_match.vc.stages = sample_vc.stages
        
        return db_match
    
    async def test_async_create_match(self, repository, mock_session, sample_match):
        """Test creating a new match asynchronously."""
        # Mock the database operations
        mock_session.add = Mock()
        mock_session.commit = AsyncMock()
        mock_session.refresh = AsyncMock()
        
        # Create the match
        result = await repository.create(sample_match)
        
        # Verify database operations
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()
        
        # Verify result
        assert result.id == sample_match.id
        assert result.score == sample_match.score
    
    async def test_async_get_match(self, repository, mock_session, sample_match, sample_db_match):
        """Test getting a match by ID asynchronously."""
        # Mock the execute result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_db_match
        mock_session.execute = AsyncMock(return_value=mock_result)
        
        # Get the match
        result = await repository.get(sample_match.id)
        
        # Verify query
        mock_session.execute.assert_called_once()
        
        # Verify result
        assert result is not None
        assert result.id == sample_match.id
        assert result.score == sample_match.score
    
    async def test_async_list_matches_by_startup(self, repository, mock_session, sample_startup, sample_db_match):
        """Test listing matches for a startup asynchronously."""
        # Mock the execute result
        mock_result = Mock()
        mock_scalars = Mock()
        mock_scalars.all.return_value = [sample_db_match]
        mock_result.scalars.return_value = mock_scalars
        mock_session.execute = AsyncMock(return_value=mock_result)
        
        # List matches
        result = await repository.list_by_startup(sample_startup.id)
        
        # Verify result
        assert len(result) == 1
        assert result[0].score == sample_db_match.score
    
    async def test_async_delete_match(self, repository, mock_session, sample_db_match):
        """Test deleting a match asynchronously."""
        # Mock the execute result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_db_match
        mock_session.execute = AsyncMock(return_value=mock_result)
        mock_session.delete = AsyncMock()
        mock_session.commit = AsyncMock()
        
        # Delete the match
        result = await repository.delete(sample_db_match.id)
        
        # Verify database operations
        mock_session.delete.assert_called_once()
        mock_session.commit.assert_called_once()
        
        # Verify result
        assert result is True