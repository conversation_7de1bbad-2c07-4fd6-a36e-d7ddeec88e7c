"""Comprehensive unit tests for PostgreSQL VC repository implementations."""

import pytest
import uuid
from datetime import datetime
from unittest.mock import <PERSON><PERSON>, <PERSON><PERSON><PERSON>, AsyncMock, patch
from sqlalchemy import create_engine
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError

from src.core.models.vc import VC as VCDomain
from src.database.models.vc import VC as VCDB
from src.database.repositories.vc_repository import (
    PostgresVCRepository,
    AsyncPostgresVCRepository
)


class TestPostgresVCRepository:
    """Comprehensive tests for PostgreSQL VC repository."""
    
    @pytest.fixture
    def mock_session(self):
        """Create mock database session."""
        session = Mock(spec=Session)
        # Mock the bind attribute with dialect
        session.bind = Mock()
        session.bind.dialect = Mock()
        session.bind.dialect.name = 'postgresql'
        return session
    
    @pytest.fixture
    def mock_sqlite_session(self):
        """Create mock SQLite database session."""
        session = Mock(spec=Session)
        # Mock the bind attribute with SQLite dialect
        session.bind = Mock()
        session.bind.dialect = Mock()
        session.bind.dialect.name = 'sqlite'
        return session
    
    @pytest.fixture
    def repository(self, mock_session):
        """Create repository instance."""
        return PostgresVCRepository(mock_session)
    
    @pytest.fixture
    def sqlite_repository(self, mock_sqlite_session):
        """Create repository instance with SQLite session."""
        return PostgresVCRepository(mock_sqlite_session)
    
    @pytest.fixture
    def sample_vc(self):
        """Create sample VC domain model."""
        vc = VCDomain(
            firm_name="Test Ventures",
            website="https://testventures.com",
            thesis="We invest in early-stage AI companies",
            check_size_min=100000.0,
            check_size_max=500000.0,
            sectors=["AI/ML", "FinTech"],
            stages=["Seed", "Series A"]
        )
        vc.id = uuid.uuid4()
        return vc
    
    @pytest.fixture
    def sample_db_vc(self, sample_vc):
        """Create sample database VC."""
        return VCDB(
            id=sample_vc.id,
            firm_name=sample_vc.firm_name,
            website=sample_vc.website,
            thesis=sample_vc.thesis,
            check_size_min=sample_vc.check_size_min,
            check_size_max=sample_vc.check_size_max,
            sectors=sample_vc.sectors,
            stages=sample_vc.stages,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
    
    @pytest.fixture
    def sample_db_vc_with_nulls(self):
        """Create sample database VC with null values."""
        vc_id = uuid.uuid4()
        return VCDB(
            id=vc_id,
            firm_name="Minimal VC",
            website=None,
            thesis=None,
            check_size_min=None,
            check_size_max=None,
            sectors=None,
            stages=None,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
    
    # CRUD Operations Tests
    
    def test_create_vc(self, repository, mock_session, sample_vc, sample_db_vc):
        """Test creating a new VC."""
        # Mock the database operations
        mock_session.add = Mock()
        mock_session.commit = Mock()
        mock_session.refresh = Mock()
        
        # Configure refresh to populate the created_at/updated_at
        def refresh_side_effect(obj):
            obj.created_at = sample_db_vc.created_at
            obj.updated_at = sample_db_vc.updated_at
        
        mock_session.refresh.side_effect = refresh_side_effect
        
        # Create the VC
        result = repository.create(sample_vc)
        
        # Verify database operations
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()
        
        # Verify result
        assert result.id == sample_vc.id
        assert result.firm_name == sample_vc.firm_name
        assert result.thesis == sample_vc.thesis
        assert result.check_size_min == sample_vc.check_size_min
        assert result.check_size_max == sample_vc.check_size_max
        assert result.sectors == sample_vc.sectors
        assert result.stages == sample_vc.stages
    
    def test_create_vc_with_database_error(self, repository, mock_session, sample_vc):
        """Test creating a VC when database error occurs."""
        mock_session.add = Mock()
        mock_session.commit = Mock(side_effect=SQLAlchemyError("Database error"))
        
        with pytest.raises(SQLAlchemyError):
            repository.create(sample_vc)
    
    def test_get_vc(self, repository, mock_session, sample_vc, sample_db_vc):
        """Test getting a VC by ID."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = sample_db_vc
        
        # Get the VC
        result = repository.get(sample_vc.id)
        
        # Verify query
        mock_session.query.assert_called_once_with(VCDB)
        mock_query.filter_by.assert_called_once_with(id=sample_vc.id)
        
        # Verify result
        assert result is not None
        assert result.id == sample_vc.id
        assert result.firm_name == sample_vc.firm_name
    
    def test_get_vc_not_found(self, repository, mock_session):
        """Test getting a non-existent VC."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = None
        
        # Get the VC
        result = repository.get(uuid.uuid4())
        
        # Verify result
        assert result is None
    
    def test_get_vc_with_nulls(self, repository, mock_session, sample_db_vc_with_nulls):
        """Test getting a VC with null values."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = sample_db_vc_with_nulls
        
        # Get the VC
        result = repository.get(sample_db_vc_with_nulls.id)
        
        # Verify result handles nulls properly
        assert result is not None
        assert result.firm_name == "Minimal VC"
        assert result.website == ""  # None converted to empty string
        assert result.thesis == ""  # None converted to empty string
        assert result.check_size_min == 0  # None converted to 0
        assert result.check_size_max == 0  # None converted to 0
        assert result.sectors == []  # None converted to empty list
        assert result.stages == []  # None converted to empty list
    
    def test_list_vcs(self, repository, mock_session, sample_db_vc):
        """Test listing VCs with pagination."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = [sample_db_vc]
        
        # List VCs
        result = repository.list(limit=10, offset=5)
        
        # Verify query
        mock_session.query.assert_called_once_with(VCDB)
        mock_query.offset.assert_called_once_with(5)
        mock_query.limit.assert_called_once_with(10)
        
        # Verify result
        assert len(result) == 1
        assert result[0].firm_name == sample_db_vc.firm_name
    
    def test_list_vcs_default_pagination(self, repository, mock_session):
        """Test listing VCs with default pagination."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = []
        
        # List VCs with defaults
        result = repository.list()
        
        # Verify default values
        mock_query.offset.assert_called_once_with(0)
        mock_query.limit.assert_called_once_with(100)
    
    def test_update_vc(self, repository, mock_session, sample_vc, sample_db_vc):
        """Test updating a VC."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = sample_db_vc
        mock_session.commit = Mock()
        mock_session.refresh = Mock()
        
        # Update the VC
        sample_vc.firm_name = "Updated Ventures"
        sample_vc.check_size_max = 1000000.0
        result = repository.update(sample_vc.id, sample_vc)
        
        # Verify database operations
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()
        
        # Verify the update
        assert sample_db_vc.firm_name == "Updated Ventures"
        assert sample_db_vc.check_size_max == 1000000.0
        assert result is not None
        assert result.firm_name == "Updated Ventures"
    
    def test_update_vc_not_found(self, repository, mock_session, sample_vc):
        """Test updating a non-existent VC."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = None
        
        # Try to update
        result = repository.update(uuid.uuid4(), sample_vc)
        
        # Verify result
        assert result is None
    
    def test_delete_vc(self, repository, mock_session, sample_db_vc):
        """Test deleting a VC."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = sample_db_vc
        mock_session.delete = Mock()
        mock_session.commit = Mock()
        
        # Delete the VC
        result = repository.delete(sample_db_vc.id)
        
        # Verify database operations
        mock_session.delete.assert_called_once_with(sample_db_vc)
        mock_session.commit.assert_called_once()
        
        # Verify result
        assert result is True
    
    def test_delete_vc_not_found(self, repository, mock_session):
        """Test deleting a non-existent VC."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = None
        
        # Delete the VC
        result = repository.delete(uuid.uuid4())
        
        # Verify result
        assert result is False
    
    # Search Operations Tests
    
    def test_search_vcs(self, repository, mock_session, sample_db_vc):
        """Test searching VCs by name or thesis."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = [sample_db_vc]
        
        # Search VCs
        result = repository.search("Ventures")
        
        # Verify query
        mock_session.query.assert_called_once_with(VCDB)
        mock_query.filter.assert_called_once()
        mock_query.limit.assert_called_once_with(50)
        
        # Verify result
        assert len(result) == 1
        assert result[0].firm_name == sample_db_vc.firm_name
    
    def test_search_vcs_empty_query(self, repository, mock_session):
        """Test searching VCs with empty query."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = []
        
        # Search VCs
        result = repository.search("")
        
        # Verify result
        assert len(result) == 0
    
    def test_search_by_sector_focus_postgresql(self, repository, mock_session, sample_db_vc):
        """Test searching VCs by sector focus with PostgreSQL."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        
        # Mock filter with JSON operator
        mock_filter = Mock()
        mock_query.filter.return_value = mock_filter
        mock_filter.all.return_value = [sample_db_vc]
        
        # Search VCs
        result = repository.search_by_sector_focus("AI/ML")
        
        # Verify query
        mock_session.query.assert_called_once_with(VCDB)
        mock_query.filter.assert_called_once()
        
        # Verify result
        assert len(result) == 1
        assert result[0].firm_name == sample_db_vc.firm_name
    
    def test_search_by_sector_focus_sqlite(self, sqlite_repository, mock_sqlite_session, sample_db_vc):
        """Test searching VCs by sector focus with SQLite fallback."""
        # Mock the query to return all VCs
        mock_query = Mock()
        mock_sqlite_session.query.return_value = mock_query
        mock_query.all.return_value = [sample_db_vc]
        
        # Search VCs
        result = sqlite_repository.search_by_sector_focus("AI/ML")
        
        # Verify SQLite fallback is used
        mock_sqlite_session.query.assert_called_once_with(VCDB)
        mock_query.all.assert_called_once()
        
        # Verify result
        assert len(result) == 1
        assert result[0].firm_name == sample_db_vc.firm_name
    
    def test_search_by_sector_focus_no_match_sqlite(self, sqlite_repository, mock_sqlite_session, sample_db_vc):
        """Test searching VCs by sector focus with no match in SQLite."""
        # Mock the query to return VC without matching sector
        mock_query = Mock()
        mock_sqlite_session.query.return_value = mock_query
        mock_query.all.return_value = [sample_db_vc]
        
        # Search VCs for non-existent sector
        result = sqlite_repository.search_by_sector_focus("Blockchain")
        
        # Verify result is empty
        assert len(result) == 0
    
    def test_search_by_stage_focus_postgresql(self, repository, mock_session, sample_db_vc):
        """Test searching VCs by stage focus with PostgreSQL."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        
        # Mock filter with JSON operator
        mock_filter = Mock()
        mock_query.filter.return_value = mock_filter
        mock_filter.all.return_value = [sample_db_vc]
        
        # Search VCs
        result = repository.search_by_stage_focus("Seed")
        
        # Verify query
        mock_session.query.assert_called_once_with(VCDB)
        mock_query.filter.assert_called_once()
        
        # Verify result
        assert len(result) == 1
        assert result[0].firm_name == sample_db_vc.firm_name
    
    def test_search_by_stage_focus_sqlite(self, sqlite_repository, mock_sqlite_session, sample_db_vc):
        """Test searching VCs by stage focus with SQLite fallback."""
        # Mock the query to return all VCs
        mock_query = Mock()
        mock_sqlite_session.query.return_value = mock_query
        mock_query.all.return_value = [sample_db_vc]
        
        # Search VCs
        result = sqlite_repository.search_by_stage_focus("Seed")
        
        # Verify SQLite fallback is used
        mock_sqlite_session.query.assert_called_once_with(VCDB)
        mock_query.all.assert_called_once()
        
        # Verify result
        assert len(result) == 1
        assert result[0].firm_name == sample_db_vc.firm_name
    
    def test_search_by_check_size_range(self, repository, mock_session, sample_db_vc):
        """Test searching VCs by check size range."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.all.return_value = [sample_db_vc]
        
        # Search VCs that can write a 200k check
        result = repository.search_by_check_size_range(200000.0)
        
        # Verify query
        mock_session.query.assert_called_once_with(VCDB)
        mock_query.filter.assert_called_once()
        
        # Verify result
        assert len(result) == 1
        assert result[0].firm_name == sample_db_vc.firm_name
    
    def test_search_by_check_size_range_no_match(self, repository, mock_session):
        """Test searching VCs by check size range with no matches."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.all.return_value = []
        
        # Search VCs for amount outside range
        result = repository.search_by_check_size_range(10000000.0)
        
        # Verify result
        assert len(result) == 0
    
    def test_get_active_vcs(self, repository, mock_session, sample_db_vc):
        """Test getting active VCs."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.all.return_value = [sample_db_vc]
        
        # Get active VCs
        result = repository.get_active()
        
        # Verify query
        mock_session.query.assert_called_once_with(VCDB)
        mock_query.filter.assert_called_once()
        
        # Verify result
        assert len(result) == 1
        assert result[0].firm_name == sample_db_vc.firm_name
    
    def test_get_active_vcs_empty(self, repository, mock_session):
        """Test getting active VCs when none exist."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.all.return_value = []
        
        # Get active VCs
        result = repository.get_active()
        
        # Verify result
        assert len(result) == 0
    
    # Domain/DB Model Conversion Tests
    
    def test_to_domain_conversion(self, repository, sample_db_vc):
        """Test conversion from database model to domain model."""
        result = repository._to_domain(sample_db_vc)
        
        assert result.id == sample_db_vc.id
        assert result.firm_name == sample_db_vc.firm_name
        assert result.website == sample_db_vc.website
        assert result.thesis == sample_db_vc.thesis
        assert result.check_size_min == sample_db_vc.check_size_min
        assert result.check_size_max == sample_db_vc.check_size_max
        assert result.sectors == sample_db_vc.sectors
        assert result.stages == sample_db_vc.stages
    
    def test_to_domain_conversion_with_nulls(self, repository, sample_db_vc_with_nulls):
        """Test conversion from database model to domain model with null values."""
        result = repository._to_domain(sample_db_vc_with_nulls)
        
        assert result.id == sample_db_vc_with_nulls.id
        assert result.firm_name == sample_db_vc_with_nulls.firm_name
        assert result.website == ""  # None -> ""
        assert result.thesis == ""  # None -> ""
        assert result.check_size_min == 0  # None -> 0
        assert result.check_size_max == 0  # None -> 0
        assert result.sectors == []  # None -> []
        assert result.stages == []  # None -> []


class TestAsyncPostgresVCRepository:
    """Comprehensive tests for async PostgreSQL VC repository."""
    
    @pytest.fixture
    def mock_session(self):
        """Create mock async database session."""
        session = AsyncMock(spec=AsyncSession)
        # Mock the bind attribute with dialect
        session.bind = Mock()
        session.bind.dialect = Mock()
        session.bind.dialect.name = 'postgresql'
        return session
    
    @pytest.fixture
    def repository(self, mock_session):
        """Create repository instance."""
        return AsyncPostgresVCRepository(mock_session)
    
    @pytest.fixture
    def sample_vc(self):
        """Create sample VC domain model."""
        vc = VCDomain(
            firm_name="Async Capital",
            website="https://asynccapital.com",
            thesis="We invest in async-first companies",
            check_size_min=200000.0,
            check_size_max=800000.0,
            sectors=["SaaS", "Developer Tools"],
            stages=["Pre-seed", "Seed"]
        )
        vc.id = uuid.uuid4()
        return vc
    
    @pytest.fixture
    def sample_db_vc(self, sample_vc):
        """Create sample database VC."""
        return VCDB(
            id=sample_vc.id,
            firm_name=sample_vc.firm_name,
            website=sample_vc.website,
            thesis=sample_vc.thesis,
            check_size_min=sample_vc.check_size_min,
            check_size_max=sample_vc.check_size_max,
            sectors=sample_vc.sectors,
            stages=sample_vc.stages,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
    
    @pytest.fixture
    def sample_db_vc_with_nulls(self):
        """Create sample database VC with null values."""
        vc_id = uuid.uuid4()
        return VCDB(
            id=vc_id,
            firm_name="Minimal Async VC",
            website=None,
            thesis=None,
            check_size_min=None,
            check_size_max=None,
            sectors=None,
            stages=None,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
    
    # CRUD Operations Tests
    
    async def test_create_vc(self, repository, mock_session, sample_vc):
        """Test creating a new VC asynchronously."""
        # Mock the database operations
        mock_session.add = Mock()
        mock_session.commit = AsyncMock()
        mock_session.refresh = AsyncMock()
        
        # Create the VC
        result = await repository.create(sample_vc)
        
        # Verify database operations
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()
        
        # Verify result
        assert result.id == sample_vc.id
        assert result.firm_name == sample_vc.firm_name
    
    async def test_create_vc_with_database_error(self, repository, mock_session, sample_vc):
        """Test creating a VC when database error occurs."""
        mock_session.add = Mock()
        mock_session.commit = AsyncMock(side_effect=SQLAlchemyError("Database error"))
        
        with pytest.raises(SQLAlchemyError):
            await repository.create(sample_vc)
    
    async def test_get_vc(self, repository, mock_session, sample_vc, sample_db_vc):
        """Test getting a VC by ID asynchronously."""
        # Mock the execute result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_db_vc
        mock_session.execute = AsyncMock(return_value=mock_result)
        
        # Get the VC
        result = await repository.get(sample_vc.id)
        
        # Verify query
        mock_session.execute.assert_called_once()
        
        # Verify result
        assert result is not None
        assert result.id == sample_vc.id
        assert result.firm_name == sample_vc.firm_name
    
    async def test_get_vc_not_found(self, repository, mock_session):
        """Test getting a non-existent VC."""
        # Mock the execute result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute = AsyncMock(return_value=mock_result)
        
        # Get the VC
        result = await repository.get(uuid.uuid4())
        
        # Verify result
        assert result is None
    
    async def test_get_vc_with_nulls(self, repository, mock_session, sample_db_vc_with_nulls):
        """Test getting a VC with null values."""
        # Mock the execute result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_db_vc_with_nulls
        mock_session.execute = AsyncMock(return_value=mock_result)
        
        # Get the VC
        result = await repository.get(sample_db_vc_with_nulls.id)
        
        # Verify result handles nulls properly
        assert result is not None
        assert result.firm_name == "Minimal Async VC"
        assert result.website == ""
        assert result.thesis == ""
        assert result.check_size_min == 0
        assert result.check_size_max == 0
        assert result.sectors == []
        assert result.stages == []
    
    async def test_list_vcs(self, repository, mock_session, sample_db_vc):
        """Test listing VCs with pagination."""
        # Mock the execute result
        mock_result = Mock()
        mock_scalars = Mock()
        mock_scalars.all.return_value = [sample_db_vc]
        mock_result.scalars.return_value = mock_scalars
        mock_session.execute = AsyncMock(return_value=mock_result)
        
        # List VCs
        result = await repository.list(limit=20, offset=10)
        
        # Verify result
        assert len(result) == 1
        assert result[0].firm_name == sample_db_vc.firm_name
    
    async def test_list_vcs_default_pagination(self, repository, mock_session):
        """Test listing VCs with default pagination."""
        # Mock the execute result
        mock_result = Mock()
        mock_scalars = Mock()
        mock_scalars.all.return_value = []
        mock_result.scalars.return_value = mock_scalars
        mock_session.execute = AsyncMock(return_value=mock_result)
        
        # List VCs with defaults
        result = await repository.list()
        
        # Verify result
        assert len(result) == 0
    
    async def test_update_vc(self, repository, mock_session, sample_vc, sample_db_vc):
        """Test updating a VC asynchronously."""
        # Mock the execute result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_db_vc
        mock_session.execute = AsyncMock(return_value=mock_result)
        mock_session.commit = AsyncMock()
        mock_session.refresh = AsyncMock()
        
        # Update the VC
        sample_vc.firm_name = "Updated Async Capital"
        result = await repository.update(sample_vc.id, sample_vc)
        
        # Verify database operations
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()
        
        # Verify the update
        assert sample_db_vc.firm_name == "Updated Async Capital"
        assert result is not None
        assert result.firm_name == "Updated Async Capital"
    
    async def test_update_vc_not_found(self, repository, mock_session, sample_vc):
        """Test updating a non-existent VC."""
        # Mock the execute result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute = AsyncMock(return_value=mock_result)
        
        # Try to update
        result = await repository.update(uuid.uuid4(), sample_vc)
        
        # Verify result
        assert result is None
    
    async def test_delete_vc(self, repository, mock_session, sample_db_vc):
        """Test deleting a VC asynchronously."""
        # Mock the execute result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_db_vc
        mock_session.execute = AsyncMock(return_value=mock_result)
        mock_session.delete = AsyncMock()
        mock_session.commit = AsyncMock()
        
        # Delete the VC
        result = await repository.delete(sample_db_vc.id)
        
        # Verify database operations
        mock_session.delete.assert_called_once()
        mock_session.commit.assert_called_once()
        
        # Verify result
        assert result is True
    
    async def test_delete_vc_not_found(self, repository, mock_session):
        """Test deleting a non-existent VC asynchronously."""
        # Mock the execute result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute = AsyncMock(return_value=mock_result)
        
        # Delete the VC
        result = await repository.delete(uuid.uuid4())
        
        # Verify result
        assert result is False
    
    # Search Operations Tests
    
    async def test_search_vcs(self, repository, mock_session, sample_db_vc):
        """Test searching VCs asynchronously."""
        # Mock the execute result
        mock_result = Mock()
        mock_scalars = Mock()
        mock_scalars.all.return_value = [sample_db_vc]
        mock_result.scalars.return_value = mock_scalars
        mock_session.execute = AsyncMock(return_value=mock_result)
        
        # Search VCs
        result = await repository.search("Async")
        
        # Verify result
        assert len(result) == 1
        assert result[0].firm_name == sample_db_vc.firm_name
    
    async def test_search_vcs_empty_query(self, repository, mock_session):
        """Test searching VCs with empty query."""
        # Mock the execute result
        mock_result = Mock()
        mock_scalars = Mock()
        mock_scalars.all.return_value = []
        mock_result.scalars.return_value = mock_scalars
        mock_session.execute = AsyncMock(return_value=mock_result)
        
        # Search VCs
        result = await repository.search("")
        
        # Verify result
        assert len(result) == 0
    
    # Domain/DB Model Conversion Tests
    
    def test_to_domain_conversion(self, repository, sample_db_vc):
        """Test conversion from database model to domain model."""
        result = repository._to_domain(sample_db_vc)
        
        assert result.id == sample_db_vc.id
        assert result.firm_name == sample_db_vc.firm_name
        assert result.website == sample_db_vc.website
        assert result.thesis == sample_db_vc.thesis
        assert result.check_size_min == sample_db_vc.check_size_min
        assert result.check_size_max == sample_db_vc.check_size_max
        assert result.sectors == sample_db_vc.sectors
        assert result.stages == sample_db_vc.stages
    
    def test_to_domain_conversion_with_nulls(self, repository, sample_db_vc_with_nulls):
        """Test conversion from database model to domain model with null values."""
        result = repository._to_domain(sample_db_vc_with_nulls)
        
        assert result.id == sample_db_vc_with_nulls.id
        assert result.firm_name == sample_db_vc_with_nulls.firm_name
        assert result.website == ""
        assert result.thesis == ""
        assert result.check_size_min == 0
        assert result.check_size_max == 0
        assert result.sectors == []
        assert result.stages == []


# Edge Case Tests
class TestVCRepositoryEdgeCases:
    """Test edge cases for VC repository implementations."""
    
    @pytest.fixture
    def mock_session(self):
        """Create mock database session."""
        session = Mock(spec=Session)
        session.bind = Mock()
        session.bind.dialect = Mock()
        session.bind.dialect.name = 'postgresql'
        return session
    
    @pytest.fixture
    def repository(self, mock_session):
        """Create repository instance."""
        return PostgresVCRepository(mock_session)
    
    def test_create_vc_with_empty_arrays(self, repository, mock_session):
        """Test creating a VC with empty sectors and stages arrays."""
        vc = VCDomain(
            firm_name="Empty Arrays VC",
            website="https://empty.com",
            thesis="Test",
            check_size_min=100000,
            check_size_max=500000,
            sectors=[],
            stages=[]
        )
        vc.id = uuid.uuid4()
        
        mock_session.add = Mock()
        mock_session.commit = Mock()
        mock_session.refresh = Mock()
        
        result = repository.create(vc)
        
        assert result.sectors == []
        assert result.stages == []
    
    def test_search_with_special_characters(self, repository, mock_session):
        """Test searching with special SQL characters."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = []
        
        # Search with special characters
        repository.search("Test%_'\"")
        
        # Verify the search pattern was properly escaped
        mock_query.filter.assert_called_once()
    
    def test_very_large_check_sizes(self, repository, mock_session):
        """Test handling very large check sizes."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.all.return_value = []
        
        # Search with very large amount
        result = repository.search_by_check_size_range(1e12)  # 1 trillion
        
        assert len(result) == 0
    
    def test_negative_check_sizes(self, repository, mock_session):
        """Test handling negative check sizes."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.all.return_value = []
        
        # Search with negative amount
        result = repository.search_by_check_size_range(-100000)
        
        assert len(result) == 0
    
    def test_unicode_in_firm_name(self, repository, mock_session):
        """Test handling Unicode characters in firm names."""
        vc = VCDomain(
            firm_name="テスト Ventures 🚀",
            website="https://unicode.com",
            thesis="We invest in 多様性",
            check_size_min=100000,
            check_size_max=500000,
            sectors=["Tech"],
            stages=["Seed"]
        )
        vc.id = uuid.uuid4()
        
        mock_session.add = Mock()
        mock_session.commit = Mock()
        mock_session.refresh = Mock()
        
        result = repository.create(vc)
        
        assert result.firm_name == "テスト Ventures 🚀"
        assert result.thesis == "We invest in 多様性"