"""Comprehensive tests for match repository."""

import pytest
import uuid
from datetime import datetime
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker

from src.database.setup import Base
from tests.test_db_utils import init_test_db_with_migrations
from src.database.models.match import Match as MatchModel
from src.database.models.startup import Startup as StartupModel
from src.database.models.vc import VC as VCModel
from src.database.repositories.match_repository import (
    PostgresMatchRepository,
    AsyncPostgresMatchRepository
)
from src.core.models.match import Match
from src.core.models.startup import Startup
from src.core.models.vc import VC


class TestPostgresMatchRepository:
    """Test PostgreSQL match repository."""
    
    @pytest.fixture
    def engine(self):
        """Create test database engine."""
        engine = create_engine("sqlite:///:memory:")
        init_test_db_with_migrations(engine)
        return engine
    
    @pytest.fixture
    def session(self, engine):
        """Create test database session."""
        SessionLocal = sessionmaker(bind=engine)
        session = SessionLocal()
        yield session
        session.close()
    
    @pytest.fixture
    def repository(self, session):
        """Create repository instance."""
        return PostgresMatchRepository(session)
    
    @pytest.fixture
    def test_startup(self, session):
        """Create a test startup."""
        startup = StartupModel(
            id=uuid.uuid4(),
            name="Test Startup",
            sector="AI/ML",
            stage="Series A"
        )
        session.add(startup)
        session.commit()
        return startup
    
    @pytest.fixture
    def test_vc(self, session):
        """Create a test VC."""
        vc = VCModel(
            id=uuid.uuid4(),
            firm_name="Test Ventures",
            thesis="We invest in AI",
            check_size_min=100000.0,
            check_size_max=500000.0
        )
        session.add(vc)
        session.commit()
        return vc
    
    def test_create_match(self, repository, session, test_startup, test_vc):
        """Test creating a match."""
        # Create domain objects
        startup = Startup(
            id=test_startup.id,
            name=test_startup.name,
            sector=test_startup.sector,
            stage=test_startup.stage
        )
        vc = VC(
            id=test_vc.id,
            firm_name=test_vc.firm_name,
            thesis=test_vc.thesis,
            check_size_min=test_vc.check_size_min,
            check_size_max=test_vc.check_size_max
        )
        
        match = Match(
            startup=startup,
            vc=vc,
            score=0.85,
            reasons=["Sector match", "Stage match", "Check size match"]
        )
        
        created = repository.create(match)
        
        assert created.id is not None
        assert created.startup.id == startup.id
        assert created.vc.id == vc.id
        assert created.score == 0.85
        assert created.reasons == ["Sector match", "Stage match", "Check size match"]
        
        # Verify it's in the database
        db_match = session.query(MatchModel).filter_by(id=created.id).first()
        assert db_match is not None
        assert db_match.startup_id == startup.id
        assert db_match.vc_id == vc.id
    
    def test_get_match_by_id(self, repository, session, test_startup, test_vc):
        """Test getting a match by ID."""
        match_id = uuid.uuid4()
        match_model = MatchModel(
            id=match_id,
            startup_id=test_startup.id,
            vc_id=test_vc.id,
            score=0.75,
            reasons=["Test reason"]
        )
        session.add(match_model)
        session.commit()
        
        match = repository.get(match_id)
        
        assert match is not None
        assert match.id == match_id
        assert match.startup.id == test_startup.id
        assert match.vc.id == test_vc.id
        assert match.score == 0.75
        assert match.reasons == ["Test reason"]
    
    def test_get_nonexistent_match(self, repository):
        """Test getting a match that doesn't exist."""
        match = repository.get(uuid.uuid4())
        assert match is None
    
    def test_list_matches(self, repository, session, test_startup, test_vc):
        """Test listing all matches."""
        # Create multiple matches
        matches = []
        for i in range(3):
            match = MatchModel(
                id=uuid.uuid4(),
                startup_id=test_startup.id,
                vc_id=test_vc.id,
                score=0.5 + i * 0.1,
                reasons=[f"Reason {i}"]
            )
            matches.append(match)
            session.add(match)
        session.commit()
        
        result = repository.list()
        
        assert len(result) == 3
        scores = {m.score for m in result}
        assert scores == {0.5, 0.6, 0.7}
    
    def test_list_with_limit(self, repository, session, test_startup, test_vc):
        """Test listing matches with limit."""
        # Create multiple matches
        for i in range(5):
            match = MatchModel(
                id=uuid.uuid4(),
                startup_id=test_startup.id,
                vc_id=test_vc.id,
                score=0.5,
                reasons=["Test"]
            )
            session.add(match)
        session.commit()
        
        result = repository.list(limit=3)
        
        assert len(result) == 3
    
    def test_update_match(self, repository, session, test_startup, test_vc):
        """Test updating a match."""
        match_id = uuid.uuid4()
        match_model = MatchModel(
            id=match_id,
            startup_id=test_startup.id,
            vc_id=test_vc.id,
            score=0.5,
            reasons=["Original reason"]
        )
        session.add(match_model)
        session.commit()
        
        # Update the match
        startup = Startup(
            id=test_startup.id,
            name=test_startup.name,
            sector=test_startup.sector,
            stage=test_startup.stage
        )
        vc = VC(
            id=test_vc.id,
            firm_name=test_vc.firm_name,
            thesis=test_vc.thesis,
            check_size_min=test_vc.check_size_min,
            check_size_max=test_vc.check_size_max
        )
        
        updated = Match(
            id=match_id,
            startup=startup,
            vc=vc,
            score=0.9,
            reasons=["Updated reason 1", "Updated reason 2"]
        )
        
        result = repository.update(match_id, updated)
        
        assert result is not None
        assert result.score == 0.9
        assert result.reasons == ["Updated reason 1", "Updated reason 2"]
        
        # Verify in database
        db_match = session.query(MatchModel).filter_by(id=match_id).first()
        assert db_match.score == 0.9
    
    def test_delete_match(self, repository, session, test_startup, test_vc):
        """Test deleting a match."""
        match_id = uuid.uuid4()
        match_model = MatchModel(
            id=match_id,
            startup_id=test_startup.id,
            vc_id=test_vc.id,
            score=0.7,
            reasons=["To delete"]
        )
        session.add(match_model)
        session.commit()
        
        # Delete the match
        result = repository.delete(match_id)
        
        assert result is True
        
        # Verify it's gone from database
        db_match = session.query(MatchModel).filter_by(id=match_id).first()
        assert db_match is None
    
    def test_get_by_startup_id(self, repository, session):
        """Test getting matches by startup ID."""
        # Create multiple startups and VCs
        startup1 = StartupModel(id=uuid.uuid4(), name="Startup 1", sector="AI/ML", stage="Seed")
        startup2 = StartupModel(id=uuid.uuid4(), name="Startup 2", sector="FinTech", stage="Series A")
        vc1 = VCModel(id=uuid.uuid4(), firm_name="VC 1", thesis="AI", check_size_min=100000, check_size_max=500000)
        vc2 = VCModel(id=uuid.uuid4(), firm_name="VC 2", thesis="FinTech", check_size_min=200000, check_size_max=800000)
        
        session.add_all([startup1, startup2, vc1, vc2])
        session.commit()
        
        # Create matches
        match1 = MatchModel(id=uuid.uuid4(), startup_id=startup1.id, vc_id=vc1.id, score=0.8, reasons=["AI match"])
        match2 = MatchModel(id=uuid.uuid4(), startup_id=startup1.id, vc_id=vc2.id, score=0.6, reasons=["Partial match"])
        match3 = MatchModel(id=uuid.uuid4(), startup_id=startup2.id, vc_id=vc2.id, score=0.9, reasons=["FinTech match"])
        
        session.add_all([match1, match2, match3])
        session.commit()
        
        # Get matches for startup1
        result = repository.get_by_startup_id(startup1.id)
        
        assert len(result) == 2
        vc_names = {m.vc.firm_name for m in result}
        assert vc_names == {"VC 1", "VC 2"}
    
    def test_get_by_vc_id(self, repository, session):
        """Test getting matches by VC ID."""
        # Create multiple startups and VCs
        startup1 = StartupModel(id=uuid.uuid4(), name="Startup 1", sector="AI/ML", stage="Seed")
        startup2 = StartupModel(id=uuid.uuid4(), name="Startup 2", sector="AI/ML", stage="Series A")
        vc1 = VCModel(id=uuid.uuid4(), firm_name="VC 1", thesis="AI", check_size_min=100000, check_size_max=500000)
        vc2 = VCModel(id=uuid.uuid4(), firm_name="VC 2", thesis="FinTech", check_size_min=200000, check_size_max=800000)
        
        session.add_all([startup1, startup2, vc1, vc2])
        session.commit()
        
        # Create matches
        match1 = MatchModel(id=uuid.uuid4(), startup_id=startup1.id, vc_id=vc1.id, score=0.8, reasons=["AI match"])
        match2 = MatchModel(id=uuid.uuid4(), startup_id=startup2.id, vc_id=vc1.id, score=0.7, reasons=["AI match 2"])
        match3 = MatchModel(id=uuid.uuid4(), startup_id=startup2.id, vc_id=vc2.id, score=0.5, reasons=["Partial"])
        
        session.add_all([match1, match2, match3])
        session.commit()
        
        # Get matches for vc1
        result = repository.get_by_vc_id(vc1.id)
        
        assert len(result) == 2
        startup_names = {m.startup.name for m in result}
        assert startup_names == {"Startup 1", "Startup 2"}
    
    def test_get_top_matches_for_startup(self, repository, session):
        """Test getting top matches for a startup."""
        # Create startup and multiple VCs
        startup = StartupModel(id=uuid.uuid4(), name="Top Startup", sector="AI/ML", stage="Series A")
        vcs = []
        for i in range(5):
            vc = VCModel(
                id=uuid.uuid4(),
                firm_name=f"VC {i}",
                thesis="Test",
                check_size_min=100000,
                check_size_max=500000
            )
            vcs.append(vc)
        
        session.add(startup)
        session.add_all(vcs)
        session.commit()
        
        # Create matches with different scores
        scores = [0.9, 0.7, 0.85, 0.6, 0.95]
        for i, (vc, score) in enumerate(zip(vcs, scores)):
            match = MatchModel(
                id=uuid.uuid4(),
                startup_id=startup.id,
                vc_id=vc.id,
                score=score,
                reasons=[f"Score {score}"]
            )
            session.add(match)
        session.commit()
        
        # Get top 3 matches
        result = repository.get_top_matches_for_startup(startup.id, limit=3)
        
        assert len(result) == 3
        # Should be sorted by score descending
        assert result[0].score == 0.95
        assert result[1].score == 0.9
        assert result[2].score == 0.85


class TestAsyncPostgresMatchRepository:
    """Test async PostgreSQL match repository."""
    
    @pytest.fixture
    def async_engine(self):
        """Create async test database engine."""
        engine = create_async_engine("sqlite+aiosqlite:///:memory:")
        return engine
    
    @pytest.fixture
    async def async_session(self, async_engine):
        """Create async test database session."""
        async with async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        AsyncSessionLocal = async_sessionmaker(async_engine)
        async with AsyncSessionLocal() as session:
            yield session
    
    @pytest.fixture
    def async_repository(self, async_session):
        """Create async repository instance."""
        return AsyncPostgresMatchRepository(async_session)
    
    @pytest.fixture
    async def async_test_startup(self, async_session):
        """Create a test startup asynchronously."""
        startup = StartupModel(
            id=uuid.uuid4(),
            name="Async Test Startup",
            sector="AI/ML",
            stage="Series A"
        )
        async_session.add(startup)
        await async_session.commit()
        return startup
    
    @pytest.fixture
    async def async_test_vc(self, async_session):
        """Create a test VC asynchronously."""
        vc = VCModel(
            id=uuid.uuid4(),
            firm_name="Async Test Ventures",
            thesis="We invest in AI",
            check_size_min=100000.0,
            check_size_max=500000.0
        )
        async_session.add(vc)
        await async_session.commit()
        return vc
    
    @pytest.mark.asyncio
    async def test_async_create_match(self, async_repository, async_session, async_test_startup, async_test_vc):
        """Test creating a match asynchronously."""
        startup = Startup(
            id=async_test_startup.id,
            name=async_test_startup.name,
            sector=async_test_startup.sector,
            stage=async_test_startup.stage
        )
        vc = VC(
            id=async_test_vc.id,
            firm_name=async_test_vc.firm_name,
            thesis=async_test_vc.thesis,
            check_size_min=async_test_vc.check_size_min,
            check_size_max=async_test_vc.check_size_max
        )
        
        match = Match(
            startup=startup,
            vc=vc,
            score=0.85,
            reasons=["Async match"]
        )
        
        created = await async_repository.create(match)
        
        assert created.id is not None
        assert created.score == 0.85
    
    @pytest.mark.asyncio
    async def test_async_get_match(self, async_repository, async_session, async_test_startup, async_test_vc):
        """Test getting a match asynchronously."""
        match_id = uuid.uuid4()
        match_model = MatchModel(
            id=match_id,
            startup_id=async_test_startup.id,
            vc_id=async_test_vc.id,
            score=0.75,
            reasons=["Async test"]
        )
        async_session.add(match_model)
        await async_session.commit()
        
        match = await async_repository.get(match_id)
        
        assert match is not None
        assert match.id == match_id
        assert match.score == 0.75
    
    @pytest.mark.asyncio
    async def test_async_list_matches(self, async_repository, async_session, async_test_startup, async_test_vc):
        """Test listing matches asynchronously."""
        for i in range(3):
            match = MatchModel(
                id=uuid.uuid4(),
                startup_id=async_test_startup.id,
                vc_id=async_test_vc.id,
                score=0.5 + i * 0.1,
                reasons=[f"Async reason {i}"]
            )
            async_session.add(match)
        await async_session.commit()
        
        result = await async_repository.list()
        
        assert len(result) == 3