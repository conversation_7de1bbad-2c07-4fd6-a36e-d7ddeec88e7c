"""Isolated tests for match repository without AI dependencies."""

import pytest
import uuid
from datetime import datetime
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker

from src.database.setup import Base
from tests.test_db_utils import init_test_db_with_migrations
from src.database.models.match import Match as MatchModel
from src.database.models.startup import Startup as StartupModel
from src.database.models.vc import VC as VCModel
from src.database.repositories.match_repository import (
    PostgresMatchRepository,
    AsyncPostgresMatchRepository
)
from src.core.models.match import Match
from src.core.models.startup import Startup
from src.core.models.vc import VC


class TestPostgresMatchRepositoryMissingMethods:
    """Test PostgreSQL match repository methods not covered by existing tests."""
    
    @pytest.fixture
    def engine(self):
        """Create test database engine."""
        engine = create_engine("sqlite:///:memory:")
        init_test_db_with_migrations(engine)
        return engine
    
    @pytest.fixture
    def session(self, engine):
        """Create test database session."""
        SessionLocal = sessionmaker(bind=engine)
        session = SessionLocal()
        yield session
        session.close()
    
    @pytest.fixture
    def repository(self, session):
        """Create repository instance."""
        return PostgresMatchRepository(session)
    
    @pytest.fixture
    def test_startup(self, session):
        """Create a test startup."""
        startup = StartupModel(
            id=uuid.uuid4(),
            name="Test Startup",
            sector="AI/ML",
            stage="Series A"
        )
        session.add(startup)
        session.commit()
        return startup
    
    @pytest.fixture
    def test_vc(self, session):
        """Create a test VC."""
        vc = VCModel(
            id=uuid.uuid4(),
            firm_name="Test Ventures",
            thesis="We invest in AI",
            check_size_min=100000.0,
            check_size_max=500000.0
        )
        session.add(vc)
        session.commit()
        return vc
    
    def test_exists_method(self, repository, session, test_startup, test_vc):
        """Test checking if a match exists between startup and VC."""
        # Should not exist initially
        assert repository.exists(test_startup.id, test_vc.id) is False
        
        # Create a match
        match_model = MatchModel(
            id=uuid.uuid4(),
            startup_id=test_startup.id,
            vc_id=test_vc.id,
            score=0.75,
            reasons=["Test reason"]
        )
        session.add(match_model)
        session.commit()
        
        # Should exist now
        assert repository.exists(test_startup.id, test_vc.id) is True
        
        # Check with different IDs
        assert repository.exists(uuid.uuid4(), test_vc.id) is False
        assert repository.exists(test_startup.id, uuid.uuid4()) is False
    
    def test_update_score_method(self, repository, session, test_startup, test_vc):
        """Test updating only the score of a match."""
        # Create a match
        match_id = uuid.uuid4()
        match_model = MatchModel(
            id=match_id,
            startup_id=test_startup.id,
            vc_id=test_vc.id,
            score=0.5,
            reasons=["Original reason", "Another reason"]
        )
        session.add(match_model)
        session.commit()
        
        # Update the score
        updated = repository.update_score(match_id, 0.95)
        
        assert updated is not None
        assert updated.score == 0.95
        # Reasons should remain unchanged
        assert updated.reasons == ["Original reason", "Another reason"]
        
        # Verify in database
        db_match = session.query(MatchModel).filter_by(id=match_id).first()
        assert db_match.score == 0.95
        assert db_match.reasons == ["Original reason", "Another reason"]
    
    def test_update_score_nonexistent(self, repository):
        """Test updating score of non-existent match."""
        result = repository.update_score(uuid.uuid4(), 0.8)
        assert result is None
    
    def test_list_by_startup_alias(self, repository, session, test_startup, test_vc):
        """Test list_by_startup method (alias for get_by_startup_id)."""
        # Create multiple matches
        for i in range(3):
            match = MatchModel(
                id=uuid.uuid4(),
                startup_id=test_startup.id,
                vc_id=test_vc.id,
                score=0.5 + i * 0.1,
                reasons=[f"Reason {i}"]
            )
            session.add(match)
        session.commit()
        
        # Test list_by_startup
        result = repository.list_by_startup(test_startup.id)
        
        assert len(result) == 3
        scores = {m.score for m in result}
        assert scores == {0.5, 0.6, 0.7}
    
    def test_list_by_vc_alias(self, repository, session, test_startup, test_vc):
        """Test list_by_vc method (alias for get_by_vc_id)."""
        # Create multiple matches
        for i in range(2):
            match = MatchModel(
                id=uuid.uuid4(),
                startup_id=test_startup.id,
                vc_id=test_vc.id,
                score=0.8 + i * 0.05,
                reasons=[f"VC Reason {i}"]
            )
            session.add(match)
        session.commit()
        
        # Test list_by_vc
        result = repository.list_by_vc(test_vc.id)
        
        assert len(result) == 2
        scores = {m.score for m in result}
        assert 0.8 in scores
        assert abs(0.85 - max(scores)) < 1e-9
    
    def test_update_nonexistent(self, repository, test_startup, test_vc):
        """Test updating a match that doesn't exist."""
        startup = Startup(
            id=test_startup.id,
            name=test_startup.name,
            sector=test_startup.sector,
            stage=test_startup.stage
        )
        vc = VC(
            id=test_vc.id,
            firm_name=test_vc.firm_name,
            thesis=test_vc.thesis,
            check_size_min=test_vc.check_size_min,
            check_size_max=test_vc.check_size_max
        )
        
        match = Match(
            startup=startup,
            vc=vc,
            score=0.9,
            reasons=["New reason"]
        )
        
        result = repository.update(uuid.uuid4(), match)
        assert result is None
    
    def test_delete_nonexistent(self, repository):
        """Test deleting a match that doesn't exist."""
        result = repository.delete(uuid.uuid4())
        assert result is False
    
    def test_empty_list(self, repository):
        """Test listing when no matches exist."""
        result = repository.list()
        assert result == []
    
    def test_list_with_offset(self, repository, session, test_startup, test_vc):
        """Test listing with offset parameter."""
        # Create 5 matches
        for i in range(5):
            match = MatchModel(
                id=uuid.uuid4(),
                startup_id=test_startup.id,
                vc_id=test_vc.id,
                score=0.5 + i * 0.1,
                reasons=[f"Match {i}"]
            )
            session.add(match)
        session.commit()
        
        # Get matches with offset
        result = repository.list(limit=2, offset=2)
        
        assert len(result) == 2


class TestAsyncPostgresMatchRepositoryMissingMethods:
    """Test async PostgreSQL match repository methods not covered."""
    
    @pytest.fixture
    def async_engine(self):
        """Create async test database engine."""
        engine = create_async_engine("sqlite+aiosqlite:///:memory:")
        return engine
    
    @pytest.fixture
    async def async_session(self, async_engine):
        """Create async test database session."""
        async with async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        AsyncSessionLocal = async_sessionmaker(async_engine)
        async with AsyncSessionLocal() as session:
            yield session
    
    @pytest.fixture
    def async_repository(self, async_session):
        """Create async repository instance."""
        return AsyncPostgresMatchRepository(async_session)
    
    @pytest.fixture
    async def async_test_startup(self, async_session):
        """Create a test startup asynchronously."""
        startup = StartupModel(
            id=uuid.uuid4(),
            name="Async Test Startup",
            sector="AI/ML",
            stage="Series A"
        )
        async_session.add(startup)
        await async_session.commit()
        return startup
    
    @pytest.fixture
    async def async_test_vc(self, async_session):
        """Create a test VC asynchronously."""
        vc = VCModel(
            id=uuid.uuid4(),
            firm_name="Async Test Ventures",
            thesis="We invest in AI",
            check_size_min=100000.0,
            check_size_max=500000.0
        )
        async_session.add(vc)
        await async_session.commit()
        return vc
    
    @pytest.mark.asyncio
    async def test_async_update_nonexistent(self, async_repository):
        """Test updating a non-existent match."""
        # Create domain objects directly without relying on DB fixtures
        startup = Startup(
            id=uuid.uuid4(),
            name="Test Startup",
            sector="AI/ML",
            stage="Series A"
        )
        vc = VC(
            id=uuid.uuid4(),
            firm_name="Test Ventures",
            thesis="We invest in AI",
            check_size_min=100000.0,
            check_size_max=500000.0
        )
        
        match = Match(
            startup=startup,
            vc=vc,
            score=0.9,
            reasons=["New reason"]
        )
        
        result = await async_repository.update(uuid.uuid4(), match)
        assert result is None
    
    @pytest.mark.asyncio
    async def test_async_delete_nonexistent(self, async_repository):
        """Test deleting a non-existent match."""
        result = await async_repository.delete(uuid.uuid4())
        assert result is False
    
    @pytest.mark.asyncio
    async def test_async_get_nonexistent(self, async_repository):
        """Test getting a non-existent match."""
        result = await async_repository.get(uuid.uuid4())
        assert result is None
    
    @pytest.mark.asyncio
    async def test_async_exists(self, async_repository, async_session):
        """Test async exists method."""
        # Create startup and VC in the same async context
        startup_id = uuid.uuid4()
        vc_id = uuid.uuid4()
        
        startup = StartupModel(
            id=startup_id,
            name="Async Exists Test Startup",
            sector="AI/ML",
            stage="Series A"
        )
        vc = VCModel(
            id=vc_id,
            firm_name="Async Exists Test Ventures",
            thesis="We invest in AI",
            check_size_min=100000.0,
            check_size_max=500000.0
        )
        async_session.add(startup)
        async_session.add(vc)
        await async_session.commit()
        
        # Should not exist initially
        assert await async_repository.exists(startup_id, vc_id) is False
        
        # Create a match
        match_model = MatchModel(
            id=uuid.uuid4(),
            startup_id=startup_id,
            vc_id=vc_id,
            score=0.75,
            reasons=["Test reason"]
        )
        async_session.add(match_model)
        await async_session.commit()
        
        # Should exist now
        assert await async_repository.exists(startup_id, vc_id) is True
    
    @pytest.mark.asyncio
    async def test_async_update_score(self, async_repository, async_session):
        """Test async update_score method."""
        # Create startup and VC in the same async context
        startup = StartupModel(
            id=uuid.uuid4(),
            name="Async Score Test Startup",
            sector="AI/ML",
            stage="Series A"
        )
        vc = VCModel(
            id=uuid.uuid4(),
            firm_name="Async Score Test Ventures",
            thesis="We invest in AI",
            check_size_min=100000.0,
            check_size_max=500000.0
        )
        async_session.add(startup)
        async_session.add(vc)
        await async_session.commit()
        
        # Refresh to avoid lazy loading issues
        await async_session.refresh(startup)
        await async_session.refresh(vc)
        
        # Create a match
        match_id = uuid.uuid4()
        match_model = MatchModel(
            id=match_id,
            startup_id=startup.id,
            vc_id=vc.id,
            score=0.5,
            reasons=["Original async reason"]
        )
        async_session.add(match_model)
        await async_session.commit()
        
        # Update the score
        updated = await async_repository.update_score(match_id, 0.9)
        
        assert updated is not None
        assert updated.score == 0.9
        assert updated.reasons == ["Original async reason"]
    
    @pytest.mark.asyncio
    async def test_async_update_score_nonexistent(self, async_repository):
        """Test updating score of non-existent match."""
        result = await async_repository.update_score(uuid.uuid4(), 0.8)
        assert result is None
    
    @pytest.mark.asyncio
    async def test_async_list_empty(self, async_repository):
        """Test listing when no matches exist."""
        result = await async_repository.list()
        assert result == []
    
    @pytest.mark.asyncio
    async def test_async_list_with_offset(self, async_repository, async_session):
        """Test async listing with offset."""
        # Create startup and VC in the same async context
        startup = StartupModel(
            id=uuid.uuid4(),
            name="Async Offset Test Startup",
            sector="AI/ML",
            stage="Series A"
        )
        vc = VCModel(
            id=uuid.uuid4(),
            firm_name="Async Offset Test Ventures",
            thesis="We invest in AI",
            check_size_min=100000.0,
            check_size_max=500000.0
        )
        async_session.add(startup)
        async_session.add(vc)
        await async_session.commit()
        
        # Refresh to avoid lazy loading issues
        await async_session.refresh(startup)
        await async_session.refresh(vc)
        
        # Create 4 matches
        for i in range(4):
            match = MatchModel(
                id=uuid.uuid4(),
                startup_id=startup.id,
                vc_id=vc.id,
                score=0.6 + i * 0.05,
                reasons=[f"Async Match {i}"]
            )
            async_session.add(match)
        await async_session.commit()
        
        # Get matches with offset
        result = await async_repository.list(limit=2, offset=1)
        
        assert len(result) == 2