"""Comprehensive tests for database models."""

import pytest
import uuid
from datetime import datetime
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from src.database.setup import Base
from tests.test_db_utils import init_test_db_with_migrations
from src.database.models.startup import Startup
from src.database.models.vc import VC
from src.database.models.match import Match


class TestDatabaseModels:
    """Test all database models."""
    
    @pytest.fixture
    def engine(self):
        """Create test database engine."""
        engine = create_engine("sqlite:///:memory:")
        init_test_db_with_migrations(engine)
        return engine
    
    @pytest.fixture
    def session(self, engine):
        """Create test database session."""
        SessionLocal = sessionmaker(bind=engine)
        session = SessionLocal()
        yield session
        session.close()
    
    def test_startup_model_creation(self, session):
        """Test creating a startup model."""
        startup_id = uuid.uuid4()
        startup = Startup(
            id=startup_id,
            name="Test Startup",
            sector="AI/ML",
            stage="Series A",
            description="Test description",
            website="https://test.com",
            team_size=10,
            monthly_revenue=50000.0,
            meta_data={"custom": "data"}
        )
        
        session.add(startup)
        session.commit()
        
        # Verify all fields
        result = session.query(Startup).filter_by(id=startup_id).first()
        assert result.name == "Test Startup"
        assert result.sector == "AI/ML"
        assert result.stage == "Series A"
        assert result.description == "Test description"
        assert result.website == "https://test.com"
        assert result.team_size == 10
        assert result.monthly_revenue == 50000.0
        assert result.meta_data == {"custom": "data"}
        assert result.created_at is not None
        assert result.updated_at is not None
    
    def test_vc_model_creation(self, session):
        """Test creating a VC model."""
        vc_id = uuid.uuid4()
        vc = VC(
            id=vc_id,
            firm_name="Test Ventures",
            website="https://testventures.com",
            thesis="We invest in AI",
            check_size_min=100000.0,
            check_size_max=500000.0,
            sectors=["AI/ML", "SaaS"],
            stages=["Seed", "Series A"]
        )
        
        session.add(vc)
        session.commit()
        
        # Verify all fields
        result = session.query(VC).filter_by(id=vc_id).first()
        assert result.firm_name == "Test Ventures"
        assert result.website == "https://testventures.com"
        assert result.thesis == "We invest in AI"
        assert result.check_size_min == 100000.0
        assert result.check_size_max == 500000.0
        assert result.sectors == ["AI/ML", "SaaS"]
        assert result.stages == ["Seed", "Series A"]
        assert result.created_at is not None
        assert result.updated_at is not None
    
    def test_match_model_creation(self, session):
        """Test creating a match model with relationships."""
        # Create startup and VC first
        startup = Startup(
            id=uuid.uuid4(),
            name="Matched Startup",
            sector="FinTech",
            stage="Seed"
        )
        vc = VC(
            id=uuid.uuid4(),
            firm_name="Matched VC",
            thesis="FinTech focus",
            check_size_min=50000,
            check_size_max=200000
        )
        
        session.add(startup)
        session.add(vc)
        session.commit()
        
        # Create match
        match_id = uuid.uuid4()
        match = Match(
            id=match_id,
            startup_id=startup.id,
            vc_id=vc.id,
            score=0.85,
            reasons=["Sector match", "Stage match", "Check size match"]
        )
        
        session.add(match)
        session.commit()
        
        # Verify all fields and relationships
        result = session.query(Match).filter_by(id=match_id).first()
        assert result.startup_id == startup.id
        assert result.vc_id == vc.id
        assert result.score == 0.85
        assert result.reasons == ["Sector match", "Stage match", "Check size match"]
        assert result.created_at is not None
        
        # Test relationships
        assert result.startup.name == "Matched Startup"
        assert result.vc.firm_name == "Matched VC"
    
    def test_startup_matches_relationship(self, session):
        """Test startup can have multiple matches."""
        startup = Startup(
            id=uuid.uuid4(),
            name="Popular Startup",
            sector="AI/ML",
            stage="Series A"
        )
        
        vc1 = VC(id=uuid.uuid4(), firm_name="VC1", thesis="AI focus", check_size_min=100000, check_size_max=500000)
        vc2 = VC(id=uuid.uuid4(), firm_name="VC2", thesis="ML focus", check_size_min=200000, check_size_max=800000)
        
        session.add_all([startup, vc1, vc2])
        session.commit()
        
        # Create matches
        match1 = Match(id=uuid.uuid4(), startup_id=startup.id, vc_id=vc1.id, score=0.9, reasons=["Great fit"])
        match2 = Match(id=uuid.uuid4(), startup_id=startup.id, vc_id=vc2.id, score=0.8, reasons=["Good fit"])
        
        session.add_all([match1, match2])
        session.commit()
        
        # Test relationship
        startup_with_matches = session.query(Startup).filter_by(id=startup.id).first()
        assert len(startup_with_matches.matches) == 2
        assert set(m.vc.firm_name for m in startup_with_matches.matches) == {"VC1", "VC2"}
    
    def test_vc_matches_relationship(self, session):
        """Test VC can have multiple matches."""
        vc = VC(
            id=uuid.uuid4(),
            firm_name="Popular VC",
            thesis="Diverse portfolio",
            check_size_min=50000,
            check_size_max=1000000
        )
        
        startup1 = Startup(id=uuid.uuid4(), name="Startup1", sector="AI/ML", stage="Seed")
        startup2 = Startup(id=uuid.uuid4(), name="Startup2", sector="FinTech", stage="Series A")
        
        session.add_all([vc, startup1, startup2])
        session.commit()
        
        # Create matches
        match1 = Match(id=uuid.uuid4(), startup_id=startup1.id, vc_id=vc.id, score=0.85, reasons=["Early stage"])
        match2 = Match(id=uuid.uuid4(), startup_id=startup2.id, vc_id=vc.id, score=0.75, reasons=["Later stage"])
        
        session.add_all([match1, match2])
        session.commit()
        
        # Test relationship
        vc_with_matches = session.query(VC).filter_by(id=vc.id).first()
        assert len(vc_with_matches.matches) == 2
        assert set(m.startup.name for m in vc_with_matches.matches) == {"Startup1", "Startup2"}
    
    def test_cascade_delete_startup(self, session):
        """Test that deleting a startup cascades to matches."""
        startup = Startup(id=uuid.uuid4(), name="To Delete", sector="SaaS", stage="Seed")
        vc = VC(id=uuid.uuid4(), firm_name="Investor", thesis="SaaS", check_size_min=100000, check_size_max=500000)
        
        session.add_all([startup, vc])
        session.commit()
        
        match = Match(id=uuid.uuid4(), startup_id=startup.id, vc_id=vc.id, score=0.7, reasons=["Test"])
        session.add(match)
        session.commit()
        
        # Delete startup
        session.delete(startup)
        session.commit()
        
        # Verify match is also deleted
        remaining_matches = session.query(Match).filter_by(startup_id=startup.id).all()
        assert len(remaining_matches) == 0
        
        # But VC should still exist
        remaining_vc = session.query(VC).filter_by(id=vc.id).first()
        assert remaining_vc is not None
    
    def test_model_repr_methods(self, session):
        """Test model string representations."""
        startup = Startup(id=uuid.uuid4(), name="Repr Test", sector="AI", stage="Seed")
        vc = VC(id=uuid.uuid4(), firm_name="Repr VC", thesis="Test", check_size_min=100000, check_size_max=500000)
        
        # Just verify repr doesn't crash
        str(startup)
        str(vc)
        
        session.add_all([startup, vc])
        session.commit()
        
        match = Match(id=uuid.uuid4(), startup_id=startup.id, vc_id=vc.id, score=0.8, reasons=["Test"])
        str(match)