"""Test PostgreSQL implementation of VCRepository."""

import pytest
import uuid
from datetime import datetime
from unittest.mock import Mock, MagicMock, AsyncMock
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession

from src.core.models.vc import VC as VCDomain
from src.database.models.vc import VC as VCDB
from src.database.repositories.vc_repository import (
    PostgresVCRepository,
    AsyncPostgresVCRepository
)


class TestPostgresVCRepository:
    """Test PostgreSQL VC repository."""
    
    @pytest.fixture
    def mock_session(self):
        """Create mock database session."""
        session = Mock(spec=Session)
        return session
    
    @pytest.fixture
    def repository(self, mock_session):
        """Create repository instance."""
        return PostgresVCRepository(mock_session)
    
    @pytest.fixture
    def sample_vc(self):
        """Create sample VC domain model."""
        vc = VCDomain(
            firm_name="Test Ventures",
            website="https://testventures.com",
            thesis="We invest in early-stage AI companies",
            check_size_min=100000.0,
            check_size_max=500000.0,
            sectors=["AI/ML", "FinTech"],
            stages=["Seed", "Series A"]
        )
        vc.id = uuid.uuid4()
        return vc
    
    @pytest.fixture
    def sample_db_vc(self, sample_vc):
        """Create sample database VC."""
        return VCDB(
            id=sample_vc.id,
            firm_name=sample_vc.firm_name,
            website=sample_vc.website,
            thesis=sample_vc.thesis,
            check_size_min=sample_vc.check_size_min,
            check_size_max=sample_vc.check_size_max,
            sectors=sample_vc.sectors,
            stages=sample_vc.stages,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
    
    async def test_create_vc(self, repository, mock_session, sample_vc, sample_db_vc):
        """Test creating a new VC."""
        # Mock the database operations
        mock_session.add = Mock()
        mock_session.commit = Mock()
        mock_session.refresh = Mock()
        
        # Configure refresh to populate the created_at/updated_at
        def refresh_side_effect(obj):
            obj.created_at = sample_db_vc.created_at
            obj.updated_at = sample_db_vc.updated_at
        
        mock_session.refresh.side_effect = refresh_side_effect
        
        # Create the VC
        result = await repository.create(sample_vc)
        
        # Verify database operations
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()
        
        # Verify result
        assert result.id == sample_vc.id
        assert result.firm_name == sample_vc.firm_name
        assert result.thesis == sample_vc.thesis
    
    async def test_get_vc(self, repository, mock_session, sample_vc, sample_db_vc):
        """Test getting a VC by ID."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = sample_db_vc
        
        # Get the VC
        result = await repository.get(sample_vc.id)
        
        # Verify query
        mock_session.query.assert_called_once_with(VCDB)
        mock_query.filter_by.assert_called_once_with(id=sample_vc.id)
        
        # Verify result
        assert result is not None
        assert result.id == sample_vc.id
        assert result.firm_name == sample_vc.firm_name
    
    async def test_get_vc_not_found(self, repository, mock_session):
        """Test getting a non-existent VC."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = None
        
        # Get the VC
        result = await repository.get(uuid.uuid4())
        
        # Verify result
        assert result is None
    
    async def test_list_vcs(self, repository, mock_session, sample_db_vc):
        """Test listing VCs with filters."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = [sample_db_vc]
        
        # List VCs
        result = await repository.list(stage="Seed", sector="AI/ML")
        
        # Verify query
        mock_session.query.assert_called_once_with(VCDB)
        
        # Verify result
        assert len(result) == 1
        assert result[0].firm_name == sample_db_vc.firm_name
    
    async def test_update_vc(self, repository, mock_session, sample_vc, sample_db_vc):
        """Test updating a VC."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = sample_db_vc
        mock_session.commit = Mock()
        mock_session.refresh = Mock()
        
        # Update the VC
        sample_vc.firm_name = "Updated Ventures"
        sample_vc.check_size_max = 1000000.0
        result = await repository.update(sample_vc)
        
        # Verify database operations
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()
        
        # Verify the update
        assert sample_db_vc.firm_name == "Updated Ventures"
        assert sample_db_vc.check_size_max == 1000000.0
    
    async def test_update_vc_not_found(self, repository, mock_session, sample_vc):
        """Test updating a non-existent VC."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = None
        
        # Try to update
        with pytest.raises(ValueError, match="not found"):
            await repository.update(sample_vc)
    
    async def test_delete_vc(self, repository, mock_session, sample_db_vc):
        """Test deleting a VC."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = sample_db_vc
        mock_session.delete = Mock()
        mock_session.commit = Mock()
        
        # Delete the VC
        result = await repository.delete(sample_db_vc.id)
        
        # Verify database operations
        mock_session.delete.assert_called_once_with(sample_db_vc)
        mock_session.commit.assert_called_once()
        
        # Verify result
        assert result is True
    
    async def test_delete_vc_not_found(self, repository, mock_session):
        """Test deleting a non-existent VC."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter_by.return_value = mock_query
        mock_query.first.return_value = None
        
        # Delete the VC
        result = await repository.delete(uuid.uuid4())
        
        # Verify result
        assert result is False
    
    async def test_search_vcs(self, repository, mock_session, sample_db_vc):
        """Test searching VCs."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = [sample_db_vc]
        
        # Search VCs
        result = await repository.search("Ventures")
        
        # Verify query
        mock_session.query.assert_called_once_with(VCDB)
        mock_query.filter.assert_called_once()
        
        # Verify result
        assert len(result) == 1
        assert result[0].firm_name == sample_db_vc.firm_name
    
    async def test_find_by_check_size(self, repository, mock_session, sample_db_vc):
        """Test finding VCs by check size range."""
        # Mock the query
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.all.return_value = [sample_db_vc]
        
        # Find VCs by check size
        result = await repository.find_by_check_size(50000.0, 200000.0)
        
        # Verify query
        mock_session.query.assert_called_once_with(VCDB)
        mock_query.filter.assert_called_once()
        
        # Verify result
        assert len(result) == 1
        assert result[0].check_size_min == sample_db_vc.check_size_min


class TestAsyncPostgresVCRepository:
    """Test async PostgreSQL VC repository."""
    
    @pytest.fixture
    def mock_session(self):
        """Create mock async database session."""
        session = AsyncMock(spec=AsyncSession)
        return session
    
    @pytest.fixture
    def repository(self, mock_session):
        """Create repository instance."""
        return AsyncPostgresVCRepository(mock_session)
    
    @pytest.fixture
    def sample_vc(self):
        """Create sample VC domain model."""
        vc = VCDomain(
            firm_name="Async Capital",
            website="https://asynccapital.com",
            thesis="We invest in async-first companies",
            check_size_min=200000.0,
            check_size_max=800000.0,
            sectors=["SaaS", "Developer Tools"],
            stages=["Pre-seed", "Seed"]
        )
        vc.id = uuid.uuid4()
        return vc
    
    @pytest.fixture
    def sample_db_vc(self, sample_vc):
        """Create sample database VC."""
        return VCDB(
            id=sample_vc.id,
            firm_name=sample_vc.firm_name,
            website=sample_vc.website,
            thesis=sample_vc.thesis,
            check_size_min=sample_vc.check_size_min,
            check_size_max=sample_vc.check_size_max,
            sectors=sample_vc.sectors,
            stages=sample_vc.stages,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
    
    async def test_async_create_vc(self, repository, mock_session, sample_vc):
        """Test creating a new VC asynchronously."""
        # Mock the database operations
        mock_session.add = Mock()
        mock_session.commit = AsyncMock()
        mock_session.refresh = AsyncMock()
        
        # Create the VC
        result = await repository.create(sample_vc)
        
        # Verify database operations
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()
        
        # Verify result
        assert result.id == sample_vc.id
        assert result.firm_name == sample_vc.firm_name
    
    async def test_async_get_vc(self, repository, mock_session, sample_vc, sample_db_vc):
        """Test getting a VC by ID asynchronously."""
        # Mock the execute result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_db_vc
        mock_session.execute = AsyncMock(return_value=mock_result)
        
        # Get the VC
        result = await repository.get(sample_vc.id)
        
        # Verify query
        mock_session.execute.assert_called_once()
        
        # Verify result
        assert result is not None
        assert result.id == sample_vc.id
        assert result.firm_name == sample_vc.firm_name
    
    async def test_async_list_vcs(self, repository, mock_session, sample_db_vc):
        """Test listing VCs asynchronously."""
        # Mock the execute result
        mock_result = Mock()
        mock_scalars = Mock()
        mock_scalars.all.return_value = [sample_db_vc]
        mock_result.scalars.return_value = mock_scalars
        mock_session.execute = AsyncMock(return_value=mock_result)
        
        # List VCs
        result = await repository.list(stage="Seed")
        
        # Verify result
        assert len(result) == 1
        assert result[0].firm_name == sample_db_vc.firm_name
    
    async def test_async_update_vc(self, repository, mock_session, sample_vc, sample_db_vc):
        """Test updating a VC asynchronously."""
        # Mock the execute result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_db_vc
        mock_session.execute = AsyncMock(return_value=mock_result)
        mock_session.commit = AsyncMock()
        mock_session.refresh = AsyncMock()
        
        # Update the VC
        sample_vc.firm_name = "Updated Async Capital"
        result = await repository.update(sample_vc)
        
        # Verify database operations
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()
        
        # Verify the update
        assert sample_db_vc.firm_name == "Updated Async Capital"
    
    async def test_async_delete_vc(self, repository, mock_session, sample_db_vc):
        """Test deleting a VC asynchronously."""
        # Mock the execute result
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = sample_db_vc
        mock_session.execute = AsyncMock(return_value=mock_result)
        mock_session.delete = AsyncMock()
        mock_session.commit = AsyncMock()
        
        # Delete the VC
        result = await repository.delete(sample_db_vc.id)
        
        # Verify database operations
        mock_session.delete.assert_called_once()
        mock_session.commit.assert_called_once()
        
        # Verify result
        assert result is True
    
    async def test_async_search_vcs(self, repository, mock_session, sample_db_vc):
        """Test searching VCs asynchronously."""
        # Mock the execute result
        mock_result = Mock()
        mock_scalars = Mock()
        mock_scalars.all.return_value = [sample_db_vc]
        mock_result.scalars.return_value = mock_scalars
        mock_session.execute = AsyncMock(return_value=mock_result)
        
        # Search VCs
        result = await repository.search("Async")
        
        # Verify result
        assert len(result) == 1
        assert result[0].firm_name == sample_db_vc.firm_name
    
    async def test_async_find_by_check_size(self, repository, mock_session, sample_db_vc):
        """Test finding VCs by check size asynchronously."""
        # Mock the execute result
        mock_result = Mock()
        mock_scalars = Mock()
        mock_scalars.all.return_value = [sample_db_vc]
        mock_result.scalars.return_value = mock_scalars
        mock_session.execute = AsyncMock(return_value=mock_result)
        
        # Find VCs by check size
        result = await repository.find_by_check_size(150000.0, 500000.0)
        
        # Verify result
        assert len(result) == 1
        assert result[0].check_size_min == sample_db_vc.check_size_min