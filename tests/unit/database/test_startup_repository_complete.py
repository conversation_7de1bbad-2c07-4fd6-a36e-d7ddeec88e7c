"""Complete tests for startup repository to achieve 100% coverage."""

import pytest
import uuid
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker

from src.database.setup import Base
from tests.test_db_utils import init_test_db_with_migrations
from src.database.models.startup import Startup as StartupModel
from src.database.repositories.startup_repository import (
    PostgresStartupRepository,
    AsyncPostgresStartupRepository
)
from src.core.models.startup import Startup


class TestPostgresStartupRepositoryComplete:
    """Complete test suite for PostgreSQL startup repository."""
    
    @pytest.fixture
    def engine(self):
        """Create test database engine."""
        engine = create_engine("sqlite:///:memory:")
        init_test_db_with_migrations(engine)
        return engine
    
    @pytest.fixture
    def session(self, engine):
        """Create test database session."""
        SessionLocal = sessionmaker(bind=engine)
        session = SessionLocal()
        yield session
        session.close()
    
    @pytest.fixture
    def repository(self, session):
        """Create repository instance."""
        return PostgresStartupRepository(session)
    
    def test_list_with_sector_filter(self, repository, session):
        """Test listing startups with sector filter."""
        startup1 = StartupModel(id=uuid.uuid4(), name="AI 1", sector="AI/ML", stage="Seed")
        startup2 = StartupModel(id=uuid.uuid4(), name="AI 2", sector="AI/ML", stage="Series A")
        startup3 = StartupModel(id=uuid.uuid4(), name="Fin 1", sector="FinTech", stage="Seed")
        
        session.add_all([startup1, startup2, startup3])
        session.commit()
        
        result = repository.list(sector="AI/ML")
        
        assert len(result) == 2
        assert all(s.sector == "AI/ML" for s in result)
    
    def test_list_with_stage_filter(self, repository, session):
        """Test listing startups with stage filter."""
        startup1 = StartupModel(id=uuid.uuid4(), name="Seed 1", sector="AI/ML", stage="Seed")
        startup2 = StartupModel(id=uuid.uuid4(), name="Seed 2", sector="FinTech", stage="Seed")
        startup3 = StartupModel(id=uuid.uuid4(), name="Series A", sector="AI/ML", stage="Series A")
        
        session.add_all([startup1, startup2, startup3])
        session.commit()
        
        result = repository.list(stage="Seed")
        
        assert len(result) == 2
        assert all(s.stage == "Seed" for s in result)
    
    def test_search_method(self, repository, session):
        """Test search method with name and description."""
        startup1 = StartupModel(
            id=uuid.uuid4(),
            name="TechCorp AI",
            sector="AI/ML",
            stage="Series A",
            description="We build AI solutions for enterprises"
        )
        startup2 = StartupModel(
            id=uuid.uuid4(),
            name="FinanceApp",
            sector="FinTech",
            stage="Seed",
            description="AI-powered financial analytics"
        )
        startup3 = StartupModel(
            id=uuid.uuid4(),
            name="HealthTech Solutions",
            sector="HealthTech",
            stage="Series B",
            description="Healthcare platform for patients"
        )
        
        session.add_all([startup1, startup2, startup3])
        session.commit()
        
        # Search by name
        results = repository.search("Tech")
        assert len(results) == 2
        names = {s.name for s in results}
        assert "TechCorp AI" in names
        assert "HealthTech Solutions" in names
        
        # Search by description
        results = repository.search("AI")
        assert len(results) == 2
        names = {s.name for s in results}
        assert "TechCorp AI" in names
        assert "FinanceApp" in names
    
    def test_to_domain_with_nulls(self, repository, session):
        """Test converting startup with null fields."""
        startup = StartupModel(
            id=uuid.uuid4(),
            name="Minimal Startup",
            sector="Tech",
            stage="Seed",
            description=None,
            website=None,
            team_size=None,
            monthly_revenue=None
        )
        session.add(startup)
        session.commit()
        
        result = repository.get(startup.id)
        
        assert result is not None
        assert result.description == ""
        assert result.website == ""
        assert result.team_size == 0
        assert result.monthly_revenue == 0
    
    def test_delete_sync_method(self, repository, session):
        """Test the _delete_sync method directly."""
        startup_id = uuid.uuid4()
        startup = StartupModel(
            id=startup_id,
            name="To Delete",
            sector="Tech",
            stage="Seed"
        )
        session.add(startup)
        session.commit()
        
        # Delete using _delete_sync
        result = repository._delete_sync(startup_id)
        
        assert result is True
        
        # Verify it's gone
        db_startup = session.query(StartupModel).filter_by(id=startup_id).first()
        assert db_startup is None
    
    def test_delete_sync_nonexistent(self, repository):
        """Test deleting a non-existent startup with _delete_sync."""
        result = repository._delete_sync(uuid.uuid4())
        assert result is False
    
    @pytest.mark.asyncio
    async def test_async_wrapper_methods(self, repository, session):
        """Test all async wrapper methods."""
        startup = Startup(
            name="Async Wrapper Test",
            sector="Tech",
            stage="Seed",
            description="Testing async wrappers"
        )
        
        # Test save (wrapper for create)
        saved = await repository.save(startup)
        assert saved.id is not None
        assert saved.name == "Async Wrapper Test"
        
        # Test find_by_id (wrapper for get)
        found = await repository.find_by_id(saved.id)
        assert found is not None
        assert found.name == "Async Wrapper Test"
        
        # Test find_by_sector (wrapper for search_by_sector)
        sector_results = await repository.find_by_sector("Tech")
        assert len(sector_results) >= 1
        assert any(s.name == "Async Wrapper Test" for s in sector_results)
        
        # Test find_by_stage (wrapper for search_by_stage)
        stage_results = await repository.find_by_stage("Seed")
        assert len(stage_results) >= 1
        assert any(s.name == "Async Wrapper Test" for s in stage_results)
        
        # Test find_all
        all_results = await repository.find_all()
        assert len(all_results) >= 1
        assert any(s.name == "Async Wrapper Test" for s in all_results)
        
        # Test delete (wrapper for _delete_sync)
        deleted = await repository.delete(saved.id)
        assert deleted is True
        
        # Verify deletion
        not_found = repository.get(saved.id)
        assert not_found is None


class TestAsyncPostgresStartupRepositoryComplete:
    """Complete test suite for async PostgreSQL startup repository."""
    
    @pytest.fixture
    def async_engine(self):
        """Create async test database engine."""
        engine = create_async_engine("sqlite+aiosqlite:///:memory:")
        return engine
    
    @pytest.fixture
    async def async_session(self, async_engine):
        """Create async test database session."""
        async with async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        AsyncSessionLocal = async_sessionmaker(async_engine)
        async with AsyncSessionLocal() as session:
            yield session
    
    @pytest.fixture
    def async_repository(self, async_session):
        """Create async repository instance."""
        return AsyncPostgresStartupRepository(async_session)
    
    @pytest.mark.asyncio
    async def test_async_get_nonexistent(self, async_repository):
        """Test getting a non-existent startup."""
        result = await async_repository.get(uuid.uuid4())
        assert result is None
    
    @pytest.mark.asyncio
    async def test_async_list_with_filters(self, async_repository, async_session):
        """Test listing with both sector and stage filters."""
        startup1 = StartupModel(id=uuid.uuid4(), name="AI Seed", sector="AI/ML", stage="Seed")
        startup2 = StartupModel(id=uuid.uuid4(), name="AI Series A", sector="AI/ML", stage="Series A")
        startup3 = StartupModel(id=uuid.uuid4(), name="Fin Seed", sector="FinTech", stage="Seed")
        startup4 = StartupModel(id=uuid.uuid4(), name="Fin Series A", sector="FinTech", stage="Series A")
        
        async_session.add_all([startup1, startup2, startup3, startup4])
        await async_session.commit()
        
        # Test sector filter
        ai_results = await async_repository.list(sector="AI/ML")
        assert len(ai_results) == 2
        assert all(s.sector == "AI/ML" for s in ai_results)
        
        # Test stage filter
        seed_results = await async_repository.list(stage="Seed")
        assert len(seed_results) == 2
        assert all(s.stage == "Seed" for s in seed_results)
        
        # Test both filters
        filtered = await async_repository.list(sector="AI/ML", stage="Series A")
        assert len(filtered) == 1
        assert filtered[0].name == "AI Series A"
    
    @pytest.mark.asyncio
    async def test_async_list_with_pagination(self, async_repository, async_session):
        """Test pagination in async list."""
        # Create 5 startups
        for i in range(5):
            startup = StartupModel(
                id=uuid.uuid4(),
                name=f"Async Startup {i}",
                sector="Tech",
                stage="Seed"
            )
            async_session.add(startup)
        await async_session.commit()
        
        # Test limit and offset
        results = await async_repository.list(limit=2, offset=2)
        assert len(results) == 2
    
    @pytest.mark.asyncio
    async def test_async_update_nonexistent(self, async_repository):
        """Test updating a non-existent startup."""
        startup = Startup(
            id=uuid.uuid4(),
            name="Nonexistent",
            sector="Tech",
            stage="Seed"
        )
        
        with pytest.raises(ValueError, match="not found"):
            await async_repository.update(startup)
    
    @pytest.mark.asyncio
    async def test_async_delete_nonexistent(self, async_repository):
        """Test deleting a non-existent startup."""
        result = await async_repository.delete(uuid.uuid4())
        assert result is False
    
    @pytest.mark.asyncio
    async def test_async_search(self, async_repository, async_session):
        """Test async search method."""
        startup1 = StartupModel(
            id=uuid.uuid4(),
            name="AsyncTech Corp",
            sector="Tech",
            stage="Seed",
            description="Async technology solutions"
        )
        startup2 = StartupModel(
            id=uuid.uuid4(),
            name="Finance Async",
            sector="FinTech",
            stage="Series A",
            description="Tech-powered async finance"
        )
        
        async_session.add_all([startup1, startup2])
        await async_session.commit()
        
        # Search for "Async"
        results = await async_repository.search("Async")
        assert len(results) == 2
        
        # Search for "Tech" in name or description
        results = await async_repository.search("Tech")
        assert len(results) == 2
    
    @pytest.mark.asyncio
    async def test_async_to_domain_with_nulls(self, async_repository, async_session):
        """Test converting startup with null fields in async repository."""
        startup = StartupModel(
            id=uuid.uuid4(),
            name="Async Minimal",
            sector="Tech",
            stage="Seed",
            description=None,
            website=None,
            team_size=None,
            monthly_revenue=None
        )
        async_session.add(startup)
        await async_session.commit()
        
        result = await async_repository.get(startup.id)
        
        assert result is not None
        assert result.description == ""
        assert result.website == ""
        assert result.team_size == 0
        assert result.monthly_revenue == 0