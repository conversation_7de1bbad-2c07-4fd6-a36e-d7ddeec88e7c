"""Test Startup SQLAlchemy model."""

import pytest
import uuid
from datetime import datetime
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from src.database.setup import Base
from tests.test_db_utils import init_test_db_with_migrations
from src.database.models.startup import Startup


class TestStartupModel:
    """Test Startup database model."""
    
    @pytest.fixture
    def engine(self):
        """Create test database engine."""
        engine = create_engine("sqlite:///:memory:")
        init_test_db_with_migrations(engine)
        return engine
    
    @pytest.fixture
    def session(self, engine):
        """Create test database session."""
        SessionLocal = sessionmaker(bind=engine)
        session = SessionLocal()
        yield session
        session.close()
    
    def test_startup_table_exists(self, engine):
        """Test that startups table is created."""
        from sqlalchemy import inspect
        
        inspector = inspect(engine)
        assert 'startups' in inspector.get_table_names()
    
    def test_startup_columns(self, engine):
        """Test that startup has all required columns."""
        from sqlalchemy import inspect
        
        inspector = inspect(engine)
        columns = {col['name'] for col in inspector.get_columns('startups')}
        
        expected_columns = {
            'id', 'name', 'sector', 'stage', 'description', 
            'website', 'team_size', 'monthly_revenue',
            'created_at', 'updated_at'
        }
        
        assert expected_columns.issubset(columns)
    
    def test_create_startup(self, session):
        """Test creating a startup."""
        startup_id = uuid.uuid4()
        startup = Startup(
            id=startup_id,
            name="Test Startup",
            sector="AI/ML",
            stage="Series A",
            description="A test startup",
            website="https://test.com",
            team_size=10,
            monthly_revenue=50000.0
        )
        
        session.add(startup)
        session.commit()
        
        # Query it back
        result = session.query(Startup).filter_by(id=startup_id).first()
        assert result is not None
        assert result.name == "Test Startup"
        assert result.sector == "AI/ML"
        assert result.stage == "Series A"
    
    def test_startup_timestamps(self, session):
        """Test that created_at and updated_at are set automatically."""
        startup = Startup(
            id=uuid.uuid4(),
            name="Timestamp Test",
            sector="FinTech",
            stage="Seed"
        )
        
        session.add(startup)
        session.commit()
        
        assert startup.created_at is not None
        assert startup.updated_at is not None
        assert isinstance(startup.created_at, datetime)
        assert isinstance(startup.updated_at, datetime)
    
    def test_startup_update(self, session):
        """Test updating a startup."""
        startup_id = uuid.uuid4()
        startup = Startup(
            id=startup_id,
            name="Original Name",
            sector="EdTech",
            stage="Pre-seed"
        )
        
        session.add(startup)
        session.commit()
        
        # Update the startup
        startup.name = "Updated Name"
        startup.stage = "Seed"
        session.commit()
        
        # Query it back
        result = session.query(Startup).filter_by(id=startup_id).first()
        assert result.name == "Updated Name"
        assert result.stage == "Seed"
        assert result.updated_at > result.created_at
    
    def test_startup_required_fields(self, session):
        """Test that required fields are enforced."""
        # Missing name should fail
        startup = Startup(
            id=uuid.uuid4(),
            sector="AI/ML",
            stage="Series A"
        )
        
        session.add(startup)
        
        with pytest.raises(Exception):  # IntegrityError in real DB
            session.commit()
    
    def test_startup_defaults(self, session):
        """Test default values."""
        startup = Startup(
            id=uuid.uuid4(),
            name="Default Test",
            sector="SaaS",
            stage="Series B"
        )
        
        session.add(startup)
        session.commit()
        
        assert startup.team_size == 0
        assert startup.monthly_revenue == 0.0
        assert startup.description is None
        assert startup.website is None
    
    def test_startup_relationships(self, session):
        """Test that startup can have relationships."""
        startup = Startup(
            id=uuid.uuid4(),
            name="Relationship Test",
            sector="HealthTech",
            stage="Series A"
        )
        
        session.add(startup)
        session.commit()
        
        # Test that matches relationship exists
        assert hasattr(startup, 'matches')
    
    def test_startup_json_fields(self, session):
        """Test JSON fields for flexible data storage."""
        startup_id = uuid.uuid4()
        startup = Startup(
            id=startup_id,
            name="JSON Test",
            sector="AI/ML",
            stage="Series A",
            meta_data={
                "founders": ["Alice", "Bob"],
                "technologies": ["Python", "React"],
                "funding_rounds": [
                    {"stage": "Seed", "amount": 1000000}
                ]
            }
        )
        
        session.add(startup)
        session.commit()
        
        # Query it back
        result = session.query(Startup).filter_by(id=startup_id).first()
        assert result.meta_data is not None
        assert "founders" in result.meta_data
        assert len(result.meta_data["founders"]) == 2