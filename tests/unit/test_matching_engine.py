from src.core.services.matching_engine import MatchingEngine
from src.core.models.startup import Startup
from src.core.models.vc import VC
from src.core.models.match import Match

def test_basic_matching():
    startup = Startup(
        name="TechCo",
        sector="B2B SaaS",
        stage="Seed",
        description="AI for sales"
    )
    
    vc = VC(
        firm_name="AI Ventures",
        sectors=["B2B SaaS", "AI/ML"],
        stages=["Seed", "Series A"],
        thesis="We invest in AI-powered B2B SaaS"
    )
    
    engine = MatchingEngine()
    match = engine.calculate_match(startup, vc)
    
    assert match.score >= 0.8
    assert "sector alignment" in match.reasons
    assert "stage match" in match.reasons
    
def test_no_match_wrong_stage():
    startup = Startup(name="LateCo", sector="B2B", stage="Series D")
    vc = VC(firm_name="Early VC", stages=["Pre-seed", "Seed"])
    
    engine = MatchingEngine()
    match = engine.calculate_match(startup, vc)
    
    assert match.score < 0.3
    assert "stage mismatch" in match.reasons

def test_no_sector_match():
    """Test case where startup and VC have no matching sectors."""
    startup = Startup(
        name="HealthTech Co",
        sector="HealthTech",
        stage="Seed",
        description="Medical devices"
    )
    
    vc = VC(
        firm_name="FinTech Ventures",
        sectors=["FinTech", "Crypto"],
        stages=["Seed", "Series A"],
        thesis="We invest in financial technology"
    )
    
    engine = MatchingEngine()
    match = engine.calculate_match(startup, vc)
    
    assert "no sector match" in match.reasons
    assert "stage match" in match.reasons

def test_thesis_alignment():
    """Test case where thesis keywords match startup description."""
    startup = Startup(
        name="AI Sales Co",
        sector="B2B SaaS",
        stage="Seed",
        description="We use artificial intelligence and machine learning to improve sales productivity"
    )
    
    vc = VC(
        firm_name="AI Focus VC",
        sectors=["B2B SaaS"],
        stages=["Seed"],
        thesis="We invest in artificial intelligence and machine learning startups"
    )
    
    engine = MatchingEngine()
    match = engine.calculate_match(startup, vc)
    
    assert "thesis alignment" in match.reasons
    assert match.score >= 0.9  # Should get high score for all alignments