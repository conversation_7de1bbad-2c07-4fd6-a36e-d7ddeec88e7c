"""Comprehensive unit tests for the LangChain AI adapter.

This test suite covers all functionality of the LangChainAIAdapter including:
- Initialization with different configurations
- analyze_startup method with cache enabled/disabled
- analyze_vc method with/without website content
- generate_match_rationale with/without pre-computed insights
- batch_analyze_startups with concurrency limits
- Usage stats methods
- All private helper methods
- Error handling and AIAnalysisError exceptions
- Model conversions between LangChain models and domain models
- Edge cases (empty data, null values, API failures)
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import List, Dict, Any
import asyncio

from src.adapters.ai.langchain_adapter import LangChainAIAdapter
from src.core.ports.ai_port import (
    StartupInsights, VCInsights, MatchRationale, AIAnalysisError
)
from src.core.models.startup import Startup
from src.core.models.vc import VC
from src.core.ai.models_v2 import StartupAnalysis, VCThesisAnalysis, InvestmentFocus, Stage
from src.core.ai.analyzer import AIAnalyzerService


@pytest.fixture
def mock_ai_analyzer():
    """Mock AIAnalyzerService for testing."""
    analyzer = Mock(spec=AIAnalyzerService)
    
    # Setup default mock responses
    analyzer.analyze_startup = AsyncMock()
    analyzer.extract_vc_thesis = AsyncMock()
    analyzer.batch_analyze_startups = AsyncMock()
    analyzer.get_usage_stats = Mock(return_value={
        "total_tokens": 1000,
        "total_cost": 0.05,
        "requests": 10
    })
    analyzer.reset_usage_stats = Mock()
    
    return analyzer


@pytest.fixture
def sample_startup():
    """Create a sample startup for testing."""
    return Startup(
        name="TechCo",
        description="AI-powered analytics platform",
        sector="Technology",
        stage="Series A",
        website="https://techco.com",
        team_size=25,
        monthly_revenue=100000
    )


@pytest.fixture
def sample_vc():
    """Create a sample VC for testing."""
    return VC(
        firm_name="Innovation Ventures",
        website="https://innovation.vc",
        thesis="Early-stage technology companies",
        check_size_min=1000000,
        check_size_max=10000000,
        stages=["Seed", "Series A"],
        sectors=["Technology", "AI/ML", "SaaS"]
    )


@pytest.fixture
def sample_startup_analysis():
    """Create a sample StartupAnalysis response."""
    return StartupAnalysis(
        company_summary="AI-powered analytics platform with strong growth",
        key_technologies=["Machine Learning", "Natural Language Processing", "Cloud Computing"],
        market_opportunity="$50B analytics market growing at 25% CAGR",
        competitive_advantages=["Proprietary AI algorithms", "First-mover advantage"],
        team_strengths=["Ex-Google engineers", "Strong domain expertise"],
        risk_factors=["Competitive market", "High customer acquisition cost"],
        growth_potential=8.5,
        innovation_score=9.0,
        market_fit_score=7.5,
        sectors=["Technology", "Analytics"],
        target_customers=["Enterprise", "Mid-market"],
        revenue_model="SaaS subscription"
    )


@pytest.fixture
def sample_vc_thesis_analysis():
    """Create a sample VCThesisAnalysis response."""
    return VCThesisAnalysis(
        thesis_summary="Focus on early-stage AI/ML companies with enterprise applications",
        investment_focus=InvestmentFocus(
            sectors=["Technology", "AI/ML", "SaaS"],
            stages=[Stage.SEED, Stage.SERIES_A],
            geographies=["North America", "Europe"]
        ),
        check_size_range={"min": 1000000, "max": 10000000},
        portfolio_themes=["Enterprise AI", "Developer Tools", "Data Infrastructure"],
        investment_criteria=[
            "Strong technical team",
            "Clear path to $100M revenue",
            "Defensible technology"
        ],
        notable_investments=["DataCo", "MLPlatform", "CloudAnalytics"],
        investment_timeline="12-18 months",
        value_add=["Technical recruiting", "Enterprise sales", "Strategic partnerships"]
    )


class TestLangChainAIAdapterInitialization:
    """Test adapter initialization with different configurations."""
    
    def test_init_default_config(self):
        """Test initialization with default configuration."""
        with patch('src.adapters.ai.langchain_adapter.AIAnalyzerService') as mock_analyzer_class:
            adapter = LangChainAIAdapter()
            
            mock_analyzer_class.assert_called_once_with(
                openai_api_key=None,
                model_name="gpt-4",
                temperature=0.3,
                enable_streaming=False
            )
            assert adapter.enable_caching is True
    
    def test_init_custom_config(self):
        """Test initialization with custom configuration."""
        with patch('src.adapters.ai.langchain_adapter.AIAnalyzerService') as mock_analyzer_class:
            adapter = LangChainAIAdapter(
                openai_api_key="test-key",
                model_name="gpt-3.5-turbo",
                temperature=0.5,
                enable_caching=False
            )
            
            mock_analyzer_class.assert_called_once_with(
                openai_api_key="test-key",
                model_name="gpt-3.5-turbo",
                temperature=0.5,
                enable_streaming=False
            )
            assert adapter.enable_caching is False


class TestAnalyzeStartup:
    """Test analyze_startup method."""
    
    @pytest.mark.asyncio
    async def test_analyze_startup_success(self, mock_ai_analyzer, sample_startup, sample_startup_analysis):
        """Test successful startup analysis."""
        mock_ai_analyzer.analyze_startup.return_value = sample_startup_analysis
        
        with patch('src.adapters.ai.langchain_adapter.AIAnalyzerService', return_value=mock_ai_analyzer):
            adapter = LangChainAIAdapter()
            adapter.analyzer = mock_ai_analyzer
            
            insights = await adapter.analyze_startup(sample_startup)
            
            # Verify the call
            mock_ai_analyzer.analyze_startup.assert_called_once_with(
                sample_startup,
                use_cache=True
            )
            
            # Verify the conversion
            assert isinstance(insights, StartupInsights)
            assert insights.key_technologies == sample_startup_analysis.key_technologies
            assert insights.market_opportunity == sample_startup_analysis.market_opportunity
            assert insights.competitive_advantages == sample_startup_analysis.competitive_advantages
            assert insights.team_strengths == sample_startup_analysis.team_strengths
            assert insights.risk_factors == sample_startup_analysis.risk_factors
            assert insights.growth_potential_score == 0.85  # 8.5 / 10
            assert insights.innovation_score == 0.9  # 9.0 / 10
            assert insights.market_fit_score == 0.75  # 7.5 / 10
    
    @pytest.mark.asyncio
    async def test_analyze_startup_cache_disabled(self, mock_ai_analyzer, sample_startup, sample_startup_analysis):
        """Test startup analysis with cache disabled."""
        mock_ai_analyzer.analyze_startup.return_value = sample_startup_analysis
        
        with patch('src.adapters.ai.langchain_adapter.AIAnalyzerService', return_value=mock_ai_analyzer):
            adapter = LangChainAIAdapter(enable_caching=False)
            adapter.analyzer = mock_ai_analyzer
            
            await adapter.analyze_startup(sample_startup, use_cache=True)
            
            # Should pass False for use_cache when caching is disabled
            mock_ai_analyzer.analyze_startup.assert_called_once_with(
                sample_startup,
                use_cache=False
            )
    
    @pytest.mark.asyncio
    async def test_analyze_startup_error_handling(self, mock_ai_analyzer, sample_startup):
        """Test error handling in startup analysis."""
        mock_ai_analyzer.analyze_startup.side_effect = Exception("API error")
        
        with patch('src.adapters.ai.langchain_adapter.AIAnalyzerService', return_value=mock_ai_analyzer):
            adapter = LangChainAIAdapter()
            adapter.analyzer = mock_ai_analyzer
            
            with pytest.raises(AIAnalysisError) as exc_info:
                await adapter.analyze_startup(sample_startup)
            
            assert "Failed to analyze startup" in str(exc_info.value)
            assert exc_info.value.original_error is not None


class TestAnalyzeVC:
    """Test analyze_vc method."""
    
    @pytest.mark.asyncio
    async def test_analyze_vc_success(self, mock_ai_analyzer, sample_vc, sample_vc_thesis_analysis):
        """Test successful VC analysis."""
        mock_ai_analyzer.extract_vc_thesis.return_value = sample_vc_thesis_analysis
        
        with patch('src.adapters.ai.langchain_adapter.AIAnalyzerService', return_value=mock_ai_analyzer):
            adapter = LangChainAIAdapter()
            adapter.analyzer = mock_ai_analyzer
            
            website_content = "VC website content..."
            insights = await adapter.analyze_vc(sample_vc, website_content)
            
            # Verify the call
            mock_ai_analyzer.extract_vc_thesis.assert_called_once_with(
                sample_vc,
                website_content,
                use_cache=True
            )
            
            # Verify the conversion
            assert isinstance(insights, VCInsights)
            assert insights.thesis_summary == sample_vc_thesis_analysis.thesis_summary
            assert insights.preferred_sectors == ["Technology", "AI/ML", "SaaS"]
            assert insights.preferred_stages == ["seed", "series_a"]
            assert insights.typical_check_size == {"min": 1000000, "max": 10000000}
            assert insights.portfolio_focus == sample_vc_thesis_analysis.portfolio_themes
            assert insights.investment_criteria == sample_vc_thesis_analysis.investment_criteria
            assert insights.exclusion_criteria == []
    
    @pytest.mark.asyncio
    async def test_analyze_vc_no_website_content(self, mock_ai_analyzer, sample_vc):
        """Test VC analysis without website content."""
        with patch('src.adapters.ai.langchain_adapter.AIAnalyzerService', return_value=mock_ai_analyzer):
            adapter = LangChainAIAdapter()
            adapter.analyzer = mock_ai_analyzer
            
            with pytest.raises(AIAnalysisError) as exc_info:
                await adapter.analyze_vc(sample_vc, website_content=None)
            
            assert "Website content is required" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_analyze_vc_error_handling(self, mock_ai_analyzer, sample_vc):
        """Test error handling in VC analysis."""
        mock_ai_analyzer.extract_vc_thesis.side_effect = Exception("API error")
        
        with patch('src.adapters.ai.langchain_adapter.AIAnalyzerService', return_value=mock_ai_analyzer):
            adapter = LangChainAIAdapter()
            adapter.analyzer = mock_ai_analyzer
            
            with pytest.raises(AIAnalysisError) as exc_info:
                await adapter.analyze_vc(sample_vc, "website content")
            
            assert "Failed to analyze VC" in str(exc_info.value)
            assert exc_info.value.original_error is not None


class TestGenerateMatchRationale:
    """Test generate_match_rationale method."""
    
    @pytest.mark.asyncio
    async def test_generate_match_rationale_with_insights(self, sample_startup, sample_vc):
        """Test match rationale generation with pre-computed insights."""
        startup_insights = StartupInsights(
            key_technologies=["AI", "ML", "Cloud"],
            market_opportunity="Large and growing market",
            competitive_advantages=["First mover", "Strong team"],
            team_strengths=["Technical expertise", "Domain knowledge"],
            risk_factors=["Competition", "Market timing"],
            growth_potential_score=0.85,
            innovation_score=0.9,
            market_fit_score=0.75
        )
        
        vc_insights = VCInsights(
            thesis_summary="Focus on AI companies",
            preferred_sectors=["Technology", "AI/ML"],
            preferred_stages=["Series A"],
            typical_check_size={"min": 1000000, "max": 10000000},
            portfolio_focus=["AI", "Enterprise"],
            investment_criteria=["Strong team", "Large market"],
            exclusion_criteria=[]
        )
        
        with patch('src.adapters.ai.langchain_adapter.AIAnalyzerService'):
            adapter = LangChainAIAdapter(openai_api_key="test-key")
            
            rationale = await adapter.generate_match_rationale(
                sample_startup, sample_vc, startup_insights, vc_insights
            )
        
        assert isinstance(rationale, MatchRationale)
        assert 0 <= rationale.compatibility_score <= 1
        assert len(rationale.key_alignments) > 0
        assert isinstance(rationale.potential_concerns, list)
        assert len(rationale.suggested_talking_points) > 0
        assert rationale.confidence_level == 0.85
    
    @pytest.mark.asyncio
    async def test_generate_match_rationale_without_insights(
        self, mock_ai_analyzer, sample_startup, sample_vc,
        sample_startup_analysis, sample_vc_thesis_analysis
    ):
        """Test match rationale generation without pre-computed insights."""
        mock_ai_analyzer.analyze_startup.return_value = sample_startup_analysis
        mock_ai_analyzer.extract_vc_thesis.return_value = sample_vc_thesis_analysis
        
        with patch('src.adapters.ai.langchain_adapter.AIAnalyzerService', return_value=mock_ai_analyzer):
            adapter = LangChainAIAdapter()
            adapter.analyzer = mock_ai_analyzer
            
            # Mock analyze_vc to avoid website content requirement
            async def mock_analyze_vc(vc, website_content=None, use_cache=True):
                return VCInsights(
                    thesis_summary="Focus on AI companies",
                    preferred_sectors=["Technology"],
                    preferred_stages=["Series A"],
                    typical_check_size={"min": 1000000, "max": 10000000},
                    portfolio_focus=["AI"],
                    investment_criteria=["Strong team"],
                    exclusion_criteria=[]
                )
            
            adapter.analyze_vc = mock_analyze_vc
            
            rationale = await adapter.generate_match_rationale(sample_startup, sample_vc)
            
            assert isinstance(rationale, MatchRationale)
            assert rationale.compatibility_score > 0
    
    @pytest.mark.asyncio
    async def test_generate_match_rationale_error_handling(self, mock_ai_analyzer, sample_startup, sample_vc):
        """Test error handling in match rationale generation."""
        mock_ai_analyzer.analyze_startup.side_effect = Exception("Analysis failed")
        
        with patch('src.adapters.ai.langchain_adapter.AIAnalyzerService', return_value=mock_ai_analyzer):
            adapter = LangChainAIAdapter()
            adapter.analyzer = mock_ai_analyzer
            
            with pytest.raises(AIAnalysisError) as exc_info:
                await adapter.generate_match_rationale(sample_startup, sample_vc)
            
            assert "Failed to generate match rationale" in str(exc_info.value)


class TestBatchAnalyzeStartups:
    """Test batch_analyze_startups method."""
    
    @pytest.mark.asyncio
    async def test_batch_analyze_success(self, mock_ai_analyzer, sample_startup, sample_startup_analysis):
        """Test successful batch analysis."""
        startups = [sample_startup, sample_startup]
        analyses = [sample_startup_analysis, sample_startup_analysis]
        
        mock_ai_analyzer.batch_analyze_startups.return_value = analyses
        
        with patch('src.adapters.ai.langchain_adapter.AIAnalyzerService', return_value=mock_ai_analyzer):
            adapter = LangChainAIAdapter()
            adapter.analyzer = mock_ai_analyzer
            
            insights_list = await adapter.batch_analyze_startups(startups, max_concurrent=3)
            
            # Verify the call
            mock_ai_analyzer.batch_analyze_startups.assert_called_once_with(
                startups,
                use_cache=True,
                max_concurrent=3
            )
            
            # Verify results
            assert len(insights_list) == 2
            for insights in insights_list:
                assert isinstance(insights, StartupInsights)
                assert insights.growth_potential_score == 0.85
    
    @pytest.mark.asyncio
    async def test_batch_analyze_empty_list(self, mock_ai_analyzer):
        """Test batch analysis with empty list."""
        mock_ai_analyzer.batch_analyze_startups.return_value = []
        
        with patch('src.adapters.ai.langchain_adapter.AIAnalyzerService', return_value=mock_ai_analyzer):
            adapter = LangChainAIAdapter()
            adapter.analyzer = mock_ai_analyzer
            
            insights_list = await adapter.batch_analyze_startups([])
            
            assert insights_list == []
    
    @pytest.mark.asyncio
    async def test_batch_analyze_error_handling(self, mock_ai_analyzer, sample_startup):
        """Test error handling in batch analysis."""
        mock_ai_analyzer.batch_analyze_startups.side_effect = Exception("Batch failed")
        
        with patch('src.adapters.ai.langchain_adapter.AIAnalyzerService', return_value=mock_ai_analyzer):
            adapter = LangChainAIAdapter()
            adapter.analyzer = mock_ai_analyzer
            
            with pytest.raises(AIAnalysisError) as exc_info:
                await adapter.batch_analyze_startups([sample_startup])
            
            assert "Failed to batch analyze startups" in str(exc_info.value)


class TestUsageStats:
    """Test usage statistics methods."""
    
    def test_get_usage_stats(self, mock_ai_analyzer):
        """Test getting usage statistics."""
        expected_stats = {
            "total_tokens": 5000,
            "total_cost": 0.25,
            "requests": 50
        }
        mock_ai_analyzer.get_usage_stats.return_value = expected_stats
        
        with patch('src.adapters.ai.langchain_adapter.AIAnalyzerService', return_value=mock_ai_analyzer):
            adapter = LangChainAIAdapter()
            adapter.analyzer = mock_ai_analyzer
            
            stats = adapter.get_usage_stats()
            
            assert stats == expected_stats
            mock_ai_analyzer.get_usage_stats.assert_called_once()
    
    def test_reset_usage_stats(self, mock_ai_analyzer):
        """Test resetting usage statistics."""
        with patch('src.adapters.ai.langchain_adapter.AIAnalyzerService', return_value=mock_ai_analyzer):
            adapter = LangChainAIAdapter()
            adapter.analyzer = mock_ai_analyzer
            
            adapter.reset_usage_stats()
            
            mock_ai_analyzer.reset_usage_stats.assert_called_once()


class TestPrivateHelperMethods:
    """Test private helper methods."""
    
    def test_calculate_compatibility_full_alignment(self, sample_startup, sample_vc):
        """Test compatibility calculation with full alignment."""
        startup_insights = StartupInsights(
            key_technologies=["AI"],
            market_opportunity="Large",
            competitive_advantages=["Strong"],
            team_strengths=["Expert"],
            risk_factors=[],
            growth_potential_score=0.8,
            innovation_score=0.9,
            market_fit_score=0.7
        )
        
        vc_insights = VCInsights(
            thesis_summary="AI focus",
            preferred_sectors=["Technology"],  # Matches startup sector
            preferred_stages=["Series A"],  # Matches startup stage
            typical_check_size={"min": 1000000, "max": 10000000},
            portfolio_focus=["AI"],
            investment_criteria=["Strong team"],
            exclusion_criteria=[]
        )
        
        with patch('src.adapters.ai.langchain_adapter.AIAnalyzerService'):
            adapter = LangChainAIAdapter(openai_api_key="test-key")
            score = adapter._calculate_compatibility(
                sample_startup, sample_vc, startup_insights, vc_insights
            )
        
        # Should have high score due to alignment
        assert score > 0.6
    
    def test_calculate_compatibility_no_alignment(self, sample_startup, sample_vc):
        """Test compatibility calculation with no alignment."""
        startup_insights = StartupInsights(
            key_technologies=["Blockchain"],
            market_opportunity="Small",
            competitive_advantages=[],
            team_strengths=[],
            risk_factors=["High risk"],
            growth_potential_score=0.3,
            innovation_score=0.4,
            market_fit_score=0.2
        )
        
        vc_insights = VCInsights(
            thesis_summary="Biotech focus",
            preferred_sectors=["Healthcare"],  # Doesn't match
            preferred_stages=["Series C"],  # Doesn't match
            typical_check_size={"min": 50000000, "max": 100000000},
            portfolio_focus=["Biotech"],
            investment_criteria=["FDA approval"],
            exclusion_criteria=["Technology"]
        )
        
        with patch('src.adapters.ai.langchain_adapter.AIAnalyzerService'):
            adapter = LangChainAIAdapter(openai_api_key="test-key")
            score = adapter._calculate_compatibility(
                sample_startup, sample_vc, startup_insights, vc_insights
            )
        
        # Should have low score due to misalignment
        assert score < 0.2
    
    def test_find_alignments(self):
        """Test finding alignments between startup and VC."""
        startup_insights = StartupInsights(
            key_technologies=["AI", "ML", "Cloud"],
            market_opportunity="$10B market growing at 30% CAGR",
            competitive_advantages=["Patent portfolio"],
            team_strengths=["PhD team"],
            risk_factors=[],
            growth_potential_score=0.9,
            innovation_score=0.85,
            market_fit_score=0.8
        )
        
        vc_insights = VCInsights(
            thesis_summary="AI focus",
            preferred_sectors=["AI"],
            preferred_stages=["Series A"],
            typical_check_size={"min": 1000000, "max": 10000000},
            portfolio_focus=["AI", "ML", "Enterprise"],
            investment_criteria=["Technical team"],
            exclusion_criteria=[]
        )
        
        with patch('src.adapters.ai.langchain_adapter.AIAnalyzerService'):
            adapter = LangChainAIAdapter(openai_api_key="test-key")
            alignments = adapter._find_alignments(startup_insights, vc_insights)
        
        assert len(alignments) > 0
        assert any("Technology alignment" in align for align in alignments)
        assert any("market opportunity" in align for align in alignments)
        assert any("growth potential" in align for align in alignments)
    
    def test_identify_concerns(self):
        """Test identifying concerns in a match."""
        startup_insights = StartupInsights(
            key_technologies=["Unproven tech"],
            market_opportunity="Uncertain",
            competitive_advantages=[],
            team_strengths=[],
            risk_factors=["Market risk", "Tech risk", "Team risk", "Funding risk"],
            growth_potential_score=0.3,
            innovation_score=0.4,
            market_fit_score=0.2
        )
        
        vc_insights = VCInsights(
            thesis_summary="Conservative",
            preferred_sectors=["Proven tech"],
            preferred_stages=["Series B"],
            typical_check_size={"min": 10000000, "max": 50000000},
            portfolio_focus=["Mature companies"],
            investment_criteria=["Proven revenue"],
            exclusion_criteria=["High risk"]
        )
        
        with patch('src.adapters.ai.langchain_adapter.AIAnalyzerService'):
            adapter = LangChainAIAdapter(openai_api_key="test-key")
            concerns = adapter._identify_concerns(startup_insights, vc_insights)
        
        assert len(concerns) > 0
        assert any("risk factors" in concern for concern in concerns)
        assert any("Market fit" in concern for concern in concerns)
    
    def test_generate_talking_points(self, sample_startup, sample_vc):
        """Test generating talking points."""
        startup_insights = StartupInsights(
            key_technologies=["AI", "NLP", "Cloud"],
            market_opportunity="Enterprise AI market",
            competitive_advantages=["First mover"],
            team_strengths=["Ex-Google team", "Domain experts"],
            risk_factors=[],
            growth_potential_score=0.8,
            innovation_score=0.9,
            market_fit_score=0.7
        )
        
        vc_insights = VCInsights(
            thesis_summary="Enterprise AI focus",
            preferred_sectors=["Technology", "AI"],
            preferred_stages=["Series A"],
            typical_check_size={"min": 1000000, "max": 10000000},
            portfolio_focus=["Enterprise", "AI"],
            investment_criteria=["Strong team"],
            exclusion_criteria=[]
        )
        
        with patch('src.adapters.ai.langchain_adapter.AIAnalyzerService'):
            adapter = LangChainAIAdapter(openai_api_key="test-key")
            talking_points = adapter._generate_talking_points(
                sample_startup, sample_vc, startup_insights, vc_insights
            )
        
        assert len(talking_points) >= 4
        assert any("technology" in point.lower() for point in talking_points)
        assert any("market" in point.lower() for point in talking_points)
        assert any("team" in point.lower() for point in talking_points)
        assert any(sample_vc.firm_name in point for point in talking_points)


class TestEdgeCases:
    """Test edge cases and error conditions."""
    
    @pytest.mark.asyncio
    async def test_analyze_startup_with_minimal_data(self, mock_ai_analyzer):
        """Test analyzing startup with minimal data."""
        minimal_startup = Startup(
            name="MinCo",
            description="",
            sector="Unknown",
            stage="Unknown",
            website="",
            team_size=0,
            monthly_revenue=0
        )
        
        minimal_analysis = StartupAnalysis(
            company_summary="Unknown company",
            key_technologies=[],
            market_opportunity="Unknown",
            competitive_advantages=[],
            team_strengths=[],
            risk_factors=["Limited information"],
            growth_potential=5.0,
            innovation_score=5.0,
            market_fit_score=5.0,
            sectors=[],
            target_customers=[],
            revenue_model="Unknown"
        )
        
        mock_ai_analyzer.analyze_startup.return_value = minimal_analysis
        
        with patch('src.adapters.ai.langchain_adapter.AIAnalyzerService', return_value=mock_ai_analyzer):
            adapter = LangChainAIAdapter()
            adapter.analyzer = mock_ai_analyzer
            
            insights = await adapter.analyze_startup(minimal_startup)
            
            assert insights.key_technologies == []
            assert insights.growth_potential_score == 0.5
    
    @pytest.mark.asyncio
    async def test_analyze_vc_with_null_check_size(self, mock_ai_analyzer, sample_vc):
        """Test analyzing VC with null check size."""
        vc_analysis = VCThesisAnalysis(
            thesis_summary="Investment thesis",
            investment_focus=InvestmentFocus(
                sectors=["Tech"],
                stages=[Stage.SEED],
                geographies=["US"]
            ),
            check_size_range=None,  # Null check size
            portfolio_themes=["AI"],
            investment_criteria=["Strong team"],
            notable_investments=[],
            investment_timeline="12 months",
            value_add=[]
        )
        
        mock_ai_analyzer.extract_vc_thesis.return_value = vc_analysis
        
        with patch('src.adapters.ai.langchain_adapter.AIAnalyzerService', return_value=mock_ai_analyzer):
            adapter = LangChainAIAdapter()
            adapter.analyzer = mock_ai_analyzer
            
            insights = await adapter.analyze_vc(sample_vc, "website content")
            
            assert insights.typical_check_size == {"min": 0, "max": 0}
    
    @pytest.mark.asyncio
    async def test_batch_analyze_with_mixed_failures(self, mock_ai_analyzer, sample_startup):
        """Test batch analysis where some startups fail."""
        startups = [sample_startup] * 3
        
        # Mock analyzer to fail on second startup
        async def batch_with_partial_failure(startups_list, **kwargs):
            if len(startups_list) > 2:
                raise Exception("Partial batch failure")
            return []
        
        mock_ai_analyzer.batch_analyze_startups = batch_with_partial_failure
        
        with patch('src.adapters.ai.langchain_adapter.AIAnalyzerService', return_value=mock_ai_analyzer):
            adapter = LangChainAIAdapter()
            adapter.analyzer = mock_ai_analyzer
            
            with pytest.raises(AIAnalysisError):
                await adapter.batch_analyze_startups(startups)
    
    def test_calculate_compatibility_with_missing_funding_amount(self):
        """Test compatibility calculation when startup has no funding_amount."""
        startup = Startup(
            name="NoFundingCo",
            description="Company without funding info",
            sector="Technology",
            stage="Series A",
            website="https://nofunding.com",
            team_size=10,
            monthly_revenue=0
            # No funding_amount attribute
        )
        
        startup_insights = StartupInsights(
            key_technologies=["AI"],
            market_opportunity="Large",
            competitive_advantages=["Strong"],
            team_strengths=["Expert"],
            risk_factors=[],
            growth_potential_score=0.8,
            innovation_score=0.9,
            market_fit_score=0.7
        )
        
        vc_insights = VCInsights(
            thesis_summary="Tech focus",
            preferred_sectors=["Technology"],
            preferred_stages=["Series A"],
            typical_check_size={"min": 1000000, "max": 5000000},
            portfolio_focus=["Tech"],
            investment_criteria=["Team"],
            exclusion_criteria=[]
        )
        
        with patch('src.adapters.ai.langchain_adapter.AIAnalyzerService'):
            adapter = LangChainAIAdapter(openai_api_key="test-key")
            score = adapter._calculate_compatibility(
                startup, startup, startup_insights, vc_insights
            )
        
        # Should still calculate score without funding info
        assert 0 <= score <= 1
    
    def test_find_alignments_empty_data(self):
        """Test finding alignments with empty data."""
        startup_insights = StartupInsights(
            key_technologies=[],
            market_opportunity="",
            competitive_advantages=[],
            team_strengths=[],
            risk_factors=[],
            growth_potential_score=0.0,
            innovation_score=0.0,
            market_fit_score=0.0
        )
        
        vc_insights = VCInsights(
            thesis_summary="",
            preferred_sectors=[],
            preferred_stages=[],
            typical_check_size={"min": 0, "max": 0},
            portfolio_focus=[],
            investment_criteria=[],
            exclusion_criteria=[]
        )
        
        with patch('src.adapters.ai.langchain_adapter.AIAnalyzerService'):
            adapter = LangChainAIAdapter(openai_api_key="test-key")
            alignments = adapter._find_alignments(startup_insights, vc_insights)
        
        # Should handle empty data gracefully
        assert isinstance(alignments, list)
        assert len(alignments) == 0 or all(isinstance(a, str) for a in alignments)


class TestConcurrency:
    """Test concurrent operations."""
    
    @pytest.mark.asyncio
    async def test_concurrent_startup_analyses(self, mock_ai_analyzer, sample_startup, sample_startup_analysis):
        """Test multiple concurrent startup analyses."""
        mock_ai_analyzer.analyze_startup.return_value = sample_startup_analysis
        
        with patch('src.adapters.ai.langchain_adapter.AIAnalyzerService', return_value=mock_ai_analyzer):
            adapter = LangChainAIAdapter()
            adapter.analyzer = mock_ai_analyzer
            
            # Run multiple analyses concurrently
            tasks = [
                adapter.analyze_startup(sample_startup)
                for _ in range(5)
            ]
            
            results = await asyncio.gather(*tasks)
            
            assert len(results) == 5
            assert all(isinstance(r, StartupInsights) for r in results)
            assert mock_ai_analyzer.analyze_startup.call_count == 5
    
    @pytest.mark.asyncio
    async def test_concurrent_mixed_operations(
        self, mock_ai_analyzer, sample_startup, sample_vc,
        sample_startup_analysis, sample_vc_thesis_analysis
    ):
        """Test concurrent mixed operations (startup + VC analysis)."""
        mock_ai_analyzer.analyze_startup.return_value = sample_startup_analysis
        mock_ai_analyzer.extract_vc_thesis.return_value = sample_vc_thesis_analysis
        
        with patch('src.adapters.ai.langchain_adapter.AIAnalyzerService', return_value=mock_ai_analyzer):
            adapter = LangChainAIAdapter()
            adapter.analyzer = mock_ai_analyzer
            
            # Run mixed operations concurrently
            startup_task = adapter.analyze_startup(sample_startup)
            vc_task = adapter.analyze_vc(sample_vc, "website content")
            
            startup_insights, vc_insights = await asyncio.gather(
                startup_task, vc_task
            )
            
            assert isinstance(startup_insights, StartupInsights)
            assert isinstance(vc_insights, VCInsights)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])