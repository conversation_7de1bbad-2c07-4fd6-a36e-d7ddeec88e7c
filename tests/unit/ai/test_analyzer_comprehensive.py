"""Comprehensive unit tests for AI Analyzer Service to achieve 80%+ coverage."""

import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
import asyncio
from datetime import datetime
import time
from tenacity import RetryError
import json

from src.core.ai.analyzer import AIAnalyzerService, TokenUsageCallback
from src.core.ai.models import StartupAnalysis, VCThesisAnalysis, InvestmentFocus
from src.core.ai.exceptions import AIAnalyzerError
from src.core.models.startup import Startup
from src.core.models.vc import VC


@pytest.fixture
def mock_llm():
    """Create a mock LLM for testing."""
    return Mock()


@pytest.fixture
def mock_cache():
    """Create a mock cache for testing."""
    cache = Mock()
    cache.get_startup_analysis = AsyncMock(return_value=None)
    cache.set_startup_analysis = AsyncMock()
    cache.get_vc_thesis = AsyncMock(return_value=None)
    cache.set_vc_thesis = AsyncMock()
    cache.get_cache_stats = Mock(return_value={
        "hits": 10,
        "misses": 5,
        "total_requests": 15
    })
    cache.clear = AsyncMock()
    return cache


@pytest.fixture
def mock_startup():
    """Create a mock startup for testing."""
    startup = Mock(spec=Startup)
    startup.name = "TechCorp"
    startup.description = "AI-powered analytics platform"
    startup.sector = "AI/ML"
    startup.stage = "Series A"
    startup.website = "https://techcorp.com"
    startup.founded_year = 2020
    startup.team_size = 25
    return startup


@pytest.fixture
def mock_vc():
    """Create a mock VC for testing."""
    vc = Mock(spec=VC)
    vc.firm_name = "Venture Partners"
    vc.website = "https://venturepartners.com"
    vc.sectors = ["AI/ML", "FinTech"]
    vc.stages = ["Series A", "Series B"]
    return vc


@pytest.fixture
def sample_startup_analysis():
    """Create a sample startup analysis result."""
    from src.core.ai.models import BusinessModel, BusinessModelType, TechnologyStack
    
    return StartupAnalysis(
        sectors=["AI/ML", "Analytics"],
        technologies=TechnologyStack(
            languages=["Python", "JavaScript"],
            frameworks=["TensorFlow", "React"],
            infrastructure=["AWS", "Kubernetes"],
            ai_ml_tools=["OpenAI", "LangChain"]
        ),
        business_model=BusinessModel(
            type=BusinessModelType.SAAS,
            revenue_streams=["Subscriptions", "Enterprise licenses"],
            target_market="Enterprise companies",
            pricing_model="Tiered pricing"
        ),
        team_analysis={
            "size": 25,
            "key_roles": ["CTO", "AI Lead", "Product Manager"],
            "strengths": ["Technical expertise", "Domain knowledge"]
        },
        market_analysis={
            "tam": "$10B",
            "growth_rate": "25% YoY",
            "competition": "Moderate"
        },
        funding_status={
            "stage": "Series A",
            "raised": "$5M",
            "runway": "18 months"
        },
        key_risks=["Market competition", "Technical complexity"],
        keywords=["AI", "Analytics", "Enterprise", "SaaS"],
        value_proposition="AI-powered analytics platform for enterprise data insights",
        competitive_advantages=["First mover advantage", "Patent portfolio", "Enterprise relationships"],
        confidence_score=0.85
    )


@pytest.fixture
def sample_vc_thesis():
    """Create a sample VC thesis analysis."""
    from src.core.ai.models import InvestmentStage
    
    return VCThesisAnalysis(
        thesis_summary="Focus on AI-first companies",
        investment_focus=InvestmentFocus(
            sectors=["AI/ML", "FinTech"],
            stages=[InvestmentStage.SERIES_A, InvestmentStage.SERIES_B],
            technologies=["Machine Learning", "Blockchain"],
            geographical_focus=["US", "Europe"]
        ),
        check_size_range={"min": 2000000, "max": 10000000},
        portfolio_themes=["Enterprise AI", "Automation"],
        avoided_sectors=["Gaming", "Social Media"],
        key_criteria=["Strong technical team", "Scalable model"],
        notable_partners=["John Doe", "Jane Smith"],
        confidence_score=0.90
    )


class TestAIAnalyzerServiceInitialization:
    """Test AIAnalyzerService initialization."""
    
    @patch('src.core.ai.analyzer.ChatOpenAI')
    @patch('src.core.ai.analyzer.StartupAnalyzerChain')
    @patch('src.core.ai.analyzer.VCThesisExtractorChain')
    def test_initializes_with_default_values(self, mock_vc_chain, mock_startup_chain, mock_chat):
        # Act
        service = AIAnalyzerService()
        
        # Assert
        mock_chat.assert_called_once_with(
            openai_api_key=None,
            model_name="gpt-4",
            temperature=0.3,
            streaming=False
        )
        assert service.max_retries == 3
        assert service.cache is not None
        assert service.token_tracker is not None
    
    @patch('src.core.ai.analyzer.ChatOpenAI')
    @patch('src.core.ai.analyzer.StartupAnalyzerChain')
    @patch('src.core.ai.analyzer.VCThesisExtractorChain')
    def test_initializes_with_custom_values(self, mock_vc_chain, mock_startup_chain, mock_chat, mock_cache):
        # Act
        service = AIAnalyzerService(
            openai_api_key="test-key",
            model_name="gpt-3.5-turbo",
            temperature=0.7,
            cache=mock_cache,
            max_retries=5,
            enable_streaming=True
        )
        
        # Assert
        mock_chat.assert_called_once_with(
            openai_api_key="test-key",
            model_name="gpt-3.5-turbo",
            temperature=0.7,
            streaming=True
        )
        assert service.max_retries == 5
        assert service.cache == mock_cache


class TestStartupAnalysis:
    """Test startup analysis functionality."""
    
    @pytest.mark.asyncio
    async def test_analyze_startup_with_cache_hit(self, mock_cache, mock_startup, sample_startup_analysis):
        # Arrange
        mock_cache.get_startup_analysis.return_value = sample_startup_analysis
        
        with patch('src.core.ai.analyzer.ChatOpenAI'):
            service = AIAnalyzerService(cache=mock_cache)
        
        # Act
        result = await service.analyze_startup(mock_startup, use_cache=True)
        
        # Assert
        assert result == sample_startup_analysis
        mock_cache.get_startup_analysis.assert_called_once()
        mock_cache.set_startup_analysis.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_analyze_startup_with_cache_miss(self, mock_cache, mock_startup, sample_startup_analysis):
        # Arrange
        mock_cache.get_startup_analysis.return_value = None
        
        with patch('src.core.ai.analyzer.ChatOpenAI'):
            with patch('src.core.ai.analyzer.StartupAnalyzerChain') as mock_chain_class:
                mock_chain = Mock()
                mock_chain.analyze = AsyncMock(return_value=sample_startup_analysis)
                mock_chain_class.return_value = mock_chain
                
                service = AIAnalyzerService(cache=mock_cache)
                
                # Act
                result = await service.analyze_startup(mock_startup, use_cache=True)
        
        # Assert
        assert result == sample_startup_analysis
        mock_cache.get_startup_analysis.assert_called_once()
        mock_cache.set_startup_analysis.assert_called_once_with(
            description=mock_startup.description,
            name=mock_startup.name,
            sector=mock_startup.sector,
            stage=mock_startup.stage,
            analysis=sample_startup_analysis,
            ttl=None
        )
    
    @pytest.mark.asyncio
    async def test_analyze_startup_without_cache(self, mock_cache, mock_startup, sample_startup_analysis):
        # Arrange
        with patch('src.core.ai.analyzer.ChatOpenAI'):
            with patch('src.core.ai.analyzer.StartupAnalyzerChain') as mock_chain_class:
                mock_chain = Mock()
                mock_chain.analyze = AsyncMock(return_value=sample_startup_analysis)
                mock_chain_class.return_value = mock_chain
                
                service = AIAnalyzerService(cache=mock_cache)
                
                # Act
                result = await service.analyze_startup(mock_startup, use_cache=False)
        
        # Assert
        assert result == sample_startup_analysis
        mock_cache.get_startup_analysis.assert_not_called()
        mock_cache.set_startup_analysis.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_analyze_startup_with_retry_on_failure(self, mock_cache, mock_startup, sample_startup_analysis):
        # Arrange
        mock_cache.get_startup_analysis.return_value = None
        
        with patch('src.core.ai.analyzer.ChatOpenAI'):
            with patch('src.core.ai.analyzer.StartupAnalyzerChain') as mock_chain_class:
                mock_chain = Mock()
                # Fail twice, then succeed
                mock_chain.analyze = AsyncMock(
                    side_effect=[Exception("API Error"), Exception("API Error"), sample_startup_analysis]
                )
                mock_chain_class.return_value = mock_chain
                
                service = AIAnalyzerService(cache=mock_cache)
                
                # Act & Assert - should not raise exception
                with patch('time.sleep'):  # Speed up test
                    result = await service.analyze_startup(mock_startup)
                    
                assert mock_chain.analyze.call_count == 3
    
    @pytest.mark.asyncio
    async def test_analyze_startup_raises_after_max_retries(self, mock_cache, mock_startup):
        # Arrange
        mock_cache.get_startup_analysis.return_value = None
        
        with patch('src.core.ai.analyzer.ChatOpenAI'):
            with patch('src.core.ai.analyzer.StartupAnalyzerChain') as mock_chain_class:
                mock_chain = Mock()
                mock_chain.analyze = AsyncMock(side_effect=Exception("API Error"))
                mock_chain_class.return_value = mock_chain
                
                service = AIAnalyzerService(cache=mock_cache, max_retries=2)
                
                # Act & Assert
                with patch('time.sleep'):  # Speed up test
                    with pytest.raises(RetryError):
                        await service.analyze_startup(mock_startup)


class TestVCAnalysis:
    """Test VC thesis analysis functionality."""
    
    @pytest.mark.asyncio
    async def test_extract_vc_thesis_with_cache_hit(self, mock_cache, mock_vc, sample_vc_thesis):
        # Arrange
        mock_cache.get_vc_thesis.return_value = sample_vc_thesis
        website_content = "VC investment thesis content..."
        
        with patch('src.core.ai.analyzer.ChatOpenAI'):
            service = AIAnalyzerService(cache=mock_cache)
        
        # Act
        result = await service.extract_vc_thesis(mock_vc, website_content, use_cache=True)
        
        # Assert
        assert result == sample_vc_thesis
        mock_cache.get_vc_thesis.assert_called_once()
        mock_cache.set_vc_thesis.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_extract_vc_thesis_with_cache_miss(self, mock_cache, mock_vc, sample_vc_thesis):
        # Arrange
        mock_cache.get_vc_thesis.return_value = None
        website_content = "VC investment thesis content..."
        
        with patch('src.core.ai.analyzer.ChatOpenAI'):
            with patch('src.core.ai.analyzer.VCThesisExtractorChain') as mock_chain_class:
                mock_chain = Mock()
                mock_chain.extract = AsyncMock(return_value=sample_vc_thesis)
                mock_chain_class.return_value = mock_chain
                
                service = AIAnalyzerService(cache=mock_cache)
                
                # Act
                result = await service.extract_vc_thesis(mock_vc, website_content, use_cache=True)
        
        # Assert
        assert result == sample_vc_thesis
        mock_cache.get_vc_thesis.assert_called_once()
        mock_cache.set_vc_thesis.assert_called_once()


class TestBatchAnalysis:
    """Test batch analysis functionality."""
    
    @pytest.mark.asyncio
    async def test_batch_analyze_startups(self, mock_cache, sample_startup_analysis):
        # Arrange
        startups = []
        for i in range(3):
            startup = Mock(spec=Startup)
            startup.configure_mock(
                name=f"Startup{i}",
                description=f"Desc{i}",
                sector="AI",
                stage="Seed",
                website=f"https://startup{i}.com"
            )
            startups.append(startup)
        
        with patch('src.core.ai.analyzer.ChatOpenAI'):
            with patch('src.core.ai.analyzer.StartupAnalyzerChain') as mock_chain_class:
                mock_chain = Mock()
                mock_chain.analyze = AsyncMock(return_value=sample_startup_analysis)
                mock_chain_class.return_value = mock_chain
                
                service = AIAnalyzerService(cache=mock_cache)
                
                # Act
                results = await service.batch_analyze_startups(startups, use_cache=False)
        
        # Assert
        assert len(results) == 3
        assert all(result == sample_startup_analysis for result in results)
        assert mock_chain.analyze.call_count == 3
    
    @pytest.mark.asyncio
    async def test_batch_analyze_with_concurrency_limit(self, mock_cache):
        # Arrange
        startups = []
        for i in range(10):
            startup = Mock(spec=Startup)
            startup.configure_mock(
                name=f"Startup{i}",
                description=f"Desc{i}",
                sector="AI",
                stage="Seed"
            )
            startups.append(startup)
        
        call_times = []
        async def mock_analyze(startup):
            call_times.append(time.time())
            await asyncio.sleep(0.1)  # Simulate API call
            return Mock(spec=StartupAnalysis)
        
        with patch('src.core.ai.analyzer.ChatOpenAI'):
            with patch('src.core.ai.analyzer.StartupAnalyzerChain') as mock_chain_class:
                mock_chain = Mock()
                mock_chain.analyze = mock_analyze
                mock_chain_class.return_value = mock_chain
                
                service = AIAnalyzerService(cache=mock_cache)
                
                # Act
                await service.batch_analyze_startups(startups, use_cache=False, max_concurrent=3)
        
        # Assert - verify concurrency limit was respected
        # With max_concurrent=3, we should see batches of calls
        for i in range(0, len(call_times) - 3):
            batch_duration = call_times[i + 3] - call_times[i]
            assert batch_duration >= 0.09  # Should wait for batch to complete


class TestUsageStatistics:
    """Test usage statistics functionality."""
    
    def test_get_usage_stats(self, mock_cache):
        # Arrange
        with patch('src.core.ai.analyzer.ChatOpenAI'):
            service = AIAnalyzerService(cache=mock_cache)
            service.token_tracker.total_tokens = 1000
            service.token_tracker.total_cost = 0.05
        
        # Act
        stats = service.get_usage_stats()
        
        # Assert
        assert stats["total_tokens"] == 1000
        assert stats["total_cost"] == 0.05
        assert stats["cache_stats"] == {"hits": 10, "misses": 5, "total_requests": 15}
    
    def test_reset_usage_stats(self, mock_cache):
        # Arrange
        with patch('src.core.ai.analyzer.ChatOpenAI'):
            service = AIAnalyzerService(cache=mock_cache)
            service.token_tracker.total_tokens = 1000
            service.token_tracker.total_cost = 0.05
        
        # Act
        service.reset_usage_stats()
        
        # Assert
        assert service.token_tracker.total_tokens == 0
        assert service.token_tracker.total_cost == 0.0


class TestErrorHandling:
    """Test error handling and edge cases."""
    
    @pytest.mark.asyncio
    async def test_handles_cache_errors_gracefully(self, mock_cache, mock_startup, sample_startup_analysis):
        # Arrange
        mock_cache.get_startup_analysis.side_effect = Exception("Cache error")
        mock_cache.set_startup_analysis.side_effect = Exception("Cache error")
        
        with patch('src.core.ai.analyzer.ChatOpenAI'):
            with patch('src.core.ai.analyzer.StartupAnalyzerChain') as mock_chain_class:
                mock_chain = Mock()
                mock_chain.analyze = AsyncMock(return_value=sample_startup_analysis)
                mock_chain_class.return_value = mock_chain
                
                service = AIAnalyzerService(cache=mock_cache)
                
                # Act - should not raise cache errors
                result = await service.analyze_startup(mock_startup, use_cache=True)
        
        # Assert
        assert result == sample_startup_analysis
    
    @pytest.mark.asyncio
    async def test_logs_analysis_duration(self, mock_cache, mock_startup, sample_startup_analysis):
        # Arrange
        mock_cache.get_startup_analysis.return_value = None
        
        with patch('src.core.ai.analyzer.ChatOpenAI'):
            with patch('src.core.ai.analyzer.StartupAnalyzerChain') as mock_chain_class:
                with patch('src.core.ai.analyzer.logger') as mock_logger:
                    mock_chain = Mock()
                    mock_chain.analyze = AsyncMock(return_value=sample_startup_analysis)
                    mock_chain_class.return_value = mock_chain
                    
                    service = AIAnalyzerService(cache=mock_cache)
                    
                    # Act
                    await service.analyze_startup(mock_startup)
        
        # Assert - verify duration logging
        mock_logger.info.assert_called()
        log_call = mock_logger.info.call_args[0][0]
        assert "Analyzed startup" in log_call
        assert "TechCorp" in log_call
        assert "s" in log_call
        assert "tokens:" in log_call
        assert "cost:" in log_call