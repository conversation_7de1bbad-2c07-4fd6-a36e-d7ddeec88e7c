"""Unit tests for AI Cache functionality."""

import pytest
import json
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta

from src.core.ai.cache import AI<PERSON>ache
from src.core.ai.models import StartupAnalysis, VCThesisAnalysis, InvestmentFocus


class TestAICache:
    """Test AI caching functionality with Redis."""
    
    @pytest.fixture
    def cache(self, mock_redis):
        """Create cache instance with mocked Redis."""
        return AICache(redis_client=mock_redis)
    
    def test_generates_consistent_cache_keys_for_startup_analysis(self, cache):
        # Arrange
        params = {
            "description": "AI startup building ML tools",
            "name": "TechCo",
            "sector": "AI/ML",
            "stage": "Series A"
        }
        
        # Act
        key1 = cache._get_startup_cache_key(**params)
        key2 = cache._get_startup_cache_key(**params)
        
        # Assert
        assert key1 == key2
        assert key1.startswith("ai_analysis:startup:")
        assert len(key1) > 20  # Should include hash
    
    def test_generates_different_keys_for_different_startup_inputs(self, cache):
        # Arrange
        params1 = {"description": "AI startup", "name": "TechCo", "sector": "AI", "stage": "Seed"}
        params2 = {"description": "AI startup", "name": "TechCo", "sector": "AI", "stage": "Series A"}
        
        # Act
        key1 = cache._get_startup_cache_key(**params1)
        key2 = cache._get_startup_cache_key(**params2)
        
        # Assert
        assert key1 != key2
    
    def test_generates_consistent_cache_keys_for_vc_thesis(self, cache):
        # Arrange
        params = {
            "website_content": "We invest in AI companies...",
            "firm_name": "AI Ventures"
        }
        
        # Act
        key1 = cache._get_vc_cache_key(**params)
        key2 = cache._get_vc_cache_key(**params)
        
        # Assert
        assert key1 == key2
        assert key1.startswith("ai_analysis:vc:")
        assert len(key1) > 15
    
    @pytest.mark.asyncio
    async def test_get_startup_analysis_returns_none_on_cache_miss(self, cache, mock_redis):
        # Arrange
        mock_redis.get = AsyncMock(return_value=None)
        
        # Act
        result = await cache.get_startup_analysis(
            description="Test", name="Test", sector="Tech", stage="Seed"
        )
        
        # Assert
        assert result is None
        mock_redis.get.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_startup_analysis_deserializes_cached_data(
        self, cache, mock_redis, sample_startup_analysis
    ):
        # Arrange
        cached_data = {
            "sectors": sample_startup_analysis.sectors,
            "technologies": sample_startup_analysis.technologies.model_dump(),
            "business_model": sample_startup_analysis.business_model.model_dump(),
            "keywords": sample_startup_analysis.keywords,
            "value_proposition": sample_startup_analysis.value_proposition,
            "target_customers": sample_startup_analysis.target_customers,
            "competitive_advantages": sample_startup_analysis.competitive_advantages,
            "confidence_score": sample_startup_analysis.confidence_score
        }
        mock_redis.get = AsyncMock(return_value=json.dumps(cached_data))
        
        # Act
        result = await cache.get_startup_analysis(
            description="Test", name="Test", sector="Tech", stage="Seed"
        )
        
        # Assert
        assert isinstance(result, StartupAnalysis)
        assert result.sectors == sample_startup_analysis.sectors
        assert result.confidence_score == sample_startup_analysis.confidence_score
    
    @pytest.mark.asyncio
    async def test_get_startup_analysis_handles_corrupted_cache_data(
        self, cache, mock_redis
    ):
        # Arrange
        mock_redis.get = AsyncMock(return_value="invalid json{}")
        
        # Act
        result = await cache.get_startup_analysis(
            description="Test", name="Test", sector="Tech", stage="Seed"
        )
        
        # Assert
        assert result is None
    
    @pytest.mark.asyncio
    async def test_set_startup_analysis_serializes_and_stores_data(
        self, cache, mock_redis, sample_startup_analysis
    ):
        # Arrange
        ttl = 3600
        
        # Act
        await cache.set_startup_analysis(
            description="Test",
            name="Test",
            sector="Tech",
            stage="Seed",
            analysis=sample_startup_analysis,
            ttl=ttl
        )
        
        # Assert
        mock_redis.setex.assert_called_once()
        call_args = mock_redis.setex.call_args
        # setex is called with (key, ttl, data) as positional args
        assert call_args[0][1] == ttl  # Second positional arg is TTL
        
        # Verify serialized data
        stored_data = json.loads(call_args[0][2])  # Third positional arg is data
        assert stored_data['sectors'] == sample_startup_analysis.sectors
        assert stored_data['confidence_score'] == sample_startup_analysis.confidence_score
    
    @pytest.mark.asyncio
    async def test_set_startup_analysis_uses_default_ttl_when_not_specified(
        self, cache, mock_redis, sample_startup_analysis
    ):
        # Act
        await cache.set_startup_analysis(
            description="Test",
            name="Test",
            sector="Tech",
            stage="Seed",
            analysis=sample_startup_analysis
        )
        
        # Assert
        call_args = mock_redis.setex.call_args
        # setex is called with (key, ttl, data) as positional args
        assert call_args[0][1] == cache.default_ttl  # Second positional arg is TTL
    
    @pytest.mark.asyncio
    async def test_get_vc_thesis_deserializes_complex_nested_data(
        self, cache, mock_redis, sample_vc_thesis_analysis
    ):
        # Arrange
        cached_data = {
            "thesis_summary": sample_vc_thesis_analysis.thesis_summary,
            "investment_focus": {
                "sectors": sample_vc_thesis_analysis.investment_focus.sectors,
                "stages": ["Series A", "Series B"],
                "technologies": sample_vc_thesis_analysis.investment_focus.technologies,
                "geographical_focus": sample_vc_thesis_analysis.investment_focus.geographical_focus
            },
            "check_size_range": sample_vc_thesis_analysis.check_size_range,
            "portfolio_themes": sample_vc_thesis_analysis.portfolio_themes,
            "avoided_sectors": sample_vc_thesis_analysis.avoided_sectors,
            "key_criteria": sample_vc_thesis_analysis.key_criteria,
            "notable_partners": sample_vc_thesis_analysis.notable_partners,
            "confidence_score": sample_vc_thesis_analysis.confidence_score
        }
        mock_redis.get = AsyncMock(return_value=json.dumps(cached_data))
        
        # Act
        result = await cache.get_vc_thesis(
            website_content="Test content",
            firm_name="Test VC"
        )
        
        # Assert
        assert isinstance(result, VCThesisAnalysis)
        assert result.thesis_summary == sample_vc_thesis_analysis.thesis_summary
        assert isinstance(result.investment_focus, InvestmentFocus)
        assert result.investment_focus.sectors == sample_vc_thesis_analysis.investment_focus.sectors
        assert result.check_size_range == sample_vc_thesis_analysis.check_size_range
    
    @pytest.mark.asyncio
    async def test_invalidate_startup_cache_deletes_correct_key(self, cache, mock_redis):
        # Act
        await cache.invalidate_startup_cache(
            description="Test", name="Test", sector="Tech", stage="Seed"
        )
        
        # Assert
        mock_redis.delete.assert_called_once()
        deleted_key = mock_redis.delete.call_args[0][0]
        assert deleted_key.startswith("ai_analysis:startup:")
    
    @pytest.mark.asyncio
    async def test_invalidate_vc_cache_deletes_correct_key(self, cache, mock_redis):
        # Act
        await cache.invalidate_vc_cache(
            website_content="Test content",
            firm_name="Test VC"
        )
        
        # Assert
        mock_redis.delete.assert_called_once()
        deleted_key = mock_redis.delete.call_args[0][0]
        assert deleted_key.startswith("ai_analysis:vc:")
    
    def test_get_cache_stats_calculates_metrics_correctly(self, cache, mock_redis):
        # Arrange
        mock_redis.info.return_value = {
            'keyspace_hits': 75,
            'keyspace_misses': 25
        }
        mock_redis.keys.return_value = ['key1', 'key2', 'key3']
        
        # Act
        stats = cache.get_cache_stats()
        
        # Assert
        assert stats['hits'] == 75
        assert stats['misses'] == 25
        assert stats['total_keys'] == 3
        assert stats['hit_rate'] == 0.75
    
    def test_get_cache_stats_handles_zero_requests(self, cache, mock_redis):
        # Arrange - Fresh cache with no requests
        mock_redis.info.return_value = {
            'keyspace_hits': 0,
            'keyspace_misses': 0
        }
        mock_redis.keys.return_value = []
        
        # Act
        stats = cache.get_cache_stats()
        
        # Assert
        assert stats['hits'] == 0
        assert stats['misses'] == 0
        assert stats['total_keys'] == 0
        assert stats['hit_rate'] == 0
    
    @pytest.mark.asyncio
    async def test_cache_operations_update_statistics(self, cache, mock_redis):
        # Arrange
        # Create a valid StartupAnalysis JSON
        valid_analysis = {
            "sectors": ["AI"],
            "technologies": {"languages": [], "frameworks": [], "infrastructure": [], "ai_ml_tools": []},
            "business_model": {"type": "SaaS", "revenue_streams": [], "target_market": "B2B", "pricing_model": None},
            "keywords": ["AI"],
            "value_proposition": "AI platform",
            "target_customers": ["Businesses"],
            "competitive_advantages": ["First mover"],
            "confidence_score": 0.8
        }
        mock_redis.get = AsyncMock(side_effect=[None, json.dumps(valid_analysis)])
        
        # Act - Cache miss
        result1 = await cache.get_startup_analysis(
            description="Test", name="Test", sector="Tech", stage="Seed"
        )
        
        # Act - Cache hit
        result2 = await cache.get_startup_analysis(
            description="Test", name="Test", sector="Tech", stage="Seed"
        )
        
        # Assert
        assert result1 is None  # Cache miss
        assert result2 is not None  # Cache hit
        assert result2.sectors == ["AI"]