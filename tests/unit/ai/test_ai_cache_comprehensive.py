"""Comprehensive unit tests for AI Cache functionality."""

import pytest
import json
import hashlib
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
from redis.exceptions import RedisError

from src.core.ai.cache import AICache
from src.core.ai.models import (
    StartupAnalysis, VCThesisAnalysis, InvestmentFocus,
    BusinessModel, BusinessModelType, TechnologyStack, InvestmentStage
)


class TestAICacheInitialization:
    """Test AICache initialization with different configurations."""
    
    def test_init_with_default_values(self):
        """Test initialization with default values."""
        cache = AICache()
        
        assert cache.default_ttl == 86400  # 24 hours
        assert cache.key_prefix == "ai_analysis"
        assert cache.redis_client is not None
        
    def test_init_with_custom_redis_client(self):
        """Test initialization with custom Redis client."""
        mock_redis = Mock()
        cache = AICache(redis_client=mock_redis)
        
        assert cache.redis_client == mock_redis
        
    def test_init_with_custom_ttl(self):
        """Test initialization with custom TTL."""
        custom_ttl = 3600  # 1 hour
        cache = AICache(default_ttl=custom_ttl)
        
        assert cache.default_ttl == custom_ttl
        
    def test_init_with_custom_key_prefix(self):
        """Test initialization with custom key prefix."""
        custom_prefix = "custom_cache"
        cache = AICache(key_prefix=custom_prefix)
        
        assert cache.key_prefix == custom_prefix
        
    def test_init_with_all_custom_values(self):
        """Test initialization with all custom values."""
        mock_redis = Mock()
        custom_ttl = 7200
        custom_prefix = "test_cache"
        
        cache = AICache(
            redis_client=mock_redis,
            default_ttl=custom_ttl,
            key_prefix=custom_prefix
        )
        
        assert cache.redis_client == mock_redis
        assert cache.default_ttl == custom_ttl
        assert cache.key_prefix == custom_prefix


class TestKeyGeneration:
    """Test cache key generation methods."""
    
    @pytest.fixture
    def cache(self):
        """Create cache instance."""
        return AICache()
    
    def test_generate_key_basic(self, cache):
        """Test basic key generation."""
        key = cache._generate_key("test_op", "test_content")
        
        assert key.startswith("ai_analysis:test_op:")
        assert len(key) > len("ai_analysis:test_op:")
        
    def test_generate_key_with_kwargs(self, cache):
        """Test key generation with additional kwargs."""
        key1 = cache._generate_key("test_op", "content", param1="value1", param2="value2")
        key2 = cache._generate_key("test_op", "content", param2="value2", param1="value1")
        
        # Keys should be the same regardless of kwargs order
        assert key1 == key2
        
    def test_generate_key_different_content(self, cache):
        """Test that different content generates different keys."""
        key1 = cache._generate_key("test_op", "content1")
        key2 = cache._generate_key("test_op", "content2")
        
        assert key1 != key2
        
    def test_generate_key_different_operation(self, cache):
        """Test that different operations generate different keys."""
        key1 = cache._generate_key("op1", "content")
        key2 = cache._generate_key("op2", "content")
        
        assert key1 != key2
        
    def test_get_startup_cache_key(self, cache):
        """Test startup-specific cache key generation."""
        key = cache._get_startup_cache_key(
            description="AI startup",
            name="TestCo",
            sector="AI/ML",
            stage="Series A"
        )
        
        assert key.startswith("ai_analysis:startup:")
        assert isinstance(key, str)
        
    def test_get_startup_cache_key_consistency(self, cache):
        """Test that startup cache keys are consistent."""
        params = {
            "description": "AI startup building ML tools",
            "name": "TechCo",
            "sector": "AI/ML",
            "stage": "Series A"
        }
        
        key1 = cache._get_startup_cache_key(**params)
        key2 = cache._get_startup_cache_key(**params)
        
        assert key1 == key2
        
    def test_get_vc_cache_key(self, cache):
        """Test VC-specific cache key generation."""
        key = cache._get_vc_cache_key(
            website_content="We invest in AI...",
            firm_name="AI Ventures"
        )
        
        assert key.startswith("ai_analysis:vc:")
        assert isinstance(key, str)
        
    def test_get_vc_cache_key_consistency(self, cache):
        """Test that VC cache keys are consistent."""
        params = {
            "website_content": "We focus on early-stage AI companies...",
            "firm_name": "Tech Capital"
        }
        
        key1 = cache._get_vc_cache_key(**params)
        key2 = cache._get_vc_cache_key(**params)
        
        assert key1 == key2
        
    def test_key_generation_with_special_characters(self, cache):
        """Test key generation with special characters in content."""
        key = cache._generate_key(
            "test_op",
            "Content with special chars: !@#$%^&*()_+-=[]{}|;':\",./<>?"
        )
        
        assert isinstance(key, str)
        assert key.startswith("ai_analysis:test_op:")
        
    def test_key_generation_with_unicode(self, cache):
        """Test key generation with Unicode content."""
        key = cache._generate_key(
            "test_op",
            "Unicode content: 你好世界 🌍 émojis"
        )
        
        assert isinstance(key, str)
        assert key.startswith("ai_analysis:test_op:")


class TestStartupAnalysisCaching:
    """Test startup analysis caching methods."""
    
    @pytest.fixture
    def cache(self, mock_redis):
        """Create cache instance with mocked Redis."""
        return AICache(redis_client=mock_redis)
    
    @pytest.fixture
    def valid_startup_analysis(self):
        """Create a valid StartupAnalysis instance."""
        return StartupAnalysis(
            sectors=["AI/ML", "E-commerce"],
            technologies=TechnologyStack(
                languages=["Python", "JavaScript"],
                frameworks=["TensorFlow", "React"],
                infrastructure=["AWS", "Docker"],
                ai_ml_tools=["GPT-4", "Langchain"]
            ),
            business_model=BusinessModel(
                type=BusinessModelType.SAAS,
                revenue_streams=["Subscription", "API"],
                target_market="B2B",
                pricing_model="Tiered"
            ),
            keywords=["AI", "automation", "analytics"],
            value_proposition="AI-powered business automation",
            target_customers=["SMBs", "Enterprises"],
            competitive_advantages=["Proprietary AI", "First mover"],
            confidence_score=0.85
        )
    
    @pytest.mark.asyncio
    async def test_get_startup_analysis_cache_hit(self, cache, mock_redis, valid_startup_analysis):
        """Test retrieving startup analysis from cache (cache hit)."""
        # Prepare cached data
        cached_data = valid_startup_analysis.model_dump_json()
        mock_redis.get = AsyncMock(return_value=cached_data)
        
        # Execute
        result = await cache.get_startup_analysis(
            description="Test startup",
            name="TestCo",
            sector="AI",
            stage="Series A"
        )
        
        # Assert
        assert isinstance(result, StartupAnalysis)
        assert result.sectors == valid_startup_analysis.sectors
        assert result.confidence_score == valid_startup_analysis.confidence_score
        mock_redis.get.assert_called_once()
        
    @pytest.mark.asyncio
    async def test_get_startup_analysis_cache_miss(self, cache, mock_redis):
        """Test retrieving startup analysis from cache (cache miss)."""
        mock_redis.get = AsyncMock(return_value=None)
        
        result = await cache.get_startup_analysis(
            description="Test startup",
            name="TestCo",
            sector="AI",
            stage="Series A"
        )
        
        assert result is None
        mock_redis.get.assert_called_once()
        
    @pytest.mark.asyncio
    async def test_get_startup_analysis_sync_redis(self, cache, mock_redis):
        """Test get_startup_analysis with synchronous Redis client."""
        mock_redis.get = Mock(return_value=None)  # Sync mock
        
        result = await cache.get_startup_analysis(
            description="Test", name="Test", sector="Tech", stage="Seed"
        )
        
        assert result is None
        mock_redis.get.assert_called_once()
        
    @pytest.mark.asyncio
    async def test_get_startup_analysis_redis_error(self, cache, mock_redis):
        """Test get_startup_analysis handles Redis errors gracefully."""
        mock_redis.get = AsyncMock(side_effect=RedisError("Connection failed"))
        
        result = await cache.get_startup_analysis(
            description="Test", name="Test", sector="Tech", stage="Seed"
        )
        
        assert result is None
        
    @pytest.mark.asyncio
    async def test_get_startup_analysis_json_decode_error(self, cache, mock_redis):
        """Test get_startup_analysis handles JSON decode errors."""
        mock_redis.get = AsyncMock(return_value="invalid json{")
        
        result = await cache.get_startup_analysis(
            description="Test", name="Test", sector="Tech", stage="Seed"
        )
        
        assert result is None
        
    @pytest.mark.asyncio
    async def test_set_startup_analysis_success(self, cache, mock_redis, valid_startup_analysis):
        """Test successfully setting startup analysis in cache."""
        mock_redis.setex = AsyncMock(return_value=True)
        
        result = await cache.set_startup_analysis(
            description="Test startup",
            name="TestCo",
            sector="AI",
            stage="Series A",
            analysis=valid_startup_analysis,
            ttl=3600
        )
        
        assert result is True
        mock_redis.setex.assert_called_once()
        
        # Verify the call arguments
        call_args = mock_redis.setex.call_args[0]
        assert call_args[1] == 3600  # TTL
        assert isinstance(call_args[2], str)  # Serialized JSON
        
    @pytest.mark.asyncio
    async def test_set_startup_analysis_default_ttl(self, cache, mock_redis, valid_startup_analysis):
        """Test set_startup_analysis uses default TTL when not specified."""
        mock_redis.setex = AsyncMock(return_value=True)
        
        result = await cache.set_startup_analysis(
            description="Test",
            name="Test",
            sector="Tech",
            stage="Seed",
            analysis=valid_startup_analysis
        )
        
        assert result is True
        call_args = mock_redis.setex.call_args[0]
        assert call_args[1] == cache.default_ttl
        
    @pytest.mark.asyncio
    async def test_set_startup_analysis_sync_redis(self, cache, mock_redis, valid_startup_analysis):
        """Test set_startup_analysis with synchronous Redis client."""
        mock_redis.setex = Mock(return_value=True)  # Sync mock
        
        result = await cache.set_startup_analysis(
            description="Test",
            name="Test",
            sector="Tech",
            stage="Seed",
            analysis=valid_startup_analysis
        )
        
        assert result is True
        mock_redis.setex.assert_called_once()
        
    @pytest.mark.asyncio
    async def test_set_startup_analysis_redis_error(self, cache, mock_redis, valid_startup_analysis):
        """Test set_startup_analysis handles Redis errors gracefully."""
        mock_redis.setex = AsyncMock(side_effect=RedisError("Connection failed"))
        
        result = await cache.set_startup_analysis(
            description="Test",
            name="Test",
            sector="Tech",
            stage="Seed",
            analysis=valid_startup_analysis
        )
        
        assert result is False


class TestVCThesisCaching:
    """Test VC thesis caching methods."""
    
    @pytest.fixture
    def cache(self, mock_redis):
        """Create cache instance with mocked Redis."""
        return AICache(redis_client=mock_redis)
    
    @pytest.fixture
    def valid_vc_thesis(self):
        """Create a valid VCThesisAnalysis instance."""
        return VCThesisAnalysis(
            thesis_summary="We invest in AI-first B2B SaaS companies",
            investment_focus=InvestmentFocus(
                sectors=["AI/ML", "B2B SaaS"],
                stages=[InvestmentStage.SEED, InvestmentStage.SERIES_A],
                technologies=["Machine Learning", "NLP"],
                geographical_focus=["US", "Europe"]
            ),
            check_size_range={"min": 1000000, "max": 5000000},
            portfolio_themes=["AI", "Enterprise", "Data"],
            avoided_sectors=["Gaming", "Hardware"],
            key_criteria=["Strong tech team", "$1M ARR"],
            notable_partners=["John Doe", "Jane Smith"],
            confidence_score=0.92
        )
    
    @pytest.mark.asyncio
    async def test_get_vc_thesis_cache_hit(self, cache, mock_redis, valid_vc_thesis):
        """Test retrieving VC thesis from cache (cache hit)."""
        cached_data = valid_vc_thesis.model_dump_json()
        mock_redis.get = AsyncMock(return_value=cached_data)
        
        result = await cache.get_vc_thesis(
            website_content="Investment thesis content...",
            firm_name="Test VC"
        )
        
        assert isinstance(result, VCThesisAnalysis)
        assert result.thesis_summary == valid_vc_thesis.thesis_summary
        assert result.confidence_score == valid_vc_thesis.confidence_score
        assert len(result.investment_focus.stages) == 2
        
    @pytest.mark.asyncio
    async def test_get_vc_thesis_cache_miss(self, cache, mock_redis):
        """Test retrieving VC thesis from cache (cache miss)."""
        mock_redis.get = AsyncMock(return_value=None)
        
        result = await cache.get_vc_thesis(
            website_content="Content",
            firm_name="Test VC"
        )
        
        assert result is None
        
    @pytest.mark.asyncio
    async def test_get_vc_thesis_sync_redis(self, cache, mock_redis):
        """Test get_vc_thesis with synchronous Redis client."""
        mock_redis.get = Mock(return_value=None)
        
        result = await cache.get_vc_thesis(
            website_content="Content",
            firm_name="Test VC"
        )
        
        assert result is None
        
    @pytest.mark.asyncio
    async def test_get_vc_thesis_redis_error(self, cache, mock_redis):
        """Test get_vc_thesis handles Redis errors gracefully."""
        mock_redis.get = AsyncMock(side_effect=RedisError("Connection failed"))
        
        result = await cache.get_vc_thesis(
            website_content="Content",
            firm_name="Test VC"
        )
        
        assert result is None
        
    @pytest.mark.asyncio
    async def test_get_vc_thesis_json_decode_error(self, cache, mock_redis):
        """Test get_vc_thesis handles JSON decode errors."""
        mock_redis.get = AsyncMock(return_value="malformed json}")
        
        result = await cache.get_vc_thesis(
            website_content="Content",
            firm_name="Test VC"
        )
        
        assert result is None
        
    @pytest.mark.asyncio
    async def test_set_vc_thesis_success(self, cache, mock_redis, valid_vc_thesis):
        """Test successfully setting VC thesis in cache."""
        mock_redis.setex = AsyncMock(return_value=True)
        
        result = await cache.set_vc_thesis(
            website_content="Investment thesis...",
            firm_name="Test VC",
            analysis=valid_vc_thesis,
            ttl=7200
        )
        
        assert result is True
        call_args = mock_redis.setex.call_args[0]
        assert call_args[1] == 7200
        
    @pytest.mark.asyncio
    async def test_set_vc_thesis_sync_redis(self, cache, mock_redis, valid_vc_thesis):
        """Test set_vc_thesis with synchronous Redis client."""
        mock_redis.setex = Mock(return_value=True)
        
        result = await cache.set_vc_thesis(
            website_content="Content",
            firm_name="Test VC",
            analysis=valid_vc_thesis
        )
        
        assert result is True
        
    @pytest.mark.asyncio
    async def test_set_vc_thesis_redis_error(self, cache, mock_redis, valid_vc_thesis):
        """Test set_vc_thesis handles Redis errors gracefully."""
        mock_redis.setex = AsyncMock(side_effect=RedisError("Write failed"))
        
        result = await cache.set_vc_thesis(
            website_content="Content",
            firm_name="Test VC",
            analysis=valid_vc_thesis
        )
        
        assert result is False


class TestCacheInvalidation:
    """Test cache invalidation methods."""
    
    @pytest.fixture
    def cache(self, mock_redis):
        """Create cache instance with mocked Redis."""
        return AICache(redis_client=mock_redis)
    
    @pytest.mark.asyncio
    async def test_invalidate_startup_cache_success(self, cache, mock_redis):
        """Test successful startup cache invalidation."""
        mock_redis.delete = AsyncMock(return_value=1)
        
        result = await cache.invalidate_startup_cache(
            description="Test",
            name="TestCo",
            sector="AI",
            stage="Series A"
        )
        
        assert result is True
        mock_redis.delete.assert_called_once()
        
    @pytest.mark.asyncio
    async def test_invalidate_startup_cache_not_found(self, cache, mock_redis):
        """Test startup cache invalidation when key doesn't exist."""
        mock_redis.delete = AsyncMock(return_value=0)
        
        result = await cache.invalidate_startup_cache(
            description="Test",
            name="TestCo",
            sector="AI",
            stage="Series A"
        )
        
        assert result is False
        
    @pytest.mark.asyncio
    async def test_invalidate_startup_cache_sync_redis(self, cache, mock_redis):
        """Test invalidate_startup_cache with synchronous Redis client."""
        mock_redis.delete = Mock(return_value=1)
        
        result = await cache.invalidate_startup_cache(
            description="Test",
            name="TestCo",
            sector="AI",
            stage="Series A"
        )
        
        assert result is True
        
    @pytest.mark.asyncio
    async def test_invalidate_startup_cache_redis_error(self, cache, mock_redis):
        """Test invalidate_startup_cache handles Redis errors."""
        mock_redis.delete = AsyncMock(side_effect=RedisError("Delete failed"))
        
        result = await cache.invalidate_startup_cache(
            description="Test",
            name="TestCo",
            sector="AI",
            stage="Series A"
        )
        
        assert result is False
        
    @pytest.mark.asyncio
    async def test_invalidate_vc_cache_success(self, cache, mock_redis):
        """Test successful VC cache invalidation."""
        mock_redis.delete = AsyncMock(return_value=1)
        
        result = await cache.invalidate_vc_cache(
            website_content="Content",
            firm_name="Test VC"
        )
        
        assert result is True
        
    @pytest.mark.asyncio
    async def test_invalidate_vc_cache_sync_redis(self, cache, mock_redis):
        """Test invalidate_vc_cache with synchronous Redis client."""
        mock_redis.delete = Mock(return_value=1)
        
        result = await cache.invalidate_vc_cache(
            website_content="Content",
            firm_name="Test VC"
        )
        
        assert result is True
        
    @pytest.mark.asyncio
    async def test_invalidate_vc_cache_redis_error(self, cache, mock_redis):
        """Test invalidate_vc_cache handles Redis errors."""
        mock_redis.delete = AsyncMock(side_effect=RedisError("Delete failed"))
        
        result = await cache.invalidate_vc_cache(
            website_content="Content",
            firm_name="Test VC"
        )
        
        assert result is False


class TestClearCache:
    """Test clear_cache method."""
    
    @pytest.fixture
    def cache(self):
        """Create cache instance with mocked Redis."""
        mock_redis = Mock()
        mock_redis.keys = Mock()
        mock_redis.delete = Mock()  # Synchronous mock for clear_cache
        return AICache(redis_client=mock_redis)
    
    def test_clear_cache_all(self, cache):
        """Test clearing all cache entries."""
        cache.redis_client.keys.return_value = ["key1", "key2", "key3"]
        cache.redis_client.delete.return_value = 3
        
        result = cache.clear_cache()
        
        assert result == 3
        cache.redis_client.keys.assert_called_once_with("ai_analysis:*")
        cache.redis_client.delete.assert_called_once_with("key1", "key2", "key3")
        
    def test_clear_cache_with_pattern(self, cache):
        """Test clearing cache with specific pattern."""
        cache.redis_client.keys.return_value = ["key1", "key2"]
        cache.redis_client.delete.return_value = 2
        
        result = cache.clear_cache(pattern="startup")
        
        assert result == 2
        cache.redis_client.keys.assert_called_once_with("ai_analysis:startup:*")
        
    def test_clear_cache_no_keys(self, cache):
        """Test clearing cache when no keys match."""
        cache.redis_client.keys.return_value = []
        
        result = cache.clear_cache()
        
        assert result == 0
        cache.redis_client.delete.assert_not_called()
        
    def test_clear_cache_redis_error(self, cache):
        """Test clear_cache handles Redis errors."""
        cache.redis_client.keys.side_effect = RedisError("Keys failed")
        
        result = cache.clear_cache()
        
        assert result == 0
        
    def test_clear_cache_delete_error(self, cache):
        """Test clear_cache handles delete errors."""
        cache.redis_client.keys.return_value = ["key1", "key2"]
        cache.redis_client.delete.side_effect = RedisError("Delete failed")
        
        result = cache.clear_cache()
        
        assert result == 0


class TestCacheStats:
    """Test get_cache_stats method."""
    
    @pytest.fixture
    def cache(self, mock_redis):
        """Create cache instance with mocked Redis."""
        return AICache(redis_client=mock_redis)
    
    def test_get_cache_stats_with_data(self, cache, mock_redis):
        """Test getting cache stats with actual data."""
        mock_redis.info.return_value = {
            'keyspace_hits': 100,
            'keyspace_misses': 25
        }
        mock_redis.keys.return_value = ['key1', 'key2', 'key3', 'key4', 'key5']
        
        stats = cache.get_cache_stats()
        
        assert stats['total_keys'] == 5
        assert stats['hits'] == 100
        assert stats['misses'] == 25
        assert stats['hit_rate'] == 0.8  # 100 / (100 + 25)
        
    def test_get_cache_stats_no_hits(self, cache, mock_redis):
        """Test cache stats with no hits."""
        mock_redis.info.return_value = {
            'keyspace_hits': 0,
            'keyspace_misses': 50
        }
        mock_redis.keys.return_value = []
        
        stats = cache.get_cache_stats()
        
        assert stats['total_keys'] == 0
        assert stats['hits'] == 0
        assert stats['misses'] == 50
        assert stats['hit_rate'] == 0
        
    def test_get_cache_stats_all_hits(self, cache, mock_redis):
        """Test cache stats with 100% hit rate."""
        mock_redis.info.return_value = {
            'keyspace_hits': 200,
            'keyspace_misses': 0
        }
        mock_redis.keys.return_value = ['key1', 'key2']
        
        stats = cache.get_cache_stats()
        
        assert stats['hits'] == 200
        assert stats['misses'] == 0
        assert stats['hit_rate'] == 1.0
        
    def test_get_cache_stats_redis_error(self, cache, mock_redis):
        """Test get_cache_stats handles Redis errors."""
        mock_redis.info.side_effect = RedisError("Info failed")
        
        stats = cache.get_cache_stats()
        
        assert stats['total_keys'] == 0
        assert stats['hits'] == 0
        assert stats['misses'] == 0
        assert stats['hit_rate'] == 0
        
    def test_get_cache_stats_missing_fields(self, cache, mock_redis):
        """Test cache stats with missing info fields."""
        mock_redis.info.return_value = {}  # No keyspace info
        mock_redis.keys.return_value = ['key1']
        
        stats = cache.get_cache_stats()
        
        assert stats['total_keys'] == 1
        assert stats['hits'] == 0
        assert stats['misses'] == 0
        assert stats['hit_rate'] == 0


class TestEdgeCases:
    """Test edge cases and error handling."""
    
    @pytest.fixture
    def cache(self, mock_redis):
        """Create cache instance with mocked Redis."""
        return AICache(redis_client=mock_redis)
    
    @pytest.mark.asyncio
    async def test_malformed_json_data(self, cache, mock_redis):
        """Test handling of malformed JSON data."""
        # This tests the json.JSONDecodeError exception handling
        mock_redis.get = AsyncMock(return_value='{"invalid": json}')  # Malformed JSON
        
        result = await cache.get_startup_analysis(
            description="Test", name="Test", sector="Tech", stage="Seed"
        )
        
        # The cache handles JSON decode errors gracefully
        assert result is None
        
    @pytest.mark.asyncio
    async def test_empty_string_parameters(self, cache, mock_redis):
        """Test handling of empty string parameters."""
        mock_redis.get = AsyncMock(return_value=None)
        
        result = await cache.get_startup_analysis(
            description="",
            name="",
            sector="",
            stage=""
        )
        
        assert result is None
        # Should still generate a valid key
        mock_redis.get.assert_called_once()
        
    @pytest.mark.asyncio
    async def test_very_large_content(self, cache, mock_redis):
        """Test handling of very large content strings."""
        large_content = "x" * 1000000  # 1MB of text
        mock_redis.setex = AsyncMock(return_value=True)
        
        # Create a minimal valid analysis
        analysis = StartupAnalysis(
            sectors=["Tech"],
            technologies=TechnologyStack(),
            business_model=BusinessModel(
                type=BusinessModelType.SAAS,
                revenue_streams=[],
                target_market="B2B"
            ),
            keywords=["test"],
            value_proposition="Test",
            target_customers=["Test"],
            competitive_advantages=["Test"],
            confidence_score=0.5
        )
        
        result = await cache.set_startup_analysis(
            description=large_content,
            name="Test",
            sector="Tech",
            stage="Seed",
            analysis=analysis
        )
        
        assert result is True
        
    def test_concurrent_access_simulation(self, cache, mock_redis):
        """Test simulated concurrent access to cache stats."""
        # Simulate stats changing between calls
        call_count = 0
        
        def info_side_effect(*args):
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                return {'keyspace_hits': 100, 'keyspace_misses': 20}
            else:
                return {'keyspace_hits': 110, 'keyspace_misses': 22}
        
        mock_redis.info.side_effect = info_side_effect
        mock_redis.keys.return_value = ['key1', 'key2']
        
        stats1 = cache.get_cache_stats()
        stats2 = cache.get_cache_stats()
        
        # Stats should reflect the values at time of call
        assert stats1['hits'] == 100
        assert stats2['hits'] == 110
        
    @pytest.mark.asyncio
    async def test_cache_key_collision_prevention(self, cache, mock_redis):
        """Test that similar but different inputs don't cause key collisions."""
        mock_redis.setex = AsyncMock(return_value=True)
        
        # Create two similar but different analyses
        analysis1 = StartupAnalysis(
            sectors=["AI"],
            technologies=TechnologyStack(),
            business_model=BusinessModel(
                type=BusinessModelType.SAAS,
                revenue_streams=[],
                target_market="B2B"
            ),
            keywords=["AI"],
            value_proposition="AI Platform",
            target_customers=["Enterprises"],
            competitive_advantages=["First"],
            confidence_score=0.8
        )
        
        analysis2 = StartupAnalysis(
            sectors=["ML"],  # Different sector
            technologies=TechnologyStack(),
            business_model=BusinessModel(
                type=BusinessModelType.SAAS,
                revenue_streams=[],
                target_market="B2B"
            ),
            keywords=["ML"],
            value_proposition="ML Platform",
            target_customers=["SMBs"],
            competitive_advantages=["Second"],
            confidence_score=0.7
        )
        
        # Set both with similar parameters
        await cache.set_startup_analysis(
            description="AI startup",
            name="TechCo",
            sector="AI",
            stage="Series A",
            analysis=analysis1
        )
        
        await cache.set_startup_analysis(
            description="AI startup",  # Same description
            name="TechCo",  # Same name
            sector="ML",  # Different sector
            stage="Series A",  # Same stage
            analysis=analysis2
        )
        
        # Should have been called twice with different keys
        assert mock_redis.setex.call_count == 2
        
        # Get the keys used
        call1_key = mock_redis.setex.call_args_list[0][0][0]
        call2_key = mock_redis.setex.call_args_list[1][0][0]
        
        assert call1_key != call2_key
        
    @pytest.mark.asyncio
    async def test_redis_connection_failure_recovery(self, cache, mock_redis):
        """Test that cache operations handle connection failures gracefully."""
        # Simulate connection failure then recovery
        call_count = 0
        
        async def get_side_effect(*args):
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                raise RedisError("Connection lost")
            return None
        
        mock_redis.get = AsyncMock(side_effect=get_side_effect)
        
        # First call should fail gracefully
        result1 = await cache.get_startup_analysis(
            description="Test", name="Test", sector="Tech", stage="Seed"
        )
        assert result1 is None
        
        # Second call should work
        result2 = await cache.get_startup_analysis(
            description="Test", name="Test", sector="Tech", stage="Seed"
        )
        assert result2 is None  # Still None but no error
        
    def test_custom_prefix_in_operations(self):
        """Test that custom prefix is used in all operations."""
        custom_prefix = "custom_ai_cache"
        mock_redis = Mock()
        cache = AICache(redis_client=mock_redis, key_prefix=custom_prefix)
        
        # Test clear_cache uses custom prefix
        mock_redis.keys.return_value = []
        cache.clear_cache()
        mock_redis.keys.assert_called_with(f"{custom_prefix}:*")
        
        # Test key generation uses custom prefix
        key = cache._generate_key("test", "content")
        assert key.startswith(f"{custom_prefix}:test:")