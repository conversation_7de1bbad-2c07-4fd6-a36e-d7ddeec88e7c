"""Unit tests for AI Analyzer Service following TDD principles."""

import pytest
from unittest.mock import Mock, AsyncMock, patch, call, ANY
import asyncio
from datetime import datetime
import time
from tenacity import RetryError

from src.core.ai.analyzer import AIAnalyzerService, TokenUsageCallback
from src.core.ai.models import StartupAnalysis, VCThesisAnalysis, InvestmentFocus
from src.core.ai.exceptions import AIAnalyzerError
from src.core.models.startup import Startup
from src.core.models.vc import VC


class TestTokenUsageCallback:
    """Test token usage tracking callback."""
    
    def test_initializes_with_zero_values(self):
        # Arrange & Act
        callback = TokenUsageCallback()
        
        # Assert
        assert callback.total_tokens == 0
        assert callback.prompt_tokens == 0
        assert callback.completion_tokens == 0
        assert callback.total_cost == 0.0
    
    @pytest.mark.asyncio
    async def test_tracks_token_usage_on_llm_end(self):
        # Arrange
        callback = TokenUsageCallback()
        mock_response = Mock()
        mock_response.llm_output = {
            'token_usage': {
                'total_tokens': 150,
                'prompt_tokens': 100,
                'completion_tokens': 50
            }
        }
        
        # Act
        await callback.on_llm_end(mock_response)
        
        # Assert
        assert callback.total_tokens == 150
        assert callback.prompt_tokens == 100
        assert callback.completion_tokens == 50
        # Cost calculation: (100 * 0.03 + 50 * 0.06) / 1000 = 0.006
        assert callback.total_cost == pytest.approx(0.006)
    
    @pytest.mark.asyncio
    async def test_accumulates_tokens_across_multiple_calls(self):
        # Arrange
        callback = TokenUsageCallback()
        
        # Act - First call
        mock_response1 = Mock()
        mock_response1.llm_output = {
            'token_usage': {
                'total_tokens': 100,
                'prompt_tokens': 60,
                'completion_tokens': 40
            }
        }
        await callback.on_llm_end(mock_response1)
        
        # Act - Second call
        mock_response2 = Mock()
        mock_response2.llm_output = {
            'token_usage': {
                'total_tokens': 200,
                'prompt_tokens': 120,
                'completion_tokens': 80
            }
        }
        await callback.on_llm_end(mock_response2)
        
        # Assert
        assert callback.total_tokens == 300
        assert callback.prompt_tokens == 180
        assert callback.completion_tokens == 120


class TestAIAnalyzerService:
    """Test AI Analyzer Service with mocked dependencies."""
    
    @pytest.fixture
    def mock_startup_chain(self):
        """Mock startup analyzer chain."""
        chain = Mock()
        chain.analyze = AsyncMock()
        return chain
    
    @pytest.fixture
    def mock_vc_chain(self):
        """Mock VC thesis extractor chain."""
        chain = Mock()
        chain.extract = AsyncMock()
        return chain
    
    @pytest.fixture
    def analyzer_service(self, mock_ai_cache, mock_startup_chain, mock_vc_chain):
        """Create analyzer service with mocked chains."""
        service = AIAnalyzerService(
            openai_api_key="test-key",
            cache=mock_ai_cache
        )
        service.startup_chain = mock_startup_chain
        service.vc_chain = mock_vc_chain
        return service
    
    @pytest.mark.asyncio
    async def test_analyze_startup_returns_cached_result_when_available(
        self, analyzer_service, sample_startup, sample_startup_analysis, mock_ai_cache
    ):
        # Arrange
        mock_ai_cache.get_startup_analysis = AsyncMock(return_value=sample_startup_analysis)
        
        # Act
        result = await analyzer_service.analyze_startup(sample_startup, use_cache=True)
        
        # Assert
        assert result == sample_startup_analysis
        mock_ai_cache.get_startup_analysis.assert_called_once()
        analyzer_service.startup_chain.analyze.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_analyze_startup_calls_chain_when_cache_miss(
        self, analyzer_service, sample_startup, sample_startup_analysis, mock_ai_cache
    ):
        # Arrange
        mock_ai_cache.get_startup_analysis = AsyncMock(return_value=None)
        mock_ai_cache.set_startup_analysis = AsyncMock()
        analyzer_service.startup_chain.analyze.return_value = sample_startup_analysis
        
        # Act
        result = await analyzer_service.analyze_startup(sample_startup, use_cache=True)
        
        # Assert
        assert result == sample_startup_analysis
        analyzer_service.startup_chain.analyze.assert_called_once_with(
            description=sample_startup.description,
            name=sample_startup.name,
            sector=sample_startup.sector,
            stage=sample_startup.stage,
            website=sample_startup.website,
            callbacks=ANY
        )
        mock_ai_cache.set_startup_analysis.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_analyze_startup_bypasses_cache_when_disabled(
        self, analyzer_service, sample_startup, sample_startup_analysis, mock_ai_cache
    ):
        # Arrange
        analyzer_service.startup_chain.analyze.return_value = sample_startup_analysis
        
        # Act
        result = await analyzer_service.analyze_startup(sample_startup, use_cache=False)
        
        # Assert
        assert result == sample_startup_analysis
        mock_ai_cache.get_startup_analysis.assert_not_called()
        mock_ai_cache.set_startup_analysis.assert_not_called()
        analyzer_service.startup_chain.analyze.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_analyze_startup_retries_on_failure(
        self, analyzer_service, sample_startup, sample_startup_analysis, mock_ai_cache
    ):
        # Arrange
        mock_ai_cache.get_startup_analysis = AsyncMock(return_value=None)
        mock_ai_cache.set_startup_analysis = AsyncMock()
        
        # First two calls fail, third succeeds
        analyzer_service.startup_chain.analyze.side_effect = [
            Exception("API Error"),
            Exception("API Error"),
            sample_startup_analysis
        ]
        
        # Act
        with patch('src.core.ai.analyzer.time.sleep', return_value=None):
            result = await analyzer_service.analyze_startup(sample_startup)
        
        # Assert
        assert result == sample_startup_analysis
        assert analyzer_service.startup_chain.analyze.call_count == 3
    
    @pytest.mark.asyncio
    async def test_analyze_startup_raises_after_max_retries(
        self, analyzer_service, sample_startup, mock_ai_cache
    ):
        # Arrange
        mock_ai_cache.get_startup_analysis = AsyncMock(return_value=None)
        analyzer_service.startup_chain.analyze.side_effect = Exception("Persistent API Error")
        
        # Act & Assert
        with patch('src.core.ai.analyzer.time.sleep', return_value=None):
            with pytest.raises(RetryError):
                await analyzer_service.analyze_startup(sample_startup)
        
        assert analyzer_service.startup_chain.analyze.call_count == 3
    
    @pytest.mark.asyncio
    async def test_extract_vc_thesis_updates_vc_object(
        self, analyzer_service, sample_vc, sample_vc_thesis_analysis, mock_ai_cache
    ):
        # Arrange
        mock_ai_cache.get_vc_thesis = AsyncMock(return_value=None)
        mock_ai_cache.set_vc_thesis = AsyncMock()
        analyzer_service.vc_chain.extract.return_value = sample_vc_thesis_analysis
        website_content = "Sample website content about investing in AI companies..."
        
        # Act
        result = await analyzer_service.extract_vc_thesis(sample_vc, website_content)
        
        # Assert
        assert result == sample_vc_thesis_analysis
        assert sample_vc.thesis == sample_vc_thesis_analysis.thesis_summary
        assert sample_vc.sectors == sample_vc_thesis_analysis.investment_focus.sectors
        assert sample_vc.stages == ["Series A", "Series B"]
        assert sample_vc.check_size_min == 2000000
        assert sample_vc.check_size_max == 10000000
    
    @pytest.mark.asyncio
    async def test_batch_analyze_startups_processes_concurrently(
        self, analyzer_service, startup_factory, sample_startup_analysis
    ):
        # Arrange
        startups = [startup_factory() for _ in range(10)]
        analyzer_service.analyze_startup = AsyncMock(return_value=sample_startup_analysis)
        
        # Act
        start_time = time.time()
        results = await analyzer_service.batch_analyze_startups(
            startups, 
            max_concurrent=5
        )
        elapsed_time = time.time() - start_time
        
        # Assert
        assert len(results) == 10
        assert all(r == sample_startup_analysis for r in results)
        assert analyzer_service.analyze_startup.call_count == 10
        # Should be faster than sequential (simulated by the async nature)
        assert elapsed_time < 1.0
    
    @pytest.mark.asyncio
    async def test_batch_analyze_startups_handles_partial_failures(
        self, analyzer_service, startup_factory, sample_startup_analysis
    ):
        # Arrange
        startups = [startup_factory() for _ in range(5)]
        
        # Make some analyses fail
        side_effects = [
            sample_startup_analysis,
            Exception("Analysis failed"),
            sample_startup_analysis,
            Exception("Analysis failed"),
            sample_startup_analysis
        ]
        analyzer_service.analyze_startup = AsyncMock(side_effect=side_effects)
        
        # Act
        results = await analyzer_service.batch_analyze_startups(startups)
        
        # Assert
        assert len(results) == 3  # Only successful analyses
        assert all(r == sample_startup_analysis for r in results)
    
    def test_get_usage_stats_returns_complete_metrics(
        self, analyzer_service, mock_ai_cache
    ):
        # Arrange
        analyzer_service.token_tracker.total_tokens = 1000
        analyzer_service.token_tracker.prompt_tokens = 600
        analyzer_service.token_tracker.completion_tokens = 400
        analyzer_service.token_tracker.total_cost = 0.042
        
        mock_ai_cache.get_cache_stats.return_value = {
            'hits': 10,
            'misses': 5,
            'total_requests': 15
        }
        
        # Act
        stats = analyzer_service.get_usage_stats()
        
        # Assert
        assert stats['total_tokens'] == 1000
        assert stats['prompt_tokens'] == 600
        assert stats['completion_tokens'] == 400
        assert stats['total_cost'] == 0.042
        assert stats['cache_stats']['hits'] == 10
        assert stats['cache_stats']['misses'] == 5
    
    def test_reset_usage_stats_clears_token_tracker(self, analyzer_service):
        # Arrange
        analyzer_service.token_tracker.total_tokens = 1000
        analyzer_service.token_tracker.total_cost = 0.042
        
        # Act
        analyzer_service.reset_usage_stats()
        
        # Assert
        assert analyzer_service.token_tracker.total_tokens == 0
        assert analyzer_service.token_tracker.total_cost == 0.0