"""Comprehensive unit tests for the AI analyzer service."""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime
import json
import time
from tenacity import RetryError
from langchain_core.outputs import LLMResult, Generation
from langchain_core.exceptions import LangChainException
from redis.exceptions import RedisError

from src.core.ai.analyzer import AIAnalyzerService, TokenUsageCallback
from src.core.ai.models import (
    StartupAnalysis, 
    VCThesisAnalysis, 
    BusinessModel, 
    BusinessModelType,
    TechnologyStack, 
    InvestmentFocus,
    InvestmentStage
)
from src.core.ai.cache import AICache
from src.core.models.startup import Startup
from src.core.models.vc import VC


@pytest.fixture
def mock_startup():
    """Create a mock startup object."""
    return Startup(
        name="TechCorp",
        sector="AI/ML",
        stage="Series A",
        description="An AI-powered platform for business automation",
        website="https://techcorp.com"
    )


@pytest.fixture
def mock_vc():
    """Create a mock VC object."""
    return VC(
        firm_name="VenturePartners",
        website="https://venturepartners.com",
        thesis="We invest in early-stage AI companies",
        check_size_min=1000000,
        check_size_max=5000000,
        sectors=["AI/ML", "B2B SaaS"],
        stages=["Seed", "Series A"]
    )


@pytest.fixture
def mock_startup_analysis():
    """Create a mock startup analysis result."""
    return StartupAnalysis(
        sectors=["AI/ML", "B2B SaaS"],
        technologies=TechnologyStack(
            languages=["Python", "JavaScript"],
            frameworks=["TensorFlow", "React"],
            infrastructure=["AWS", "Docker"],
            ai_ml_tools=["OpenAI", "LangChain"]
        ),
        business_model=BusinessModel(
            type=BusinessModelType.SAAS,
            revenue_streams=["Subscription", "Enterprise licenses"],
            target_market="Enterprise companies",
            pricing_model="Tiered pricing"
        ),
        keywords=["AI", "automation", "enterprise"],
        value_proposition="Automate business processes with AI",
        target_customers=["Enterprise", "Mid-market companies"],
        competitive_advantages=["Advanced AI models", "Easy integration"],
        confidence_score=0.9
    )


@pytest.fixture
def mock_vc_thesis_analysis():
    """Create a mock VC thesis analysis result."""
    return VCThesisAnalysis(
        thesis_summary="Focus on early-stage AI companies with enterprise customers",
        investment_focus=InvestmentFocus(
            sectors=["AI/ML", "B2B SaaS", "Enterprise Software"],
            stages=[InvestmentStage.SEED, InvestmentStage.SERIES_A],
            technologies=["Machine Learning", "Cloud Infrastructure"],
            geographical_focus=["US", "Europe"]
        ),
        check_size_range={"min": 1000000, "max": 5000000},
        portfolio_themes=["AI-first", "Enterprise SaaS"],
        avoided_sectors=["Consumer", "Hardware"],
        key_criteria=["Strong technical team", "Enterprise traction"],
        notable_partners=["John Doe", "Jane Smith"],
        confidence_score=0.85
    )


@pytest.fixture
def mock_cache():
    """Create a mock cache object."""
    cache = AsyncMock(spec=AICache)
    cache.get_startup_analysis = AsyncMock(return_value=None)
    cache.set_startup_analysis = AsyncMock(return_value=True)
    cache.get_vc_thesis = AsyncMock(return_value=None)
    cache.set_vc_thesis = AsyncMock(return_value=True)
    cache.get_cache_stats = Mock(return_value={
        "total_keys": 10,
        "hits": 50,
        "misses": 10,
        "hit_rate": 0.83
    })
    return cache


@pytest.fixture
def mock_llm():
    """Create a mock LLM object."""
    llm = AsyncMock()
    return llm


@pytest.fixture
def analyzer_service(mock_cache, mock_llm):
    """Create an analyzer service with mocked dependencies."""
    with patch('src.core.ai.analyzer.ChatOpenAI', return_value=mock_llm):
        with patch('src.core.ai.analyzer.StartupAnalyzerChain') as mock_startup_chain:
            with patch('src.core.ai.analyzer.VCThesisExtractorChain') as mock_vc_chain:
                service = AIAnalyzerService(
                    openai_api_key="test-key",
                    model_name="gpt-4",
                    temperature=0.3,
                    cache=mock_cache,
                    max_retries=3,
                    enable_streaming=False
                )
                # Mock the chains
                service.startup_chain = AsyncMock()
                service.vc_chain = AsyncMock()
                return service


class TestAIAnalyzerServiceInitialization:
    """Test AIAnalyzerService initialization with different configurations."""
    
    def test_init_with_defaults(self):
        """Test initialization with default parameters."""
        with patch('src.core.ai.analyzer.ChatOpenAI') as mock_openai:
            with patch('src.core.ai.analyzer.StartupAnalyzerChain'):
                with patch('src.core.ai.analyzer.VCThesisExtractorChain'):
                    service = AIAnalyzerService()
                    
                    assert service.llm is not None
                    assert service.cache is not None
                    assert service.max_retries == 3
                    assert isinstance(service.token_tracker, TokenUsageCallback)
                    mock_openai.assert_called_once()
    
    def test_init_with_custom_params(self, mock_cache):
        """Test initialization with custom parameters."""
        with patch('src.core.ai.analyzer.ChatOpenAI') as mock_openai:
            with patch('src.core.ai.analyzer.StartupAnalyzerChain'):
                with patch('src.core.ai.analyzer.VCThesisExtractorChain'):
                    service = AIAnalyzerService(
                        openai_api_key="custom-key",
                        model_name="gpt-3.5-turbo",
                        temperature=0.5,
                        cache=mock_cache,
                        max_retries=5,
                        enable_streaming=True
                    )
                    
                    assert service.cache == mock_cache
                    assert service.max_retries == 5
                    mock_openai.assert_called_with(
                        openai_api_key="custom-key",
                        model_name="gpt-3.5-turbo",
                        temperature=0.5,
                        streaming=True
                    )


class TestAnalyzeStartup:
    """Test analyze_startup method with various scenarios."""
    
    @pytest.mark.asyncio
    async def test_analyze_startup_success(self, analyzer_service, mock_startup, mock_startup_analysis, mock_cache):
        """Test successful startup analysis."""
        analyzer_service.startup_chain.analyze = AsyncMock(return_value=mock_startup_analysis)
        
        result = await analyzer_service.analyze_startup(mock_startup)
        
        assert result == mock_startup_analysis
        analyzer_service.startup_chain.analyze.assert_called_once()
        mock_cache.get_startup_analysis.assert_called_once()
        mock_cache.set_startup_analysis.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_analyze_startup_with_cache_hit(self, analyzer_service, mock_startup, mock_startup_analysis, mock_cache):
        """Test startup analysis with cache hit."""
        mock_cache.get_startup_analysis.return_value = mock_startup_analysis
        
        result = await analyzer_service.analyze_startup(mock_startup, use_cache=True)
        
        assert result == mock_startup_analysis
        analyzer_service.startup_chain.analyze.assert_not_called()
        mock_cache.get_startup_analysis.assert_called_once()
        mock_cache.set_startup_analysis.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_analyze_startup_without_cache(self, analyzer_service, mock_startup, mock_startup_analysis, mock_cache):
        """Test startup analysis without using cache."""
        analyzer_service.startup_chain.analyze = AsyncMock(return_value=mock_startup_analysis)
        
        result = await analyzer_service.analyze_startup(mock_startup, use_cache=False)
        
        assert result == mock_startup_analysis
        analyzer_service.startup_chain.analyze.assert_called_once()
        mock_cache.get_startup_analysis.assert_not_called()
        mock_cache.set_startup_analysis.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_analyze_startup_cache_error(self, analyzer_service, mock_startup, mock_startup_analysis, mock_cache):
        """Test startup analysis when cache operations fail."""
        mock_cache.get_startup_analysis.side_effect = RedisError("Cache error")
        mock_cache.set_startup_analysis.side_effect = RedisError("Cache error")
        analyzer_service.startup_chain.analyze = AsyncMock(return_value=mock_startup_analysis)
        
        result = await analyzer_service.analyze_startup(mock_startup, use_cache=True)
        
        assert result == mock_startup_analysis
        analyzer_service.startup_chain.analyze.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_analyze_startup_with_custom_ttl(self, analyzer_service, mock_startup, mock_startup_analysis, mock_cache):
        """Test startup analysis with custom cache TTL."""
        analyzer_service.startup_chain.analyze = AsyncMock(return_value=mock_startup_analysis)
        
        result = await analyzer_service.analyze_startup(mock_startup, cache_ttl=3600)
        
        assert result == mock_startup_analysis
        mock_cache.set_startup_analysis.assert_called_once()
        call_args = mock_cache.set_startup_analysis.call_args
        assert call_args.kwargs['ttl'] == 3600


class TestExtractVCThesis:
    """Test extract_vc_thesis method with various scenarios."""
    
    @pytest.mark.asyncio
    async def test_extract_vc_thesis_success(self, analyzer_service, mock_vc, mock_vc_thesis_analysis, mock_cache):
        """Test successful VC thesis extraction."""
        website_content = "We invest in AI startups..."
        analyzer_service.vc_chain.extract = AsyncMock(return_value=mock_vc_thesis_analysis)
        
        result = await analyzer_service.extract_vc_thesis(mock_vc, website_content)
        
        assert result == mock_vc_thesis_analysis
        assert mock_vc.thesis == mock_vc_thesis_analysis.thesis_summary
        assert mock_vc.sectors == mock_vc_thesis_analysis.investment_focus.sectors
        analyzer_service.vc_chain.extract.assert_called_once()
        mock_cache.get_vc_thesis.assert_called_once()
        mock_cache.set_vc_thesis.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_extract_vc_thesis_with_cache_hit(self, analyzer_service, mock_vc, mock_vc_thesis_analysis, mock_cache):
        """Test VC thesis extraction with cache hit."""
        website_content = "We invest in AI startups..."
        mock_cache.get_vc_thesis.return_value = mock_vc_thesis_analysis
        
        result = await analyzer_service.extract_vc_thesis(mock_vc, website_content, use_cache=True)
        
        assert result == mock_vc_thesis_analysis
        analyzer_service.vc_chain.extract.assert_not_called()
        mock_cache.get_vc_thesis.assert_called_once()
        mock_cache.set_vc_thesis.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_extract_vc_thesis_updates_vc_object(self, analyzer_service, mock_vc, mock_vc_thesis_analysis, mock_cache):
        """Test that VC object is updated with extracted data."""
        website_content = "We invest in AI startups..."
        analyzer_service.vc_chain.extract = AsyncMock(return_value=mock_vc_thesis_analysis)
        
        await analyzer_service.extract_vc_thesis(mock_vc, website_content)
        
        assert mock_vc.thesis == mock_vc_thesis_analysis.thesis_summary
        assert mock_vc.sectors == mock_vc_thesis_analysis.investment_focus.sectors
        assert mock_vc.stages == ["Seed", "Series A"]
        assert mock_vc.check_size_min == 1000000
        assert mock_vc.check_size_max == 5000000


class TestBatchAnalyzeStartups:
    """Test batch_analyze_startups method."""
    
    @pytest.mark.asyncio
    async def test_batch_analyze_success(self, analyzer_service, mock_startup_analysis):
        """Test successful batch analysis of startups."""
        startups = [
            Startup(name=f"Startup{i}", sector="AI", stage="Seed", description=f"Description {i}")
            for i in range(5)
        ]
        analyzer_service.analyze_startup = AsyncMock(return_value=mock_startup_analysis)
        
        results = await analyzer_service.batch_analyze_startups(startups, max_concurrent=2)
        
        assert len(results) == 5
        assert all(r == mock_startup_analysis for r in results)
        assert analyzer_service.analyze_startup.call_count == 5
    
    @pytest.mark.asyncio
    async def test_batch_analyze_with_failures(self, analyzer_service, mock_startup_analysis):
        """Test batch analysis with some failures."""
        startups = [
            Startup(name=f"Startup{i}", sector="AI", stage="Seed", description=f"Description {i}")
            for i in range(3)
        ]
        # First succeeds, second fails, third succeeds
        analyzer_service.analyze_startup = AsyncMock(
            side_effect=[mock_startup_analysis, Exception("Analysis failed"), mock_startup_analysis]
        )
        
        results = await analyzer_service.batch_analyze_startups(startups)
        
        assert len(results) == 2  # Only successful analyses
        assert analyzer_service.analyze_startup.call_count == 3
    
    @pytest.mark.asyncio
    async def test_batch_analyze_respects_concurrency_limit(self, analyzer_service, mock_startup_analysis):
        """Test that batch analysis respects concurrency limit."""
        startups = [
            Startup(name=f"Startup{i}", sector="AI", stage="Seed", description=f"Description {i}")
            for i in range(10)
        ]
        
        call_times = []
        
        async def mock_analyze(startup, **kwargs):
            call_times.append(time.time())
            await asyncio.sleep(0.1)  # Simulate processing time
            return mock_startup_analysis
        
        analyzer_service.analyze_startup = mock_analyze
        
        start_time = time.time()
        results = await analyzer_service.batch_analyze_startups(startups, max_concurrent=3)
        
        assert len(results) == 10
        # Check that no more than 3 were running concurrently
        # With max_concurrent=3 and 10 items, we expect at least 4 batches
        assert (time.time() - start_time) >= 0.3  # At least 4 batches * 0.1s


class TestTokenUsageCallback:
    """Test TokenUsageCallback functionality."""
    
    @pytest.mark.asyncio
    async def test_token_usage_tracking(self):
        """Test that token usage is tracked correctly."""
        callback = TokenUsageCallback()
        
        # Create mock LLM response
        mock_response = Mock()
        mock_response.llm_output = {
            'token_usage': {
                'total_tokens': 150,
                'prompt_tokens': 100,
                'completion_tokens': 50
            }
        }
        
        await callback.on_llm_end(mock_response)
        
        assert callback.total_tokens == 150
        assert callback.prompt_tokens == 100
        assert callback.completion_tokens == 50
        assert callback.total_cost > 0
    
    @pytest.mark.asyncio
    async def test_token_usage_accumulation(self):
        """Test that token usage accumulates across multiple calls."""
        callback = TokenUsageCallback()
        
        # First call
        mock_response1 = Mock()
        mock_response1.llm_output = {
            'token_usage': {
                'total_tokens': 100,
                'prompt_tokens': 60,
                'completion_tokens': 40
            }
        }
        await callback.on_llm_end(mock_response1)
        
        # Second call
        mock_response2 = Mock()
        mock_response2.llm_output = {
            'token_usage': {
                'total_tokens': 200,
                'prompt_tokens': 120,
                'completion_tokens': 80
            }
        }
        await callback.on_llm_end(mock_response2)
        
        assert callback.total_tokens == 300
        assert callback.prompt_tokens == 180
        assert callback.completion_tokens == 120
    
    @pytest.mark.asyncio
    async def test_token_usage_no_data(self):
        """Test token usage callback with missing data."""
        callback = TokenUsageCallback()
        
        # Response without llm_output
        mock_response1 = Mock()
        mock_response1.llm_output = None
        await callback.on_llm_end(mock_response1)
        
        assert callback.total_tokens == 0
        
        # Response without token_usage
        mock_response2 = Mock()
        mock_response2.llm_output = {}
        await callback.on_llm_end(mock_response2)
        
        assert callback.total_tokens == 0


class TestRetryMechanism:
    """Test retry mechanism with @retry decorator."""
    
    @pytest.mark.asyncio
    async def test_retry_on_transient_error(self, analyzer_service, mock_startup, mock_startup_analysis):
        """Test that transient errors trigger retries."""
        call_count = 0
        
        async def mock_analyze(**kwargs):
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise Exception("Transient error")
            return mock_startup_analysis
        
        analyzer_service.startup_chain.analyze = mock_analyze
        
        result = await analyzer_service.analyze_startup(mock_startup, use_cache=False)
        
        assert result == mock_startup_analysis
        assert call_count == 3  # Failed twice, succeeded on third try
    
    @pytest.mark.asyncio
    async def test_retry_exhaustion(self, analyzer_service, mock_startup):
        """Test that retries are exhausted after max attempts."""
        analyzer_service.startup_chain.analyze = AsyncMock(
            side_effect=Exception("Persistent error")
        )
        
        # The retry decorator will raise a RetryError after exhausting attempts
        with pytest.raises(RetryError):
            await analyzer_service.analyze_startup(mock_startup, use_cache=False)
        
        # Should have tried 3 times (configured in the @retry decorator)
        assert analyzer_service.startup_chain.analyze.call_count == 3


class TestUsageStats:
    """Test usage statistics methods."""
    
    def test_get_usage_stats(self, analyzer_service, mock_cache):
        """Test getting usage statistics."""
        analyzer_service.token_tracker.total_tokens = 1000
        analyzer_service.token_tracker.prompt_tokens = 600
        analyzer_service.token_tracker.completion_tokens = 400
        analyzer_service.token_tracker.total_cost = 0.05
        
        stats = analyzer_service.get_usage_stats()
        
        assert stats['total_tokens'] == 1000
        assert stats['prompt_tokens'] == 600
        assert stats['completion_tokens'] == 400
        assert stats['total_cost'] == 0.05
        assert stats['cache_stats']['hit_rate'] == 0.83
    
    def test_reset_usage_stats(self, analyzer_service):
        """Test resetting usage statistics."""
        analyzer_service.token_tracker.total_tokens = 1000
        analyzer_service.token_tracker.total_cost = 0.05
        
        analyzer_service.reset_usage_stats()
        
        assert analyzer_service.token_tracker.total_tokens == 0
        assert analyzer_service.token_tracker.total_cost == 0


class TestErrorHandlingAndLogging:
    """Test error handling and logging functionality."""
    
    @pytest.mark.asyncio
    async def test_analyze_startup_error_logging(self, analyzer_service, mock_startup, caplog):
        """Test that errors are logged properly during startup analysis."""
        analyzer_service.startup_chain.analyze = AsyncMock(
            side_effect=LangChainException("LLM error")
        )
        
        with pytest.raises(RetryError):
            await analyzer_service.analyze_startup(mock_startup, use_cache=False)
        
        # The error message is logged during the retry attempts
        assert "LLM error" in caplog.text or "Error analyzing startup" in caplog.text
    
    @pytest.mark.asyncio
    async def test_extract_vc_thesis_error_logging(self, analyzer_service, mock_vc, caplog):
        """Test that errors are logged properly during VC thesis extraction."""
        analyzer_service.vc_chain.extract = AsyncMock(
            side_effect=LangChainException("LLM error")
        )
        
        with pytest.raises(RetryError):
            await analyzer_service.extract_vc_thesis(mock_vc, "website content", use_cache=False)
        
        # The error message is logged during the retry attempts  
        assert "LLM error" in caplog.text or "Error extracting thesis" in caplog.text


class TestPerformanceMetrics:
    """Test performance metrics logging."""
    
    @pytest.mark.asyncio
    async def test_startup_analysis_performance_logging(self, analyzer_service, mock_startup, mock_startup_analysis, caplog):
        """Test that performance metrics are logged for startup analysis."""
        analyzer_service.startup_chain.analyze = AsyncMock(return_value=mock_startup_analysis)
        analyzer_service.token_tracker.total_tokens = 150
        analyzer_service.token_tracker.total_cost = 0.01
        
        with caplog.at_level("INFO"):
            await analyzer_service.analyze_startup(mock_startup, use_cache=False)
        
        assert "Analyzed startup TechCorp" in caplog.text
        assert "tokens: 150" in caplog.text
        assert "cost: $0.0100" in caplog.text


class TestEdgeCases:
    """Test edge cases and null value handling."""
    
    @pytest.mark.asyncio
    async def test_analyze_startup_with_minimal_data(self, analyzer_service, mock_startup_analysis):
        """Test startup analysis with minimal data."""
        minimal_startup = Startup(
            name="MinimalCorp",
            sector="Unknown",
            stage="Unknown",
            description="",
            website=""
        )
        analyzer_service.startup_chain.analyze = AsyncMock(return_value=mock_startup_analysis)
        
        result = await analyzer_service.analyze_startup(minimal_startup, use_cache=False)
        
        assert result == mock_startup_analysis
        analyzer_service.startup_chain.analyze.assert_called_with(
            description="",
            name="MinimalCorp",
            sector="Unknown",
            stage="Unknown",
            website="",
            callbacks=[analyzer_service.token_tracker]
        )
    
    @pytest.mark.asyncio
    async def test_extract_vc_thesis_with_empty_content(self, analyzer_service, mock_vc, mock_vc_thesis_analysis):
        """Test VC thesis extraction with empty website content."""
        analyzer_service.vc_chain.extract = AsyncMock(return_value=mock_vc_thesis_analysis)
        
        result = await analyzer_service.extract_vc_thesis(mock_vc, "", use_cache=False)
        
        assert result == mock_vc_thesis_analysis
    
    @pytest.mark.asyncio
    async def test_batch_analyze_empty_list(self, analyzer_service):
        """Test batch analysis with empty startup list."""
        results = await analyzer_service.batch_analyze_startups([])
        
        assert results == []
        # Don't check analyze_startup calls since it won't be called with empty list
    
    @pytest.mark.asyncio
    async def test_vc_thesis_without_check_sizes(self, analyzer_service, mock_vc):
        """Test VC thesis extraction when check sizes are not provided."""
        thesis_without_sizes = VCThesisAnalysis(
            thesis_summary="Investment thesis",
            investment_focus=InvestmentFocus(
                sectors=["Tech"],
                stages=[InvestmentStage.SEED],
                technologies=[],
                geographical_focus=[]
            ),
            check_size_range={},  # Empty check size range
            portfolio_themes=[],
            avoided_sectors=[],
            key_criteria=[],
            notable_partners=[],
            confidence_score=0.8
        )
        analyzer_service.vc_chain.extract = AsyncMock(return_value=thesis_without_sizes)
        
        await analyzer_service.extract_vc_thesis(mock_vc, "content", use_cache=False)
        
        # Check sizes should remain as originally set in mock_vc
        assert mock_vc.check_size_min == 1000000
        assert mock_vc.check_size_max == 5000000


class TestCacheInteractions:
    """Test cache interactions including hits, misses, and errors."""
    
    @pytest.mark.asyncio
    async def test_cache_hit_rate_tracking(self, analyzer_service):
        """Test that cache hit rate is properly tracked."""
        stats = analyzer_service.get_usage_stats()
        
        assert 'cache_stats' in stats
        assert 'hit_rate' in stats['cache_stats']
        assert 0 <= stats['cache_stats']['hit_rate'] <= 1
    
    @pytest.mark.asyncio
    async def test_cache_miss_handling(self, analyzer_service, mock_startup, mock_startup_analysis, mock_cache):
        """Test proper handling of cache misses."""
        mock_cache.get_startup_analysis.return_value = None  # Cache miss
        analyzer_service.startup_chain.analyze = AsyncMock(return_value=mock_startup_analysis)
        
        result = await analyzer_service.analyze_startup(mock_startup)
        
        assert result == mock_startup_analysis
        mock_cache.get_startup_analysis.assert_called_once()
        mock_cache.set_startup_analysis.assert_called_once()
        analyzer_service.startup_chain.analyze.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_cache_error_recovery(self, analyzer_service, mock_startup, mock_startup_analysis, mock_cache):
        """Test that the service continues working when cache fails."""
        mock_cache.get_startup_analysis.side_effect = RedisError("Connection failed")
        mock_cache.set_startup_analysis.side_effect = RedisError("Connection failed")
        analyzer_service.startup_chain.analyze = AsyncMock(return_value=mock_startup_analysis)
        
        result = await analyzer_service.analyze_startup(mock_startup)
        
        assert result == mock_startup_analysis
        # Should still work despite cache errors
        analyzer_service.startup_chain.analyze.assert_called_once()


class TestConcurrencyAndThreadSafety:
    """Test concurrency handling and thread safety."""
    
    @pytest.mark.asyncio
    async def test_concurrent_startup_analyses(self, analyzer_service, mock_startup_analysis):
        """Test multiple concurrent analyses of different startups."""
        startups = [
            Startup(name=f"Startup{i}", sector="AI", stage="Seed", description=f"Desc {i}")
            for i in range(10)
        ]
        
        async def analyze_with_delay(startup):
            await asyncio.sleep(0.01)  # Small delay to test concurrency
            return mock_startup_analysis
        
        analyzer_service.analyze_startup = analyze_with_delay
        
        # Run all analyses concurrently
        tasks = [analyzer_service.analyze_startup(s) for s in startups]
        results = await asyncio.gather(*tasks)
        
        assert len(results) == 10
        assert all(r == mock_startup_analysis for r in results)
    
    @pytest.mark.asyncio
    async def test_token_tracker_thread_safety(self, analyzer_service):
        """Test that token tracker handles concurrent updates safely."""
        callback = analyzer_service.token_tracker
        
        async def update_tokens():
            mock_response = Mock()
            mock_response.llm_output = {
                'token_usage': {
                    'total_tokens': 10,
                    'prompt_tokens': 5,
                    'completion_tokens': 5
                }
            }
            await callback.on_llm_end(mock_response)
        
        # Run multiple concurrent updates
        tasks = [update_tokens() for _ in range(100)]
        await asyncio.gather(*tasks)
        
        # Should have accumulated all tokens
        assert callback.total_tokens == 1000
        assert callback.prompt_tokens == 500
        assert callback.completion_tokens == 500


class TestIntegrationScenarios:
    """Test realistic integration scenarios."""
    
    @pytest.mark.asyncio
    async def test_full_analysis_workflow(self, analyzer_service, mock_startup, mock_startup_analysis, mock_cache):
        """Test complete workflow from startup analysis to caching."""
        # First analysis - cache miss
        mock_cache.get_startup_analysis.return_value = None
        analyzer_service.startup_chain.analyze = AsyncMock(return_value=mock_startup_analysis)
        
        result1 = await analyzer_service.analyze_startup(mock_startup)
        assert result1 == mock_startup_analysis
        assert mock_cache.set_startup_analysis.called
        
        # Second analysis - cache hit
        mock_cache.get_startup_analysis.return_value = mock_startup_analysis
        mock_cache.reset_mock()
        analyzer_service.startup_chain.analyze.reset_mock()
        
        result2 = await analyzer_service.analyze_startup(mock_startup)
        assert result2 == mock_startup_analysis
        assert not analyzer_service.startup_chain.analyze.called
        assert not mock_cache.set_startup_analysis.called
    
    @pytest.mark.asyncio
    async def test_mixed_success_failure_batch(self, analyzer_service, mock_startup_analysis):
        """Test batch processing with mixed success and failure."""
        startups = [
            Startup(name="Success1", sector="AI", stage="Seed"),
            Startup(name="Failure1", sector="AI", stage="Seed"),
            Startup(name="Success2", sector="AI", stage="Seed"),
            Startup(name="Failure2", sector="AI", stage="Seed"),
            Startup(name="Success3", sector="AI", stage="Seed"),
        ]
        
        # Alternate between success and failure
        side_effects = []
        for i, startup in enumerate(startups):
            if i % 2 == 0:
                side_effects.append(mock_startup_analysis)
            else:
                side_effects.append(Exception(f"Failed to analyze {startup.name}"))
        
        analyzer_service.analyze_startup = AsyncMock(side_effect=side_effects)
        
        results = await analyzer_service.batch_analyze_startups(startups)
        
        # Should have 3 successful results
        assert len(results) == 3
        assert all(r == mock_startup_analysis for r in results)


class TestConfigurationEdgeCases:
    """Test edge cases in service configuration."""
    
    def test_init_without_api_key(self):
        """Test initialization without API key (should use env var)."""
        with patch('src.core.ai.analyzer.ChatOpenAI') as mock_openai:
            with patch('src.core.ai.analyzer.StartupAnalyzerChain'):
                with patch('src.core.ai.analyzer.VCThesisExtractorChain'):
                    service = AIAnalyzerService(openai_api_key=None)
                    
                    mock_openai.assert_called_with(
                        openai_api_key=None,
                        model_name="gpt-4",
                        temperature=0.3,
                        streaming=False
                    )
    
    def test_init_with_zero_temperature(self):
        """Test initialization with zero temperature for deterministic output."""
        with patch('src.core.ai.analyzer.ChatOpenAI') as mock_openai:
            with patch('src.core.ai.analyzer.StartupAnalyzerChain'):
                with patch('src.core.ai.analyzer.VCThesisExtractorChain'):
                    service = AIAnalyzerService(temperature=0.0)
                    
                    mock_openai.assert_called_with(
                        openai_api_key=None,
                        model_name="gpt-4",
                        temperature=0.0,
                        streaming=False
                    )


if __name__ == "__main__":
    pytest.main([__file__, "-v"])