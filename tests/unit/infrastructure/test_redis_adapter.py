"""Comprehensive tests for Redis adapter and connection factory."""

import pytest
import asyncio
import json
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from redis.exceptions import RedisError, ConnectionError

from src.infrastructure.redis.adapter import RedisAdapter
from src.infrastructure.redis.connection import RedisConnectionFactory
from src.infrastructure.redis.port import CachePort


class TestRedisConnectionFactory:
    """Test Redis connection factory functionality."""
    
    def setup_method(self):
        """Reset connection factory state before each test."""
        RedisConnectionFactory.reset_connections()
    
    def teardown_method(self):
        """Clean up after each test."""
        RedisConnectionFactory.reset_connections()
    
    @patch('src.infrastructure.redis.connection.redis.Redis')
    @patch('src.infrastructure.redis.connection.redis.ConnectionPool')
    def test_get_sync_client_success(self, mock_pool, mock_redis):
        """Test successful sync client creation."""
        # Setup mocks
        mock_client = Mock()
        mock_client.ping.return_value = True
        mock_redis.return_value = mock_client
        
        # Test
        client = RedisConnectionFactory.get_sync_client("redis://localhost:6379")
        
        # Assertions
        assert client == mock_client
        mock_client.ping.assert_called_once()
        mock_pool.assert_called_once()
        mock_redis.assert_called_once()
    
    @patch('src.infrastructure.redis.connection.redis.Redis')
    def test_get_sync_client_connection_error(self, mock_redis):
        """Test sync client creation with connection error."""
        # Setup mock to raise error
        mock_client = Mock()
        mock_client.ping.side_effect = ConnectionError("Cannot connect")
        mock_redis.return_value = mock_client
        
        # Test
        with pytest.raises(ConnectionError):
            RedisConnectionFactory.get_sync_client("redis://localhost:6379")
    
    @patch('src.infrastructure.redis.connection.redis_async.Redis')
    @patch('src.infrastructure.redis.connection.redis_async.ConnectionPool')
    async def test_get_async_client_success(self, mock_pool, mock_redis):
        """Test successful async client creation."""
        # Setup mocks
        mock_client = AsyncMock()
        mock_client.ping.return_value = True
        mock_redis.return_value = mock_client
        
        # Test
        client = await RedisConnectionFactory.get_async_client("redis://localhost:6379")
        
        # Assertions
        assert client == mock_client
        mock_client.ping.assert_called_once()
        mock_pool.assert_called_once()
        mock_redis.assert_called_once()
    
    @patch('src.infrastructure.redis.connection.redis_async.Redis')
    async def test_get_async_client_connection_error(self, mock_redis):
        """Test async client creation with connection error."""
        # Setup mock to raise error
        mock_client = AsyncMock()
        mock_client.ping.side_effect = ConnectionError("Cannot connect")
        mock_redis.return_value = mock_client
        
        # Test
        with pytest.raises(ConnectionError):
            await RedisConnectionFactory.get_async_client("redis://localhost:6379")
    
    def test_parse_redis_url_basic(self):
        """Test parsing basic Redis URL."""
        params = RedisConnectionFactory._parse_redis_url("redis://localhost:6379/0")
        
        expected = {
            'host': 'localhost',
            'port': 6379,
            'db': 0,
            'decode_responses': True,
            'health_check_interval': 30,
            'socket_connect_timeout': 5,
            'socket_timeout': 5,
            'retry_on_timeout': True,
            'max_connections': 20,
        }
        
        assert params == expected
    
    def test_parse_redis_url_with_auth(self):
        """Test parsing Redis URL with authentication."""
        params = RedisConnectionFactory._parse_redis_url("redis://user:pass@localhost:6379/1")
        
        assert params['username'] == 'user'
        assert params['password'] == 'pass'
        assert params['db'] == 1
    
    def test_parse_redis_url_ssl(self):
        """Test parsing Redis SSL URL."""
        params = RedisConnectionFactory._parse_redis_url("rediss://localhost:6380/0")
        
        assert params['ssl'] is True
        assert params['port'] == 6380
    
    def test_parse_redis_url_invalid_scheme(self):
        """Test parsing invalid Redis URL scheme."""
        params = RedisConnectionFactory._parse_redis_url("http://localhost:6379")
        
        # Should fallback to defaults
        assert params['host'] == 'localhost'
        assert params['port'] == 6379
    
    @patch('src.infrastructure.redis.connection.RedisConnectionFactory.get_sync_client')
    @patch('src.infrastructure.redis.connection.RedisConnectionFactory.get_async_client')
    async def test_health_check_success(self, mock_async_client, mock_sync_client):
        """Test successful health check."""
        # Setup mocks
        sync_client = Mock()
        sync_client.ping.return_value = True
        sync_client.info.return_value = {
            'used_memory_human': '1.2MB',
            'connected_clients': 5
        }
        mock_sync_client.return_value = sync_client
        
        async_client = AsyncMock()
        async_client.ping.return_value = True
        mock_async_client.return_value = async_client
        
        # Test
        health = await RedisConnectionFactory.health_check()
        
        # Assertions
        assert health['healthy'] is True
        assert health['sync_connection'] is True
        assert health['async_connection'] is True
        assert health['memory_usage'] == '1.2MB'
        assert health['connected_clients'] == 5
        assert health['latency_ms'] is not None
    
    @patch('src.infrastructure.redis.connection.RedisConnectionFactory.get_sync_client')
    async def test_health_check_failure(self, mock_sync_client):
        """Test health check with connection failure."""
        # Setup mock to raise error
        mock_sync_client.side_effect = ConnectionError("Connection failed")
        
        # Test
        health = await RedisConnectionFactory.health_check()
        
        # Assertions
        assert health['healthy'] is False
        assert health['error'] is not None


class TestRedisAdapter:
    """Test Redis adapter implementation."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.mock_client = AsyncMock()
        self.adapter = RedisAdapter(
            default_ttl=3600,
            key_prefix="test"
        )
        self.adapter._client = self.mock_client
    
    async def test_get_success(self):
        """Test successful get operation."""
        # Setup
        self.mock_client.get.return_value = "test_value"
        
        # Test
        result = await self.adapter.get("test_key")
        
        # Assertions
        assert result == "test_value"
        self.mock_client.get.assert_called_once_with("test:test_key")
    
    async def test_get_not_found(self):
        """Test get operation when key not found."""
        # Setup
        self.mock_client.get.return_value = None
        
        # Test
        result = await self.adapter.get("test_key")
        
        # Assertions
        assert result is None
        self.mock_client.get.assert_called_once_with("test:test_key")
    
    async def test_get_redis_error(self):
        """Test get operation with Redis error."""
        # Setup
        self.mock_client.get.side_effect = RedisError("Redis error")
        
        # Test
        result = await self.adapter.get("test_key")
        
        # Assertions
        assert result is None  # Should return None on error
    
    async def test_set_success(self):
        """Test successful set operation."""
        # Setup
        self.mock_client.setex.return_value = True
        
        # Test
        result = await self.adapter.set("test_key", "test_value", ttl=1800)
        
        # Assertions
        assert result is True
        self.mock_client.setex.assert_called_once_with("test:test_key", 1800, "test_value")
    
    async def test_set_with_default_ttl(self):
        """Test set operation using default TTL."""
        # Setup
        self.mock_client.setex.return_value = True
        
        # Test
        result = await self.adapter.set("test_key", "test_value")
        
        # Assertions
        assert result is True
        self.mock_client.setex.assert_called_once_with("test:test_key", 3600, "test_value")
    
    async def test_set_redis_error(self):
        """Test set operation with Redis error."""
        # Setup
        self.mock_client.setex.side_effect = RedisError("Redis error")
        
        # Test
        result = await self.adapter.set("test_key", "test_value")
        
        # Assertions
        assert result is False  # Should return False on error
    
    async def test_delete_success(self):
        """Test successful delete operation."""
        # Setup
        self.mock_client.delete.return_value = 1
        
        # Test
        result = await self.adapter.delete("test_key")
        
        # Assertions
        assert result is True
        self.mock_client.delete.assert_called_once_with("test:test_key")
    
    async def test_delete_key_not_found(self):
        """Test delete operation when key doesn't exist."""
        # Setup
        self.mock_client.delete.return_value = 0
        
        # Test
        result = await self.adapter.delete("test_key")
        
        # Assertions
        assert result is False
    
    async def test_exists_true(self):
        """Test exists operation when key exists."""
        # Setup
        self.mock_client.exists.return_value = 1
        
        # Test
        result = await self.adapter.exists("test_key")
        
        # Assertions
        assert result is True
        self.mock_client.exists.assert_called_once_with("test:test_key")
    
    async def test_exists_false(self):
        """Test exists operation when key doesn't exist."""
        # Setup
        self.mock_client.exists.return_value = 0
        
        # Test
        result = await self.adapter.exists("test_key")
        
        # Assertions
        assert result is False
    
    async def test_increment_default(self):
        """Test increment operation with default amount."""
        # Setup
        self.mock_client.incr.return_value = 5
        
        # Test
        result = await self.adapter.increment("test_key")
        
        # Assertions
        assert result == 5
        self.mock_client.incr.assert_called_once_with("test:test_key")
    
    async def test_increment_custom_amount(self):
        """Test increment operation with custom amount."""
        # Setup
        self.mock_client.incrby.return_value = 10
        
        # Test
        result = await self.adapter.increment("test_key", 5)
        
        # Assertions
        assert result == 10
        self.mock_client.incrby.assert_called_once_with("test:test_key", 5)
    
    async def test_expire_success(self):
        """Test successful expire operation."""
        # Setup
        self.mock_client.expire.return_value = True
        
        # Test
        result = await self.adapter.expire("test_key", 3600)
        
        # Assertions
        assert result is True
        self.mock_client.expire.assert_called_once_with("test:test_key", 3600)
    
    async def test_keys_with_prefix(self):
        """Test keys operation with prefix."""
        # Setup
        self.mock_client.keys.return_value = ["test:key1", "test:key2", "test:key3"]
        
        # Test
        result = await self.adapter.keys("*")
        
        # Assertions
        assert result == ["key1", "key2", "key3"]
        self.mock_client.keys.assert_called_once_with("test:*")
    
    async def test_delete_many_success(self):
        """Test successful delete_many operation."""
        # Setup
        self.mock_client.delete.return_value = 3
        
        # Test
        result = await self.adapter.delete_many(["key1", "key2", "key3"])
        
        # Assertions
        assert result == 3
        self.mock_client.delete.assert_called_once_with("test:key1", "test:key2", "test:key3")
    
    async def test_delete_many_empty_list(self):
        """Test delete_many with empty list."""
        # Test
        result = await self.adapter.delete_many([])
        
        # Assertions
        assert result == 0
        self.mock_client.delete.assert_not_called()
    
    async def test_health_check_success(self):
        """Test successful health check."""
        # Setup
        self.mock_client.ping.return_value = True
        
        # Test
        result = await self.adapter.health_check()
        
        # Assertions
        assert result is True
        self.mock_client.ping.assert_called_once()
    
    async def test_health_check_failure(self):
        """Test health check failure."""
        # Setup
        self.mock_client.ping.side_effect = RedisError("Connection failed")
        
        # Test
        result = await self.adapter.health_check()
        
        # Assertions
        assert result is False
    
    async def test_set_json_success(self):
        """Test successful JSON set operation."""
        # Setup
        self.mock_client.setex.return_value = True
        test_data = {"key": "value", "number": 123}
        
        # Test
        result = await self.adapter.set_json("test_key", test_data)
        
        # Assertions
        assert result is True
        expected_json = json.dumps(test_data)
        self.mock_client.setex.assert_called_once_with("test:test_key", 3600, expected_json)
    
    async def test_set_json_serialization_error(self):
        """Test JSON set with serialization error."""
        # Setup - create non-serializable object
        class NonSerializable:
            pass
        
        # Test
        result = await self.adapter.set_json("test_key", NonSerializable())
        
        # Assertions
        assert result is False
        self.mock_client.setex.assert_not_called()
    
    async def test_get_json_success(self):
        """Test successful JSON get operation."""
        # Setup
        test_data = {"key": "value", "number": 123}
        self.mock_client.get.return_value = json.dumps(test_data)
        
        # Test
        result = await self.adapter.get_json("test_key")
        
        # Assertions
        assert result == test_data
        self.mock_client.get.assert_called_once_with("test:test_key")
    
    async def test_get_json_invalid_json(self):
        """Test JSON get with invalid JSON."""
        # Setup
        self.mock_client.get.return_value = "invalid json"
        
        # Test
        result = await self.adapter.get_json("test_key")
        
        # Assertions
        assert result is None
    
    async def test_get_json_not_found(self):
        """Test JSON get when key not found."""
        # Setup
        self.mock_client.get.return_value = None
        
        # Test
        result = await self.adapter.get_json("test_key")
        
        # Assertions
        assert result is None
    
    async def test_clear_prefix_success(self):
        """Test successful clear_prefix operation."""
        # Setup
        self.mock_client.keys.return_value = ["test:prefix:key1", "test:prefix:key2"]
        self.mock_client.delete.return_value = 2
        
        # Test
        result = await self.adapter.clear_prefix("prefix")
        
        # Assertions
        assert result == 2
        self.mock_client.keys.assert_called_once_with("test:prefix:*")
        self.mock_client.delete.assert_called_once_with("prefix:key1", "prefix:key2")
    
    async def test_clear_prefix_no_keys(self):
        """Test clear_prefix when no keys match."""
        # Setup
        self.mock_client.keys.return_value = []
        
        # Test
        result = await self.adapter.clear_prefix("prefix")
        
        # Assertions
        assert result == 0
        self.mock_client.delete.assert_not_called()


class TestRedisAdapterIntegration:
    """Integration tests for Redis adapter (require actual Redis instance)."""
    
    @pytest.mark.integration
    async def test_end_to_end_operations(self):
        """Test end-to-end Redis operations with real Redis instance."""
        # Note: This test requires a running Redis instance
        # Skip if Redis is not available
        try:
            adapter = RedisAdapter(
                redis_url="redis://localhost:6379",
                key_prefix="test_integration"
            )
            
            # Test health check
            healthy = await adapter.health_check()
            if not healthy:
                pytest.skip("Redis not available for integration test")
            
            # Test set and get
            success = await adapter.set("test_key", "test_value", ttl=60)
            assert success is True
            
            value = await adapter.get("test_key")
            assert value == "test_value"
            
            # Test JSON operations
            test_data = {"name": "test", "value": 42}
            json_success = await adapter.set_json("json_key", test_data, ttl=60)
            assert json_success is True
            
            json_value = await adapter.get_json("json_key")
            assert json_value == test_data
            
            # Test exists
            exists = await adapter.exists("test_key")
            assert exists is True
            
            # Test delete
            deleted = await adapter.delete("test_key")
            assert deleted is True
            
            exists_after_delete = await adapter.exists("test_key")
            assert exists_after_delete is False
            
            # Cleanup
            await adapter.clear_prefix("test_integration")
            
        except Exception as e:
            pytest.skip(f"Redis integration test failed: {e}")


class TestCachePortInterface:
    """Test that RedisAdapter properly implements CachePort interface."""
    
    def test_implements_cache_port(self):
        """Test that RedisAdapter implements all CachePort methods."""
        adapter = RedisAdapter()
        
        # Check that adapter is instance of CachePort
        assert isinstance(adapter, CachePort)
        
        # Check all required methods exist
        required_methods = [
            'get', 'set', 'delete', 'exists', 'increment',
            'expire', 'keys', 'delete_many', 'get_stats', 'health_check'
        ]
        
        for method_name in required_methods:
            assert hasattr(adapter, method_name)
            assert callable(getattr(adapter, method_name))
    
    def test_key_prefix_functionality(self):
        """Test key prefix functionality."""
        adapter = RedisAdapter(key_prefix="test_prefix")
        
        # Test key generation
        full_key = adapter._make_key("test_key")
        assert full_key == "test_prefix:test_key"
        
        # Test without prefix
        adapter_no_prefix = RedisAdapter(key_prefix="")
        full_key_no_prefix = adapter_no_prefix._make_key("test_key")
        assert full_key_no_prefix == "test_key"