"""
Tests for the validation service.
"""
import pytest
from uuid import uuid4

from src.core.services.validation_service import ValidationService, get_validation_service


class TestValidationService:
    """Test suite for validation service."""
    
    @pytest.fixture
    def validation_service(self):
        """Create validation service instance."""
        return ValidationService()
    
    def test_singleton_pattern(self):
        """Test that get_validation_service returns singleton."""
        service1 = get_validation_service()
        service2 = get_validation_service()
        assert service1 is service2
    
    # URL Validation Tests
    
    def test_validate_url_valid(self, validation_service):
        """Test valid URL validation."""
        test_cases = [
            ("https://example.com", "https://example.com"),
            ("http://example.com", "http://example.com"),
            ("example.com", "https://example.com"),  # Should add https
            ("https://example.com/path", "https://example.com/path"),
            ("https://example.com:8080", "https://example.com:8080"),
        ]
        
        for url, expected in test_cases:
            is_valid, cleaned = validation_service.validate_url(url)
            assert is_valid is True
            assert cleaned == expected
    
    def test_validate_url_invalid(self, validation_service):
        """Test invalid URL validation."""
        test_cases = [
            "invalid url",
            "http://",
            "://example.com",
            "https://test.com",  # Spam domain
            "https://fake.com",  # Spam domain
        ]
        
        for url in test_cases:
            is_valid, cleaned = validation_service.validate_url(url)
            assert is_valid is False
            assert cleaned is None
    
    def test_validate_url_empty(self, validation_service):
        """Test empty URL validation."""
        assert validation_service.validate_url(None) == (True, None)
        assert validation_service.validate_url("") == (True, None)
    
    # Email Validation Tests
    
    def test_validate_email_valid(self, validation_service):
        """Test valid email validation."""
        test_cases = [
            ("<EMAIL>", "<EMAIL>"),
            ("<EMAIL>", "<EMAIL>"),  # Should lowercase
            ("<EMAIL>", "<EMAIL>"),
        ]
        
        for email, expected in test_cases:
            is_valid, cleaned = validation_service.validate_email(email)
            assert is_valid is True
            assert cleaned == expected
    
    def test_validate_email_invalid(self, validation_service):
        """Test invalid email validation."""
        test_cases = [
            "invalid.email",
            "@company.com",
            "user@",
            "<EMAIL>",  # Spam domain
            "<EMAIL>",  # Spam domain
        ]
        
        for email in test_cases:
            is_valid, cleaned = validation_service.validate_email(email)
            assert is_valid is False
            assert cleaned is None
    
    # Startup Validation Tests
    
    def test_validate_startup_data_valid(self, validation_service):
        """Test valid startup data validation."""
        data = {
            "name": "Valid Startup Inc",
            "website": "validstartup.com",
            "email": "<EMAIL>",
            "stage": "Series A",  # Should normalize to series-a
            "sector": "fintech",
            "team_size": 50,
            "monthly_revenue": 100000,
            "description": "We are building the future of finance technology"
        }
        
        errors = validation_service.validate_startup_data(data)
        assert errors == {}
        assert data["stage"] == "series-a"  # Should be normalized
        assert data["website"] == "https://validstartup.com"  # Should add https
    
    def test_validate_startup_data_invalid(self, validation_service):
        """Test invalid startup data validation."""
        data = {
            "name": "A",  # Too short
            "website": "invalid url",
            "email": "invalid.email",
            "stage": "invalid-stage",
            "team_size": -5,
            "monthly_revenue": -1000,
            "description": "Too short"
        }
        
        errors = validation_service.validate_startup_data(data)
        assert "name" in errors
        assert "website" in errors
        assert "email" in errors
        assert "stage" in errors
        assert "team_size" in errors
        assert "monthly_revenue" in errors
        assert "description" in errors
    
    def test_validate_startup_data_partial(self, validation_service):
        """Test partial startup data validation."""
        data = {
            "name": "Valid Startup"
        }
        
        errors = validation_service.validate_startup_data(data)
        assert errors == {}
    
    # VC Validation Tests
    
    def test_validate_vc_data_valid(self, validation_service):
        """Test valid VC data validation."""
        data = {
            "firm_name": "Sequoia Capital",
            "website": "sequoiacap.com",
            "email": "<EMAIL>",
            "thesis": "We invest in early-stage companies with strong technical founders",
            "min_check_size": 500000,
            "max_check_size": 5000000,
            "sectors": ["ai/ml", "fintech", "saas"],
            "stages": ["seed", "series-a"]
        }
        
        errors = validation_service.validate_vc_data(data)
        assert errors == {}
        assert data["website"] == "https://sequoiacap.com"  # Should add https
    
    def test_validate_vc_data_invalid_check_sizes(self, validation_service):
        """Test VC data with invalid check sizes."""
        data = {
            "firm_name": "Test VC",
            "min_check_size": 5000000,
            "max_check_size": 1000000  # Less than min
        }
        
        errors = validation_service.validate_vc_data(data)
        assert "check_sizes" in errors
    
    # Duplicate Detection Tests
    
    def test_find_duplicate_startups_exact_match(self, validation_service):
        """Test finding exact duplicate startups."""
        new_startup = {
            "name": "OpenAI",
            "website": "openai.com",
            "sector": "ai/ml",
            "stage": "growth"
        }
        
        existing_startups = [
            {"name": "OpenAI", "website": "openai.com", "sector": "ai/ml", "stage": "growth"},
            {"name": "Anthropic", "website": "anthropic.com", "sector": "ai/ml", "stage": "growth"},
        ]
        
        duplicates = validation_service.find_duplicate_startups(new_startup, existing_startups)
        assert len(duplicates) == 1
        assert duplicates[0]["score"] == 1.0
        assert "Exact match" in duplicates[0]["reason"]
    
    def test_find_duplicate_startups_similar_name(self, validation_service):
        """Test finding startups with similar names."""
        new_startup = {
            "name": "Open AI",  # Space in name
            "website": "different.com",
            "sector": "ai/ml",
            "stage": "growth"
        }
        
        existing_startups = [
            {"name": "OpenAI", "website": "openai.com", "sector": "ai/ml", "stage": "growth"},
        ]
        
        duplicates = validation_service.find_duplicate_startups(new_startup, existing_startups)
        assert len(duplicates) == 1
        assert duplicates[0]["score"] == 0.8
        assert "similar name" in duplicates[0]["reason"]
    
    def test_find_duplicate_vcs_exact_match(self, validation_service):
        """Test finding exact duplicate VCs."""
        new_vc = {
            "firm_name": "Sequoia Capital",
            "website": "sequoiacap.com"
        }
        
        existing_vcs = [
            {"firm_name": "Sequoia Capital", "website": "sequoiacap.com"},
            {"firm_name": "Andreessen Horowitz", "website": "a16z.com"},
        ]
        
        duplicates = validation_service.find_duplicate_vcs(new_vc, existing_vcs)
        assert len(duplicates) == 1
        assert duplicates[0]["score"] == 1.0
        assert "Exact match" in duplicates[0]["reason"]
    
    def test_find_duplicate_vcs_similar_variations(self, validation_service):
        """Test finding VCs with common name variations."""
        test_cases = [
            ({"firm_name": "Sequoia", "website": "different.com"}, "Sequoia Capital"),
            ({"firm_name": "SequoiaCapital", "website": "different.com"}, "Sequoia Capital"),
            ({"firm_name": "Sequoia Ventures", "website": "different.com"}, "Sequoia Capital"),
        ]
        
        existing_vcs = [
            {"firm_name": "Sequoia Capital", "website": "sequoiacap.com"},
        ]
        
        for new_vc, existing_name in test_cases:
            duplicates = validation_service.find_duplicate_vcs(new_vc, existing_vcs)
            assert len(duplicates) > 0
            assert duplicates[0]["score"] >= 0.8
    
    def test_hash_generation_consistency(self, validation_service):
        """Test that hash generation is consistent."""
        startup1 = {"name": "Test Startup", "website": "test.com", "sector": "saas", "stage": "seed"}
        startup2 = {"name": "Test Startup", "website": "test.com", "sector": "saas", "stage": "seed"}
        
        hash1 = validation_service.generate_startup_hash(startup1)
        hash2 = validation_service.generate_startup_hash(startup2)
        
        assert hash1 == hash2
    
    def test_hash_generation_case_insensitive(self, validation_service):
        """Test that hash generation is case insensitive."""
        startup1 = {"name": "Test Startup", "website": "TEST.COM", "sector": "SaaS", "stage": "Seed"}
        startup2 = {"name": "test startup", "website": "test.com", "sector": "saas", "stage": "seed"}
        
        hash1 = validation_service.generate_startup_hash(startup1)
        hash2 = validation_service.generate_startup_hash(startup2)
        
        assert hash1 == hash2