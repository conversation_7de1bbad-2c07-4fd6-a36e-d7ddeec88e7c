"""Tests for VC thesis extraction service."""

import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime

from src.core.services.vc_thesis_extractor import VCThesisExtractor, ExtractedThesis
from src.core.models.vc import VC


@pytest.fixture
def vc_thesis_extractor():
    """Create VC thesis extractor with mock API key."""
    return VCThesisExtractor("test-api-key")


@pytest.fixture
def mock_scraped_data():
    """Mock data from website scraper."""
    return {
        "success": True,
        "data": {
            "website": "https://example-vc.com",
            "title": "Example VC - Early Stage Investor",
            "description": "We invest in ambitious founders building AI and B2B SaaS companies",
            "thesis_pages": [
                {
                    "url": "https://example-vc.com/thesis",
                    "content": """Our Investment Thesis
                    
                    We focus on early-stage investments in AI/ML and B2B SaaS companies.
                    We typically invest $500K to $2M in seed and Series A rounds.
                    
                    We look for:
                    - Technical founders with deep domain expertise
                    - Products solving real business problems
                    - Strong product-market fit signals
                    - Capital efficient business models
                    
                    We avoid:
                    - Hardware companies
                    - Consumer social apps
                    - Crypto/Web3 projects
                    
                    Our sweet spot is $1M initial checks with reserves for follow-on."""
                }
            ],
            "investment_focus": {
                "stages": ["seed", "series a"],
                "sectors": ["ai", "b2b", "saas", "enterprise"],
                "check_sizes": ["$500k", "$2m", "$1m"],
                "geography": ["bay area", "us"]
            },
            "team_members": [
                {
                    "name": "Jane Partner",
                    "role": "Managing Partner",
                    "bio": "Former founder, focuses on AI/ML investments"
                },
                {
                    "name": "John Associate",
                    "role": "Partner",
                    "bio": "Enterprise SaaS expert"
                }
            ],
            "portfolio_companies": ["AI Startup", "SaaS Co", "ML Platform"]
        }
    }


@pytest.fixture
def mock_extracted_thesis():
    """Mock extracted thesis data."""
    return ExtractedThesis(
        firm_name="Example VC",
        firm_description="Early-stage investor in AI and B2B SaaS",
        investment_stages=["Seed", "Series A"],
        sectors=["AI/ML", "B2B SaaS"],
        check_size_min=500000,
        check_size_max=2000000,
        sweet_spot_check_size=1000000,
        geography=["Bay Area", "US"],
        thesis_summary="We invest in technical founders building AI and B2B SaaS companies at seed and Series A stages.",
        key_criteria=[
            "Technical founders with deep domain expertise",
            "Products solving real business problems",
            "Strong product-market fit signals"
        ],
        avoid_criteria=["Hardware companies", "Consumer social apps", "Crypto/Web3 projects"],
        portfolio_examples=["AI Startup", "SaaS Co", "ML Platform"],
        key_partners=[
            {"name": "Jane Partner", "focus": "AI/ML investments"},
            {"name": "John Associate", "focus": "Enterprise SaaS"}
        ],
        confidence_score=0.9
    )


@pytest.mark.asyncio
async def test_extract_thesis_from_website_success(vc_thesis_extractor, mock_scraped_data, mock_extracted_thesis):
    """Test successful thesis extraction from website."""
    with patch('src.core.services.vc_thesis_extractor.analyze_vc_website') as mock_analyze:
        mock_analyze.return_value = mock_scraped_data
        
        # Mock the LLM response
        mock_llm_response = mock_extracted_thesis.json()
        vc_thesis_extractor.llm.apredict = AsyncMock(return_value=mock_llm_response)
        
        # Mock the parser
        vc_thesis_extractor.parser.parse = MagicMock(return_value=mock_extracted_thesis)
        
        result = await vc_thesis_extractor.extract_thesis_from_website("https://example-vc.com")
        
        assert result["success"] is True
        assert result["thesis"]["firm_name"] == "Example VC"
        assert "AI/ML" in result["thesis"]["sectors"]
        assert "B2B SaaS" in result["thesis"]["sectors"]
        assert result["thesis"]["check_size_min"] == 500000
        assert result["thesis"]["check_size_max"] == 2000000
        assert len(result["thesis"]["key_criteria"]) == 3
        assert len(result["thesis"]["avoid_criteria"]) == 3


@pytest.mark.asyncio
async def test_extract_thesis_from_website_scrape_failure(vc_thesis_extractor):
    """Test handling of website scraping failure."""
    with patch('src.core.services.vc_thesis_extractor.analyze_vc_website') as mock_analyze:
        mock_analyze.return_value = {
            "success": False,
            "error": "Failed to fetch website",
            "website": "https://example-vc.com"
        }
        
        result = await vc_thesis_extractor.extract_thesis_from_website("https://example-vc.com")
        
        assert result["success"] is False
        assert "Failed to scrape website" in result["error"]


@pytest.mark.asyncio
async def test_update_vc_with_thesis(vc_thesis_extractor, mock_extracted_thesis):
    """Test updating VC object with extracted thesis."""
    # Create a VC object
    vc = VC(
        name="Example VC",
        website="https://example-vc.com",
        sectors=[],
        stages=[]
    )
    
    thesis_data = {
        "thesis": mock_extracted_thesis.dict(),
        "extracted_at": datetime.utcnow().isoformat()
    }
    
    updated_vc = await vc_thesis_extractor.update_vc_with_thesis(vc, thesis_data)
    
    assert updated_vc.sectors == ["AI/ML", "B2B SaaS"]
    assert updated_vc.stages == ["Seed", "Series A"]
    assert updated_vc.check_size_min == 500000
    assert updated_vc.check_size_max == 2000000
    assert updated_vc.sweet_spot_check_size == 1000000
    
    # Check metadata
    assert updated_vc.metadata["thesis_summary"] == mock_extracted_thesis.thesis_summary
    assert updated_vc.metadata["geography"] == ["Bay Area", "US"]
    assert len(updated_vc.metadata["key_criteria"]) == 3
    assert updated_vc.metadata["extraction_confidence"] == 0.9


def test_generate_thesis_summary(vc_thesis_extractor, mock_extracted_thesis):
    """Test thesis summary generation."""
    thesis_data = {
        "thesis": mock_extracted_thesis.dict()
    }
    
    summary = vc_thesis_extractor.generate_thesis_summary(thesis_data)
    
    assert "Example VC" in summary
    assert "AI/ML, B2B SaaS" in summary
    assert "Seed, Series A" in summary
    assert "$500,000 - $2,000,000" in summary
    assert "Bay Area, US" in summary
    assert mock_extracted_thesis.thesis_summary in summary


@pytest.mark.asyncio
async def test_extract_thesis_for_multiple_vcs(vc_thesis_extractor, mock_scraped_data, mock_extracted_thesis):
    """Test batch thesis extraction."""
    vc_websites = [
        {"name": "VC 1", "website": "https://vc1.com"},
        {"name": "VC 2", "website": "https://vc2.com"}
    ]
    
    with patch.object(vc_thesis_extractor, 'extract_thesis_from_website') as mock_extract:
        mock_extract.return_value = {
            "success": True,
            "thesis": mock_extracted_thesis.dict(),
            "extracted_at": datetime.utcnow().isoformat()
        }
        
        results = await vc_thesis_extractor.extract_thesis_for_multiple_vcs(vc_websites)
        
        assert len(results) == 2
        assert all(r["success"] for r in results)
        assert results[0]["vc_name"] == "VC 1"
        assert results[1]["vc_name"] == "VC 2"
        
        # Verify sleep was called between extractions
        assert mock_extract.call_count == 2