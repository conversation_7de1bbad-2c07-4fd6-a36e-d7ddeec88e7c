"""Tests for cache service."""

import pytest
from unittest.mock import <PERSON>Mock, patch
from datetime import datetime, timedelta
import json

from src.core.services.cache_service import <PERSON>acheService, <PERSON><PERSON><PERSON><PERSON>, CachedResult


@pytest.fixture
def mock_redis():
    """Create mock Redis client."""
    redis = MagicMock()
    redis.get.return_value = None
    redis.set.return_value = True
    redis.delete.return_value = 1
    redis.scan_iter.return_value = []
    redis.info.return_value = {
        "used_memory_human": "10MB",
        "used_memory_peak_human": "15MB",
        "keyspace_hits": 100,
        "keyspace_misses": 20,
        "evicted_keys": 5,
        "connected_clients": 3
    }
    return redis


@pytest.fixture
def cache_service(mock_redis):
    """Create cache service with mock Redis."""
    return CacheService(mock_redis)


class TestCacheKey:
    """Test cache key generation."""
    
    def test_discovery_search_key(self):
        """Test discovery search cache key generation."""
        key = CacheKey.discovery_search("AI startups", {"sectors": ["AI/ML"], "limit": 20})
        assert key.startswith("discovery:search:")
        assert len(key) > 20  # Should have hash
        
        # Same params should generate same key
        key2 = CacheKey.discovery_search("AI startups", {"sectors": ["AI/ML"], "limit": 20})
        assert key == key2
        
        # Different params should generate different key
        key3 = CacheKey.discovery_search("AI startups", {"sectors": ["B2B SaaS"], "limit": 20})
        assert key != key3
    
    def test_vc_discovery_key(self):
        """Test VC discovery cache key generation."""
        key = CacheKey.vc_discovery("vc-123", {"min_score": 0.7, "page": 1})
        assert key.startswith("discovery:vc:vc-123:")
    
    def test_startup_discovery_key(self):
        """Test startup discovery cache key generation."""
        key = CacheKey.startup_discovery("startup-456", {"limit": 10})
        assert key.startswith("discovery:startup:startup-456:")
    
    def test_ai_analysis_key(self):
        """Test AI analysis cache key generation."""
        key = CacheKey.ai_analysis("startup", "123", "sector_analysis")
        assert key == "ai:startup:123:sector_analysis"
    
    def test_matching_score_key(self):
        """Test matching score cache key generation."""
        key = CacheKey.matching_score("startup-1", "vc-2")
        assert key == "match:score:startup-1:vc-2"
    
    def test_warm_intro_paths_key(self):
        """Test warm intro paths cache key generation."""
        key = CacheKey.warm_intro_paths("user-1", "user-2", 3)
        assert key == "intro:paths:user-1:user-2:3"


class TestCachedResult:
    """Test cached result wrapper."""
    
    def test_cached_result_not_expired(self):
        """Test cached result that is not expired."""
        result = CachedResult(
            data={"test": "data"},
            cached_at=datetime.utcnow(),
            expires_at=datetime.utcnow() + timedelta(hours=1)
        )
        assert not result.is_expired()
    
    def test_cached_result_expired(self):
        """Test cached result that is expired."""
        result = CachedResult(
            data={"test": "data"},
            cached_at=datetime.utcnow() - timedelta(hours=2),
            expires_at=datetime.utcnow() - timedelta(hours=1)
        )
        assert result.is_expired()
    
    def test_cached_result_no_expiry(self):
        """Test cached result with no expiry."""
        result = CachedResult(
            data={"test": "data"},
            cached_at=datetime.utcnow()
        )
        assert not result.is_expired()


@pytest.mark.asyncio
class TestCacheService:
    """Test cache service operations."""
    
    async def test_get_cache_miss(self, cache_service, mock_redis):
        """Test cache get when key doesn't exist."""
        mock_redis.get.return_value = None
        
        result = await cache_service.get("test-key")
        assert result is None
        mock_redis.get.assert_called_once_with("vc_platform:test-key")
    
    async def test_get_cache_hit(self, cache_service, mock_redis):
        """Test cache get when key exists."""
        cached_data = CachedResult(
            data={"test": "value"},
            cached_at=datetime.utcnow(),
            expires_at=datetime.utcnow() + timedelta(hours=1),
            hit_count=5
        )
        mock_redis.get.return_value = cached_data.json()
        
        result = await cache_service.get("test-key")
        assert result == {"test": "value"}
        
        # Should increment hit count
        assert mock_redis.set.called
    
    async def test_get_expired_cache(self, cache_service, mock_redis):
        """Test cache get when entry is expired."""
        cached_data = CachedResult(
            data={"test": "value"},
            cached_at=datetime.utcnow() - timedelta(hours=2),
            expires_at=datetime.utcnow() - timedelta(hours=1)
        )
        mock_redis.get.return_value = cached_data.json()
        
        result = await cache_service.get("test-key")
        assert result is None
        
        # Should delete expired entry
        mock_redis.delete.assert_called_once()
    
    async def test_set_cache(self, cache_service, mock_redis):
        """Test setting cache value."""
        data = {"test": "value"}
        success = await cache_service.set("test-key", data, ttl=3600)
        
        assert success
        mock_redis.set.assert_called_once()
        
        # Check the call arguments
        call_args = mock_redis.set.call_args
        assert call_args[0][0] == "vc_platform:test-key"
        assert call_args[1]["ex"] == 3600
    
    async def test_delete_cache(self, cache_service, mock_redis):
        """Test deleting cache value."""
        success = await cache_service.delete("test-key")
        
        assert success
        mock_redis.delete.assert_called_once_with("vc_platform:test-key")
    
    async def test_delete_pattern(self, cache_service, mock_redis):
        """Test deleting cache by pattern."""
        mock_redis.scan_iter.return_value = [
            "vc_platform:discovery:vc:123:abc",
            "vc_platform:discovery:vc:123:def"
        ]
        mock_redis.delete.return_value = 2
        
        deleted = await cache_service.delete_pattern("discovery:vc:123:*")
        
        assert deleted == 2
        mock_redis.scan_iter.assert_called_once_with(match="vc_platform:discovery:vc:123:*")
        mock_redis.delete.assert_called_once()
    
    async def test_cache_discovery_search(self, cache_service):
        """Test caching discovery search results."""
        results = [{"id": "1", "name": "Test Startup"}]
        success = await cache_service.cache_discovery_search(
            "test query",
            {"sectors": ["AI/ML"]},
            results
        )
        
        assert success
    
    async def test_cache_ai_analysis(self, cache_service):
        """Test caching AI analysis results."""
        analysis = {"score": 0.85, "insights": ["Good fit"]}
        success = await cache_service.cache_ai_analysis(
            "startup",
            "123",
            "thesis_match",
            analysis
        )
        
        assert success
    
    async def test_invalidate_entity(self, cache_service, mock_redis):
        """Test invalidating all caches for an entity."""
        mock_redis.scan_iter.side_effect = [
            ["key1", "key2"],  # discovery pattern
            ["key3"],          # ai pattern
            ["key4", "key5"],  # match pattern
            []                 # intro pattern
        ]
        mock_redis.delete.side_effect = [2, 1, 2, 0]
        
        total_deleted = await cache_service.invalidate_entity("startup", "123")
        
        assert total_deleted == 5
        assert mock_redis.scan_iter.call_count == 4
    
    async def test_get_cache_stats(self, cache_service, mock_redis):
        """Test getting cache statistics."""
        mock_redis.scan_iter.side_effect = [
            ["key1", "key2"],  # discovery
            ["key3"],          # ai_analysis
            [],                # matching
            ["key4"]           # warm_intro
        ]
        
        stats = await cache_service.get_cache_stats()
        
        assert stats["memory_used"] == "10MB"
        assert stats["memory_peak"] == "15MB"
        assert stats["total_keys"] == 4
        assert stats["keys_by_type"]["discovery"] == 2
        assert stats["keys_by_type"]["ai_analysis"] == 1
        assert stats["hit_rate"] == 83.33  # 100 / (100 + 20)
    
    async def test_cache_error_handling(self, cache_service, mock_redis):
        """Test error handling in cache operations."""
        mock_redis.get.side_effect = Exception("Redis connection error")
        
        # Should return None on error
        result = await cache_service.get("test-key")
        assert result is None
        
        mock_redis.set.side_effect = Exception("Redis connection error")
        
        # Should return False on error
        success = await cache_service.set("test-key", {"data": "value"})
        assert not success