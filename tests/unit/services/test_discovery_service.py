"""Comprehensive tests for discovery service - the core bilateral matching feature."""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import UUID, uuid4
from datetime import datetime

from src.core.services.cached_discovery_service import CachedDiscoveryService
from src.core.models.startup import Startup
from src.core.models.vc import VC


@pytest.fixture
def mock_db():
    """Mock database session."""
    return MagicMock()


@pytest.fixture
def mock_redis():
    """Mock Redis client."""
    redis = MagicMock()
    redis.get.return_value = None
    redis.set.return_value = True
    return redis


@pytest.fixture
def discovery_service(mock_db, mock_redis):
    """Create discovery service with mocks."""
    return CachedDiscoveryService(mock_db, mock_redis)


@pytest.fixture
def sample_vc():
    """Create sample VC for testing."""
    return VC(
        id=uuid4(),
        name="AI Ventures",
        thesis="We invest in AI/ML companies transforming enterprise workflows",
        sectors=["AI/ML", "B2B SaaS"],
        stages=["Seed", "Series A"],
        check_size_min=500000,
        check_size_max=2000000,
        website="https://aiventures.com"
    )


@pytest.fixture
def sample_startup():
    """Create sample startup for testing."""
    return Startup(
        id=uuid4(),
        name="SmartAI Corp",
        sector="AI/ML",
        stage="Seed",
        description="AI-powered analytics for enterprise customers",
        website="https://smartai.com",
        team_size=12,
        monthly_revenue=50000
    )


@pytest.fixture
def sample_startups():
    """Create multiple sample startups."""
    return [
        Startup(
            id=uuid4(),
            name="AI Analytics",
            sector="AI/ML",
            stage="Seed",
            description="Real-time AI analytics platform",
            team_size=10,
            monthly_revenue=30000
        ),
        Startup(
            id=uuid4(),
            name="B2B SaaS Tool",
            sector="B2B SaaS",
            stage="Series A",
            description="Workflow automation for enterprises",
            team_size=25,
            monthly_revenue=150000
        ),
        Startup(
            id=uuid4(),
            name="FinTech Startup",
            sector="Fintech",
            stage="Pre-seed",
            description="Payment processing for SMBs",
            team_size=5,
            monthly_revenue=10000
        ),
        Startup(
            id=uuid4(),
            name="Healthcare AI",
            sector="Healthcare",
            stage="Seed",
            description="AI diagnostics for medical imaging",
            team_size=15,
            monthly_revenue=0
        )
    ]


@pytest.fixture
def sample_vcs():
    """Create multiple sample VCs."""
    return [
        VC(
            id=uuid4(),
            name="Early Stage Capital",
            thesis="Seed stage B2B SaaS companies",
            sectors=["B2B SaaS", "AI/ML"],
            stages=["Pre-seed", "Seed"],
            check_size_min=100000,
            check_size_max=1000000
        ),
        VC(
            id=uuid4(),
            name="Growth Partners",
            thesis="Series A/B enterprise software",
            sectors=["B2B SaaS", "Enterprise"],
            stages=["Series A", "Series B"],
            check_size_min=2000000,
            check_size_max=10000000
        ),
        VC(
            id=uuid4(),
            name="FinTech Ventures",
            thesis="Financial technology innovation",
            sectors=["Fintech", "Crypto"],
            stages=["Seed", "Series A"],
            check_size_min=500000,
            check_size_max=3000000
        )
    ]


class TestDiscoveryCalculations:
    """Test core matching calculations."""
    
    def test_calculate_sector_match_exact(self, discovery_service):
        """Test exact sector match."""
        score = discovery_service._calculate_sector_match("AI/ML", ["AI/ML", "B2B SaaS"])
        assert score == 1.0
    
    def test_calculate_sector_match_related(self, discovery_service):
        """Test related sector match."""
        score = discovery_service._calculate_sector_match("B2B SaaS", ["AI/ML"])
        assert score == 0.7  # Related sectors
    
    def test_calculate_sector_match_none(self, discovery_service):
        """Test no sector match."""
        score = discovery_service._calculate_sector_match("Healthcare", ["Crypto", "Fintech"])
        assert score == 0.0
    
    def test_calculate_sector_match_no_preference(self, discovery_service):
        """Test when VC has no sector preference."""
        score = discovery_service._calculate_sector_match("AI/ML", [])
        assert score == 0.5  # Neutral
    
    def test_calculate_stage_match_exact(self, discovery_service):
        """Test exact stage match."""
        score = discovery_service._calculate_stage_match("Seed", ["Seed", "Series A"])
        assert score == 1.0
    
    def test_calculate_stage_match_adjacent(self, discovery_service):
        """Test adjacent stage match."""
        score = discovery_service._calculate_stage_match("Pre-seed", ["Seed"])
        assert score == 0.6
    
    def test_calculate_stage_match_none(self, discovery_service):
        """Test no stage match."""
        score = discovery_service._calculate_stage_match("Series C", ["Pre-seed", "Seed"])
        assert score == 0.0
    
    def test_calculate_funding_match_in_range(self, discovery_service):
        """Test funding amount in range."""
        score = discovery_service._calculate_funding_match(1000000, 500000, 2000000)
        assert score == 1.0
    
    def test_calculate_funding_match_too_small(self, discovery_service):
        """Test funding amount too small."""
        score = discovery_service._calculate_funding_match(250000, 500000, 2000000)
        assert score == 0.5  # 250k / 500k
    
    def test_calculate_funding_match_too_large(self, discovery_service):
        """Test funding amount too large."""
        score = discovery_service._calculate_funding_match(4000000, 500000, 2000000)
        assert score == 0.5  # 2M / 4M
    
    def test_calculate_thesis_alignment(self, discovery_service, sample_startup):
        """Test thesis alignment calculation."""
        thesis = "We invest in AI companies building enterprise solutions"
        score = discovery_service._calculate_thesis_alignment(sample_startup, thesis)
        assert score > 0.5  # Should find AI and enterprise keywords
    
    def test_calculate_vc_startup_match(self, discovery_service, sample_vc, sample_startup):
        """Test overall VC-startup match calculation."""
        score, reasons = discovery_service._calculate_vc_startup_match(sample_vc, sample_startup)
        
        assert score > 0.7  # Should be a good match
        assert len(reasons) > 0
        assert any("Sector match" in r for r in reasons)
        assert any("Stage match" in r for r in reasons)


@pytest.mark.asyncio
class TestVCDiscovery:
    """Test VC discovering startups."""
    
    async def test_discover_startups_for_vc_cached(self, discovery_service, sample_vc, sample_startups):
        """Test discovering startups for a VC with caching."""
        # Mock repository methods
        discovery_service.vc_repo.get = AsyncMock(return_value=sample_vc)
        discovery_service.startup_repo.find_for_vc_discovery = AsyncMock(return_value=sample_startups)
        discovery_service.startup_repo.count_for_vc_discovery = AsyncMock(return_value=len(sample_startups))
        
        # First call - should hit database
        result = await discovery_service.discover_startups_for_vc(
            vc_id=sample_vc.id,
            limit=10,
            min_score=0.5,
            page=1,
            use_cache=True
        )
        
        assert result["vc"]["id"] == str(sample_vc.id)
        assert len(result["matches"]) > 0
        assert result["pagination"]["page"] == 1
        
        # Verify database was called
        discovery_service.startup_repo.find_for_vc_discovery.assert_called_once()
        
        # Mock cache hit for second call
        discovery_service.cache.get_vc_discovery = AsyncMock(return_value=result)
        
        # Second call - should hit cache
        result2 = await discovery_service.discover_startups_for_vc(
            vc_id=sample_vc.id,
            limit=10,
            min_score=0.5,
            page=1,
            use_cache=True
        )
        
        # Should return same result from cache
        assert result2 == result
    
    async def test_discover_startups_filtering(self, discovery_service, sample_vc, sample_startups):
        """Test that discovery properly filters by score."""
        discovery_service.vc_repo.get = AsyncMock(return_value=sample_vc)
        discovery_service.startup_repo.find_for_vc_discovery = AsyncMock(return_value=sample_startups)
        discovery_service.startup_repo.count_for_vc_discovery = AsyncMock(return_value=len(sample_startups))
        
        # High min_score should filter out poor matches
        result = await discovery_service.discover_startups_for_vc(
            vc_id=sample_vc.id,
            limit=10,
            min_score=0.8,
            page=1,
            use_cache=False
        )
        
        # Should only include high-scoring matches
        for match in result["matches"]:
            assert match["match_score"] >= 0.8
    
    async def test_discover_startups_pagination(self, discovery_service, sample_vc, sample_startups):
        """Test pagination in discovery results."""
        # Create many startups
        many_startups = sample_startups * 5  # 20 startups
        
        discovery_service.vc_repo.get = AsyncMock(return_value=sample_vc)
        discovery_service.startup_repo.find_for_vc_discovery = AsyncMock(return_value=many_startups)
        discovery_service.startup_repo.count_for_vc_discovery = AsyncMock(return_value=len(many_startups))
        
        # Get page 1
        result_page1 = await discovery_service.discover_startups_for_vc(
            vc_id=sample_vc.id,
            limit=5,
            min_score=0.0,  # Accept all
            page=1,
            use_cache=False
        )
        
        assert len(result_page1["matches"]) == 5
        assert result_page1["pagination"]["page"] == 1
        
        # Get page 2
        result_page2 = await discovery_service.discover_startups_for_vc(
            vc_id=sample_vc.id,
            limit=5,
            min_score=0.0,
            page=2,
            use_cache=False
        )
        
        assert len(result_page2["matches"]) == 5
        assert result_page2["pagination"]["page"] == 2
        
        # Pages should have different startups
        page1_ids = {m["startup"]["id"] for m in result_page1["matches"]}
        page2_ids = {m["startup"]["id"] for m in result_page2["matches"]}
        assert page1_ids.isdisjoint(page2_ids)


@pytest.mark.asyncio
class TestStartupDiscovery:
    """Test startup discovering VCs."""
    
    async def test_discover_vcs_for_startup_cached(self, discovery_service, sample_startup, sample_vcs):
        """Test discovering VCs for a startup with caching."""
        discovery_service.startup_repo.get = AsyncMock(return_value=sample_startup)
        discovery_service.vc_repo.find_for_startup_discovery = AsyncMock(return_value=sample_vcs)
        
        result = await discovery_service.discover_vcs_for_startup(
            startup_id=sample_startup.id,
            limit=10,
            min_score=0.5,
            page=1,
            use_cache=True
        )
        
        assert result["startup"]["id"] == str(sample_startup.id)
        assert len(result["matches"]) > 0
        
        # Each match should have required fields
        for match in result["matches"]:
            assert "vc" in match
            assert "match_score" in match
            assert "match_reasons" in match
            assert match["match_score"] >= 0.5
    
    async def test_discover_vcs_for_different_stages(self, discovery_service, sample_vcs):
        """Test that stage matching works correctly."""
        # Create startups at different stages
        seed_startup = Startup(
            id=uuid4(),
            name="Seed Startup",
            sector="B2B SaaS",
            stage="Seed",
            description="Early stage SaaS",
            team_size=5,
            monthly_revenue=10000
        )
        
        series_b_startup = Startup(
            id=uuid4(),
            name="Growth Startup",
            sector="B2B SaaS",
            stage="Series B",
            description="Growth stage SaaS",
            team_size=50,
            monthly_revenue=500000
        )
        
        discovery_service.startup_repo.get = AsyncMock()
        discovery_service.vc_repo.find_for_startup_discovery = AsyncMock(return_value=sample_vcs)
        
        # Test seed startup - should match early stage VCs
        discovery_service.startup_repo.get.return_value = seed_startup
        seed_result = await discovery_service.discover_vcs_for_startup(
            startup_id=seed_startup.id,
            limit=10,
            min_score=0.0,
            page=1,
            use_cache=False
        )
        
        # Early Stage Capital should score highest for seed
        seed_matches = seed_result["matches"]
        early_stage_match = next(m for m in seed_matches if "Early Stage" in m["vc"]["name"])
        growth_match = next(m for m in seed_matches if "Growth Partners" in m["vc"]["name"])
        assert early_stage_match["match_score"] > growth_match["match_score"]


@pytest.mark.asyncio
class TestSearch:
    """Test search functionality."""
    
    async def test_search_all_entities(self, discovery_service, sample_startups, sample_vcs):
        """Test searching across all entities."""
        discovery_service.startup_repo.search = AsyncMock(return_value=sample_startups[:2])
        discovery_service.vc_repo.search = AsyncMock(return_value=sample_vcs[:2])
        
        results = await discovery_service.search_with_cache(
            query="AI",
            entity_type="all",
            limit=10,
            use_cache=False
        )
        
        assert len(results) == 4  # 2 startups + 2 VCs
        assert any(r["type"] == "startup" for r in results)
        assert any(r["type"] == "vc" for r in results)
    
    async def test_search_filtered_by_type(self, discovery_service, sample_startups):
        """Test searching filtered by entity type."""
        discovery_service.startup_repo.search = AsyncMock(return_value=sample_startups)
        discovery_service.vc_repo.search = AsyncMock(return_value=[])
        
        results = await discovery_service.search_with_cache(
            query="SaaS",
            entity_type="startup",
            limit=10,
            use_cache=False
        )
        
        assert all(r["type"] == "startup" for r in results)
        discovery_service.vc_repo.search.assert_not_called()
    
    async def test_search_with_filters(self, discovery_service, sample_startups):
        """Test search with sector and stage filters."""
        discovery_service.startup_repo.search = AsyncMock(return_value=sample_startups[:1])
        discovery_service.vc_repo.search = AsyncMock(return_value=[])
        
        results = await discovery_service.search_with_cache(
            query="analytics",
            entity_type="startup",
            sectors=["AI/ML"],
            stages=["Seed"],
            limit=10,
            use_cache=False
        )
        
        # Verify search was called with filters
        discovery_service.startup_repo.search.assert_called_with(
            query="analytics",
            sectors=["AI/ML"],
            stages=["Seed"],
            limit=10
        )


@pytest.mark.asyncio
class TestMatchingScore:
    """Test matching score calculations."""
    
    async def test_get_matching_score_detailed(self, discovery_service, sample_startup, sample_vc):
        """Test getting detailed matching score."""
        discovery_service.startup_repo.get = AsyncMock(return_value=sample_startup)
        discovery_service.vc_repo.get = AsyncMock(return_value=sample_vc)
        
        score_data = await discovery_service.get_matching_score_with_cache(
            startup_id=sample_startup.id,
            vc_id=sample_vc.id,
            use_cache=False
        )
        
        assert "score" in score_data
        assert "reasons" in score_data
        assert "breakdown" in score_data
        assert "recommendation" in score_data
        
        # Check breakdown components
        breakdown = score_data["breakdown"]
        assert "sector_match" in breakdown
        assert "stage_match" in breakdown
        assert "thesis_alignment" in breakdown
    
    async def test_matching_recommendations(self, discovery_service, sample_startup, sample_vc):
        """Test that recommendations match scores."""
        discovery_service.startup_repo.get = AsyncMock(return_value=sample_startup)
        discovery_service.vc_repo.get = AsyncMock(return_value=sample_vc)
        
        # Mock different scores to test recommendations
        test_cases = [
            (0.95, "Excellent match"),
            (0.75, "Good match"),
            (0.55, "Potential match"),
            (0.3, "Weak match")
        ]
        
        for test_score, expected_text in test_cases:
            # Mock the score calculation
            with patch.object(discovery_service, '_calculate_vc_startup_match', 
                            return_value=(test_score, ["Test reason"])):
                
                score_data = await discovery_service.get_matching_score_with_cache(
                    startup_id=sample_startup.id,
                    vc_id=sample_vc.id,
                    use_cache=False
                )
                
                assert expected_text in score_data["recommendation"]


@pytest.mark.asyncio
class TestCacheInvalidation:
    """Test cache invalidation."""
    
    async def test_invalidate_vc_cache(self, discovery_service):
        """Test invalidating VC-related caches."""
        vc_id = uuid4()
        
        # Mock cache invalidation
        discovery_service.cache.invalidate_entity = AsyncMock(return_value=5)
        
        deleted = await discovery_service.invalidate_vc_cache(vc_id)
        
        assert deleted == 5
        discovery_service.cache.invalidate_entity.assert_called_once_with("vc", str(vc_id))
    
    async def test_invalidate_startup_cache(self, discovery_service):
        """Test invalidating startup-related caches."""
        startup_id = uuid4()
        
        discovery_service.cache.invalidate_entity = AsyncMock(return_value=3)
        
        deleted = await discovery_service.invalidate_startup_cache(startup_id)
        
        assert deleted == 3
        discovery_service.cache.invalidate_entity.assert_called_once_with("startup", str(startup_id))


@pytest.mark.asyncio
class TestErrorHandling:
    """Test error handling in discovery service."""
    
    async def test_vc_not_found(self, discovery_service):
        """Test error when VC not found."""
        discovery_service.vc_repo.get = AsyncMock(return_value=None)
        
        with pytest.raises(ValueError, match="VC .* not found"):
            await discovery_service.discover_startups_for_vc(
                vc_id=uuid4(),
                limit=10,
                min_score=0.7,
                page=1
            )
    
    async def test_startup_not_found(self, discovery_service):
        """Test error when startup not found."""
        discovery_service.startup_repo.get = AsyncMock(return_value=None)
        
        with pytest.raises(ValueError, match="Startup .* not found"):
            await discovery_service.discover_vcs_for_startup(
                startup_id=uuid4(),
                limit=10,
                min_score=0.7,
                page=1
            )
    
    async def test_cache_failure_fallback(self, discovery_service, sample_vc, sample_startups):
        """Test that discovery works even if cache fails."""
        # Mock cache to raise exception
        discovery_service.cache.get_vc_discovery = AsyncMock(side_effect=Exception("Redis error"))
        discovery_service.cache.cache_vc_discovery = AsyncMock(side_effect=Exception("Redis error"))
        
        # Mock normal repository calls
        discovery_service.vc_repo.get = AsyncMock(return_value=sample_vc)
        discovery_service.startup_repo.find_for_vc_discovery = AsyncMock(return_value=sample_startups)
        discovery_service.startup_repo.count_for_vc_discovery = AsyncMock(return_value=len(sample_startups))
        
        # Should still work despite cache errors
        result = await discovery_service.discover_startups_for_vc(
            vc_id=sample_vc.id,
            limit=10,
            min_score=0.5,
            page=1,
            use_cache=True
        )
        
        assert result["vc"]["id"] == str(sample_vc.id)
        assert len(result["matches"]) > 0