"""
Tests for the email service.
"""
import pytest
import os
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

from src.core.services.email_service import EmailService, get_email_service


class TestEmailService:
    """Test suite for email service."""
    
    @pytest.fixture
    def email_service_mock_sendgrid(self):
        """Create email service with mocked SendGrid."""
        with patch.dict(os.environ, {
            'SENDGRID_API_KEY': 'test-api-key',
            'EMAIL_FROM': '<EMAIL>',
            'EMAIL_FROM_NAME': 'Test Platform',
            'ENVIRONMENT': 'test',
            'APP_URL': 'http://test.vcmatching.com'
        }):
            service = EmailService()
            service.sg_client = Mock()
            service.use_sendgrid = True
            return service
    
    @pytest.fixture
    def email_service_no_sendgrid(self):
        """Create email service without SendGrid."""
        with patch.dict(os.environ, {
            'SENDGRID_API_KEY': '',
            'ENVIRONMENT': 'development'
        }):
            return EmailService()
    
    def test_singleton_pattern(self):
        """Test that get_email_service returns singleton."""
        service1 = get_email_service()
        service2 = get_email_service()
        assert service1 is service2
    
    def test_initialization_with_sendgrid(self):
        """Test service initialization with SendGrid configured."""
        with patch.dict(os.environ, {'SENDGRID_API_KEY': 'test-key'}):
            with patch('sendgrid.SendGridAPIClient'):
                service = EmailService()
                assert service.use_sendgrid is True
                assert service.sg_client is not None
    
    def test_initialization_without_sendgrid(self):
        """Test service initialization without SendGrid."""
        with patch.dict(os.environ, {'SENDGRID_API_KEY': ''}):
            service = EmailService()
            assert service.use_sendgrid is False
            assert service.sg_client is None
    
    def test_validate_email_address(self, email_service_no_sendgrid):
        """Test email address validation."""
        # Valid emails
        assert email_service_no_sendgrid.validate_email_address("<EMAIL>") is True
        assert email_service_no_sendgrid.validate_email_address("<EMAIL>") is True
        
        # Invalid emails
        assert email_service_no_sendgrid.validate_email_address("invalid.email") is False
        assert email_service_no_sendgrid.validate_email_address("@company.com") is False
        assert email_service_no_sendgrid.validate_email_address("user@") is False
    
    def test_send_email_with_sendgrid_success(self, email_service_mock_sendgrid):
        """Test successful email sending with SendGrid."""
        # Mock SendGrid response
        mock_response = Mock()
        mock_response.status_code = 202
        mock_response.headers = {'X-Message-Id': 'test-message-id'}
        email_service_mock_sendgrid.sg_client.send.return_value = mock_response
        
        result = email_service_mock_sendgrid.send_email(
            to_email="<EMAIL>",
            template_name="match_notification",
            data={
                "recipient_name": "Test User",
                "match_name": "Test Startup",
                "match_id": "123",
                "score": 85,
                "reasons": "Good fit"
            }
        )
        
        assert result["success"] is True
        assert result["message_id"] == "test-message-id"
        assert result["status_code"] == 202
        
        # Verify SendGrid was called
        email_service_mock_sendgrid.sg_client.send.assert_called_once()
    
    def test_send_email_with_sendgrid_failure(self, email_service_mock_sendgrid):
        """Test email sending failure with SendGrid."""
        # Mock SendGrid exception
        email_service_mock_sendgrid.sg_client.send.side_effect = Exception("SendGrid error")
        
        result = email_service_mock_sendgrid.send_email(
            to_email="<EMAIL>",
            template_name="match_notification",
            data={"recipient_name": "Test User"}
        )
        
        assert result["success"] is False
        assert "SendGrid error" in result["error"]
    
    def test_send_email_without_sendgrid(self, email_service_no_sendgrid):
        """Test email logging when SendGrid is not configured."""
        with patch('logging.Logger.info') as mock_logger:
            result = email_service_no_sendgrid.send_email(
                to_email="<EMAIL>",
                template_name="weekly_digest",
                data={
                    "recipient_name": "Test User",
                    "new_matches": 5,
                    "updated_profiles": 3,
                    "top_match": "90%"
                }
            )
            
            assert result["success"] is True
            assert result["status"] == "logged"
            assert result["environment"] == "development"
            
            # Verify logging was called
            assert mock_logger.called
    
    def test_send_email_invalid_email(self, email_service_no_sendgrid):
        """Test sending to invalid email address."""
        result = email_service_no_sendgrid.send_email(
            to_email="invalid.email",
            template_name="match_notification",
            data={"recipient_name": "Test"}
        )
        
        assert result["success"] is False
        assert "Invalid email address" in result["error"]
    
    def test_send_email_invalid_template(self, email_service_no_sendgrid):
        """Test sending with invalid template name."""
        result = email_service_no_sendgrid.send_email(
            to_email="<EMAIL>",
            template_name="invalid_template",
            data={"recipient_name": "Test"}
        )
        
        assert result["success"] is False
        assert "Template invalid_template not found" in result["error"]
    
    def test_template_rendering(self, email_service_no_sendgrid):
        """Test that templates are rendered correctly."""
        # This would normally send, but we're testing the template rendering
        with patch.object(email_service_no_sendgrid, '_log_email') as mock_log:
            mock_log.return_value = {"success": True}
            
            email_service_no_sendgrid.send_email(
                to_email="<EMAIL>",
                template_name="analysis_complete",
                data={
                    "recipient_name": "Test User",
                    "entity_type": "startup",
                    "entity_id": "123",
                    "insights": ["Insight 1", "Insight 2", "Insight 3"]
                }
            )
            
            # Check that template was rendered with correct data
            call_args = mock_log.call_args[1]
            assert "Test User" in call_args["html_content"]
            assert "startup" in call_args["html_content"]
            assert "Insight 1" in call_args["html_content"]
    
    def test_send_bulk_emails(self, email_service_no_sendgrid):
        """Test bulk email sending."""
        with patch.object(email_service_no_sendgrid, 'send_email') as mock_send:
            mock_send.return_value = {"success": True}
            
            recipients = [
                {
                    "email": "<EMAIL>",
                    "data": {"recipient_name": "User 1", "new_matches": 3}
                },
                {
                    "email": "<EMAIL>",
                    "data": {"recipient_name": "User 2", "new_matches": 5}
                },
                {
                    "email": "invalid.email",  # This should fail
                    "data": {"recipient_name": "User 3"}
                }
            ]
            
            result = email_service_no_sendgrid.send_bulk_emails(
                template_name="weekly_digest",
                recipients=recipients,
                global_data={"updated_profiles": 10, "top_match": "85%"}
            )
            
            assert result["total"] == 3
            assert mock_send.call_count == 3
            
            # Verify global data was merged
            first_call = mock_send.call_args_list[0]
            assert first_call[1]["data"]["updated_profiles"] == 10
            assert first_call[1]["data"]["new_matches"] == 3  # Recipient-specific
    
    def test_cc_emails(self, email_service_mock_sendgrid):
        """Test sending with CC recipients."""
        mock_response = Mock()
        mock_response.status_code = 202
        mock_response.headers = {}
        email_service_mock_sendgrid.sg_client.send.return_value = mock_response
        
        result = email_service_mock_sendgrid.send_email(
            to_email="<EMAIL>",
            template_name="introduction_request",
            data={
                "recipient_name": "Test User",
                "requester_name": "Requester",
                "message": "Please connect",
                "requester_description": "A great startup",
                "request_id": "123"
            },
            cc_emails=["<EMAIL>", "<EMAIL>"]
        )
        
        assert result["success"] is True
        
        # Verify Mail object was created with CC
        call_args = email_service_mock_sendgrid.sg_client.send.call_args
        # In real implementation, would verify CC was added to Mail object
    
    def test_all_templates_exist(self, email_service_no_sendgrid):
        """Test that all expected templates exist."""
        expected_templates = [
            "match_notification",
            "weekly_digest",
            "analysis_complete",
            "introduction_request",
            "introduction_accepted"
        ]
        
        for template in expected_templates:
            assert template in email_service_no_sendgrid.templates
            assert "subject" in email_service_no_sendgrid.templates[template]
            assert "html" in email_service_no_sendgrid.templates[template]
    
    def test_default_data_added(self, email_service_no_sendgrid):
        """Test that default data (app_url, current_year) is added."""
        with patch.object(email_service_no_sendgrid, '_log_email') as mock_log:
            mock_log.return_value = {"success": True}
            
            email_service_no_sendgrid.send_email(
                to_email="<EMAIL>",
                template_name="match_notification",
                data={"recipient_name": "Test"}
            )
            
            # The data should have been enhanced with defaults
            call_args = mock_log.call_args[1]
            assert "http://localhost:3000" in call_args["html_content"]  # app_url
            assert str(datetime.utcnow().year) in call_args["html_content"]  # current_year