"""Tests for WarmIntroService."""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime
from uuid import uuid4

from src.core.services.warm_intro_service import WarmIntroService
from src.core.models.connection import (
    Connection,
    ConnectionId,
    ConnectionMetrics,
    ConnectionStrength,
    RelationshipType,
    IntroductionRequest,
    IntroductionStatus,
    ConnectionPath
)
from src.core.models.user import User
from src.core.models.startup import Startup
from src.core.models.vc import VC


@pytest.fixture
def mock_repositories():
    """Create mock repositories for testing."""
    return {
        'connection_repo': Mock(),
        'introduction_repo': <PERSON>ck(),
        'user_repo': <PERSON>ck(),
        'startup_repo': <PERSON>ck(),
        'vc_repo': <PERSON>ck()
    }


@pytest.fixture
def warm_intro_service(mock_repositories):
    """Create WarmIntroService with mocked dependencies."""
    return WarmIntroService(
        connection_repo=mock_repositories['connection_repo'],
        introduction_repo=mock_repositories['introduction_repo'],
        user_repo=mock_repositories['user_repo'],
        startup_repo=mock_repositories['startup_repo'],
        vc_repo=mock_repositories['vc_repo']
    )


@pytest.fixture
def sample_users():
    """Create sample users for testing."""
    return {
        'user_a': User(
            id=uuid4(),
            email="<EMAIL>",
            username="user_a",
            full_name="User A",
            is_active=True
        ),
        'user_b': User(
            id=uuid4(),
            email="<EMAIL>",
            username="user_b",
            full_name="User B",
            is_active=True
        ),
        'user_c': User(
            id=uuid4(),
            email="<EMAIL>",
            username="user_c",
            full_name="User C",
            is_active=True
        )
    }


@pytest.fixture
def sample_connection(sample_users):
    """Create a sample connection."""
    return Connection(
        id=ConnectionId(),
        user_a_id=sample_users['user_a'].id,
        user_b_id=sample_users['user_b'].id,
        relationship_type=RelationshipType.COLLEAGUE,
        strength=ConnectionStrength.STRONG,
        metrics=ConnectionMetrics(trust_score=0.8)
    )


class TestCreateConnection:
    """Test connection creation functionality."""
    
    @pytest.mark.asyncio
    async def test_create_connection_success(self, warm_intro_service, mock_repositories, sample_users):
        """Test successful connection creation."""
        # Setup mocks
        mock_repositories['user_repo'].get = AsyncMock(side_effect=lambda user_id: 
            sample_users['user_a'] if user_id == sample_users['user_a'].id else
            sample_users['user_b'] if user_id == sample_users['user_b'].id else
            sample_users['user_c'] if user_id == sample_users['user_c'].id else None
        )
        mock_repositories['connection_repo'].get_connection = AsyncMock(return_value=None)
        mock_repositories['connection_repo'].create_connection = AsyncMock(
            return_value=Mock(spec=Connection)
        )
        
        # Execute
        result = await warm_intro_service.create_connection(
            user_a_id=sample_users['user_a'].id,
            user_b_id=sample_users['user_b'].id,
            relationship_type=RelationshipType.COLLEAGUE,
            notes="Met at conference",
            trust_score=0.7
        )
        
        # Verify
        assert mock_repositories['user_repo'].get.call_count == 2
        mock_repositories['connection_repo'].get_connection.assert_called_once()
        mock_repositories['connection_repo'].create_connection.assert_called_once()
        
        # Check connection attributes
        created_conn_arg = mock_repositories['connection_repo'].create_connection.call_args[0][0]
        assert created_conn_arg.relationship_type == RelationshipType.COLLEAGUE
        assert created_conn_arg.notes == "Met at conference"
        assert created_conn_arg.metrics.trust_score == 0.7
    
    @pytest.mark.asyncio
    async def test_create_connection_user_not_found(self, warm_intro_service, mock_repositories, sample_users):
        """Test connection creation when user not found."""
        # Setup mocks
        mock_repositories['user_repo'].get = AsyncMock(side_effect=[
            sample_users['user_a'],
            None  # User B not found
        ])
        
        # Execute and verify
        with pytest.raises(ValueError, match="One or both users not found"):
            await warm_intro_service.create_connection(
                user_a_id=sample_users['user_a'].id,
                user_b_id=uuid4(),
                relationship_type=RelationshipType.COLLEAGUE
            )
    
    @pytest.mark.asyncio
    async def test_create_connection_already_exists(self, warm_intro_service, mock_repositories, sample_users, sample_connection):
        """Test connection creation when connection already exists."""
        # Setup mocks
        mock_repositories['user_repo'].get = AsyncMock(side_effect=[
            sample_users['user_a'],
            sample_users['user_b']
        ])
        mock_repositories['connection_repo'].get_connection = AsyncMock(
            return_value=sample_connection
        )
        
        # Execute and verify
        with pytest.raises(ValueError, match="Connection already exists"):
            await warm_intro_service.create_connection(
                user_a_id=sample_users['user_a'].id,
                user_b_id=sample_users['user_b'].id,
                relationship_type=RelationshipType.COLLEAGUE
            )


class TestFindIntroPathsForMatch:
    """Test finding introduction paths for matches."""
    
    @pytest.mark.asyncio
    async def test_find_paths_to_startup(self, warm_intro_service, mock_repositories, sample_users):
        """Test finding paths to startup team members."""
        startup_id = uuid4()
        requester_id = sample_users['user_a'].id
        
        # Create mock startup
        startup = Startup(
            id=startup_id,
            name="Test Startup",
            sector="AI",
            stage="Seed",
            description="AI startup"
        )
        
        # Setup mocks
        mock_repositories['startup_repo'].get = AsyncMock(return_value=startup)
        mock_repositories['user_repo'].get_users_by_startup = AsyncMock(
            return_value=[sample_users['user_b'], sample_users['user_c']]
        )
        
        # Mock connection paths
        mock_path = ConnectionPath(
            source_user_id=requester_id,
            target_user_id=sample_users['user_b'].id,
            path=[requester_id, sample_users['user_b'].id],
            connections=[Mock(strength=ConnectionStrength.STRONG)],
            total_strength_score=0.8
        )
        
        mock_repositories['connection_repo'].find_shortest_paths = AsyncMock(
            return_value=[mock_path]
        )
        
        # Execute
        result = await warm_intro_service.find_intro_paths_for_match(
            requester_id=requester_id,
            startup_id=startup_id,
            max_depth=3
        )
        
        # Verify
        assert len(result) == 2  # Paths to both users
        assert result[0]['target_user']['id'] == str(sample_users['user_b'].id)
        assert result[0]['strength_score'] == 0.8
        assert result[0]['hop_count'] == 1
        assert result[0]['can_request_intro'] is True
    
    @pytest.mark.asyncio
    async def test_find_paths_to_vc(self, warm_intro_service, mock_repositories, sample_users):
        """Test finding paths to VC partners."""
        vc_id = uuid4()
        requester_id = sample_users['user_a'].id
        
        # Create mock VC
        vc = VC(
            id=vc_id,
            firm_name="Test Ventures",
            website="https://test.vc",
            thesis="B2B SaaS",
            sectors=["SaaS"],
            stages=["Series A"],
            check_size_min=1000000,
            check_size_max=5000000
        )
        
        # Setup mocks
        mock_repositories['vc_repo'].get = AsyncMock(return_value=vc)
        mock_repositories['user_repo'].get_users_by_vc = AsyncMock(
            return_value=[sample_users['user_c']]
        )
        
        # No path found
        mock_repositories['connection_repo'].find_shortest_paths = AsyncMock(
            return_value=[]
        )
        
        # Execute
        result = await warm_intro_service.find_intro_paths_for_match(
            requester_id=requester_id,
            vc_id=vc_id,
            max_depth=3
        )
        
        # Verify
        assert len(result) == 0  # No paths found
    
    @pytest.mark.asyncio
    async def test_find_paths_invalid_params(self, warm_intro_service):
        """Test finding paths with invalid parameters."""
        with pytest.raises(ValueError, match="Either startup_id or vc_id must be provided"):
            await warm_intro_service.find_intro_paths_for_match(
                requester_id=uuid4()
            )


class TestRequestIntroduction:
    """Test introduction request functionality."""
    
    @pytest.mark.asyncio
    async def test_request_introduction_success(self, warm_intro_service, mock_repositories, sample_users):
        """Test successful introduction request."""
        requester = sample_users['user_a']
        target = sample_users['user_b']
        connector = sample_users['user_c']
        
        # Setup mocks
        mock_repositories['user_repo'].get = AsyncMock(side_effect=[
            requester, target, connector
        ])
        
        # Mock connections exist
        warm_intro_service._verify_connector_can_introduce = AsyncMock(return_value=True)
        
        # Mock request creation
        mock_request = Mock(spec=IntroductionRequest)
        mock_repositories['introduction_repo'].create_request = AsyncMock(
            return_value=mock_request
        )
        
        # Execute
        result = await warm_intro_service.request_introduction(
            requester_id=requester.id,
            target_id=target.id,
            connector_id=connector.id,
            message="Would love an introduction to discuss our AI platform"
        )
        
        # Verify
        assert result == mock_request
        mock_repositories['introduction_repo'].create_request.assert_called_once()
        
        # Check request attributes
        created_request = mock_repositories['introduction_repo'].create_request.call_args[0][0]
        assert created_request.requester_id == requester.id
        assert created_request.target_id == target.id
        assert created_request.connector_id == connector.id
        assert created_request.message == "Would love an introduction to discuss our AI platform"
    
    @pytest.mark.asyncio
    async def test_request_introduction_invalid_connector(self, warm_intro_service, mock_repositories, sample_users):
        """Test introduction request with invalid connector."""
        requester = sample_users['user_a']
        target = sample_users['user_b']
        connector = sample_users['user_c']
        
        # Setup mocks
        mock_repositories['user_repo'].get = AsyncMock(side_effect=[
            requester, target, connector
        ])
        
        # Connector cannot introduce
        warm_intro_service._verify_connector_can_introduce = AsyncMock(return_value=False)
        
        # Execute and verify
        with pytest.raises(ValueError, match="Connector cannot introduce"):
            await warm_intro_service.request_introduction(
                requester_id=requester.id,
                target_id=target.id,
                connector_id=connector.id,
                message="Please introduce me"
            )


class TestGetPendingIntroRequests:
    """Test getting pending introduction requests."""
    
    @pytest.mark.asyncio
    async def test_get_pending_requests(self, warm_intro_service, mock_repositories, sample_users):
        """Test getting pending requests for a connector."""
        connector = sample_users['user_c']
        requester = sample_users['user_a']
        target = sample_users['user_b']
        
        # Create mock request
        mock_request = IntroductionRequest(
            requester_id=requester.id,
            target_id=target.id,
            connector_id=connector.id,
            message="Please introduce me"
        )
        
        # Setup mocks
        mock_repositories['introduction_repo'].get_pending_requests = AsyncMock(
            return_value=[mock_request]
        )
        mock_repositories['user_repo'].get = AsyncMock(side_effect=[
            requester, target
        ])
        
        # Mock connections
        mock_connection = Mock(strength=ConnectionStrength.STRONG)
        mock_repositories['connection_repo'].get_connection = AsyncMock(
            return_value=mock_connection
        )
        
        # Execute
        result = await warm_intro_service.get_pending_intro_requests(connector.id)
        
        # Verify
        assert len(result) == 1
        assert result[0]['requester']['id'] == str(requester.id)
        assert result[0]['target']['id'] == str(target.id)
        assert result[0]['connections']['requester_strength'] == ConnectionStrength.STRONG.value


class TestRespondToIntroRequest:
    """Test responding to introduction requests."""
    
    @pytest.mark.asyncio
    async def test_accept_intro_request(self, warm_intro_service, mock_repositories):
        """Test accepting an introduction request."""
        request_id = uuid4()
        connector_id = uuid4()
        
        # Create mock request
        mock_request = IntroductionRequest(
            id=request_id,
            requester_id=uuid4(),
            target_id=uuid4(),
            connector_id=connector_id,
            status=IntroductionStatus.PENDING,
            message="Please introduce me"
        )
        
        # Setup mocks
        mock_repositories['introduction_repo'].get_request = AsyncMock(
            return_value=mock_request
        )
        mock_repositories['introduction_repo'].update_request = AsyncMock(
            return_value=mock_request
        )
        
        # Execute
        result = await warm_intro_service.respond_to_intro_request(
            request_id=request_id,
            connector_id=connector_id,
            accept=True,
            notes="Happy to connect you both"
        )
        
        # Verify
        assert result.status == IntroductionStatus.ACCEPTED
        assert result.connector_notes == "Happy to connect you both"
        mock_repositories['introduction_repo'].update_request.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_decline_intro_request(self, warm_intro_service, mock_repositories):
        """Test declining an introduction request."""
        request_id = uuid4()
        connector_id = uuid4()
        
        # Create mock request
        mock_request = IntroductionRequest(
            id=request_id,
            requester_id=uuid4(),
            target_id=uuid4(),
            connector_id=connector_id,
            status=IntroductionStatus.PENDING,
            message="Please introduce me"
        )
        
        # Setup mocks
        mock_repositories['introduction_repo'].get_request = AsyncMock(
            return_value=mock_request
        )
        mock_repositories['introduction_repo'].update_request = AsyncMock(
            return_value=mock_request
        )
        
        # Execute
        result = await warm_intro_service.respond_to_intro_request(
            request_id=request_id,
            connector_id=connector_id,
            accept=False,
            notes="Not the right time"
        )
        
        # Verify
        assert result.status == IntroductionStatus.DECLINED
        assert result.connector_notes == "Not the right time"


class TestConnectionAnalytics:
    """Test connection analytics functionality."""
    
    @pytest.mark.asyncio
    async def test_get_connection_analytics(self, warm_intro_service, mock_repositories, sample_users):
        """Test getting connection analytics for a user."""
        user_id = sample_users['user_a'].id
        
        # Create mock connections
        mock_connections = [
            Mock(strength=ConnectionStrength.STRONG, relationship_type=RelationshipType.COLLEAGUE),
            Mock(strength=ConnectionStrength.MEDIUM, relationship_type=RelationshipType.COLLEAGUE),
            Mock(strength=ConnectionStrength.WEAK, relationship_type=RelationshipType.INDUSTRY_PEER)
        ]
        
        # Setup mocks
        mock_repositories['connection_repo'].get_user_connections = AsyncMock(
            return_value=mock_connections
        )
        
        # Mock introduction requests
        mock_requests = [
            Mock(requester_id=user_id, connector_id=uuid4(), status=IntroductionStatus.PENDING),
            Mock(target_id=user_id, connector_id=uuid4(), status=IntroductionStatus.ACCEPTED),
            Mock(connector_id=user_id, requester_id=uuid4(), status=IntroductionStatus.COMPLETED)
        ]
        mock_repositories['introduction_repo'].get_user_requests = AsyncMock(
            return_value=mock_requests
        )
        
        # Mock network reach calculation
        warm_intro_service._calculate_network_reach = AsyncMock(
            return_value={"depth_1": 3, "depth_2": 15}
        )
        
        # Mock key connectors
        warm_intro_service._identify_key_connectors = AsyncMock(
            return_value=[{"user": {"id": str(uuid4()), "name": "Key Connector"}, "connector_score": 25.0}]
        )
        
        # Execute
        result = await warm_intro_service.get_connection_analytics(user_id)
        
        # Verify
        assert result['total_connections'] == 3
        assert result['strength_distribution']['strong'] == 1
        assert result['strength_distribution']['medium'] == 1
        assert result['strength_distribution']['weak'] == 1
        assert result['relationship_distribution']['colleague'] == 2
        assert result['relationship_distribution']['industry_peer'] == 1
        assert result['introduction_stats']['requests_sent'] == 1
        assert result['introduction_stats']['requests_received'] == 1
        assert result['introduction_stats']['introductions_made'] == 1
        assert result['introduction_stats']['successful_intros'] == 1