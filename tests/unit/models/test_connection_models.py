"""Tests for connection domain models."""

import pytest
from datetime import datetime, timed<PERSON><PERSON>
from uuid import uuid4

from src.core.models.connection import (
    Connection,
    ConnectionId,
    ConnectionMetrics,
    ConnectionStrength,
    RelationshipType,
    IntroductionRequest,
    IntroductionStatus,
    ConnectionPath
)


class TestConnectionMetrics:
    """Test ConnectionMetrics value object."""
    
    def test_calculate_strength_strong(self):
        """Test strong connection strength calculation."""
        metrics = ConnectionMetrics(
            interaction_frequency=5,
            last_interaction_days=1,
            mutual_connections_count=10,
            trust_score=0.9
        )
        assert metrics.calculate_strength() == ConnectionStrength.STRONG
    
    def test_calculate_strength_medium(self):
        """Test medium connection strength calculation."""
        metrics = ConnectionMetrics(
            interaction_frequency=2,
            last_interaction_days=7,
            mutual_connections_count=5,
            trust_score=0.6
        )
        assert metrics.calculate_strength() == ConnectionStrength.MEDIUM
    
    def test_calculate_strength_weak(self):
        """Test weak connection strength calculation."""
        metrics = ConnectionMetrics(
            interaction_frequency=0,
            last_interaction_days=30,
            mutual_connections_count=1,
            trust_score=0.2
        )
        assert metrics.calculate_strength() == ConnectionStrength.WEAK
    
    def test_trust_score_overrides_frequency(self):
        """Test that high trust score can override low frequency."""
        metrics = ConnectionMetrics(
            interaction_frequency=0,
            trust_score=0.85
        )
        assert metrics.calculate_strength() == ConnectionStrength.STRONG


class TestConnection:
    """Test Connection entity."""
    
    def test_connection_creation(self):
        """Test creating a valid connection."""
        user_a = uuid4()
        user_b = uuid4()
        metrics = ConnectionMetrics(trust_score=0.7)
        
        connection = Connection(
            id=ConnectionId(),
            user_a_id=user_a,
            user_b_id=user_b,
            relationship_type=RelationshipType.COLLEAGUE,
            strength=ConnectionStrength.MEDIUM,
            metrics=metrics
        )
        
        assert connection.user_a_id == min(user_a, user_b)
        assert connection.user_b_id == max(user_a, user_b)
        assert connection.is_active is True
    
    def test_connection_user_ordering(self):
        """Test that user IDs are always ordered consistently."""
        user_a = uuid4()
        user_b = uuid4()
        
        # Create with reverse order
        connection = Connection(
            id=ConnectionId(),
            user_a_id=max(user_a, user_b),
            user_b_id=min(user_a, user_b),
            relationship_type=RelationshipType.COLLEAGUE,
            strength=ConnectionStrength.MEDIUM,
            metrics=ConnectionMetrics()
        )
        
        # Should be reordered
        assert connection.user_a_id == min(user_a, user_b)
        assert connection.user_b_id == max(user_a, user_b)
    
    def test_cannot_connect_to_self(self):
        """Test that users cannot connect to themselves."""
        user_id = uuid4()
        
        with pytest.raises(ValueError, match="Cannot create connection to self"):
            Connection(
                id=ConnectionId(),
                user_a_id=user_id,
                user_b_id=user_id,
                relationship_type=RelationshipType.COLLEAGUE,
                strength=ConnectionStrength.MEDIUM,
                metrics=ConnectionMetrics()
            )
    
    def test_invalid_trust_score(self):
        """Test that trust score must be between 0 and 1."""
        with pytest.raises(ValueError, match="Trust score must be between"):
            Connection(
                id=ConnectionId(),
                user_a_id=uuid4(),
                user_b_id=uuid4(),
                relationship_type=RelationshipType.COLLEAGUE,
                strength=ConnectionStrength.MEDIUM,
                metrics=ConnectionMetrics(trust_score=1.5)
            )
    
    def test_involves_user(self):
        """Test checking if connection involves a user."""
        user_a = uuid4()
        user_b = uuid4()
        user_c = uuid4()
        
        connection = Connection(
            id=ConnectionId(),
            user_a_id=user_a,
            user_b_id=user_b,
            relationship_type=RelationshipType.COLLEAGUE,
            strength=ConnectionStrength.MEDIUM,
            metrics=ConnectionMetrics()
        )
        
        assert connection.involves_user(user_a) is True
        assert connection.involves_user(user_b) is True
        assert connection.involves_user(user_c) is False
    
    def test_get_other_user(self):
        """Test getting the other user in a connection."""
        user_a = uuid4()
        user_b = uuid4()
        
        connection = Connection(
            id=ConnectionId(),
            user_a_id=user_a,
            user_b_id=user_b,
            relationship_type=RelationshipType.COLLEAGUE,
            strength=ConnectionStrength.MEDIUM,
            metrics=ConnectionMetrics()
        )
        
        assert connection.get_other_user(user_a) == user_b
        assert connection.get_other_user(user_b) == user_a
        
        with pytest.raises(ValueError, match="User not part of this connection"):
            connection.get_other_user(uuid4())
    
    def test_update_metrics(self):
        """Test updating connection metrics."""
        connection = Connection(
            id=ConnectionId(),
            user_a_id=uuid4(),
            user_b_id=uuid4(),
            relationship_type=RelationshipType.COLLEAGUE,
            strength=ConnectionStrength.WEAK,
            metrics=ConnectionMetrics(trust_score=0.3)
        )
        
        new_metrics = ConnectionMetrics(
            interaction_frequency=5,
            trust_score=0.9
        )
        
        old_updated_at = connection.updated_at
        connection.update_metrics(new_metrics)
        
        assert connection.strength == ConnectionStrength.STRONG
        assert connection.metrics == new_metrics
        assert connection.updated_at > old_updated_at
    
    def test_tag_management(self):
        """Test adding and removing tags."""
        connection = Connection(
            id=ConnectionId(),
            user_a_id=uuid4(),
            user_b_id=uuid4(),
            relationship_type=RelationshipType.COLLEAGUE,
            strength=ConnectionStrength.MEDIUM,
            metrics=ConnectionMetrics()
        )
        
        # Add tags
        connection.add_tag("important")
        connection.add_tag("tech")
        assert "important" in connection.tags
        assert "tech" in connection.tags
        
        # Don't duplicate tags
        connection.add_tag("important")
        assert connection.tags.count("important") == 1
        
        # Remove tag
        connection.remove_tag("tech")
        assert "tech" not in connection.tags
        assert "important" in connection.tags


class TestIntroductionRequest:
    """Test IntroductionRequest entity."""
    
    def test_request_creation_with_default_expiry(self):
        """Test creating request with default 30-day expiry."""
        request = IntroductionRequest(
            requester_id=uuid4(),
            target_id=uuid4(),
            connector_id=uuid4(),
            message="Please introduce me"
        )
        
        assert request.status == IntroductionStatus.PENDING
        assert request.expires_at is not None
        expected_expiry = request.created_at + timedelta(days=30)
        assert abs((request.expires_at - expected_expiry).total_seconds()) < 1
    
    def test_request_creation_with_custom_expiry(self):
        """Test creating request with custom expiry."""
        custom_expiry = datetime.utcnow() + timedelta(days=7)
        
        request = IntroductionRequest(
            requester_id=uuid4(),
            target_id=uuid4(),
            connector_id=uuid4(),
            message="Urgent introduction",
            expires_at=custom_expiry
        )
        
        assert request.expires_at == custom_expiry
    
    def test_accept_request(self):
        """Test accepting an introduction request."""
        request = IntroductionRequest(
            requester_id=uuid4(),
            target_id=uuid4(),
            connector_id=uuid4(),
            message="Please introduce me"
        )
        
        old_updated_at = request.updated_at
        request.accept("Happy to introduce you both")
        
        assert request.status == IntroductionStatus.ACCEPTED
        assert request.connector_notes == "Happy to introduce you both"
        assert request.updated_at > old_updated_at
    
    def test_decline_request(self):
        """Test declining an introduction request."""
        request = IntroductionRequest(
            requester_id=uuid4(),
            target_id=uuid4(),
            connector_id=uuid4(),
            message="Please introduce me"
        )
        
        request.decline("Not appropriate at this time")
        
        assert request.status == IntroductionStatus.DECLINED
        assert request.connector_notes == "Not appropriate at this time"
    
    def test_complete_request(self):
        """Test completing an introduction request."""
        request = IntroductionRequest(
            requester_id=uuid4(),
            target_id=uuid4(),
            connector_id=uuid4(),
            message="Please introduce me"
        )
        
        # First accept
        request.accept()
        
        # Then complete
        request.complete()
        
        assert request.status == IntroductionStatus.COMPLETED
        assert request.completed_at is not None
    
    def test_is_expired(self):
        """Test checking if request is expired."""
        # Not expired
        request = IntroductionRequest(
            requester_id=uuid4(),
            target_id=uuid4(),
            connector_id=uuid4(),
            message="Please introduce me"
        )
        assert request.is_expired() is False
        
        # Expired
        past_date = datetime.utcnow() - timedelta(days=1)
        expired_request = IntroductionRequest(
            requester_id=uuid4(),
            target_id=uuid4(),
            connector_id=uuid4(),
            message="Old request",
            expires_at=past_date
        )
        assert expired_request.is_expired() is True


class TestConnectionPath:
    """Test ConnectionPath value object."""
    
    def test_path_creation(self):
        """Test creating a connection path."""
        user_a = uuid4()
        user_b = uuid4()
        user_c = uuid4()
        
        # Create connections
        conn1 = Connection(
            id=ConnectionId(),
            user_a_id=user_a,
            user_b_id=user_b,
            relationship_type=RelationshipType.COLLEAGUE,
            strength=ConnectionStrength.STRONG,
            metrics=ConnectionMetrics(trust_score=0.9)
        )
        
        conn2 = Connection(
            id=ConnectionId(),
            user_a_id=user_b,
            user_b_id=user_c,
            relationship_type=RelationshipType.COLLEAGUE,
            strength=ConnectionStrength.MEDIUM,
            metrics=ConnectionMetrics(trust_score=0.6)
        )
        
        path = ConnectionPath(
            source_user_id=user_a,
            target_user_id=user_c,
            path=[user_a, user_b, user_c],
            connections=[conn1, conn2]
        )
        
        assert path.length == 2  # Two hops
        assert path.intermediary_count == 1  # One intermediary (user_b)
        assert path.get_next_connector() == user_b
    
    def test_path_strength_calculation(self):
        """Test harmonic mean calculation for path strength."""
        # Strong -> Medium path
        conn1 = Connection(
            id=ConnectionId(),
            user_a_id=uuid4(),
            user_b_id=uuid4(),
            relationship_type=RelationshipType.COLLEAGUE,
            strength=ConnectionStrength.STRONG,  # 1.0
            metrics=ConnectionMetrics()
        )
        
        conn2 = Connection(
            id=ConnectionId(),
            user_a_id=uuid4(),
            user_b_id=uuid4(),
            relationship_type=RelationshipType.COLLEAGUE,
            strength=ConnectionStrength.MEDIUM,  # 0.6
            metrics=ConnectionMetrics()
        )
        
        path = ConnectionPath(
            source_user_id=uuid4(),
            target_user_id=uuid4(),
            path=[uuid4(), uuid4(), uuid4()],
            connections=[conn1, conn2]
        )
        
        # Harmonic mean of [1.0, 0.6] = 2 / (1/1.0 + 1/0.6) = 0.75
        assert abs(path.total_strength_score - 0.75) < 0.01
    
    def test_direct_connection_path(self):
        """Test path with direct connection (no intermediaries)."""
        user_a = uuid4()
        user_b = uuid4()
        
        conn = Connection(
            id=ConnectionId(),
            user_a_id=user_a,
            user_b_id=user_b,
            relationship_type=RelationshipType.COLLEAGUE,
            strength=ConnectionStrength.STRONG,
            metrics=ConnectionMetrics()
        )
        
        path = ConnectionPath(
            source_user_id=user_a,
            target_user_id=user_b,
            path=[user_a, user_b],
            connections=[conn]
        )
        
        assert path.length == 1
        assert path.intermediary_count == 0
        assert path.get_next_connector() is None
    
    def test_empty_path(self):
        """Test path with no connections."""
        path = ConnectionPath(
            source_user_id=uuid4(),
            target_user_id=uuid4(),
            path=[],
            connections=[]
        )
        
        assert path.length == -1  # No valid path
        assert path.intermediary_count == 0
        assert path.total_strength_score == 0.0