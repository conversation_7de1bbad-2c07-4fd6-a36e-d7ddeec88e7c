"""Comprehensive TDD tests for MatchService with 100% coverage."""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timezone
from uuid import UUID, uuid4
from src.core.services.match_service import MatchService, MatchType
from src.core.models.match import Match
from src.core.models.startup import Startup
from src.core.models.vc import VC
from src.core.services.matching_engine import MatchingEngine


class TestMatchService:
    """Test suite for MatchService following TDD principles."""
    
    @pytest.fixture
    def mock_match_repo(self):
        """Create a mock match repository."""
        repo = Mock()
        repo.create = AsyncMock()
        repo.get = AsyncMock()
        repo.update = AsyncMock()
        repo.delete = AsyncMock()
        repo.list = AsyncMock()
        repo.get_by_startup_id = AsyncMock()
        repo.get_by_vc_id = AsyncMock()
        repo.exists = AsyncMock()
        return repo
    
    @pytest.fixture
    def mock_startup_repo(self):
        """Create a mock startup repository."""
        repo = Mock()
        repo.get = AsyncMock()
        repo.list = AsyncMock()
        return repo
    
    @pytest.fixture
    def mock_vc_repo(self):
        """Create a mock VC repository."""
        repo = Mock()
        repo.get = AsyncMock()
        repo.list = AsyncMock()
        return repo
    
    @pytest.fixture
    def mock_matching_engine(self):
        """Create a mock matching engine."""
        engine = Mock(spec=MatchingEngine)
        engine.calculate_match = Mock()  # Not async
        return engine
    
    @pytest.fixture
    def mock_ai_analyzer(self):
        """Create a mock AI analyzer."""
        analyzer = Mock()
        analyzer.get_match_insights = AsyncMock()
        return analyzer
    
    @pytest.fixture
    def match_service(self, mock_match_repo, mock_startup_repo, mock_vc_repo, 
                     mock_matching_engine, mock_ai_analyzer):
        """Create a MatchService instance with all mocked dependencies."""
        return MatchService(
            match_repository=mock_match_repo,
            startup_repository=mock_startup_repo,
            vc_repository=mock_vc_repo,
            matching_engine=mock_matching_engine,
            ai_analyzer=mock_ai_analyzer
        )
    
    @pytest.fixture
    def sample_startup(self):
        """Create a sample startup."""
        return Startup(
            name="TechCo",
            sector="B2B SaaS",
            stage="Seed",
            description="AI-powered sales platform"
        )
    
    @pytest.fixture
    def sample_vc(self):
        """Create a sample VC."""
        return VC(
            firm_name="AI Ventures",
            sectors=["B2B SaaS", "AI/ML"],
            stages=["Seed", "Series A"],
            thesis="We invest in AI-first B2B companies"
        )
    
    @pytest.fixture
    def sample_match(self, sample_startup, sample_vc):
        """Create a sample match."""
        return Match(
            startup=sample_startup,
            vc=sample_vc,
            score=0.85,
            reasons=["sector alignment", "stage match", "thesis fit"]
        )
    
    # Test create_match method
    @pytest.mark.asyncio
    async def test_create_match_with_valid_startup_and_vc_creates_manual_match(
        self, match_service, mock_startup_repo, mock_vc_repo, mock_match_repo,
        mock_matching_engine, sample_startup, sample_vc, sample_match
    ):
        # Arrange
        startup_id = uuid4()
        vc_id = uuid4()
        mock_startup_repo.get.return_value = sample_startup
        mock_vc_repo.get.return_value = sample_vc
        mock_matching_engine.calculate_match.return_value = sample_match
        mock_match_repo.create.return_value = sample_match
        
        # Act
        result = await match_service.create_match(
            startup_id=startup_id,
            vc_id=vc_id,
            match_type=MatchType.MANUAL
        )
        
        # Assert
        assert result == sample_match
        mock_startup_repo.get.assert_called_once_with(startup_id)
        mock_vc_repo.get.assert_called_once_with(vc_id)
        mock_matching_engine.calculate_match.assert_called_once_with(sample_startup, sample_vc)
        mock_match_repo.create.assert_called_once()
        created_match = mock_match_repo.create.call_args[0][0]
        assert created_match.match_type == MatchType.MANUAL
    
    @pytest.mark.asyncio
    async def test_create_match_with_nonexistent_startup_raises_error(
        self, match_service, mock_startup_repo
    ):
        # Arrange
        startup_id = uuid4()
        vc_id = uuid4()
        mock_startup_repo.get.return_value = None
        
        # Act & Assert
        with pytest.raises(ValueError, match="Startup .* not found"):
            await match_service.create_match(startup_id, vc_id)
    
    @pytest.mark.asyncio
    async def test_create_match_with_nonexistent_vc_raises_error(
        self, match_service, mock_startup_repo, mock_vc_repo, sample_startup
    ):
        # Arrange
        startup_id = uuid4()
        vc_id = uuid4()
        mock_startup_repo.get.return_value = sample_startup
        mock_vc_repo.get.return_value = None
        
        # Act & Assert
        with pytest.raises(ValueError, match="VC .* not found"):
            await match_service.create_match(startup_id, vc_id)
    
    @pytest.mark.asyncio
    async def test_create_match_with_automated_type_includes_metadata(
        self, match_service, mock_startup_repo, mock_vc_repo, mock_match_repo,
        mock_matching_engine, sample_startup, sample_vc, sample_match
    ):
        # Arrange
        startup_id = uuid4()
        vc_id = uuid4()
        mock_startup_repo.get.return_value = sample_startup
        mock_vc_repo.get.return_value = sample_vc
        mock_matching_engine.calculate_match.return_value = sample_match
        mock_match_repo.create.return_value = sample_match
        
        # Act
        result = await match_service.create_match(
            startup_id=startup_id,
            vc_id=vc_id,
            match_type=MatchType.AUTOMATED,
            metadata={"source": "weekly_scan"}
        )
        
        # Assert
        created_match = mock_match_repo.create.call_args[0][0]
        assert created_match.match_type == MatchType.AUTOMATED
        assert created_match.metadata == {"source": "weekly_scan"}
    
    @pytest.mark.asyncio
    async def test_create_match_with_ai_enhanced_type_calls_ai_analyzer(
        self, match_service, mock_startup_repo, mock_vc_repo, mock_match_repo,
        mock_matching_engine, mock_ai_analyzer, sample_startup, sample_vc, sample_match
    ):
        # Arrange
        startup_id = uuid4()
        vc_id = uuid4()
        ai_insights = {"compatibility": "high", "key_strengths": ["team", "market"]}
        mock_startup_repo.get.return_value = sample_startup
        mock_vc_repo.get.return_value = sample_vc
        mock_matching_engine.calculate_match.return_value = sample_match
        mock_ai_analyzer.get_match_insights.return_value = ai_insights
        mock_match_repo.create.return_value = sample_match
        
        # Act
        result = await match_service.create_match(
            startup_id=startup_id,
            vc_id=vc_id,
            match_type=MatchType.AI_ENHANCED
        )
        
        # Assert
        mock_ai_analyzer.get_match_insights.assert_called_once_with(sample_startup, sample_vc)
        created_match = mock_match_repo.create.call_args[0][0]
        assert created_match.ai_insights == ai_insights
    
    @pytest.mark.asyncio
    async def test_create_match_handles_ai_analyzer_failure_gracefully(
        self, match_service, mock_startup_repo, mock_vc_repo, mock_match_repo,
        mock_matching_engine, mock_ai_analyzer, sample_startup, sample_vc, sample_match
    ):
        # Arrange
        startup_id = uuid4()
        vc_id = uuid4()
        mock_startup_repo.get.return_value = sample_startup
        mock_vc_repo.get.return_value = sample_vc
        mock_matching_engine.calculate_match.return_value = sample_match
        mock_ai_analyzer.get_match_insights.side_effect = Exception("AI service error")
        mock_match_repo.create.return_value = sample_match
        
        # Act
        result = await match_service.create_match(
            startup_id=startup_id,
            vc_id=vc_id,
            match_type=MatchType.AI_ENHANCED
        )
        
        # Assert
        # Should still create match even if AI fails
        assert result == sample_match
        created_match = mock_match_repo.create.call_args[0][0]
        assert created_match.ai_insights is None  # No insights due to failure
    
    @pytest.mark.asyncio
    async def test_create_match_sets_correct_timestamps(
        self, match_service, mock_startup_repo, mock_vc_repo, mock_match_repo,
        mock_matching_engine, sample_startup, sample_vc, sample_match
    ):
        # Arrange
        startup_id = uuid4()
        vc_id = uuid4()
        mock_startup_repo.get.return_value = sample_startup
        mock_vc_repo.get.return_value = sample_vc
        mock_matching_engine.calculate_match.return_value = sample_match
        mock_match_repo.create.return_value = sample_match
        
        # Act
        before_time = datetime.now(timezone.utc)
        await match_service.create_match(startup_id, vc_id)
        after_time = datetime.now(timezone.utc)
        
        # Assert
        created_match = mock_match_repo.create.call_args[0][0]
        assert before_time <= created_match.created_at <= after_time
        assert created_match.created_at == created_match.updated_at
    
    # Test list_matches method
    @pytest.mark.asyncio
    async def test_list_matches_returns_all_matches_with_no_filters(
        self, match_service, mock_match_repo, sample_match
    ):
        # Arrange
        matches = [sample_match, sample_match]
        mock_match_repo.list.return_value = (matches, 2)
        
        # Act
        result_matches, total = await match_service.list_matches()
        
        # Assert
        assert result_matches == matches
        assert total == 2
        mock_match_repo.list.assert_called_once_with(
            skip=0,
            limit=20,
            filters={}
        )
    
    @pytest.mark.asyncio
    async def test_list_matches_filters_by_startup_id(
        self, match_service, mock_match_repo, sample_match
    ):
        # Arrange
        startup_id = uuid4()
        matches = [sample_match]
        mock_match_repo.list.return_value = (matches, 1)
        
        # Act
        result_matches, total = await match_service.list_matches(
            startup_id=startup_id,
            page=2,
            size=10
        )
        
        # Assert
        assert result_matches == matches
        assert total == 1
        mock_match_repo.list.assert_called_once_with(
            skip=10,  # (page-1) * size = (2-1) * 10
            limit=10,
            filters={"startup_id": startup_id}
        )
    
    @pytest.mark.asyncio
    async def test_list_matches_filters_by_vc_id(
        self, match_service, mock_match_repo, sample_match
    ):
        # Arrange
        vc_id = uuid4()
        matches = [sample_match]
        mock_match_repo.list.return_value = (matches, 1)
        
        # Act
        result_matches, total = await match_service.list_matches(vc_id=vc_id)
        
        # Assert
        mock_match_repo.list.assert_called_once_with(
            skip=0,
            limit=20,
            filters={"vc_id": vc_id}
        )
    
    @pytest.mark.asyncio
    async def test_list_matches_filters_by_match_type(
        self, match_service, mock_match_repo, sample_match
    ):
        # Arrange
        matches = [sample_match]
        mock_match_repo.list.return_value = (matches, 1)
        
        # Act
        result_matches, total = await match_service.list_matches(
            match_type=MatchType.AI_ENHANCED
        )
        
        # Assert
        mock_match_repo.list.assert_called_once_with(
            skip=0,
            limit=20,
            filters={"match_type": MatchType.AI_ENHANCED}
        )
    
    @pytest.mark.asyncio
    async def test_list_matches_filters_by_minimum_score(
        self, match_service, mock_match_repo, sample_match
    ):
        # Arrange
        matches = [sample_match]
        mock_match_repo.list.return_value = (matches, 1)
        
        # Act
        result_matches, total = await match_service.list_matches(min_score=0.8)
        
        # Assert
        mock_match_repo.list.assert_called_once_with(
            skip=0,
            limit=20,
            filters={"min_score": 0.8}
        )
    
    @pytest.mark.asyncio
    async def test_list_matches_with_all_filters_combined(
        self, match_service, mock_match_repo, sample_match
    ):
        # Arrange
        startup_id = uuid4()
        vc_id = uuid4()
        matches = [sample_match]
        mock_match_repo.list.return_value = (matches, 1)
        
        # Act
        result_matches, total = await match_service.list_matches(
            startup_id=startup_id,
            vc_id=vc_id,
            match_type=MatchType.AUTOMATED,
            min_score=0.75
        )
        
        # Assert
        mock_match_repo.list.assert_called_once_with(
            skip=0,
            limit=20,
            filters={
                "startup_id": startup_id,
                "vc_id": vc_id,
                "match_type": MatchType.AUTOMATED,
                "min_score": 0.75
            }
        )
    
    @pytest.mark.asyncio
    async def test_list_matches_validates_page_parameter(
        self, match_service
    ):
        # Act & Assert
        with pytest.raises(ValueError, match="Page must be greater than 0"):
            await match_service.list_matches(page=0)
    
    @pytest.mark.asyncio
    async def test_list_matches_validates_size_parameter(
        self, match_service
    ):
        # Act & Assert
        with pytest.raises(ValueError, match="Size must be between 1 and 100"):
            await match_service.list_matches(size=101)
    
    @pytest.mark.asyncio
    async def test_list_matches_returns_empty_list_when_no_matches(
        self, match_service, mock_match_repo
    ):
        # Arrange
        mock_match_repo.list.return_value = ([], 0)
        
        # Act
        result_matches, total = await match_service.list_matches()
        
        # Assert
        assert result_matches == []
        assert total == 0
    
    # Test get_match method
    @pytest.mark.asyncio
    async def test_get_match_returns_match_when_exists(
        self, match_service, mock_match_repo, sample_match
    ):
        # Arrange
        match_id = uuid4()
        mock_match_repo.get.return_value = sample_match
        
        # Act
        result = await match_service.get_match(match_id)
        
        # Assert
        assert result == sample_match
        mock_match_repo.get.assert_called_once_with(match_id)
    
    @pytest.mark.asyncio
    async def test_get_match_raises_error_when_not_found(
        self, match_service, mock_match_repo
    ):
        # Arrange
        match_id = uuid4()
        mock_match_repo.get.return_value = None
        
        # Act & Assert
        with pytest.raises(ValueError, match="Match .* not found"):
            await match_service.get_match(match_id)
    
    # Test update_match method
    @pytest.mark.asyncio
    async def test_update_match_updates_allowed_fields(
        self, match_service, mock_match_repo, sample_match
    ):
        # Arrange
        match_id = uuid4()
        mock_match_repo.get.return_value = sample_match
        mock_match_repo.update.return_value = sample_match
        
        # Act
        result = await match_service.update_match(
            match_id=match_id,
            status="accepted",
            notes="Great fit for our portfolio"
        )
        
        # Assert
        assert result == sample_match
        updated_match = mock_match_repo.update.call_args[0][0]
        assert updated_match.status == "accepted"
        assert updated_match.notes == "Great fit for our portfolio"
    
    @pytest.mark.asyncio
    async def test_update_match_raises_error_when_not_found(
        self, match_service, mock_match_repo
    ):
        # Arrange
        match_id = uuid4()
        mock_match_repo.get.return_value = None
        
        # Act & Assert
        with pytest.raises(ValueError, match="Match .* not found"):
            await match_service.update_match(match_id, status="accepted")
    
    @pytest.mark.asyncio
    async def test_update_match_ignores_invalid_fields(
        self, match_service, mock_match_repo, sample_match
    ):
        # Arrange
        match_id = uuid4()
        mock_match_repo.get.return_value = sample_match
        mock_match_repo.update.return_value = sample_match
        
        # Act
        await match_service.update_match(
            match_id=match_id,
            status="reviewed",
            invalid_field="should be ignored",
            score=0.99  # Should be ignored
        )
        
        # Assert
        updated_match = mock_match_repo.update.call_args[0][0]
        assert updated_match.status == "reviewed"
        assert not hasattr(updated_match, "invalid_field")
        # Score should not be updated (not in allowed fields)
        assert updated_match.score == sample_match.score
    
    @pytest.mark.asyncio
    async def test_update_match_updates_timestamp(
        self, match_service, mock_match_repo, sample_match
    ):
        # Arrange
        match_id = uuid4()
        sample_match.created_at = datetime(2024, 1, 1, tzinfo=timezone.utc)
        sample_match.updated_at = datetime(2024, 1, 1, tzinfo=timezone.utc)
        mock_match_repo.get.return_value = sample_match
        mock_match_repo.update.return_value = sample_match
        
        # Act
        before_time = datetime.now(timezone.utc)
        await match_service.update_match(match_id, status="contacted")
        after_time = datetime.now(timezone.utc)
        
        # Assert
        updated_match = mock_match_repo.update.call_args[0][0]
        assert updated_match.created_at == sample_match.created_at  # Should not change
        assert before_time <= updated_match.updated_at <= after_time
    
    @pytest.mark.asyncio
    async def test_update_match_with_no_fields_still_updates_timestamp(
        self, match_service, mock_match_repo, sample_match
    ):
        # Arrange
        match_id = uuid4()
        mock_match_repo.get.return_value = sample_match
        mock_match_repo.update.return_value = sample_match
        
        # Act
        result = await match_service.update_match(match_id)
        
        # Assert
        # Should still call update even with no field changes (to update timestamp)
        mock_match_repo.update.assert_called_once()
    
    # Test delete_match method
    @pytest.mark.asyncio
    async def test_delete_match_deletes_when_exists(
        self, match_service, mock_match_repo, sample_match
    ):
        # Arrange
        match_id = uuid4()
        mock_match_repo.get.return_value = sample_match
        mock_match_repo.delete.return_value = True
        
        # Act
        result = await match_service.delete_match(match_id)
        
        # Assert
        assert result is True
        mock_match_repo.delete.assert_called_once_with(match_id)
    
    @pytest.mark.asyncio
    async def test_delete_match_raises_error_when_not_found(
        self, match_service, mock_match_repo
    ):
        # Arrange
        match_id = uuid4()
        mock_match_repo.get.return_value = None
        
        # Act & Assert
        with pytest.raises(ValueError, match="Match .* not found"):
            await match_service.delete_match(match_id)
    
    @pytest.mark.asyncio
    async def test_delete_match_verifies_before_deletion(
        self, match_service, mock_match_repo, sample_match
    ):
        # Arrange
        match_id = uuid4()
        mock_match_repo.get.return_value = sample_match
        mock_match_repo.delete.return_value = True
        
        # Act
        await match_service.delete_match(match_id)
        
        # Assert
        # Should check existence before deletion
        mock_match_repo.get.assert_called_once_with(match_id)
        mock_match_repo.delete.assert_called_once_with(match_id)
    
    # Test batch_match method
    @pytest.mark.asyncio
    async def test_batch_match_single_startup_multiple_vcs(
        self, match_service, mock_startup_repo, mock_vc_repo, mock_match_repo,
        mock_matching_engine, sample_startup
    ):
        # Arrange
        startup_id = uuid4()
        vc1 = VC(firm_name="VC1", sectors=["B2B"], stages=["Seed"])
        vc2 = VC(firm_name="VC2", sectors=["AI"], stages=["Series A"])
        
        mock_startup_repo.get.return_value = sample_startup
        mock_vc_repo.list.return_value = ([vc1, vc2], 2)
        
        match1 = Match(startup=sample_startup, vc=vc1, score=0.8, reasons=["good"])
        match2 = Match(startup=sample_startup, vc=vc2, score=0.6, reasons=["okay"])
        mock_matching_engine.calculate_match.side_effect = [match1, match2]
        
        mock_match_repo.create.side_effect = [match1, match2]
        
        # Act
        results = await match_service.batch_match(
            startup_id=startup_id,
            min_score=0.5
        )
        
        # Assert
        assert len(results) == 2
        assert results[0].score > results[1].score  # Should be sorted by score
        assert mock_match_repo.create.call_count == 2
    
    @pytest.mark.asyncio
    async def test_batch_match_single_vc_multiple_startups(
        self, match_service, mock_startup_repo, mock_vc_repo, mock_match_repo,
        mock_matching_engine, sample_vc
    ):
        # Arrange
        vc_id = uuid4()
        startup1 = Startup(name="Startup1", sector="B2B", stage="Seed")
        startup2 = Startup(name="Startup2", sector="AI", stage="Series A")
        
        mock_vc_repo.get.return_value = sample_vc
        mock_startup_repo.list.return_value = ([startup1, startup2], 2)
        
        match1 = Match(startup=startup1, vc=sample_vc, score=0.7, reasons=["good"])
        match2 = Match(startup=startup2, vc=sample_vc, score=0.9, reasons=["great"])
        mock_matching_engine.calculate_match.side_effect = [match1, match2]
        
        mock_match_repo.create.side_effect = [match1, match2]
        
        # Act
        results = await match_service.batch_match(
            vc_id=vc_id,
            min_score=0.5
        )
        
        # Assert
        assert len(results) == 2
        assert results[0].score == 0.9  # Higher score first
        assert results[1].score == 0.7
    
    @pytest.mark.asyncio
    async def test_batch_match_filters_by_minimum_score(
        self, match_service, mock_startup_repo, mock_vc_repo, mock_match_repo,
        mock_matching_engine, sample_startup
    ):
        # Arrange
        startup_id = uuid4()
        vc1 = VC(firm_name="VC1", sectors=["B2B"], stages=["Seed"])
        vc2 = VC(firm_name="VC2", sectors=["AI"], stages=["Series A"])
        
        mock_startup_repo.get.return_value = sample_startup
        mock_vc_repo.list.return_value = ([vc1, vc2], 2)
        
        match1 = Match(startup=sample_startup, vc=vc1, score=0.8, reasons=["good"])
        match2 = Match(startup=sample_startup, vc=vc2, score=0.4, reasons=["poor"])
        mock_matching_engine.calculate_match.side_effect = [match1, match2]
        
        mock_match_repo.create.return_value = match1  # Only high score match saved
        
        # Act
        results = await match_service.batch_match(
            startup_id=startup_id,
            min_score=0.7
        )
        
        # Assert
        assert len(results) == 1
        assert results[0].score == 0.8
        # Should only create one match (the one above threshold)
        mock_match_repo.create.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_batch_match_raises_error_when_no_entity_specified(
        self, match_service
    ):
        # Act & Assert
        with pytest.raises(ValueError, match="Either startup_id or vc_id must be provided"):
            await match_service.batch_match()
    
    @pytest.mark.asyncio
    async def test_batch_match_raises_error_when_both_entities_specified(
        self, match_service
    ):
        # Act & Assert
        with pytest.raises(ValueError, match="Cannot specify both startup_id and vc_id"):
            await match_service.batch_match(startup_id=uuid4(), vc_id=uuid4())
    
    @pytest.mark.asyncio
    async def test_batch_match_with_automated_type(
        self, match_service, mock_startup_repo, mock_vc_repo, mock_match_repo,
        mock_matching_engine, sample_startup, sample_vc
    ):
        # Arrange
        startup_id = uuid4()
        mock_startup_repo.get.return_value = sample_startup
        mock_vc_repo.list.return_value = ([sample_vc], 1)
        
        match = Match(startup=sample_startup, vc=sample_vc, score=0.8, reasons=["good"])
        mock_matching_engine.calculate_match.return_value = match
        mock_match_repo.create.return_value = match
        
        # Act
        results = await match_service.batch_match(
            startup_id=startup_id,
            match_type=MatchType.AUTOMATED,
            metadata={"batch_id": "123"}
        )
        
        # Assert
        created_match = mock_match_repo.create.call_args[0][0]
        assert created_match.match_type == MatchType.AUTOMATED
        assert created_match.metadata == {"batch_id": "123"}
    
    @pytest.mark.asyncio
    async def test_batch_match_returns_empty_list_when_no_matches_above_threshold(
        self, match_service, mock_startup_repo, mock_vc_repo, mock_matching_engine,
        sample_startup, sample_vc
    ):
        # Arrange
        startup_id = uuid4()
        mock_startup_repo.get.return_value = sample_startup
        mock_vc_repo.list.return_value = ([sample_vc], 1)
        
        low_match = Match(startup=sample_startup, vc=sample_vc, score=0.2, reasons=["poor"])
        mock_matching_engine.calculate_match.return_value = low_match
        
        # Act
        results = await match_service.batch_match(
            startup_id=startup_id,
            min_score=0.5
        )
        
        # Assert
        assert results == []
        mock_match_repo.create.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_batch_match_handles_empty_counterpart_list(
        self, match_service, mock_startup_repo, mock_vc_repo, sample_startup
    ):
        # Arrange
        startup_id = uuid4()
        mock_startup_repo.get.return_value = sample_startup
        mock_vc_repo.list.return_value = ([], 0)  # No VCs available
        
        # Act
        results = await match_service.batch_match(startup_id=startup_id)
        
        # Assert
        assert results == []
        mock_matching_engine.calculate_match.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_batch_match_continues_on_individual_match_failure(
        self, match_service, mock_startup_repo, mock_vc_repo, mock_match_repo,
        mock_matching_engine, sample_startup
    ):
        # Arrange
        startup_id = uuid4()
        vc1 = VC(firm_name="VC1", sectors=["B2B"], stages=["Seed"])
        vc2 = VC(firm_name="VC2", sectors=["AI"], stages=["Series A"])
        
        mock_startup_repo.get.return_value = sample_startup
        mock_vc_repo.list.return_value = ([vc1, vc2], 2)
        
        match1 = Match(startup=sample_startup, vc=vc1, score=0.8, reasons=["good"])
        match2 = Match(startup=sample_startup, vc=vc2, score=0.7, reasons=["okay"])
        
        # First match calculation fails, second succeeds
        mock_matching_engine.calculate_match.side_effect = [Exception("Calc error"), match2]
        mock_match_repo.create.return_value = match2
        
        # Act
        results = await match_service.batch_match(startup_id=startup_id)
        
        # Assert
        assert len(results) == 1
        assert results[0] == match2
        # Should only create the successful match
        mock_match_repo.create.assert_called_once()
    
    # Test get_ai_insights_for_match method
    @pytest.mark.asyncio
    async def test_get_ai_insights_returns_insights_for_existing_match(
        self, match_service, mock_match_repo, mock_ai_analyzer, 
        sample_match, sample_startup, sample_vc
    ):
        # Arrange
        match_id = uuid4()
        insights = {"compatibility": "high", "recommendations": ["Schedule meeting"]}
        sample_match.startup = sample_startup
        sample_match.vc = sample_vc
        mock_match_repo.get.return_value = sample_match
        mock_ai_analyzer.get_match_insights.return_value = insights
        
        # Act
        result = await match_service.get_ai_insights_for_match(match_id)
        
        # Assert
        assert result == insights
        mock_ai_analyzer.get_match_insights.assert_called_once_with(
            sample_startup, sample_vc
        )
    
    @pytest.mark.asyncio
    async def test_get_ai_insights_raises_error_when_match_not_found(
        self, match_service, mock_match_repo
    ):
        # Arrange
        match_id = uuid4()
        mock_match_repo.get.return_value = None
        
        # Act & Assert
        with pytest.raises(ValueError, match="Match .* not found"):
            await match_service.get_ai_insights_for_match(match_id)
    
    @pytest.mark.asyncio
    async def test_get_ai_insights_updates_match_with_insights(
        self, match_service, mock_match_repo, mock_ai_analyzer,
        sample_match, sample_startup, sample_vc
    ):
        # Arrange
        match_id = uuid4()
        insights = {"compatibility": "high"}
        sample_match.startup = sample_startup
        sample_match.vc = sample_vc
        sample_match.ai_insights = None  # No existing insights
        mock_match_repo.get.return_value = sample_match
        mock_ai_analyzer.get_match_insights.return_value = insights
        mock_match_repo.update.return_value = sample_match
        
        # Act
        result = await match_service.get_ai_insights_for_match(match_id)
        
        # Assert
        assert result == insights
        # Should update the match with new insights
        updated_match = mock_match_repo.update.call_args[0][0]
        assert updated_match.ai_insights == insights
    
    @pytest.mark.asyncio
    async def test_get_ai_insights_returns_none_on_ai_failure(
        self, match_service, mock_match_repo, mock_ai_analyzer,
        sample_match, sample_startup, sample_vc
    ):
        # Arrange
        match_id = uuid4()
        sample_match.startup = sample_startup
        sample_match.vc = sample_vc
        mock_match_repo.get.return_value = sample_match
        mock_ai_analyzer.get_match_insights.side_effect = Exception("AI service down")
        
        # Act
        result = await match_service.get_ai_insights_for_match(match_id)
        
        # Assert
        assert result is None
        # Should not update match on failure
        mock_match_repo.update.assert_not_called()
    
    # Edge case tests
    @pytest.mark.asyncio
    async def test_concurrent_match_creation_handles_race_condition(
        self, match_service, mock_startup_repo, mock_vc_repo, mock_match_repo,
        mock_matching_engine, sample_startup, sample_vc, sample_match
    ):
        # Arrange
        startup_id = uuid4()
        vc_id = uuid4()
        mock_startup_repo.get.return_value = sample_startup
        mock_vc_repo.get.return_value = sample_vc
        mock_matching_engine.calculate_match.return_value = sample_match
        
        # Simulate multiple concurrent creates
        mock_match_repo.create.return_value = sample_match
        
        # Act - Create multiple matches concurrently
        import asyncio
        tasks = [
            match_service.create_match(startup_id, vc_id) 
            for _ in range(3)
        ]
        results = await asyncio.gather(*tasks)
        
        # Assert
        assert len(results) == 3
        assert all(r == sample_match for r in results)
        assert mock_match_repo.create.call_count == 3
    
    @pytest.mark.asyncio
    async def test_match_with_none_metadata_handled_correctly(
        self, match_service, mock_startup_repo, mock_vc_repo, mock_match_repo,
        mock_matching_engine, sample_startup, sample_vc, sample_match
    ):
        # Arrange
        startup_id = uuid4()
        vc_id = uuid4()
        mock_startup_repo.get.return_value = sample_startup
        mock_vc_repo.get.return_value = sample_vc
        mock_matching_engine.calculate_match.return_value = sample_match
        mock_match_repo.create.return_value = sample_match
        
        # Act
        result = await match_service.create_match(
            startup_id=startup_id,
            vc_id=vc_id,
            metadata=None
        )
        
        # Assert
        created_match = mock_match_repo.create.call_args[0][0]
        assert created_match.metadata is None