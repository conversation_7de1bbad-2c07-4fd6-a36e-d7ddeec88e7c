"""Comprehensive tests for PostgreSQL connection repository including path-finding."""

import pytest
from unittest.mock import Mo<PERSON>, Async<PERSON>ock, MagicMock, patch
from datetime import datetime, timedelta
from uuid import UUID, uuid4
from sqlalchemy.orm import Session
from sqlalchemy import text

from src.database.repositories.connection_repository import (
    PostgresConnectionRepository,
    PostgresIntroductionRepository
)
from src.database.models.connection import Connection as ConnectionDB, IntroductionRequest as IntroductionRequestDB
from src.core.models.connection import (
    Connection,
    ConnectionId,
    ConnectionMetrics,
    ConnectionStrength,
    RelationshipType,
    ConnectionPath,
    IntroductionRequest,
    IntroductionStatus
)
from tests.fixtures.warm_intro_fixtures import (
    sample_user_ids,
    mock_database_session,
    mock_db_connections,
    mock_db_intro_requests
)


class TestPostgresConnectionRepository:
    """Test PostgresConnectionRepository."""
    
    @pytest.fixture
    def repository(self, mock_database_session):
        """Create repository with mock session."""
        return PostgresConnectionRepository(mock_database_session)
    
    @pytest.fixture
    def sample_connection_domain(self, sample_user_ids):
        """Create a sample domain connection."""
        return Connection(
            id=ConnectionId(),
            user_a_id=sample_user_ids['alice'],
            user_b_id=sample_user_ids['bob'],
            relationship_type=RelationshipType.COLLEAGUE,
            strength=ConnectionStrength.STRONG,
            metrics=ConnectionMetrics(
                interaction_frequency=5,
                trust_score=0.8,
                mutual_connections_count=3
            ),
            notes="Test connection",
            tags=["test", "colleague"]
        )
    
    async def test_create_connection_success(self, repository, sample_connection_domain, mock_database_session):
        """Test successful connection creation."""
        # Mock database query to return no existing connection
        mock_database_session.query.return_value.filter.return_value.first.return_value = None
        
        # Mock database connection object
        mock_db_conn = Mock(spec=ConnectionDB)
        mock_db_conn.id = sample_connection_domain.id.value
        mock_db_conn.user_a_id = sample_connection_domain.user_a_id
        mock_db_conn.user_b_id = sample_connection_domain.user_b_id
        mock_db_conn.relationship_type = sample_connection_domain.relationship_type.value
        mock_db_conn.strength = sample_connection_domain.strength.value
        mock_db_conn.metrics = {
            "interaction_frequency": 5,
            "trust_score": 0.8,
            "mutual_connections_count": 3
        }
        mock_db_conn.notes = "Test connection"
        mock_db_conn.tags = ["test", "colleague"]
        mock_db_conn.created_at = sample_connection_domain.created_at
        mock_db_conn.updated_at = sample_connection_domain.updated_at
        mock_db_conn.is_active = True
        
        # Configure session.refresh to update the mock object
        def refresh_side_effect(obj):
            pass
        mock_database_session.refresh.side_effect = refresh_side_effect
        
        # Execute
        result = await repository.create_connection(sample_connection_domain)
        
        # Verify session interactions
        mock_database_session.add.assert_called_once()
        mock_database_session.commit.assert_called_once()
        mock_database_session.refresh.assert_called_once()
        
        # Verify result
        assert isinstance(result, Connection)
        assert result.relationship_type == RelationshipType.COLLEAGUE
        assert result.strength == ConnectionStrength.STRONG
    
    async def test_create_connection_already_exists(self, repository, sample_connection_domain, mock_database_session):
        """Test creation when connection already exists."""
        # Mock existing active connection
        existing_conn = Mock(spec=ConnectionDB)
        existing_conn.is_active = True
        mock_database_session.query.return_value.filter.return_value.first.return_value = existing_conn
        
        # Should raise ValueError
        with pytest.raises(ValueError, match="Active connection already exists"):
            await repository.create_connection(sample_connection_domain)
        
        # Should not add to session
        mock_database_session.add.assert_not_called()
    
    async def test_get_connection_found(self, repository, sample_user_ids, mock_database_session):
        """Test getting an existing connection."""
        # Mock database connection
        mock_db_conn = Mock(spec=ConnectionDB)
        mock_db_conn.id = uuid4()
        mock_db_conn.user_a_id = sample_user_ids['alice']
        mock_db_conn.user_b_id = sample_user_ids['bob']
        mock_db_conn.relationship_type = 'colleague'
        mock_db_conn.strength = 'strong'
        mock_db_conn.metrics = {"trust_score": 0.8}
        mock_db_conn.notes = "Test"
        mock_db_conn.tags = []
        mock_db_conn.created_at = datetime.utcnow()
        mock_db_conn.updated_at = datetime.utcnow()
        mock_db_conn.is_active = True
        
        mock_database_session.query.return_value.filter.return_value.first.return_value = mock_db_conn
        
        # Execute (IDs should be sorted)
        result = await repository.get_connection(sample_user_ids['bob'], sample_user_ids['alice'])
        
        # Verify
        assert result is not None
        assert isinstance(result, Connection)
        assert result.user_a_id == sample_user_ids['alice']  # Should be sorted
        assert result.user_b_id == sample_user_ids['bob']
    
    async def test_get_connection_not_found(self, repository, sample_user_ids, mock_database_session):
        """Test getting a non-existent connection."""
        mock_database_session.query.return_value.filter.return_value.first.return_value = None
        
        result = await repository.get_connection(sample_user_ids['alice'], sample_user_ids['bob'])
        
        assert result is None
    
    async def test_get_user_connections(self, repository, sample_user_ids, mock_database_session):
        """Test getting all connections for a user."""
        # Mock multiple connections
        mock_conn1 = Mock(spec=ConnectionDB)
        mock_conn1.id = uuid4()
        mock_conn1.user_a_id = sample_user_ids['alice']
        mock_conn1.user_b_id = sample_user_ids['bob']
        mock_conn1.relationship_type = 'colleague'
        mock_conn1.strength = 'strong'
        mock_conn1.metrics = {}
        mock_conn1.notes = None
        mock_conn1.tags = []
        mock_conn1.created_at = datetime.utcnow()
        mock_conn1.updated_at = datetime.utcnow()
        mock_conn1.is_active = True
        
        mock_conn2 = Mock(spec=ConnectionDB)
        mock_conn2.id = uuid4()
        mock_conn2.user_a_id = sample_user_ids['alice']
        mock_conn2.user_b_id = sample_user_ids['charlie']
        mock_conn2.relationship_type = 'business_partner'
        mock_conn2.strength = 'medium'
        mock_conn2.metrics = {}
        mock_conn2.notes = None
        mock_conn2.tags = []
        mock_conn2.created_at = datetime.utcnow()
        mock_conn2.updated_at = datetime.utcnow()
        mock_conn2.is_active = True
        
        mock_database_session.query.return_value.filter.return_value.all.return_value = [mock_conn1, mock_conn2]
        
        # Execute
        result = await repository.get_user_connections(sample_user_ids['alice'])
        
        # Verify
        assert len(result) == 2
        assert all(isinstance(conn, Connection) for conn in result)
    
    async def test_update_connection(self, repository, sample_connection_domain, mock_database_session):
        """Test updating an existing connection."""
        # Mock existing connection
        mock_db_conn = Mock(spec=ConnectionDB)
        mock_db_conn.id = sample_connection_domain.id.value
        mock_database_session.query.return_value.filter.return_value.first.return_value = mock_db_conn
        
        # Execute
        updated_connection = sample_connection_domain
        updated_connection.notes = "Updated notes"
        
        result = await repository.update_connection(updated_connection)
        
        # Verify database updates
        assert mock_db_conn.notes == "Updated notes"
        assert mock_db_conn.relationship_type == sample_connection_domain.relationship_type.value
        mock_database_session.commit.assert_called_once()
        mock_database_session.refresh.assert_called_once()
    
    async def test_update_connection_not_found(self, repository, sample_connection_domain, mock_database_session):
        """Test updating a non-existent connection."""
        mock_database_session.query.return_value.filter.return_value.first.return_value = None
        
        with pytest.raises(ValueError, match="Connection .* not found"):
            await repository.update_connection(sample_connection_domain)
    
    async def test_delete_connection(self, repository, mock_database_session):
        """Test soft deleting a connection."""
        connection_id = uuid4()
        
        # Mock existing connection
        mock_db_conn = Mock(spec=ConnectionDB)
        mock_db_conn.is_active = True
        mock_database_session.query.return_value.filter.return_value.first.return_value = mock_db_conn
        
        # Execute
        result = await repository.delete_connection(connection_id)
        
        # Verify
        assert result is True
        assert mock_db_conn.is_active is False
        assert isinstance(mock_db_conn.updated_at, datetime)
        mock_database_session.commit.assert_called_once()
    
    async def test_delete_connection_not_found(self, repository, mock_database_session):
        """Test deleting a non-existent connection."""
        mock_database_session.query.return_value.filter.return_value.first.return_value = None
        
        result = await repository.delete_connection(uuid4())
        
        assert result is False
        mock_database_session.commit.assert_not_called()
    
    async def test_search_connections_with_filters(self, repository, sample_user_ids, mock_database_session):
        """Test searching connections with various filters."""
        # Mock connection result
        mock_conn = Mock(spec=ConnectionDB)
        mock_conn.id = uuid4()
        mock_conn.user_a_id = sample_user_ids['alice']
        mock_conn.user_b_id = sample_user_ids['bob']
        mock_conn.relationship_type = 'colleague'
        mock_conn.strength = 'strong'
        mock_conn.metrics = {}
        mock_conn.notes = None
        mock_conn.tags = ['tech']
        mock_conn.created_at = datetime.utcnow()
        mock_conn.updated_at = datetime.utcnow()
        mock_conn.is_active = True
        
        # Configure mock query chain
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.all.return_value = [mock_conn]
        mock_database_session.query.return_value = mock_query
        
        # Execute with filters
        filters = {
            'strength': 'strong',
            'relationship_type': 'colleague',
            'tags': ['tech']
        }
        
        result = await repository.search_connections(sample_user_ids['alice'], filters)
        
        # Verify
        assert len(result) == 1
        assert isinstance(result[0], Connection)
        # Verify that filter was called multiple times (once for user, then for each filter)
        assert mock_query.filter.call_count >= 3


class TestPathFindingFunctionality:
    """Test the PostgreSQL path-finding functionality."""
    
    @pytest.fixture
    def repository(self, mock_database_session):
        """Create repository with mock session."""
        return PostgresConnectionRepository(mock_database_session)
    
    async def test_find_shortest_paths_success(self, repository, sample_user_ids, mock_database_session):
        """Test successful path finding."""
        # Mock SQL execution result
        mock_result = Mock()
        mock_row1 = Mock()
        mock_row1.path = [sample_user_ids['alice'], sample_user_ids['bob'], sample_user_ids['charlie']]
        mock_row1.depth = 2
        mock_row1.strength_score = 0.48
        
        mock_row2 = Mock()
        mock_row2.path = [sample_user_ids['alice'], sample_user_ids['diana'], sample_user_ids['charlie']]
        mock_row2.depth = 2
        mock_row2.strength_score = 0.36
        
        mock_result.__iter__ = Mock(return_value=iter([mock_row1, mock_row2]))
        mock_database_session.execute.return_value = mock_result
        
        # Mock get_connection calls for path construction
        mock_connection1 = Connection(
            id=ConnectionId(),
            user_a_id=sample_user_ids['alice'],
            user_b_id=sample_user_ids['bob'],
            relationship_type=RelationshipType.COLLEAGUE,
            strength=ConnectionStrength.STRONG,
            metrics=ConnectionMetrics(trust_score=0.8)
        )
        
        mock_connection2 = Connection(
            id=ConnectionId(),
            user_a_id=sample_user_ids['bob'],
            user_b_id=sample_user_ids['charlie'],
            relationship_type=RelationshipType.BUSINESS_PARTNER,
            strength=ConnectionStrength.MEDIUM,
            metrics=ConnectionMetrics(trust_score=0.6)
        )
        
        with patch.object(repository, 'get_connection') as mock_get_conn:
            mock_get_conn.side_effect = [mock_connection1, mock_connection2, None, None]
            
            # Execute
            result = await repository.find_shortest_paths(
                source_user_id=sample_user_ids['alice'],
                target_user_id=sample_user_ids['charlie'],
                max_depth=3
            )
        
        # Verify SQL execution
        mock_database_session.execute.assert_called_once()
        call_args = mock_database_session.execute.call_args
        assert isinstance(call_args[0][0], text)
        
        # Verify results
        assert len(result) == 2
        assert all(isinstance(path, ConnectionPath) for path in result)
        
        # Check first path
        path1 = result[0]
        assert path1.source_user_id == sample_user_ids['alice']
        assert path1.target_user_id == sample_user_ids['charlie']
        assert len(path1.path) == 3
        assert len(path1.connections) == 2
        assert path1.total_strength_score == 0.48
    
    async def test_find_shortest_paths_no_results(self, repository, sample_user_ids, mock_database_session):
        """Test path finding with no results."""
        # Mock empty result
        mock_result = Mock()
        mock_result.__iter__ = Mock(return_value=iter([]))
        mock_database_session.execute.return_value = mock_result
        
        # Execute
        result = await repository.find_shortest_paths(
            source_user_id=sample_user_ids['alice'],
            target_user_id=sample_user_ids['eve'],
            max_depth=3
        )
        
        # Verify
        assert len(result) == 0
    
    async def test_find_shortest_paths_sql_parameters(self, repository, sample_user_ids, mock_database_session):
        """Test that correct SQL parameters are passed."""
        # Mock empty result
        mock_result = Mock()
        mock_result.__iter__ = Mock(return_value=iter([]))
        mock_database_session.execute.return_value = mock_result
        
        # Execute with specific parameters
        await repository.find_shortest_paths(
            source_user_id=sample_user_ids['alice'],
            target_user_id=sample_user_ids['charlie'],
            max_depth=2
        )
        
        # Verify SQL parameters
        call_args = mock_database_session.execute.call_args
        assert call_args[1]['source_id'] == sample_user_ids['alice']
        assert call_args[1]['target_id'] == sample_user_ids['charlie']
        assert call_args[1]['max_depth'] == 2
    
    async def test_path_construction_with_missing_connections(self, repository, sample_user_ids, mock_database_session):
        """Test path construction when some connections are missing."""
        # Mock SQL result with path
        mock_result = Mock()
        mock_row = Mock()
        mock_row.path = [sample_user_ids['alice'], sample_user_ids['bob'], sample_user_ids['charlie']]
        mock_row.depth = 2
        mock_row.strength_score = 0.5
        mock_result.__iter__ = Mock(return_value=iter([mock_row]))
        mock_database_session.execute.return_value = mock_result
        
        # Mock get_connection to return None (missing connection)
        with patch.object(repository, 'get_connection') as mock_get_conn:
            mock_get_conn.return_value = None
            
            # Execute
            result = await repository.find_shortest_paths(
                source_user_id=sample_user_ids['alice'],
                target_user_id=sample_user_ids['charlie'],
                max_depth=3
            )
        
        # Should still create path but with empty connections
        assert len(result) == 1
        path = result[0]
        assert len(path.connections) == 0  # No connections found
        assert path.total_strength_score == 0.5  # From SQL result


class TestMutualConnectionsQuery:
    """Test mutual connections functionality."""
    
    @pytest.fixture
    def repository(self, mock_database_session):
        """Create repository with mock session."""
        return PostgresConnectionRepository(mock_database_session)
    
    async def test_get_mutual_connections(self, repository, sample_user_ids, mock_database_session):
        """Test getting mutual connections between two users."""
        # Mock SQL execution result
        mock_result = Mock()
        mock_row = Mock()
        mock_row.id = uuid4()
        mock_row.user_a_id = sample_user_ids['alice']
        mock_row.user_b_id = sample_user_ids['diana']
        mock_row.relationship_type = 'colleague'
        mock_row.strength = 'medium'
        mock_row.metrics = {'trust_score': 0.6}
        mock_row.notes = 'Mutual connection'
        mock_row.tags = ['mutual']
        mock_row.created_at = datetime.utcnow()
        mock_row.updated_at = datetime.utcnow()
        mock_row.is_active = True
        
        mock_result.fetchall.return_value = [mock_row]
        mock_database_session.execute.return_value = mock_result
        
        # Execute
        result = await repository.get_mutual_connections(
            sample_user_ids['alice'],
            sample_user_ids['charlie']
        )
        
        # Verify
        assert len(result) == 1
        assert isinstance(result[0], Connection)
        
        # Verify SQL execution with parameters
        mock_database_session.execute.assert_called_once()
        call_args = mock_database_session.execute.call_args
        assert call_args[1]['user_a'] == sample_user_ids['alice']
        assert call_args[1]['user_b'] == sample_user_ids['charlie']


class TestPostgresIntroductionRepository:
    """Test PostgresIntroductionRepository."""
    
    @pytest.fixture
    def repository(self, mock_database_session):
        """Create repository with mock session."""
        return PostgresIntroductionRepository(mock_database_session)
    
    @pytest.fixture
    def sample_intro_request(self, sample_user_ids):
        """Create a sample introduction request."""
        return IntroductionRequest(
            requester_id=sample_user_ids['alice'],
            target_id=sample_user_ids['charlie'],
            connector_id=sample_user_ids['bob'],
            message="Please introduce me for collaboration discussion",
            status=IntroductionStatus.PENDING
        )
    
    async def test_create_request_success(self, repository, sample_intro_request, mock_database_session):
        """Test successful introduction request creation."""
        # Mock no existing request
        mock_database_session.query.return_value.filter.return_value.first.return_value = None
        
        # Mock database object
        mock_db_request = Mock(spec=IntroductionRequestDB)
        mock_db_request.id = sample_intro_request.id
        mock_db_request.requester_id = sample_intro_request.requester_id
        mock_db_request.target_id = sample_intro_request.target_id
        mock_db_request.connector_id = sample_intro_request.connector_id
        mock_db_request.status = sample_intro_request.status.value
        mock_db_request.message = sample_intro_request.message
        mock_db_request.connector_notes = sample_intro_request.connector_notes
        mock_db_request.created_at = sample_intro_request.created_at
        mock_db_request.updated_at = sample_intro_request.updated_at
        mock_db_request.expires_at = sample_intro_request.expires_at
        mock_db_request.completed_at = sample_intro_request.completed_at
        
        # Execute
        result = await repository.create_request(sample_intro_request)
        
        # Verify
        mock_database_session.add.assert_called_once()
        mock_database_session.commit.assert_called_once()
        mock_database_session.refresh.assert_called_once()
        assert isinstance(result, IntroductionRequest)
    
    async def test_create_request_duplicate(self, repository, sample_intro_request, mock_database_session):
        """Test creating duplicate introduction request."""
        # Mock existing pending request
        existing_request = Mock(spec=IntroductionRequestDB)
        mock_database_session.query.return_value.filter.return_value.first.return_value = existing_request
        
        with pytest.raises(ValueError, match="Pending introduction request already exists"):
            await repository.create_request(sample_intro_request)
        
        mock_database_session.add.assert_not_called()
    
    async def test_get_request_found(self, repository, mock_database_session):
        """Test getting an existing introduction request."""
        request_id = uuid4()
        
        # Mock database request
        mock_db_request = Mock(spec=IntroductionRequestDB)
        mock_db_request.id = request_id
        mock_db_request.requester_id = uuid4()
        mock_db_request.target_id = uuid4()
        mock_db_request.connector_id = uuid4()
        mock_db_request.status = 'pending'
        mock_db_request.message = 'Test message'
        mock_db_request.connector_notes = None
        mock_db_request.created_at = datetime.utcnow()
        mock_db_request.updated_at = datetime.utcnow()
        mock_db_request.expires_at = datetime.utcnow() + timedelta(days=30)
        mock_db_request.completed_at = None
        
        mock_database_session.query.return_value.filter.return_value.first.return_value = mock_db_request
        
        # Execute
        result = await repository.get_request(request_id)
        
        # Verify
        assert result is not None
        assert isinstance(result, IntroductionRequest)
        assert result.id == request_id
        assert result.status == IntroductionStatus.PENDING
    
    async def test_get_request_not_found(self, repository, mock_database_session):
        """Test getting a non-existent introduction request."""
        mock_database_session.query.return_value.filter.return_value.first.return_value = None
        
        result = await repository.get_request(uuid4())
        
        assert result is None
    
    async def test_get_pending_requests(self, repository, mock_database_session):
        """Test getting pending requests for a connector."""
        connector_id = uuid4()
        
        # Mock pending requests
        mock_request1 = Mock(spec=IntroductionRequestDB)
        mock_request1.id = uuid4()
        mock_request1.status = 'pending'
        mock_request1.connector_id = connector_id
        mock_request1.requester_id = uuid4()
        mock_request1.target_id = uuid4()
        mock_request1.message = 'Request 1'
        mock_request1.connector_notes = None
        mock_request1.created_at = datetime.utcnow()
        mock_request1.updated_at = datetime.utcnow()
        mock_request1.expires_at = datetime.utcnow() + timedelta(days=30)
        mock_request1.completed_at = None
        
        mock_database_session.query.return_value.filter.return_value.order_by.return_value.all.return_value = [mock_request1]
        
        # Execute
        result = await repository.get_pending_requests(connector_id)
        
        # Verify
        assert len(result) == 1
        assert isinstance(result[0], IntroductionRequest)
        assert result[0].status == IntroductionStatus.PENDING
    
    async def test_update_request(self, repository, sample_intro_request, mock_database_session):
        """Test updating an introduction request."""
        # Mock existing request
        mock_db_request = Mock(spec=IntroductionRequestDB)
        mock_db_request.id = sample_intro_request.id
        mock_database_session.query.return_value.filter.return_value.first.return_value = mock_db_request
        
        # Update the request
        sample_intro_request.accept("Happy to make this introduction")
        
        # Execute
        result = await repository.update_request(sample_intro_request)
        
        # Verify database updates
        assert mock_db_request.status == 'accepted'
        assert mock_db_request.connector_notes == "Happy to make this introduction"
        mock_database_session.commit.assert_called_once()
        mock_database_session.refresh.assert_called_once()
        assert isinstance(result, IntroductionRequest)
    
    async def test_update_request_not_found(self, repository, sample_intro_request, mock_database_session):
        """Test updating a non-existent request."""
        mock_database_session.query.return_value.filter.return_value.first.return_value = None
        
        with pytest.raises(ValueError, match="Introduction request .* not found"):
            await repository.update_request(sample_intro_request)
    
    async def test_cleanup_expired_requests(self, repository, mock_database_session):
        """Test cleaning up expired requests."""
        # Mock update query
        mock_query = Mock()
        mock_query.update.return_value = 3  # 3 requests updated
        mock_database_session.query.return_value.filter.return_value = mock_query
        
        # Execute
        result = await repository.cleanup_expired_requests()
        
        # Verify
        assert result == 3
        mock_query.update.assert_called_once()
        mock_database_session.commit.assert_called_once()
    
    async def test_get_user_requests(self, repository, mock_database_session):
        """Test getting all requests involving a user."""
        user_id = uuid4()
        
        # Mock requests where user is involved
        mock_request = Mock(spec=IntroductionRequestDB)
        mock_request.id = uuid4()
        mock_request.requester_id = user_id
        mock_request.target_id = uuid4()
        mock_request.connector_id = uuid4()
        mock_request.status = 'completed'
        mock_request.message = 'Test request'
        mock_request.connector_notes = 'Completed successfully'
        mock_request.created_at = datetime.utcnow()
        mock_request.updated_at = datetime.utcnow()
        mock_request.expires_at = datetime.utcnow() + timedelta(days=30)
        mock_request.completed_at = datetime.utcnow()
        
        mock_database_session.query.return_value.filter.return_value.order_by.return_value.all.return_value = [mock_request]
        
        # Execute
        result = await repository.get_user_requests(user_id)
        
        # Verify
        assert len(result) == 1
        assert isinstance(result[0], IntroductionRequest)
        assert result[0].status == IntroductionStatus.COMPLETED