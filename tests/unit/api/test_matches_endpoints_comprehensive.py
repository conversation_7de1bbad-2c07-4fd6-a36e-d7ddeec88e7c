"""Comprehensive unit tests for Match API endpoints with full coverage."""

import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from fastapi import status
from datetime import datetime
import uuid
import json
from typing import List, Optional

from src.core.models.match import Match
from src.core.models.startup import Startup
from src.core.models.vc import VC
from src.core.schemas.match import MatchRequest, BatchMatchRequest, MatchUpdate, MatchStatus, MatchType
from src.core.services.match_service import MatchService
from src.core.repositories.startup_repository import StartupRepository
from src.core.repositories.vc_repository import VCRepository
from src.core.ports.ai_port import AIPort, MatchRationale
from src.api.errors import NotFoundError, BadRequestError


class TestMatchEndpointsComprehensive:
    """Comprehensive test suite for match API endpoints with full coverage."""
    
    @pytest.fixture
    def mock_startup_repository(self):
        """Mock startup repository."""
        repo = Mock(spec=StartupRepository)
        repo.find_by_id = AsyncMock()
        repo.find_all = AsyncMock(return_value=[])
        return repo
    
    @pytest.fixture
    def mock_vc_repository(self):
        """Mock VC repository."""
        repo = Mock(spec=VCRepository)
        repo.find_by_id = AsyncMock()
        repo.find_all = AsyncMock(return_value=[])
        return repo
    
    @pytest.fixture
    def mock_ai_port(self):
        """Mock AI port."""
        port = Mock(spec=AIPort)
        port.generate_match_rationale = AsyncMock()
        port.analyze_startup = AsyncMock()
        port.analyze_vc_thesis = AsyncMock()
        return port
    
    @pytest.fixture
    def mock_match_service(self, mock_startup_repository, mock_vc_repository, mock_ai_port):
        """Mock match service."""
        service = Mock(spec=MatchService)
        service.startup_repo = mock_startup_repository
        service.vc_repo = mock_vc_repository
        service.ai_port = mock_ai_port
        return service
    
    @pytest.fixture
    def mock_dependencies(self, test_client, mock_match_service, mock_db_session):
        """Override dependencies with mocks."""
        from src.api.v1.endpoints.matches import get_matching_service, get_startup_repository, get_vc_repository
        from src.api.v1.deps import get_current_user_optional
        from src.api.main import app
        
        # Mock the repository dependencies
        async def mock_get_startup_repository():
            return mock_match_service.startup_repo
        
        async def mock_get_vc_repository():
            return mock_match_service.vc_repo
        
        # Mock the service dependency
        async def mock_get_service(startup_repo=None, vc_repo=None, ai_port=None):
            return mock_match_service
        
        app.dependency_overrides[get_startup_repository] = mock_get_startup_repository
        app.dependency_overrides[get_vc_repository] = mock_get_vc_repository
        app.dependency_overrides[get_matching_service] = mock_get_service
        
        yield
        
        app.dependency_overrides.clear()
    
    @pytest.fixture
    def mock_auth(self):
        """Mock authentication dependencies."""
        from src.api.v1.deps import get_current_user_optional
        from src.api.main import app
        
        # Mock auth to return a user
        async def mock_get_current_user_optional(credentials=None):
            return "test-user"
        
        app.dependency_overrides[get_current_user_optional] = mock_get_current_user_optional
        
        yield
        
        # Clean up only this override
        app.dependency_overrides.pop(get_current_user_optional, None)
    
    @pytest.fixture
    def mock_no_auth(self):
        """Mock authentication to return None (no user)."""
        from src.api.v1.deps import get_current_user_optional
        from src.api.main import app
        
        # Mock auth to return None
        async def mock_get_current_user_optional(credentials=None):
            return None
        
        app.dependency_overrides[get_current_user_optional] = mock_get_current_user_optional
        
        yield
        
        # Clean up only this override
        app.dependency_overrides.pop(get_current_user_optional, None)
    
    @pytest.fixture
    def sample_match_request(self):
        """Sample match request data."""
        return {
            "startup_id": str(uuid.uuid4()),
            "vc_id": str(uuid.uuid4()),
            "match_type": "manual",
            "notes": "Potential good fit based on sector alignment"
        }
    
    @pytest.fixture
    def sample_batch_match_request(self):
        """Sample batch match request data."""
        return {
            "startup_ids": [str(uuid.uuid4()) for _ in range(3)],
            "vc_ids": [str(uuid.uuid4()) for _ in range(2)],
            "match_type": "ai_enhanced",
            "min_score_threshold": 0.7
        }
    
    @pytest.fixture
    def sample_match(self, sample_startup, sample_vc):
        """Create a sample match."""
        match = Match(
            id=uuid.uuid4(),
            startup=sample_startup,
            vc=sample_vc,
            score=0.85,
            reasons=["Strong sector alignment", "Stage match", "Geographic overlap"],
            status=MatchStatus.PENDING,
            match_type=MatchType.AI_ENHANCED,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
            notes="Initial match based on AI analysis"
        )
        return match
    
    @pytest.fixture
    def match_factory(self, startup_factory, vc_factory):
        """Factory for creating test matches."""
        def _create_match(**kwargs):
            defaults = {
                "id": uuid.uuid4(),
                "startup": startup_factory(),
                "vc": vc_factory(),
                "score": 0.75,
                "reasons": ["Sector alignment", "Stage match"],
                "status": MatchStatus.PENDING,
                "match_type": MatchType.AUTOMATED,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow(),
                "notes": None
            }
            defaults.update(kwargs)
            return Match(**defaults)
        
        return _create_match
    
    # Test Create Endpoint
    @pytest.mark.asyncio
    async def test_create_match_success(
        self, test_client, mock_dependencies, mock_match_service,
        sample_match_request, auth_headers, sample_match, mock_auth
    ):
        """Test successful match creation."""
        # Arrange
        mock_match_service.create_match = AsyncMock(return_value=sample_match)
        
        # Act
        response = test_client.post(
            "/api/v1/matches",
            json=sample_match_request,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert "id" in data
        assert data["startup_id"] == str(sample_match.startup.id)
        assert data["vc_id"] == str(sample_match.vc.id)
        assert data["score"] == sample_match.score
        assert data["reasons"] == sample_match.reasons
        assert data["status"] == sample_match.status
        assert data["match_type"] == sample_match.match_type
    
    @pytest.mark.asyncio
    async def test_create_match_circular_reference(
        self, test_client, mock_dependencies, auth_headers, mock_auth
    ):
        """Test creating match with same startup and VC ID (circular reference)."""
        # Arrange
        same_id = str(uuid.uuid4())
        match_request = {
            "startup_id": same_id,
            "vc_id": same_id,
            "match_type": "manual"
        }
        
        # Act
        response = test_client.post(
            "/api/v1/matches",
            json=match_request,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Cannot create match between same entity" in response.json()["error"]["detail"]
    
    @pytest.mark.asyncio
    async def test_create_match_requires_auth(
        self, test_client, mock_dependencies, mock_no_auth, sample_match_request
    ):
        """Test that creating a match requires authentication."""
        # Act
        response = test_client.post(
            "/api/v1/matches",
            json=sample_match_request
        )
        
        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Authentication required" in response.json()["error"]["detail"]
    
    @pytest.mark.asyncio
    async def test_create_match_invalid_uuid(
        self, test_client, mock_dependencies, auth_headers, mock_auth
    ):
        """Test creating match with invalid UUID format."""
        # Arrange
        match_request = {
            "startup_id": "not-a-uuid",
            "vc_id": str(uuid.uuid4()),
            "match_type": "manual"
        }
        
        # Act
        response = test_client.post(
            "/api/v1/matches",
            json=match_request,
            headers=auth_headers
        )
        
        # Assert - UUID validation happens in the endpoint and returns 400
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "badly formed hexadecimal UUID string" in response.json()["error"]["detail"]
    
    @pytest.mark.asyncio
    async def test_create_match_missing_required_fields(
        self, test_client, mock_dependencies, auth_headers, mock_auth
    ):
        """Test validation for missing required fields."""
        # Arrange
        match_request = {"startup_id": str(uuid.uuid4())}
        
        # Act
        response = test_client.post(
            "/api/v1/matches",
            json=match_request,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        error_data = response.json()
        assert "detail" in error_data
        errors = error_data["detail"]
        assert any("vc_id" in error["field"] for error in errors)
    
    @pytest.mark.asyncio
    async def test_create_match_service_value_error(
        self, test_client, mock_dependencies, mock_match_service,
        sample_match_request, auth_headers, mock_auth
    ):
        """Test handling of ValueError from service."""
        # Arrange
        mock_match_service.create_match = AsyncMock(
            side_effect=ValueError("Startup not found")
        )
        
        # Act
        response = test_client.post(
            "/api/v1/matches",
            json=sample_match_request,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Startup not found" in response.json()["error"]["detail"]
    
    @pytest.mark.asyncio
    async def test_create_match_service_exception(
        self, test_client, mock_dependencies, mock_match_service,
        sample_match_request, auth_headers, mock_auth
    ):
        """Test handling of general exception from service."""
        # Arrange
        mock_match_service.create_match = AsyncMock(
            side_effect=Exception("Database error")
        )
        
        # Act
        response = test_client.post(
            "/api/v1/matches",
            json=sample_match_request,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert "Failed to create match" in response.json()["error"]["detail"]
    
    # Test List Endpoint
    @pytest.mark.asyncio
    async def test_list_matches_success(
        self, test_client, mock_dependencies, mock_match_service, match_factory
    ):
        """Test successful match listing."""
        # Arrange
        matches = [match_factory() for _ in range(5)]
        mock_match_service.list_matches = AsyncMock(return_value=matches)
        
        # Act
        response = test_client.get("/api/v1/matches")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "items" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data
        assert "pages" in data
        assert "average_score" in data
        assert isinstance(data["items"], list)
        assert len(data["items"]) == 5
        assert data["average_score"] == sum(m.score for m in matches) / len(matches)
    
    @pytest.mark.asyncio
    async def test_list_matches_with_pagination(
        self, test_client, mock_dependencies, mock_match_service, match_factory
    ):
        """Test pagination parameters."""
        # Arrange
        matches = [match_factory() for _ in range(30)]
        mock_match_service.list_matches = AsyncMock(return_value=matches)
        
        # Act
        response = test_client.get("/api/v1/matches?page=2&size=10")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["page"] == 2
        assert data["size"] == 10
        assert len(data["items"]) == 10
        assert data["total"] == 30
        assert data["pages"] == 3
    
    @pytest.mark.asyncio
    async def test_list_matches_filter_by_startup(
        self, test_client, mock_dependencies, mock_match_service, match_factory, startup_factory
    ):
        """Test filtering by startup ID."""
        # Arrange
        startup_id = uuid.uuid4()
        startup = startup_factory()
        startup.id = startup_id
        matches = []
        for _ in range(3):
            match = match_factory()
            match.startup = startup
            matches.append(match)
        mock_match_service.list_matches = AsyncMock(return_value=matches)
        
        # Act
        response = test_client.get(f"/api/v1/matches?startup_id={startup_id}")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert all(item["startup_id"] == str(startup_id) for item in data["items"])
        mock_match_service.list_matches.assert_called_with(
            startup_id=startup_id,
            vc_id=None,
            status=None,
            min_score=None
        )
    
    @pytest.mark.asyncio
    async def test_list_matches_filter_by_vc(
        self, test_client, mock_dependencies, mock_match_service, match_factory, vc_factory
    ):
        """Test filtering by VC ID."""
        # Arrange
        vc_id = uuid.uuid4()
        vc = vc_factory()
        vc.id = vc_id
        matches = []
        for _ in range(3):
            match = match_factory()
            match.vc = vc
            matches.append(match)
        mock_match_service.list_matches = AsyncMock(return_value=matches)
        
        # Act
        response = test_client.get(f"/api/v1/matches?vc_id={vc_id}")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert all(item["vc_id"] == str(vc_id) for item in data["items"])
    
    @pytest.mark.asyncio
    async def test_list_matches_filter_by_status(
        self, test_client, mock_dependencies, mock_match_service, match_factory
    ):
        """Test filtering by status."""
        # Arrange
        matches = [match_factory(status=MatchStatus.REVIEWED) for _ in range(3)]
        mock_match_service.list_matches = AsyncMock(return_value=matches)
        
        # Act
        response = test_client.get("/api/v1/matches?status=reviewed")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert all(item["status"] == "reviewed" for item in data["items"])
    
    @pytest.mark.asyncio
    async def test_list_matches_filter_by_min_score(
        self, test_client, mock_dependencies, mock_match_service, match_factory
    ):
        """Test filtering by minimum score."""
        # Arrange
        matches = [
            match_factory(score=0.8),
            match_factory(score=0.85),
            match_factory(score=0.9)
        ]
        mock_match_service.list_matches = AsyncMock(return_value=matches)
        
        # Act
        response = test_client.get("/api/v1/matches?min_score=0.75")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert all(item["score"] >= 0.75 for item in data["items"])
    
    @pytest.mark.asyncio
    async def test_list_matches_invalid_min_score(
        self, test_client, mock_dependencies
    ):
        """Test validation for invalid min_score."""
        # Test min_score < 0
        response = test_client.get("/api/v1/matches?min_score=-0.1")
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        
        # Test min_score > 1
        response = test_client.get("/api/v1/matches?min_score=1.5")
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    @pytest.mark.asyncio
    async def test_list_matches_empty_results(
        self, test_client, mock_dependencies, mock_match_service
    ):
        """Test listing when no matches exist."""
        # Arrange
        mock_match_service.list_matches = AsyncMock(return_value=[])
        
        # Act
        response = test_client.get("/api/v1/matches")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["items"] == []
        assert data["total"] == 0
        assert data["pages"] == 0
        assert data["average_score"] == 0.0
    
    @pytest.mark.asyncio
    async def test_list_matches_combined_filters(
        self, test_client, mock_dependencies, mock_match_service, match_factory, startup_factory
    ):
        """Test multiple filters combined."""
        # Arrange
        startup_id = uuid.uuid4()
        startup = startup_factory()
        startup.id = startup_id
        matches = []
        for _ in range(2):
            match = match_factory(status=MatchStatus.REVIEWED, score=0.9)
            match.startup = startup
            matches.append(match)
        mock_match_service.list_matches = AsyncMock(return_value=matches)
        
        # Act
        response = test_client.get(
            f"/api/v1/matches?startup_id={startup_id}&status=reviewed&min_score=0.8"
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data["items"]) == 2
        assert all(
            item["startup_id"] == str(startup_id) and
            item["status"] == "reviewed" and
            item["score"] >= 0.8
            for item in data["items"]
        )
    
    # Test Get Endpoint
    @pytest.mark.asyncio
    async def test_get_match_success(
        self, test_client, mock_dependencies, mock_match_service, sample_match
    ):
        """Test successful retrieval of a match."""
        # Arrange
        match_id = sample_match.id
        mock_match_service.get_match = AsyncMock(return_value=sample_match)
        
        # Act
        response = test_client.get(f"/api/v1/matches/{match_id}")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["id"] == str(match_id)
        assert data["score"] == sample_match.score
        assert data["reasons"] == sample_match.reasons
    
    @pytest.mark.asyncio
    async def test_get_match_not_found(
        self, test_client, mock_dependencies, mock_match_service
    ):
        """Test 404 when match not found."""
        # Arrange
        match_id = uuid.uuid4()
        mock_match_service.get_match = AsyncMock(
            side_effect=ValueError(f"Match {match_id} not found")
        )
        
        # Act
        response = test_client.get(f"/api/v1/matches/{match_id}")
        
        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND
        error_data = response.json()
        assert "not found" in error_data["error"]["detail"].lower()
    
    @pytest.mark.asyncio
    async def test_get_match_invalid_uuid(
        self, test_client, mock_dependencies
    ):
        """Test handling of invalid UUID."""
        # Act
        response = test_client.get("/api/v1/matches/not-a-uuid")
        
        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    # Test Update Endpoint
    @pytest.mark.asyncio
    async def test_update_match_success(
        self, test_client, mock_dependencies, mock_match_service,
        sample_match, auth_headers, mock_auth
    ):
        """Test successful match update."""
        # Arrange
        match_id = sample_match.id
        update_data = {
            "status": "reviewed",
            "notes": "Reviewed by investment committee"
        }
        updated_match = sample_match
        updated_match.status = MatchStatus.REVIEWED
        updated_match.notes = update_data["notes"]
        mock_match_service.update_match = AsyncMock(return_value=updated_match)
        
        # Act
        response = test_client.put(
            f"/api/v1/matches/{match_id}",
            json=update_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "reviewed"
        assert data["notes"] == update_data["notes"]
    
    @pytest.mark.asyncio
    async def test_update_match_partial_update(
        self, test_client, mock_dependencies, mock_match_service,
        sample_match, auth_headers, mock_auth
    ):
        """Test partial update with only some fields."""
        # Arrange
        match_id = sample_match.id
        update_data = {"next_steps": "Schedule follow-up call"}
        sample_match.next_steps = update_data["next_steps"]
        mock_match_service.update_match = AsyncMock(return_value=sample_match)
        
        # Act
        response = test_client.put(
            f"/api/v1/matches/{match_id}",
            json=update_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["next_steps"] == update_data["next_steps"]
    
    @pytest.mark.asyncio
    async def test_update_match_requires_auth(
        self, test_client, mock_dependencies, mock_no_auth
    ):
        """Test that updating a match requires authentication."""
        # Arrange
        match_id = uuid.uuid4()
        update_data = {"status": "reviewed"}
        
        # Act
        response = test_client.put(
            f"/api/v1/matches/{match_id}",
            json=update_data
        )
        
        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Authentication required" in response.json()["error"]["detail"]
    
    @pytest.mark.asyncio
    async def test_update_match_not_found(
        self, test_client, mock_dependencies, mock_match_service, auth_headers, mock_auth
    ):
        """Test 404 when updating non-existent match."""
        # Arrange
        match_id = uuid.uuid4()
        mock_match_service.update_match = AsyncMock(
            side_effect=ValueError(f"Match {match_id} not found")
        )
        
        # Act
        response = test_client.put(
            f"/api/v1/matches/{match_id}",
            json={"status": "reviewed"},
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    @pytest.mark.asyncio
    async def test_update_match_invalid_status(
        self, test_client, mock_dependencies, auth_headers, mock_auth
    ):
        """Test validation for invalid status."""
        # Arrange
        match_id = uuid.uuid4()
        update_data = {"status": "invalid_status"}
        
        # Act
        response = test_client.put(
            f"/api/v1/matches/{match_id}",
            json=update_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    @pytest.mark.asyncio
    async def test_update_match_notes_too_long(
        self, test_client, mock_dependencies, auth_headers, mock_auth
    ):
        """Test validation for notes exceeding max length."""
        # Arrange
        match_id = uuid.uuid4()
        update_data = {"notes": "x" * 2001}  # Max is 2000
        
        # Act
        response = test_client.put(
            f"/api/v1/matches/{match_id}",
            json=update_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    # Test Delete Endpoint
    @pytest.mark.asyncio
    async def test_delete_match_success(
        self, test_client, mock_dependencies, mock_match_service,
        mock_auth, auth_headers
    ):
        """Test successful match deletion."""
        # Arrange
        match_id = uuid.uuid4()
        mock_match_service.delete_match = AsyncMock(return_value=True)
        
        # Act
        response = test_client.delete(
            f"/api/v1/matches/{match_id}",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_204_NO_CONTENT
        assert response.content == b""
    
    @pytest.mark.asyncio
    async def test_delete_match_not_found(
        self, test_client, mock_dependencies, mock_match_service,
        mock_auth, auth_headers
    ):
        """Test 404 when deleting non-existent match."""
        # Arrange
        match_id = uuid.uuid4()
        mock_match_service.delete_match = AsyncMock(return_value=False)
        
        # Act
        response = test_client.delete(
            f"/api/v1/matches/{match_id}",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    @pytest.mark.asyncio
    async def test_delete_match_requires_auth(
        self, test_client, mock_dependencies, mock_no_auth
    ):
        """Test that deletion requires authentication."""
        # Arrange
        match_id = uuid.uuid4()
        
        # Act
        response = test_client.delete(f"/api/v1/matches/{match_id}")
        
        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Authentication required" in response.json()["error"]["detail"]
    
    # Test Batch Match Endpoint
    @pytest.mark.asyncio
    async def test_batch_match_success(
        self, test_client, mock_dependencies, mock_match_service,
        sample_batch_match_request, auth_headers, match_factory, mock_auth
    ):
        """Test successful batch matching."""
        # Arrange
        matches = [match_factory() for _ in range(4)]
        mock_match_service.batch_match = AsyncMock(return_value=matches)
        
        # Act
        response = test_client.post(
            "/api/v1/matches/batch",
            json=sample_batch_match_request,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["created"] == 4
        assert data["total_combinations"] == 6  # 3 startups * 2 VCs
        assert len(data["matches"]) == 4
        assert "timestamp" in data
    
    @pytest.mark.asyncio
    async def test_batch_match_requires_auth(
        self, test_client, mock_dependencies, mock_no_auth,
        sample_batch_match_request
    ):
        """Test that batch matching requires authentication."""
        # Act
        response = test_client.post(
            "/api/v1/matches/batch",
            json=sample_batch_match_request
        )
        
        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Authentication required for batch matching" in response.json()["error"]["detail"]
    
    @pytest.mark.asyncio
    async def test_batch_match_empty_startup_ids(
        self, test_client, mock_dependencies, auth_headers, mock_auth
    ):
        """Test validation for empty startup IDs."""
        # Arrange
        batch_request = {
            "startup_ids": [],
            "vc_ids": [str(uuid.uuid4())],
            "match_type": "ai_enhanced"
        }
        
        # Act
        response = test_client.post(
            "/api/v1/matches/batch",
            json=batch_request,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    @pytest.mark.asyncio
    async def test_batch_match_empty_vc_ids(
        self, test_client, mock_dependencies, auth_headers, mock_auth
    ):
        """Test validation for empty VC IDs."""
        # Arrange
        batch_request = {
            "startup_ids": [str(uuid.uuid4())],
            "vc_ids": [],
            "match_type": "ai_enhanced"
        }
        
        # Act
        response = test_client.post(
            "/api/v1/matches/batch",
            json=batch_request,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Both startup_ids and vc_ids must be non-empty" in response.json()["error"]["detail"]
    
    @pytest.mark.asyncio
    async def test_batch_match_invalid_score_threshold(
        self, test_client, mock_dependencies, auth_headers, mock_auth
    ):
        """Test validation for invalid score threshold."""
        # Arrange
        batch_request = {
            "startup_ids": [str(uuid.uuid4())],
            "vc_ids": [str(uuid.uuid4())],
            "min_score_threshold": 1.5  # Invalid: > 1.0
        }
        
        # Act
        response = test_client.post(
            "/api/v1/matches/batch",
            json=batch_request,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    @pytest.mark.asyncio
    async def test_batch_match_service_value_error(
        self, test_client, mock_dependencies, mock_match_service,
        sample_batch_match_request, auth_headers, mock_auth
    ):
        """Test handling of ValueError from batch match service."""
        # Arrange
        mock_match_service.batch_match = AsyncMock(
            side_effect=ValueError("Startup not found")
        )
        
        # Act
        response = test_client.post(
            "/api/v1/matches/batch",
            json=sample_batch_match_request,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Startup not found" in response.json()["error"]["detail"]
    
    @pytest.mark.asyncio
    async def test_batch_match_service_exception(
        self, test_client, mock_dependencies, mock_match_service,
        sample_batch_match_request, auth_headers, mock_auth
    ):
        """Test handling of general exception from batch match service."""
        # Arrange
        mock_match_service.batch_match = AsyncMock(
            side_effect=Exception("AI service unavailable")
        )
        
        # Act
        response = test_client.post(
            "/api/v1/matches/batch",
            json=sample_batch_match_request,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert "Batch matching failed" in response.json()["error"]["detail"]
        assert "AI service unavailable" in response.json()["error"]["detail"]
    
    # Test Rate Limiting
    @pytest.mark.asyncio
    async def test_rate_limiting_applied(
        self, test_client, mock_dependencies, mock_redis
    ):
        """Test that rate limiting is applied to all endpoints."""
        # Arrange
        mock_redis.incr.return_value = 61  # Exceed limit
        
        # Act & Assert - Test each endpoint
        endpoints = [
            ("POST", "/api/v1/matches", {"startup_id": str(uuid.uuid4()), "vc_id": str(uuid.uuid4())}),
            ("GET", "/api/v1/matches", None),
            ("GET", f"/api/v1/matches/{uuid.uuid4()}", None),
            ("PUT", f"/api/v1/matches/{uuid.uuid4()}", {"status": "reviewed"}),
            ("DELETE", f"/api/v1/matches/{uuid.uuid4()}", None),
            ("POST", "/api/v1/matches/batch", {"startup_ids": [str(uuid.uuid4())], "vc_ids": [str(uuid.uuid4())]}),
        ]
        
        for method, url, json_data in endpoints:
            response = test_client.request(method, url, json=json_data)
            # Rate limiting should be triggered
            assert response.status_code in [
                status.HTTP_429_TOO_MANY_REQUESTS,
                status.HTTP_401_UNAUTHORIZED,
                status.HTTP_422_UNPROCESSABLE_ENTITY
            ]
    
    # Test Edge Cases
    @pytest.mark.asyncio
    async def test_match_response_with_missing_startup_vc_names(
        self, test_client, mock_dependencies, mock_match_service, sample_match,
        startup_factory, vc_factory
    ):
        """Test match response when startup/VC names are not available."""
        # Arrange
        # Create match with startup/vc objects but set startup_id/vc_id directly
        startup = startup_factory()
        vc = vc_factory()
        match = Match(
            id=sample_match.id,
            startup=startup,
            vc=vc,
            startup_id=str(startup.id),
            vc_id=str(vc.id),
            score=sample_match.score,
            reasons=sample_match.reasons,
            status=sample_match.status,
            match_type=sample_match.match_type,
            created_at=sample_match.created_at,
            updated_at=sample_match.updated_at
        )
        mock_match_service.get_match = AsyncMock(return_value=match)
        
        # Act
        response = test_client.get(f"/api/v1/matches/{match.id}")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        # The response should have the names from the startup/vc objects
        assert data["startup_name"] == startup.name
        assert data["vc_firm_name"] == vc.firm_name
    
    @pytest.mark.asyncio
    async def test_match_compatibility_breakdown_calculation(
        self, test_client, mock_dependencies, mock_match_service, sample_match
    ):
        """Test compatibility breakdown calculation based on reasons."""
        # Arrange
        sample_match.reasons = [
            "Strong stage match",
            "Excellent sector alignment",
            "Aligns with investment thesis",
            "Funding amount within range"
        ]
        mock_match_service.get_match = AsyncMock(return_value=sample_match)
        
        # Act
        response = test_client.get(f"/api/v1/matches/{sample_match.id}")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        breakdown = data["compatibility_breakdown"]
        assert breakdown["stage_match"] == 1.0
        assert breakdown["sector_alignment"] == 0.8
        assert breakdown["thesis_fit"] == 0.9
        assert breakdown["check_size_fit"] == 1.0
    
    @pytest.mark.asyncio
    async def test_batch_match_with_defaults(
        self, test_client, mock_dependencies, mock_match_service,
        auth_headers, match_factory, mock_auth
    ):
        """Test batch match with default parameters."""
        # Arrange
        batch_request = {
            "startup_ids": [str(uuid.uuid4()) for _ in range(2)],
            "vc_ids": [str(uuid.uuid4()) for _ in range(2)]
            # No match_type or min_score_threshold specified
        }
        matches = [match_factory() for _ in range(2)]
        mock_match_service.batch_match = AsyncMock(return_value=matches)
        
        # Act
        response = test_client.post(
            "/api/v1/matches/batch",
            json=batch_request,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        # Verify defaults were used - check the call arguments
        call_args = mock_match_service.batch_match.call_args
        assert call_args is not None
        # Check kwargs
        kwargs = call_args.kwargs
        assert kwargs["match_type"] == MatchType.AI_ENHANCED  # Default
        assert kwargs["min_score_threshold"] == 0.5  # Default
    
    @pytest.mark.asyncio
    async def test_invalid_match_type(
        self, test_client, mock_dependencies, auth_headers, mock_auth
    ):
        """Test validation for invalid match type."""
        # Arrange
        match_request = {
            "startup_id": str(uuid.uuid4()),
            "vc_id": str(uuid.uuid4()),
            "match_type": "invalid_type"
        }
        
        # Act
        response = test_client.post(
            "/api/v1/matches",
            json=match_request,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    @pytest.mark.asyncio
    async def test_list_matches_invalid_status_filter(
        self, test_client, mock_dependencies
    ):
        """Test validation for invalid status in filter."""
        # Act
        response = test_client.get("/api/v1/matches?status=invalid_status")
        
        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    @pytest.mark.asyncio
    async def test_create_match_with_long_notes(
        self, test_client, mock_dependencies, auth_headers, mock_auth
    ):
        """Test validation for notes exceeding max length."""
        # Arrange
        match_request = {
            "startup_id": str(uuid.uuid4()),
            "vc_id": str(uuid.uuid4()),
            "notes": "x" * 2001  # Max is 2000
        }
        
        # Act
        response = test_client.post(
            "/api/v1/matches",
            json=match_request,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY