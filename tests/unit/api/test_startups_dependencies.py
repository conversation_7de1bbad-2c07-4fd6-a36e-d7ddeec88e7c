"""Test the dependency injection functions for startups endpoint."""

import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from sqlalchemy.orm import Session

from src.api.v1.endpoints.startups import (
    get_startup_repository,
    get_startup_service
)
from src.core.repositories.startup_repository import InMemoryStartupRepository
from src.database.repositories.startup_repository import PostgresStartupRepository
from src.core.services.startup_service import StartupService


class TestStartupsDependencyInjection:
    """Test the dependency injection functions."""
    
    @pytest.mark.asyncio
    async def test_get_startup_repository_returns_postgres_when_db_session_provided(self):
        """Test that get_startup_repository returns PostgresStartupRepository with db session."""
        # Arrange
        mock_db_session = Mock(spec=Session)
        
        # Act
        result = await get_startup_repository(db=mock_db_session)
        
        # Assert
        assert isinstance(result, PostgresStartupRepository)
        assert result.session == mock_db_session
    
    @pytest.mark.asyncio
    async def test_get_startup_repository_returns_in_memory_when_no_db_session(self):
        """Test that get_startup_repository returns InMemoryStartupRepository without db session."""
        # Act
        result = await get_startup_repository(db=None)
        
        # Assert
        assert isinstance(result, InMemoryStartupRepository)
    
    @pytest.mark.asyncio
    async def test_get_startup_repository_returns_in_memory_when_db_is_false(self):
        """Test that get_startup_repository returns InMemoryStartupRepository when db evaluates to False."""
        # Act - test with empty db object that evaluates to False
        result = await get_startup_repository(db=False)
        
        # Assert
        assert isinstance(result, InMemoryStartupRepository)
    
    @pytest.mark.asyncio
    async def test_get_startup_service_returns_service_with_dependencies(self):
        """Test that get_startup_service returns a properly configured StartupService."""
        # Arrange
        mock_repository = Mock()
        mock_ai_port = Mock()
        
        # Act
        result = await get_startup_service(
            repository=mock_repository,
            ai_port=mock_ai_port
        )
        
        # Assert
        assert isinstance(result, StartupService)
        assert result.repository == mock_repository
        assert result.ai_port == mock_ai_port
    
    @pytest.mark.asyncio
    async def test_get_startup_service_with_default_dependencies(self):
        """Test that get_startup_service can create service with default dependencies."""
        # We need to mock the dependency functions
        with patch('src.api.v1.endpoints.startups.get_startup_repository') as mock_get_repo:
            with patch('src.api.v1.endpoints.startups.get_ai_port') as mock_get_ai:
                mock_repo = Mock()
                mock_ai = Mock()
                mock_get_repo.return_value = mock_repo
                mock_get_ai.return_value = mock_ai
                
                # Act - simulating the default Depends() behavior
                result = await get_startup_service(
                    repository=mock_repo,
                    ai_port=mock_ai
                )
                
                # Assert
                assert isinstance(result, StartupService)
                assert result.repository == mock_repo
                assert result.ai_port == mock_ai