"""Unit tests for Startup API endpoints following TDD principles."""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from fastapi import status
from datetime import datetime
import uuid
import json

from src.core.models.startup import Startup
from src.core.schemas.startup import StartupCreate, StartupUpdate
from src.core.ai.models import StartupAnalysis


class TestStartupEndpoints:
    """Test startup CRUD operations and AI analysis endpoints."""
    
    @pytest.mark.asyncio
    async def test_create_startup_returns_201_with_valid_data(
        self, test_client, mock_db_session, auth_headers
    ):
        # Arrange
        startup_data = {
            "name": "AI Analytics Corp",
            "sector": "AI/ML",
            "stage": "Series A",
            "description": "We provide AI-powered analytics for businesses",
            "website": "https://aianalytics.com",
            "team_size": 15,
            "monthly_revenue": 75000
        }
        
        # Act
        response = test_client.post(
            "/api/v1/startups",
            json=startup_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["name"] == startup_data["name"]
        assert data["sector"] == startup_data["sector"]
        assert data["stage"] == startup_data["stage"]
        assert "id" in data
        assert "created_at" in data
    
    @pytest.mark.asyncio
    async def test_create_startup_validates_required_fields(self, test_client):
        # Arrange - Missing required fields
        startup_data = {
            "name": "Incomplete Startup"
            # Missing sector and stage
        }
        
        # Act
        response = test_client.post("/api/v1/startups", json=startup_data)
        
        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        error_response = response.json()
        errors = error_response["detail"]
        assert any("sector" in error["field"] for error in errors)
        assert any("stage" in error["field"] for error in errors)
    
    @pytest.mark.asyncio
    async def test_create_startup_extracts_sectors_with_ai_when_authenticated(
        self, test_client, mock_ai_analyzer, auth_headers, sample_startup_analysis
    ):
        # Arrange
        startup_data = {
            "name": "AI Analytics Corp",
            "sector": "Technology",
            "stage": "Series A",
            "description": "We provide AI-powered analytics for e-commerce businesses"
        }
        
        mock_ai_analyzer.analyze_startup = AsyncMock(return_value=sample_startup_analysis)
        
        # Act
        response = test_client.post(
            "/api/v1/startups",
            json=startup_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert "extracted_sectors" in data
        assert set(data["extracted_sectors"]) == set(sample_startup_analysis.sectors)
        # Note: AI analyzer is not directly called in the new architecture
    
    @pytest.mark.asyncio
    async def test_list_startups_returns_paginated_results(self, test_client):
        # Act
        response = test_client.get("/api/v1/startups?page=1&size=20")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "items" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data
        assert "pages" in data
        assert isinstance(data["items"], list)
    
    @pytest.mark.asyncio
    async def test_list_startups_filters_by_sector(self, test_client):
        # Act
        response = test_client.get("/api/v1/startups?sector=AI/ML")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        # All returned items should have the filtered sector
        for item in data["items"]:
            assert item["sector"] == "AI/ML"
    
    @pytest.mark.asyncio
    async def test_list_startups_validates_pagination_parameters(self, test_client):
        # Act - Invalid page number
        response = test_client.get("/api/v1/startups?page=0")
        
        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        error_response = response.json()
        assert "Page must be greater than 0" in error_response["error"]["detail"]
        
        # Act - Size too large
        response = test_client.get("/api/v1/startups?size=101")
        
        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Size must be between 1 and 100" in response.json()["error"]["detail"]
    
    @pytest.mark.asyncio
    async def test_get_startup_returns_404_when_not_found(self, test_client):
        # Act - Using invalid UUID format
        response = test_client.get("/api/v1/startups/not-a-valid-uuid")
        
        # Assert - The custom UUID handler returns 404 for invalid UUID format
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    @pytest.mark.asyncio
    async def test_get_startup_returns_404_with_valid_uuid(self, test_client):
        # Arrange - Valid UUID that doesn't exist
        startup_id = str(uuid.uuid4())
        
        # Act
        response = test_client.get(f"/api/v1/startups/{startup_id}")
        
        # Assert - Should return 404 for non-existent startup
        assert response.status_code == status.HTTP_404_NOT_FOUND
        data = response.json()
        assert "not found" in data["error"]["detail"].lower()
    
    @pytest.mark.asyncio
    async def test_update_startup_updates_only_provided_fields(
        self, test_client, auth_headers
    ):
        # Arrange
        startup_id = str(uuid.uuid4())
        update_data = {
            "team_size": 30,
            "monthly_revenue": 200000
        }
        
        # Act
        response = test_client.put(
            f"/api/v1/startups/{startup_id}",
            json=update_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["team_size"] == 30
        assert data["monthly_revenue"] == 200000
        # Other fields should remain unchanged
        assert "name" in data
        assert "sector" in data
    
    @pytest.mark.asyncio
    async def test_update_startup_returns_404_when_not_found(
        self, test_client, auth_headers
    ):
        # Act - Using invalid UUID
        response = test_client.put(
            "/api/v1/startups/not-a-valid-uuid",
            json={"team_size": 10},
            headers=auth_headers
        )
        
        # Assert - Custom UUID handler returns 404 for invalid UUID
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    @pytest.mark.asyncio
    async def test_delete_startup_requires_authentication(self, test_client):
        # Arrange
        startup_id = str(uuid.uuid4())
        
        # Act - No auth headers
        response = test_client.delete(f"/api/v1/startups/{startup_id}")
        
        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Authentication required" in response.json()["error"]["detail"]
    
    @pytest.mark.asyncio
    async def test_delete_startup_returns_204_when_successful(
        self, test_client, auth_headers
    ):
        # Arrange
        startup_id = str(uuid.uuid4())
        
        # Act
        response = test_client.delete(
            f"/api/v1/startups/{startup_id}",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_204_NO_CONTENT
        assert response.content == b""
    
    @pytest.mark.asyncio
    async def test_analyze_startup_requires_authentication(self, test_client):
        # Arrange
        startup_id = str(uuid.uuid4())
        
        # Act
        response = test_client.post(f"/api/v1/startups/{startup_id}/analyze")
        
        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Authentication required for AI analysis" in response.json()["detail"]
    
    @pytest.mark.asyncio
    async def test_analyze_startup_returns_ai_analysis(
        self, test_client, auth_headers, mock_ai_analyzer, sample_startup_analysis
    ):
        # Arrange
        startup_id = str(uuid.uuid4())
        mock_ai_analyzer.analyze_startup = AsyncMock(return_value=sample_startup_analysis)
        
        # Act
        response = test_client.post(
            f"/api/v1/startups/{startup_id}/analyze",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["startup_id"] == startup_id
        assert data["analysis"]["sectors"] == sample_startup_analysis.sectors
        assert data["analysis"]["value_proposition"] == sample_startup_analysis.value_proposition
        assert data["analysis"]["confidence_score"] == sample_startup_analysis.confidence_score
        assert "analyzed_at" in data
    
    @pytest.mark.asyncio
    async def test_analyze_startup_respects_force_refresh_parameter(
        self, test_client, auth_headers, mock_ai_analyzer, sample_startup_analysis
    ):
        # Arrange
        startup_id = str(uuid.uuid4())
        mock_ai_analyzer.analyze_startup = AsyncMock(return_value=sample_startup_analysis)
        
        # Act
        response = test_client.post(
            f"/api/v1/startups/{startup_id}/analyze?force_refresh=true",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        # In the new architecture, the service handles caching via AI port
    
    @pytest.mark.asyncio
    async def test_analyze_startup_handles_ai_service_errors(
        self, test_client, auth_headers, mock_ai_analyzer
    ):
        # Arrange
        startup_id = str(uuid.uuid4())
        mock_ai_analyzer.analyze_startup = AsyncMock(
            side_effect=Exception("OpenAI API error")
        )
        
        # Act
        response = test_client.post(
            f"/api/v1/startups/{startup_id}/analyze",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert "AI analysis failed" in response.json()["detail"]
    
    @pytest.mark.asyncio
    async def test_create_startup_handles_database_errors(
        self, test_client, mock_db_session
    ):
        # Arrange
        startup_data = {
            "name": "Test Startup",
            "sector": "Technology", 
            "stage": "Seed"
        }
        mock_db_session.commit.side_effect = Exception("Database error")
        
        # Act
        response = test_client.post("/api/v1/startups", json=startup_data)
        
        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert "Failed to create startup" in response.json()["detail"]