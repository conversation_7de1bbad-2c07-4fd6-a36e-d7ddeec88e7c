"""Unit tests for API middleware components."""

import pytest
from unittest.mock import Mo<PERSON>, AsyncMock, patch
from fastapi import H<PERSON><PERSON><PERSON><PERSON><PERSON>, Request, status
from datetime import datetime, timedelta
import jwt

from src.api.v1.deps import (
    RateLimiter,
    get_current_user,
    get_current_user_optional,
    require_admin,
    rate_limit_per_minute,
    rate_limit_per_hour,
    PaginationParams,
    require_api_key
)
from src.core.security import create_access_token, decode_access_token
from src.api.errors import UnauthorizedError, RateLimitError


class TestRateLimiter:
    """Test rate limiting functionality."""
    
    @pytest.fixture
    def rate_limiter(self, mock_redis):
        """Create rate limiter with mocked Redis."""
        return RateLimiter(mock_redis)
    
    @pytest.mark.asyncio
    async def test_allows_requests_within_limit(self, rate_limiter, mock_redis):
        # Arrange
        mock_redis.incr.return_value = 1
        
        # Act
        result = await rate_limiter.check_rate_limit(
            identifier="user123",
            limit=10,
            window=60
        )
        
        # Assert
        assert result is True
        mock_redis.incr.assert_called_once()
        mock_redis.expire.assert_called_once_with(
            "rate_limit:user123:60", 60
        )
    
    @pytest.mark.asyncio
    async def test_blocks_requests_exceeding_limit(self, rate_limiter, mock_redis):
        # Arrange
        mock_redis.incr.return_value = 11  # Over limit of 10
        
        # Act
        result = await rate_limiter.check_rate_limit(
            identifier="user123",
            limit=10,
            window=60
        )
        
        # Assert
        assert result is False
        mock_redis.incr.assert_called_once()
        mock_redis.expire.assert_not_called()  # Only set expiry on first request
    
    @pytest.mark.asyncio
    async def test_increments_counter_for_subsequent_requests(
        self, rate_limiter, mock_redis
    ):
        # Arrange
        mock_redis.incr.side_effect = [1, 2, 3]
        
        # Act
        result1 = await rate_limiter.check_rate_limit("user123", 5, 60)
        result2 = await rate_limiter.check_rate_limit("user123", 5, 60)
        result3 = await rate_limiter.check_rate_limit("user123", 5, 60)
        
        # Assert
        assert all([result1, result2, result3])
        assert mock_redis.incr.call_count == 3
        assert mock_redis.expire.call_count == 1  # Only on first request
    
    @pytest.mark.asyncio
    async def test_uses_different_keys_for_different_windows(
        self, rate_limiter, mock_redis
    ):
        # Arrange
        mock_redis.incr.return_value = 1
        
        # Act
        await rate_limiter.check_rate_limit("user123", 10, 60)
        await rate_limiter.check_rate_limit("user123", 100, 3600)
        
        # Assert
        assert mock_redis.incr.call_count == 2
        call_args = [call[0][0] for call in mock_redis.incr.call_args_list]
        assert "rate_limit:user123:60" in call_args
        assert "rate_limit:user123:3600" in call_args
    
    @pytest.mark.asyncio
    async def test_fails_open_when_redis_errors(self, rate_limiter, mock_redis):
        # Arrange
        mock_redis.incr.side_effect = Exception("Redis connection error")
        
        # Act
        result = await rate_limiter.check_rate_limit("user123", 10, 60)
        
        # Assert
        assert result is True  # Fail open to allow request


class TestAuthenticationDependencies:
    """Test authentication and authorization dependencies."""
    
    @pytest.fixture
    def mock_credentials(self):
        """Mock HTTP authorization credentials."""
        credentials = Mock()
        credentials.credentials = create_access_token({"sub": "user123"})
        return credentials
    
    @pytest.fixture
    def expired_credentials(self):
        """Mock expired credentials."""
        credentials = Mock()
        # Create token that's already expired
        expired_token = jwt.encode(
            {
                "sub": "user123",
                "exp": datetime.utcnow() - timedelta(hours=1)
            },
            "test-secret",
            algorithm="HS256"
        )
        credentials.credentials = expired_token
        return credentials
    
    @pytest.mark.asyncio
    async def test_get_current_user_optional_returns_none_without_credentials(self):
        # Act
        result = await get_current_user_optional(credentials=None)
        
        # Assert
        assert result is None
    
    @pytest.mark.asyncio
    async def test_get_current_user_optional_returns_user_with_valid_token(
        self, mock_credentials
    ):
        # Act
        with patch('src.core.security.decode_access_token') as mock_decode:
            mock_decode.return_value = Mock(sub="user123")
            result = await get_current_user_optional(mock_credentials)
        
        # Assert
        assert result == "user123"
    
    @pytest.mark.asyncio
    async def test_get_current_user_optional_returns_none_with_invalid_token(self):
        # Arrange
        credentials = Mock()
        credentials.credentials = "invalid-token"
        
        # Act
        with patch('src.core.security.decode_access_token') as mock_decode:
            mock_decode.return_value = None
            result = await get_current_user_optional(credentials)
        
        # Assert
        assert result is None
    
    @pytest.mark.asyncio
    async def test_get_current_user_raises_when_no_credentials(self):
        # Act & Assert
        with pytest.raises(UnauthorizedError):
            await get_current_user(credentials=None)
    
    @pytest.mark.asyncio
    async def test_get_current_user_raises_with_invalid_token(self):
        # Arrange
        credentials = Mock()
        credentials.credentials = "invalid-token"
        
        # Act & Assert
        with patch('src.core.security.decode_access_token') as mock_decode:
            mock_decode.return_value = None
            with pytest.raises(UnauthorizedError):
                await get_current_user(credentials)
    
    @pytest.mark.asyncio
    async def test_get_current_user_returns_user_id_with_valid_token(
        self, mock_credentials
    ):
        # Act
        with patch('src.core.security.decode_access_token') as mock_decode:
            mock_decode.return_value = Mock(sub="user123")
            result = await get_current_user(mock_credentials)
        
        # Assert
        assert result == "user123"
    
    @pytest.mark.asyncio
    async def test_require_admin_passes_through_user_id(self):
        # Arrange
        user_id = "admin123"
        
        # Act
        result = await require_admin(current_user=user_id)
        
        # Assert
        assert result == user_id
    
    @pytest.mark.asyncio
    async def test_require_api_key_raises_when_no_key(self):
        # Act & Assert
        with pytest.raises(UnauthorizedError, match="API key required"):
            await require_api_key(api_key=None)
    
    @pytest.mark.asyncio
    async def test_require_api_key_raises_with_invalid_key(self):
        # Act & Assert
        with pytest.raises(UnauthorizedError, match="Invalid API key"):
            await require_api_key(api_key="wrong-key")
    
    @pytest.mark.asyncio
    async def test_require_api_key_returns_key_when_valid(self):
        # Act
        result = await require_api_key(api_key="test-api-key")
        
        # Assert
        assert result == "test-api-key"


class TestRateLimitDependencies:
    """Test rate limiting dependency functions."""
    
    @pytest.fixture
    def mock_request(self):
        """Mock FastAPI request."""
        request = Mock(spec=Request)
        request.client.host = "***********"
        return request
    
    @pytest.fixture
    def mock_rate_limiter(self):
        """Mock rate limiter."""
        limiter = Mock(spec=RateLimiter)
        limiter.check_rate_limit = AsyncMock(return_value=True)
        return limiter
    
    @pytest.mark.asyncio
    async def test_rate_limit_per_minute_uses_user_id_when_authenticated(
        self, mock_request, mock_rate_limiter
    ):
        # Arrange
        current_user = "user123"
        
        # Act
        with patch('src.core.config.settings') as mock_settings:
            mock_settings.rate_limit_enabled = True
            mock_settings.rate_limit_per_minute = 60
            
            await rate_limit_per_minute(
                request=mock_request,
                rate_limiter=mock_rate_limiter,
                current_user=current_user
            )
        
        # Assert
        mock_rate_limiter.check_rate_limit.assert_called_once_with(
            identifier="user123",
            limit=60,
            window=60
        )
    
    @pytest.mark.asyncio
    async def test_rate_limit_per_minute_uses_ip_when_not_authenticated(
        self, mock_request, mock_rate_limiter
    ):
        # Act
        with patch('src.core.config.settings') as mock_settings:
            mock_settings.rate_limit_enabled = True
            mock_settings.rate_limit_per_minute = 60
            
            await rate_limit_per_minute(
                request=mock_request,
                rate_limiter=mock_rate_limiter,
                current_user=None
            )
        
        # Assert
        mock_rate_limiter.check_rate_limit.assert_called_once_with(
            identifier="***********",
            limit=60,
            window=60
        )
    
    @pytest.mark.asyncio
    async def test_rate_limit_per_minute_raises_when_limit_exceeded(
        self, mock_request, mock_rate_limiter
    ):
        # Arrange
        mock_rate_limiter.check_rate_limit.return_value = False
        
        # Act & Assert
        with patch('src.core.config.settings') as mock_settings:
            mock_settings.rate_limit_enabled = True
            mock_settings.rate_limit_per_minute = 60
            
            with pytest.raises(RateLimitError, match="60 requests per minute"):
                await rate_limit_per_minute(
                    request=mock_request,
                    rate_limiter=mock_rate_limiter,
                    current_user=None
                )
    
    @pytest.mark.asyncio
    async def test_rate_limit_skips_when_disabled(
        self, mock_request, mock_rate_limiter
    ):
        # Act
        with patch('src.core.config.settings') as mock_settings:
            mock_settings.rate_limit_enabled = False
            
            await rate_limit_per_minute(
                request=mock_request,
                rate_limiter=mock_rate_limiter,
                current_user=None
            )
        
        # Assert
        mock_rate_limiter.check_rate_limit.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_rate_limit_per_hour_uses_correct_window(
        self, mock_request, mock_rate_limiter
    ):
        # Act
        with patch('src.core.config.settings') as mock_settings:
            mock_settings.rate_limit_enabled = True
            mock_settings.rate_limit_per_hour = 1000
            
            await rate_limit_per_hour(
                request=mock_request,
                rate_limiter=mock_rate_limiter,
                current_user="user123"
            )
        
        # Assert
        mock_rate_limiter.check_rate_limit.assert_called_once_with(
            identifier="user123",
            limit=1000,
            window=3600
        )


class TestPaginationParams:
    """Test pagination parameter validation."""
    
    def test_creates_with_default_values(self):
        # Act
        pagination = PaginationParams()
        
        # Assert
        assert pagination.page == 1
        assert pagination.size == 20
        assert pagination.offset == 0
    
    def test_calculates_offset_correctly(self):
        # Act
        pagination = PaginationParams(page=3, size=25)
        
        # Assert
        assert pagination.offset == 50  # (3-1) * 25
    
    def test_validates_page_must_be_positive(self):
        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            PaginationParams(page=0)
        
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "Page must be greater than 0" in str(exc_info.value.detail)
    
    def test_validates_size_minimum(self):
        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            PaginationParams(size=0)
        
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "Size must be between 1 and 100" in str(exc_info.value.detail)
    
    def test_validates_size_maximum(self):
        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            PaginationParams(size=101)
        
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "Size must be between 1 and 100" in str(exc_info.value.detail)
    
    def test_accepts_valid_parameters(self):
        # Act
        pagination = PaginationParams(page=5, size=50)
        
        # Assert
        assert pagination.page == 5
        assert pagination.size == 50
        assert pagination.offset == 200  # (5-1) * 50