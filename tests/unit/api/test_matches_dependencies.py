"""Test the dependency injection functions for matches endpoint."""

import pytest
from unittest.mock import Mock, AsyncMock, patch

from src.api.v1.endpoints.matches import (
    get_startup_repository,
    get_vc_repository,
    get_matching_service
)
from src.core.repositories.startup_repository import InMemoryStartupRepository
from src.core.repositories.vc_repository import InMemoryVCRepository
from src.core.services.match_service import MatchService


class TestMatchesDependencyInjection:
    """Test the dependency injection functions."""
    
    @pytest.mark.asyncio
    async def test_get_startup_repository_returns_in_memory_repository(self):
        """Test that get_startup_repository returns an InMemoryStartupRepository."""
        # Act
        result = await get_startup_repository()
        
        # Assert
        assert isinstance(result, InMemoryStartupRepository)
    
    @pytest.mark.asyncio
    async def test_get_vc_repository_returns_in_memory_repository(self):
        """Test that get_vc_repository returns an InMemoryVCRepository."""
        # Act
        result = await get_vc_repository()
        
        # Assert
        assert isinstance(result, InMemoryVCRepository)
    
    @pytest.mark.asyncio
    async def test_get_matching_service_returns_service_with_dependencies(self):
        """Test that get_matching_service returns a properly configured MatchService."""
        # Arrange
        mock_startup_repo = Mock()
        mock_vc_repo = Mock()
        mock_ai_port = Mock()
        
        # Act
        result = await get_matching_service(
            startup_repo=mock_startup_repo,
            vc_repo=mock_vc_repo,
            ai_port=mock_ai_port
        )
        
        # Assert
        assert isinstance(result, MatchService)
        assert result.startup_repo == mock_startup_repo
        assert result.vc_repo == mock_vc_repo
        assert result.ai_port == mock_ai_port
    
    @pytest.mark.asyncio
    async def test_get_matching_service_with_default_dependencies(self):
        """Test that get_matching_service can create service with default dependencies."""
        # We need to mock the get_ai_port dependency
        with patch('src.api.v1.endpoints.matches.get_ai_port') as mock_get_ai_port:
            mock_ai_port = Mock()
            mock_get_ai_port.return_value = mock_ai_port
            
            # Call the function with its default Depends() behavior
            # We need to manually inject the dependencies since we're not in a FastAPI context
            startup_repo = await get_startup_repository()
            vc_repo = await get_vc_repository()
            
            # Act
            result = await get_matching_service(
                startup_repo=startup_repo,
                vc_repo=vc_repo,
                ai_port=mock_ai_port
            )
            
            # Assert
            assert isinstance(result, MatchService)
            assert isinstance(result.startup_repo, InMemoryStartupRepository)
            assert isinstance(result.vc_repo, InMemoryVCRepository)
            assert result.ai_port == mock_ai_port