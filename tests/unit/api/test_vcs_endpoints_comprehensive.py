"""Comprehensive unit tests for VC API endpoints with full coverage."""

import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from fastapi import status
from datetime import datetime
import uuid
import json
import html
from typing import List, Optional

from src.core.models.vc import VC
from src.core.schemas.vc import VCCreate, VCUpdate, ThesisExtractionRequest
from src.core.ports.ai_port import VCInsights, AIAnalysisError
from src.core.ai.models import VCThesisAnalysis, InvestmentFocus, InvestmentStage
from src.core.services.vc_service import VCService
from src.core.repositories.vc_repository import VCRepository
from src.api.errors import NotFoundError, BadRequestError


class TestVCEndpointsComprehensive:
    """Comprehensive test suite for VC API endpoints with full coverage."""
    
    @pytest.fixture
    def mock_vc_repository(self):
        """Mock VC repository."""
        repo = Mock(spec=VCRepository)
        repo.save = AsyncMock()
        repo.find_by_id = AsyncMock()
        repo.find_all = AsyncMock(return_value=[])
        repo.find_by_sector = AsyncMock(return_value=[])
        repo.find_by_stage = AsyncMock(return_value=[])
        repo.delete = AsyncMock(return_value=True)
        return repo
    
    @pytest.fixture
    def mock_ai_port(self):
        """Mock AI port."""
        port = Mock()
        port.analyze_vc = AsyncMock()
        port.get_usage_stats = Mock(return_value={})
        return port
    
    @pytest.fixture
    def mock_vc_service(self, mock_vc_repository, mock_ai_port):
        """Mock VC service."""
        service = Mock(spec=VCService)
        service.repository = mock_vc_repository
        service.ai_port = mock_ai_port
        return service
    
    @pytest.fixture
    def mock_dependencies(self, test_client, mock_vc_service, mock_db_session):
        """Override dependencies with mocks."""
        from src.api.v1.endpoints.vcs import get_vc_service, get_vc_repository
        from src.api.v1.deps import get_current_user_optional
        from src.api.main import app
        
        # Mock the repository dependency
        async def mock_get_repository(db=None):
            return mock_vc_service.repository
        
        # Mock the service dependency
        async def mock_get_service(repository=None, ai_port=None):
            return mock_vc_service
        
        app.dependency_overrides[get_vc_repository] = mock_get_repository
        app.dependency_overrides[get_vc_service] = mock_get_service
        
        yield
        
        app.dependency_overrides.clear()
    
    @pytest.fixture
    def mock_auth(self):
        """Mock authentication dependencies."""
        from src.api.v1.deps import get_current_user_optional
        from src.api.main import app
        
        # Mock auth to return a user
        async def mock_get_current_user_optional(credentials=None):
            return "test-user"
        
        app.dependency_overrides[get_current_user_optional] = mock_get_current_user_optional
        
        yield
        
        # Clean up only this override
        app.dependency_overrides.pop(get_current_user_optional, None)
    
    @pytest.fixture
    def no_auth(self):
        """Mock authentication to return None."""
        from src.api.v1.deps import get_current_user_optional
        from src.api.main import app
        
        # Mock auth to return None
        async def mock_get_current_user_optional(credentials=None):
            return None
        
        app.dependency_overrides[get_current_user_optional] = mock_get_current_user_optional
        
        yield
        
        # Clean up only this override
        app.dependency_overrides.pop(get_current_user_optional, None)
    
    @pytest.fixture
    def sample_vc_data(self):
        """Sample VC data for testing."""
        return {
            "firm_name": "AI Capital Partners",
            "website": "https://aicapital.com",
            "focus_areas": ["AI/ML", "B2B SaaS", "Analytics"],
            "stage_preferences": ["Series A", "Series B"],
            "check_size_min": 2000000,
            "check_size_max": 10000000,
            "portfolio_companies": ["TechCorp", "DataCo"],
            "partners": ["John Smith", "Jane Doe"]
        }
    
    @pytest.fixture
    def sample_vc_thesis_analysis(self):
        """Sample VC thesis analysis from AI."""
        return VCThesisAnalysis(
            thesis_summary="Focus on AI-first B2B SaaS companies with strong technical teams",
            investment_focus=InvestmentFocus(
                sectors=["AI/ML", "B2B SaaS", "Analytics", "Enterprise Software"],
                stages=[InvestmentStage.SERIES_A, InvestmentStage.SERIES_B],
                geographical_focus=["US", "Europe"]
            ),
            check_size_range={"min": 2000000, "max": 10000000},
            portfolio_themes=["AI/ML", "Data Analytics", "Enterprise SaaS"],
            avoided_sectors=["Consumer Hardware", "Gaming"],
            key_criteria=[
                "Strong technical founding team",
                "Clear AI/ML differentiation",
                "Proven product-market fit",
                "$1M+ ARR"
            ],
            notable_partners=["John Smith", "Jane Doe"],
            confidence_score=0.85
        )
    
    # Test Create Endpoint
    @pytest.mark.asyncio
    async def test_create_vc_success(
        self, test_client, mock_dependencies, mock_vc_service,
        sample_vc_data, auth_headers, sample_vc
    ):
        """Test successful VC creation."""
        # Arrange
        sample_vc.id = uuid.uuid4()
        sample_vc.firm_name = html.escape(sample_vc_data["firm_name"])
        mock_vc_service.create_vc = AsyncMock(return_value=sample_vc)
        
        # Act
        response = test_client.post(
            "/api/v1/vcs",
            json=sample_vc_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["firm_name"] == html.escape(sample_vc_data["firm_name"])
        assert data["focus_areas"] == sample_vc.sectors  # Field mapping
        assert data["stage_preferences"] == sample_vc.stages  # Field mapping
        assert "id" in data
        assert "created_at" in data
    
    @pytest.mark.asyncio
    async def test_create_vc_without_auth(
        self, test_client, mock_dependencies, mock_vc_service,
        sample_vc_data, sample_vc
    ):
        """Test creating VC without authentication (should work)."""
        # Arrange
        sample_vc.id = uuid.uuid4()
        sample_vc.firm_name = html.escape(sample_vc_data["firm_name"])
        mock_vc_service.create_vc = AsyncMock(return_value=sample_vc)
        
        # Act
        response = test_client.post(
            "/api/v1/vcs",
            json=sample_vc_data
        )
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
    
    @pytest.mark.asyncio
    async def test_create_vc_html_escaping(
        self, test_client, mock_dependencies, mock_vc_service,
        auth_headers, vc_factory
    ):
        """Test HTML escaping in VC creation."""
        # Arrange
        vc_data = {
            "firm_name": "<script>alert('XSS')</script> Ventures",
            "website": "https://example.com",
            "focus_areas": ["Technology"],
            "stage_preferences": ["Seed"]
        }
        escaped_vc = vc_factory(
            firm_name=html.escape(vc_data["firm_name"]),
            sectors=vc_data["focus_areas"],
            stages=vc_data["stage_preferences"]
        )
        mock_vc_service.create_vc = AsyncMock(return_value=escaped_vc)
        
        # Act
        response = test_client.post(
            "/api/v1/vcs",
            json=vc_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["firm_name"] == html.escape(vc_data["firm_name"])
    
    @pytest.mark.asyncio
    async def test_create_vc_missing_required_fields(
        self, test_client, mock_dependencies
    ):
        """Test validation for missing required fields."""
        # Arrange
        vc_data = {"website": "https://example.com"}
        
        # Act
        response = test_client.post("/api/v1/vcs", json=vc_data)
        
        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        error_data = response.json()
        assert "detail" in error_data
        errors = error_data["detail"]
        assert any("firm_name" in error["field"] for error in errors)
    
    @pytest.mark.asyncio
    async def test_create_vc_invalid_stage(
        self, test_client, mock_dependencies
    ):
        """Test validation for invalid stage."""
        # Arrange
        vc_data = {
            "firm_name": "Test VC",
            "website": "https://example.com",
            "focus_areas": ["Technology"],
            "stage_preferences": ["Invalid Stage"]
        }
        
        # Act
        response = test_client.post("/api/v1/vcs", json=vc_data)
        
        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    @pytest.mark.asyncio
    async def test_create_vc_invalid_check_sizes(
        self, test_client, mock_dependencies
    ):
        """Test validation for invalid check sizes."""
        # Arrange
        vc_data = {
            "firm_name": "Test VC",
            "check_size_min": 10000000,
            "check_size_max": 5000000  # Max less than min
        }
        
        # Act
        response = test_client.post("/api/v1/vcs", json=vc_data)
        
        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        error_data = response.json()
        assert "detail" in error_data
    
    @pytest.mark.asyncio
    async def test_create_vc_website_url_normalization(
        self, test_client, mock_dependencies, mock_vc_service, vc_factory
    ):
        """Test website URL normalization."""
        # Arrange
        vc_data = {
            "firm_name": "Test VC",
            "website": "example.com"  # Missing protocol
        }
        normalized_vc = vc_factory(
            firm_name=vc_data["firm_name"],
            website="https://example.com"
        )
        mock_vc_service.create_vc = AsyncMock(return_value=normalized_vc)
        
        # Act
        response = test_client.post("/api/v1/vcs", json=vc_data)
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["website"] == "https://example.com"
    
    @pytest.mark.asyncio
    async def test_create_vc_service_value_error(
        self, test_client, mock_dependencies, mock_vc_service, sample_vc_data
    ):
        """Test handling of ValueError from service."""
        # Arrange
        mock_vc_service.create_vc = AsyncMock(
            side_effect=ValueError("Invalid VC data")
        )
        
        # Act
        response = test_client.post("/api/v1/vcs", json=sample_vc_data)
        
        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Invalid VC data" in response.json()["error"]["detail"]
    
    @pytest.mark.asyncio
    async def test_create_vc_service_exception(
        self, test_client, mock_dependencies, mock_vc_service, sample_vc_data
    ):
        """Test handling of general exception from service."""
        # Arrange
        mock_vc_service.create_vc = AsyncMock(
            side_effect=Exception("Database error")
        )
        
        # Act
        response = test_client.post("/api/v1/vcs", json=sample_vc_data)
        
        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert "Failed to create VC" in response.json()["error"]["detail"]
    
    # Test List Endpoint
    @pytest.mark.asyncio
    async def test_list_vcs_success(
        self, test_client, mock_dependencies, mock_vc_service, vc_factory
    ):
        """Test successful VC listing."""
        # Arrange
        vcs = [vc_factory() for _ in range(5)]
        mock_vc_service.list_vcs = AsyncMock(return_value=vcs)
        
        # Act
        response = test_client.get("/api/v1/vcs")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data["items"]) == 5
        assert data["total"] == 5
        assert data["page"] == 1
        assert data["size"] == 20
        assert data["pages"] == 1
    
    @pytest.mark.asyncio
    async def test_list_vcs_with_pagination(
        self, test_client, mock_dependencies, mock_vc_service, vc_factory
    ):
        """Test VC listing with pagination."""
        # Arrange
        vcs = [vc_factory() for _ in range(50)]
        mock_vc_service.list_vcs = AsyncMock(return_value=vcs)
        
        # Act
        response = test_client.get("/api/v1/vcs?page=2&size=10")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data["items"]) == 10
        assert data["total"] == 50
        assert data["page"] == 2
        assert data["size"] == 10
        assert data["pages"] == 5
    
    @pytest.mark.asyncio
    async def test_list_vcs_with_sector_filter(
        self, test_client, mock_dependencies, mock_vc_service, vc_factory
    ):
        """Test VC listing with sector filter."""
        # Arrange
        vcs = [vc_factory(sectors=["AI/ML"]) for _ in range(3)]
        mock_vc_service.list_vcs = AsyncMock(return_value=vcs)
        
        # Act
        response = test_client.get("/api/v1/vcs?sector=AI/ML")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data["items"]) == 3
        # Verify service was called with correct filter
        mock_vc_service.list_vcs.assert_called_once_with(
            sector="AI/ML",
            stage=None
        )
    
    @pytest.mark.asyncio
    async def test_list_vcs_with_stage_filter(
        self, test_client, mock_dependencies, mock_vc_service, vc_factory
    ):
        """Test VC listing with stage filter."""
        # Arrange
        vcs = [vc_factory(stages=["Series A"]) for _ in range(2)]
        mock_vc_service.list_vcs = AsyncMock(return_value=vcs)
        
        # Act
        response = test_client.get("/api/v1/vcs?stage=Series A")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data["items"]) == 2
        # Verify service was called with correct filter
        mock_vc_service.list_vcs.assert_called_once_with(
            sector=None,
            stage="Series A"
        )
    
    @pytest.mark.asyncio
    async def test_list_vcs_with_multiple_filters(
        self, test_client, mock_dependencies, mock_vc_service
    ):
        """Test VC listing with multiple filters."""
        # Arrange
        mock_vc_service.list_vcs = AsyncMock(return_value=[])
        
        # Act
        response = test_client.get("/api/v1/vcs?sector=AI/ML&stage=Series A")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        # Verify service was called with both filters
        mock_vc_service.list_vcs.assert_called_once_with(
            sector="AI/ML",
            stage="Series A"
        )
    
    @pytest.mark.asyncio
    async def test_list_vcs_empty_results(
        self, test_client, mock_dependencies, mock_vc_service
    ):
        """Test VC listing with no results."""
        # Arrange
        mock_vc_service.list_vcs = AsyncMock(return_value=[])
        
        # Act
        response = test_client.get("/api/v1/vcs")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data["items"]) == 0
        assert data["total"] == 0
        assert data["pages"] == 0
    
    @pytest.mark.asyncio
    async def test_list_vcs_invalid_pagination(
        self, test_client, mock_dependencies
    ):
        """Test VC listing with invalid pagination parameters."""
        # Act & Assert - Invalid page
        response = test_client.get("/api/v1/vcs?page=0")
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        
        # Act & Assert - Invalid size
        response = test_client.get("/api/v1/vcs?size=0")
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        
        # Act & Assert - Size too large
        response = test_client.get("/api/v1/vcs?size=101")
        assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    # Test Get Endpoint
    @pytest.mark.asyncio
    async def test_get_vc_success(
        self, test_client, mock_dependencies, mock_vc_service, sample_vc
    ):
        """Test successful VC retrieval."""
        # Arrange
        vc_id = uuid.uuid4()
        sample_vc.id = vc_id
        mock_vc_service.get_vc = AsyncMock(return_value=sample_vc)
        
        # Act
        response = test_client.get(f"/api/v1/vcs/{vc_id}")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["id"] == str(vc_id)
        assert data["firm_name"] == sample_vc.firm_name
    
    @pytest.mark.asyncio
    async def test_get_vc_not_found(
        self, test_client, mock_dependencies, mock_vc_service
    ):
        """Test getting non-existent VC."""
        # Arrange
        vc_id = uuid.uuid4()
        mock_vc_service.get_vc = AsyncMock(
            side_effect=ValueError(f"VC with id {vc_id} not found")
        )
        
        # Act
        response = test_client.get(f"/api/v1/vcs/{vc_id}")
        
        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert f"VC with identifier '{vc_id}' not found" in response.json()["error"]["detail"]
    
    @pytest.mark.asyncio
    async def test_get_vc_invalid_uuid(
        self, test_client, mock_dependencies, mock_vc_service
    ):
        """Test getting VC with invalid UUID."""
        # Arrange
        mock_vc_service.get_vc = AsyncMock(
            side_effect=ValueError("VC with id invalid-uuid not found")
        )
        
        # Act
        response = test_client.get("/api/v1/vcs/invalid-uuid")
        
        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    # Test Update Endpoint
    @pytest.mark.asyncio
    async def test_update_vc_success(
        self, test_client, mock_dependencies, mock_vc_service,
        sample_vc, auth_headers
    ):
        """Test successful VC update."""
        # Arrange
        vc_id = uuid.uuid4()
        update_data = {
            "firm_name": "Updated VC Name",
            "check_size_min": 3000000
        }
        updated_vc = sample_vc
        updated_vc.id = vc_id
        updated_vc.firm_name = update_data["firm_name"]
        updated_vc.check_size_min = update_data["check_size_min"]
        mock_vc_service.update_vc = AsyncMock(return_value=updated_vc)
        
        # Act
        response = test_client.put(
            f"/api/v1/vcs/{vc_id}",
            json=update_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["firm_name"] == update_data["firm_name"]
        assert data["check_size_min"] == update_data["check_size_min"]
    
    @pytest.mark.asyncio
    async def test_update_vc_partial_update(
        self, test_client, mock_dependencies, mock_vc_service,
        sample_vc, auth_headers
    ):
        """Test partial VC update."""
        # Arrange
        vc_id = uuid.uuid4()
        update_data = {"website": "https://newsite.com"}
        updated_vc = sample_vc
        updated_vc.id = vc_id
        updated_vc.website = update_data["website"]
        mock_vc_service.update_vc = AsyncMock(return_value=updated_vc)
        
        # Act
        response = test_client.put(
            f"/api/v1/vcs/{vc_id}",
            json=update_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["website"] == update_data["website"]
        # Verify service was called with only the provided fields
        mock_vc_service.update_vc.assert_called_once_with(
            vc_id,
            update_data
        )
    
    @pytest.mark.asyncio
    async def test_update_vc_not_found(
        self, test_client, mock_dependencies, mock_vc_service, auth_headers
    ):
        """Test updating non-existent VC."""
        # Arrange
        vc_id = uuid.uuid4()
        update_data = {"firm_name": "Updated Name"}
        mock_vc_service.update_vc = AsyncMock(
            side_effect=ValueError(f"VC with id {vc_id} not found")
        )
        
        # Act
        response = test_client.put(
            f"/api/v1/vcs/{vc_id}",
            json=update_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    @pytest.mark.asyncio
    async def test_update_vc_invalid_data(
        self, test_client, mock_dependencies, mock_vc_service, auth_headers
    ):
        """Test updating VC with invalid data."""
        # Arrange
        vc_id = uuid.uuid4()
        update_data = {"stage_preferences": ["Invalid Stage"]}
        mock_vc_service.update_vc = AsyncMock(
            side_effect=ValueError("Invalid stage: Invalid Stage")
        )
        
        # Act
        response = test_client.put(
            f"/api/v1/vcs/{vc_id}",
            json=update_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Invalid stage" in response.json()["error"]["detail"]
    
    @pytest.mark.asyncio
    async def test_update_vc_service_error(
        self, test_client, mock_dependencies, mock_vc_service, auth_headers
    ):
        """Test handling of non-ValueError from service during update."""
        # Arrange
        vc_id = uuid.uuid4()
        update_data = {"firm_name": "Updated Name"}
        mock_vc_service.update_vc = AsyncMock(
            side_effect=ValueError("Some other error")
        )
        
        # Act
        response = test_client.put(
            f"/api/v1/vcs/{vc_id}",
            json=update_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Some other error" in response.json()["error"]["detail"]
    
    # Test Delete Endpoint
    @pytest.mark.asyncio
    async def test_delete_vc_success(
        self, test_client, mock_dependencies, mock_vc_service, mock_auth
    ):
        """Test successful VC deletion."""
        # Arrange
        vc_id = uuid.uuid4()
        mock_vc_service.delete_vc = AsyncMock(return_value=True)
        
        # Act
        response = test_client.delete(f"/api/v1/vcs/{vc_id}")
        
        # Assert
        assert response.status_code == status.HTTP_204_NO_CONTENT
        assert response.content == b''
    
    @pytest.mark.asyncio
    async def test_delete_vc_not_found(
        self, test_client, mock_dependencies, mock_vc_service, mock_auth
    ):
        """Test deleting non-existent VC."""
        # Arrange
        vc_id = uuid.uuid4()
        mock_vc_service.delete_vc = AsyncMock(return_value=False)
        
        # Act
        response = test_client.delete(f"/api/v1/vcs/{vc_id}")
        
        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    @pytest.mark.asyncio
    async def test_delete_vc_no_auth(
        self, test_client, mock_dependencies, mock_vc_service, no_auth
    ):
        """Test deleting VC without authentication."""
        # Arrange
        vc_id = uuid.uuid4()
        
        # Act
        response = test_client.delete(f"/api/v1/vcs/{vc_id}")
        
        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Authentication required" in response.json()["error"]["detail"]
    
    @pytest.mark.asyncio
    async def test_delete_vc_invalid_uuid(
        self, test_client, mock_dependencies, mock_vc_service, mock_auth
    ):
        """Test deleting VC with invalid UUID."""
        # Arrange
        mock_vc_service.delete_vc = AsyncMock(return_value=False)
        
        # Act
        response = test_client.delete("/api/v1/vcs/invalid-uuid")
        
        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    # Test Extract Thesis Endpoint
    @pytest.mark.asyncio
    async def test_extract_thesis_success(
        self, test_client, mock_dependencies, mock_vc_service,
        sample_vc_thesis_analysis, mock_auth
    ):
        """Test successful thesis extraction."""
        # Arrange
        vc_id = uuid.uuid4()
        request_data = {
            "website_content": "We invest in AI-first B2B SaaS companies...",
            "force_refresh": False
        }
        mock_vc_service.extract_thesis = AsyncMock(return_value=sample_vc_thesis_analysis)
        
        # Act
        response = test_client.post(
            f"/api/v1/vcs/{vc_id}/extract-thesis",
            json=request_data
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["vc_id"] == str(vc_id)
        assert "thesis" in data
        assert data["thesis"]["summary"] == sample_vc_thesis_analysis.thesis_summary
        assert data["thesis"]["sectors"] == sample_vc_thesis_analysis.investment_focus.sectors
        assert data["thesis"]["stages"] == [stage.value for stage in sample_vc_thesis_analysis.investment_focus.stages]
        assert data["thesis"]["check_sizes"] == sample_vc_thesis_analysis.check_size_range
        assert data["thesis"]["geographic_focus"] == sample_vc_thesis_analysis.investment_focus.geographical_focus
        assert data["thesis"]["portfolio_traits"] == sample_vc_thesis_analysis.portfolio_themes
        assert data["thesis"]["competitive_advantages"] == sample_vc_thesis_analysis.key_criteria
        assert data["thesis"]["confidence_score"] == sample_vc_thesis_analysis.confidence_score
        assert "extracted_at" in data
    
    @pytest.mark.asyncio
    async def test_extract_thesis_no_auth(
        self, test_client, mock_dependencies, no_auth
    ):
        """Test thesis extraction without authentication."""
        # Arrange
        vc_id = uuid.uuid4()
        request_data = {
            "website_content": "Content...",
            "force_refresh": False
        }
        
        # Act
        response = test_client.post(
            f"/api/v1/vcs/{vc_id}/extract-thesis",
            json=request_data
        )
        
        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Authentication required for thesis extraction" in response.json()["error"]["detail"]
    
    @pytest.mark.asyncio
    async def test_extract_thesis_vc_not_found(
        self, test_client, mock_dependencies, mock_vc_service, mock_auth
    ):
        """Test thesis extraction for non-existent VC."""
        # Arrange
        vc_id = uuid.uuid4()
        request_data = {
            "website_content": "Content...",
            "force_refresh": False
        }
        mock_vc_service.extract_thesis = AsyncMock(
            side_effect=ValueError(f"VC with id {vc_id} not found")
        )
        
        # Act
        response = test_client.post(
            f"/api/v1/vcs/{vc_id}/extract-thesis",
            json=request_data
        )
        
        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    @pytest.mark.asyncio
    async def test_extract_thesis_missing_content(
        self, test_client, mock_dependencies, mock_auth
    ):
        """Test thesis extraction without website content."""
        # Arrange
        vc_id = uuid.uuid4()
        request_data = {"force_refresh": False}
        
        # Act
        response = test_client.post(
            f"/api/v1/vcs/{vc_id}/extract-thesis",
            json=request_data
        )
        
        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    @pytest.mark.asyncio
    async def test_extract_thesis_with_force_refresh(
        self, test_client, mock_dependencies, mock_vc_service,
        sample_vc_thesis_analysis, mock_auth
    ):
        """Test thesis extraction with force refresh."""
        # Arrange
        vc_id = uuid.uuid4()
        request_data = {
            "website_content": "Updated content...",
            "force_refresh": True
        }
        mock_vc_service.extract_thesis = AsyncMock(return_value=sample_vc_thesis_analysis)
        
        # Act
        response = test_client.post(
            f"/api/v1/vcs/{vc_id}/extract-thesis",
            json=request_data
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        # Verify service was called with force_refresh=True
        mock_vc_service.extract_thesis.assert_called_once_with(
            vc_id,
            request_data["website_content"],
            force_refresh=True
        )
    
    @pytest.mark.asyncio
    async def test_extract_thesis_ai_error(
        self, test_client, mock_dependencies, mock_vc_service, mock_auth
    ):
        """Test thesis extraction with AI analysis error."""
        # Arrange
        vc_id = uuid.uuid4()
        request_data = {
            "website_content": "Content...",
            "force_refresh": False
        }
        mock_vc_service.extract_thesis = AsyncMock(
            side_effect=Exception("AI service unavailable")
        )
        
        # Act
        response = test_client.post(
            f"/api/v1/vcs/{vc_id}/extract-thesis",
            json=request_data
        )
        
        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert "Thesis extraction failed" in response.json()["error"]["detail"]
    
    @pytest.mark.asyncio
    async def test_extract_thesis_invalid_uuid(
        self, test_client, mock_dependencies, mock_vc_service, mock_auth
    ):
        """Test thesis extraction with invalid UUID."""
        # Arrange
        request_data = {
            "website_content": "Content...",
            "force_refresh": False
        }
        mock_vc_service.extract_thesis = AsyncMock(
            side_effect=ValueError("VC with id invalid-uuid not found")
        )
        
        # Act
        response = test_client.post(
            "/api/v1/vcs/invalid-uuid/extract-thesis",
            json=request_data
        )
        
        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    # Test Field Mapping
    @pytest.mark.asyncio
    async def test_field_mapping_focus_areas_to_sectors(
        self, test_client, mock_dependencies, mock_vc_service, vc_factory
    ):
        """Test that focus_areas in schema maps to sectors in domain."""
        # Arrange
        vc_data = {
            "firm_name": "Test VC",
            "focus_areas": ["AI/ML", "FinTech"]  # Schema field
        }
        created_vc = vc_factory(
            firm_name=vc_data["firm_name"],
            sectors=vc_data["focus_areas"]  # Domain field
        )
        mock_vc_service.create_vc = AsyncMock(return_value=created_vc)
        
        # Act
        response = test_client.post("/api/v1/vcs", json=vc_data)
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["focus_areas"] == vc_data["focus_areas"]
        
        # Verify the service was called with correct mapping
        call_args = mock_vc_service.create_vc.call_args[0][0]
        assert hasattr(call_args, 'sectors')
        assert call_args.sectors == vc_data["focus_areas"]
    
    @pytest.mark.asyncio
    async def test_field_mapping_stage_preferences_to_stages(
        self, test_client, mock_dependencies, mock_vc_service, vc_factory
    ):
        """Test that stage_preferences in schema maps to stages in domain."""
        # Arrange
        vc_data = {
            "firm_name": "Test VC",
            "stage_preferences": ["Seed", "Series A"]  # Schema field
        }
        created_vc = vc_factory(
            firm_name=vc_data["firm_name"],
            stages=vc_data["stage_preferences"]  # Domain field
        )
        mock_vc_service.create_vc = AsyncMock(return_value=created_vc)
        
        # Act
        response = test_client.post("/api/v1/vcs", json=vc_data)
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["stage_preferences"] == vc_data["stage_preferences"]
        
        # Verify the service was called with correct mapping
        call_args = mock_vc_service.create_vc.call_args[0][0]
        assert hasattr(call_args, 'stages')
        assert call_args.stages == vc_data["stage_preferences"]
    
    # Test Edge Cases
    @pytest.mark.asyncio
    async def test_create_vc_empty_arrays(
        self, test_client, mock_dependencies, mock_vc_service, vc_factory
    ):
        """Test creating VC with empty arrays."""
        # Arrange
        vc_data = {
            "firm_name": "Test VC",
            "focus_areas": [],
            "stage_preferences": [],
            "portfolio_companies": [],
            "partners": []
        }
        created_vc = vc_factory(
            firm_name=vc_data["firm_name"],
            sectors=[],  # Map to domain field
            stages=[]    # Map to domain field
        )
        mock_vc_service.create_vc = AsyncMock(return_value=created_vc)
        
        # Act
        response = test_client.post("/api/v1/vcs", json=vc_data)
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["focus_areas"] == []
        assert data["stage_preferences"] == []
    
    @pytest.mark.asyncio
    async def test_create_vc_zero_check_sizes(
        self, test_client, mock_dependencies, mock_vc_service, vc_factory
    ):
        """Test creating VC with zero check sizes."""
        # Arrange
        vc_data = {
            "firm_name": "Test VC",
            "check_size_min": 0.0,
            "check_size_max": 0.0
        }
        created_vc = vc_factory(**vc_data)
        mock_vc_service.create_vc = AsyncMock(return_value=created_vc)
        
        # Act
        response = test_client.post("/api/v1/vcs", json=vc_data)
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["check_size_min"] == 0.0
        assert data["check_size_max"] == 0.0
    
    @pytest.mark.asyncio
    async def test_list_vcs_large_dataset_pagination(
        self, test_client, mock_dependencies, mock_vc_service, vc_factory
    ):
        """Test pagination with large dataset."""
        # Arrange
        vcs = [vc_factory() for _ in range(100)]
        mock_vc_service.list_vcs = AsyncMock(return_value=vcs)
        
        # Act - Get last page
        response = test_client.get("/api/v1/vcs?page=5&size=20")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data["items"]) == 20
        assert data["total"] == 100
        assert data["page"] == 5
        assert data["pages"] == 5
    
    @pytest.mark.asyncio
    async def test_list_vcs_partial_last_page(
        self, test_client, mock_dependencies, mock_vc_service, vc_factory
    ):
        """Test pagination with partial last page."""
        # Arrange
        vcs = [vc_factory() for _ in range(25)]
        mock_vc_service.list_vcs = AsyncMock(return_value=vcs)
        
        # Act - Get last page with partial results
        response = test_client.get("/api/v1/vcs?page=2&size=20")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data["items"]) == 5  # Only 5 items on last page
        assert data["total"] == 25
        assert data["page"] == 2
        assert data["pages"] == 2
    
    @pytest.mark.asyncio
    async def test_list_vcs_out_of_range_page(
        self, test_client, mock_dependencies, mock_vc_service, vc_factory
    ):
        """Test requesting page beyond available data."""
        # Arrange
        vcs = [vc_factory() for _ in range(10)]
        mock_vc_service.list_vcs = AsyncMock(return_value=vcs)
        
        # Act - Request page 3 when only 1 page exists
        response = test_client.get("/api/v1/vcs?page=3&size=20")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data["items"]) == 0
        assert data["total"] == 10
        assert data["page"] == 3
        assert data["pages"] == 1