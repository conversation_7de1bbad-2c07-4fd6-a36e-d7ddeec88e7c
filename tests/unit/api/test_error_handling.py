"""Unit tests for API error handling and edge cases."""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from fastapi import status, HTTPException
from datetime import datetime
import uuid
import json
import asyncio

from src.api.errors import (
    NotFoundError,
    BadRequestError,
    UnauthorizedError,
    ForbiddenError,
    ConflictError,
    RateLimitError,
    ValidationError
)


class TestAPIErrorHandling:
    """Test error handling across all API endpoints."""
    
    @pytest.mark.asyncio
    async def test_handles_database_connection_errors(
        self, test_client, mock_db_session
    ):
        # Arrange
        mock_db_session.query.side_effect = Exception("Database connection lost")
        
        # Act
        response = test_client.get("/api/v1/startups")
        
        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert "Internal server error" in response.json()["detail"]
    
    @pytest.mark.asyncio
    async def test_handles_redis_connection_errors(
        self, test_client, mock_redis
    ):
        # Arrange
        mock_redis.get.side_effect = Exception("Redis connection failed")
        
        # Act - Should fail gracefully
        response = test_client.get("/api/v1/startups")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK  # Should still work
    
    @pytest.mark.asyncio
    async def test_handles_malformed_json_request(self, test_client):
        # Act
        response = test_client.post(
            "/api/v1/startups",
            data="{'invalid': json}",  # Malformed JSON
            headers={"Content-Type": "application/json"}
        )
        
        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    @pytest.mark.asyncio
    async def test_handles_missing_required_fields(self, test_client):
        # Arrange - Empty payload
        startup_data = {}
        
        # Act
        response = test_client.post("/api/v1/startups", json=startup_data)
        
        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        errors = response.json()["detail"]
        required_fields = ["name", "sector", "stage"]
        for field in required_fields:
            assert any(field in str(error) for error in errors)
    
    @pytest.mark.asyncio
    async def test_handles_invalid_enum_values(self, test_client):
        # Arrange
        startup_data = {
            "name": "Test Startup",
            "sector": "Technology",
            "stage": "Invalid Stage"  # Not a valid stage
        }
        
        # Act
        response = test_client.post("/api/v1/startups", json=startup_data)
        
        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        assert "stage" in str(response.json()["detail"])
    
    @pytest.mark.asyncio
    async def test_handles_invalid_uuid_format(self, test_client):
        # Act
        response = test_client.get("/api/v1/startups/not-a-uuid")
        
        # Assert
        # Should still process but return not found
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    @pytest.mark.asyncio
    async def test_handles_concurrent_requests_gracefully(
        self, test_client, auth_headers
    ):
        # Arrange
        startup_data = {
            "name": "Concurrent Test",
            "sector": "Technology", 
            "stage": "Seed"
        }
        
        # Act - Simulate concurrent requests
        import asyncio
        
        async def make_request():
            return test_client.post(
                "/api/v1/startups",
                json=startup_data,
                headers=auth_headers
            )
        
        # Create multiple concurrent requests
        tasks = [make_request() for _ in range(5)]
        responses = await asyncio.gather(*[asyncio.create_task(t) for t in tasks])
        
        # Assert
        # All should succeed or fail gracefully
        for response in responses:
            assert response.status_code in [
                status.HTTP_201_CREATED,
                status.HTTP_409_CONFLICT,  # If duplicate detection works
                status.HTTP_429_TOO_MANY_REQUESTS  # If rate limited
            ]
    
    @pytest.mark.asyncio
    async def test_handles_request_timeout(
        self, test_client, mock_ai_analyzer, auth_headers
    ):
        # Arrange
        async def slow_analysis(*args, **kwargs):
            await asyncio.sleep(10)  # Simulate slow response
        
        mock_ai_analyzer.analyze_startup = slow_analysis
        
        # Act
        with patch('asyncio.wait_for', side_effect=asyncio.TimeoutError):
            response = test_client.post(
                "/api/v1/startups/123/analyze",
                headers=auth_headers
            )
        
        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    
    @pytest.mark.asyncio
    async def test_handles_large_payloads(self, test_client):
        # Arrange - Very large description
        startup_data = {
            "name": "Test Startup",
            "sector": "Technology",
            "stage": "Seed",
            "description": "x" * 100000  # 100KB description
        }
        
        # Act
        response = test_client.post("/api/v1/startups", json=startup_data)
        
        # Assert
        # Should either accept or reject based on limits
        assert response.status_code in [
            status.HTTP_201_CREATED,
            status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            status.HTTP_422_UNPROCESSABLE_ENTITY
        ]
    
    @pytest.mark.asyncio
    async def test_handles_injection_attempts(self, test_client):
        # Arrange - SQL injection attempt
        startup_data = {
            "name": "'; DROP TABLE startups; --",
            "sector": "Technology",
            "stage": "Seed"
        }
        
        # Act
        response = test_client.post("/api/v1/startups", json=startup_data)
        
        # Assert
        # Should safely handle the input
        assert response.status_code in [
            status.HTTP_201_CREATED,
            status.HTTP_422_UNPROCESSABLE_ENTITY
        ]
    
    @pytest.mark.asyncio
    async def test_handles_xss_attempts(self, test_client):
        # Arrange - XSS attempt
        startup_data = {
            "name": "<script>alert('XSS')</script>",
            "sector": "Technology",
            "stage": "Seed",
            "description": "<img src=x onerror=alert('XSS')>"
        }
        
        # Act
        response = test_client.post("/api/v1/startups", json=startup_data)
        
        # Assert
        if response.status_code == status.HTTP_201_CREATED:
            data = response.json()
            # Ensure data is properly escaped/sanitized
            assert "<script>" not in data["name"]
            assert "<img" not in data.get("description", "")
    
    @pytest.mark.asyncio
    async def test_rate_limit_error_includes_retry_after(
        self, test_client, mock_redis
    ):
        # Arrange
        mock_redis.incr.return_value = 61  # Over limit
        
        # Act - Make request that triggers rate limit
        response = test_client.get("/api/v1/startups")
        
        # Assert
        if response.status_code == status.HTTP_429_TOO_MANY_REQUESTS:
            assert "Retry-After" in response.headers
    
    @pytest.mark.asyncio
    async def test_handles_partial_data_updates(
        self, test_client, auth_headers
    ):
        # Arrange
        startup_id = str(uuid.uuid4())
        update_data = {
            "team_size": "not-a-number"  # Invalid type
        }
        
        # Act
        response = test_client.put(
            f"/api/v1/startups/{startup_id}",
            json=update_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    @pytest.mark.asyncio
    async def test_handles_circular_references(
        self, test_client, auth_headers
    ):
        # Arrange - Create match between same entity
        match_data = {
            "startup_id": "12345",
            "vc_id": "12345"  # Same as startup_id
        }
        
        # Act
        response = test_client.post(
            "/api/v1/matches",
            json=match_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code in [
            status.HTTP_400_BAD_REQUEST,
            status.HTTP_422_UNPROCESSABLE_ENTITY
        ]
    
    @pytest.mark.asyncio
    async def test_handles_unicode_and_special_characters(
        self, test_client
    ):
        # Arrange
        startup_data = {
            "name": "スタートアップ 🚀",  # Japanese + emoji
            "sector": "Technology",
            "stage": "Seed",
            "description": "Building AI for 中文 markets €£¥"
        }
        
        # Act
        response = test_client.post("/api/v1/startups", json=startup_data)
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["name"] == startup_data["name"]
    
    @pytest.mark.asyncio
    async def test_handles_null_values_appropriately(
        self, test_client
    ):
        # Arrange
        startup_data = {
            "name": "Test Startup",
            "sector": "Technology",
            "stage": "Seed",
            "website": None,  # Explicit null
            "team_size": None
        }
        
        # Act
        response = test_client.post("/api/v1/startups", json=startup_data)
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        # The domain model converts None to default values
        assert data["website"] == ""  # None becomes empty string
        assert data["team_size"] == 0  # None becomes 0
    
    @pytest.mark.asyncio
    async def test_handles_expired_authentication_tokens(
        self, test_client, expired_credentials
    ):
        # Act
        response = test_client.get(
            "/api/v1/startups",
            headers={"Authorization": f"Bearer {expired_credentials.credentials}"}
        )
        
        # Assert
        # Should still work for public endpoints
        assert response.status_code == status.HTTP_200_OK
        
        # But fail for protected endpoints
        import uuid
        response = test_client.delete(
            f"/api/v1/startups/{uuid.uuid4()}",
            headers={"Authorization": f"Bearer {expired_credentials.credentials}"}
        )
        # Could be 401 (auth failed) or 404 (not found), both are acceptable for expired token
        assert response.status_code in [status.HTTP_401_UNAUTHORIZED, status.HTTP_404_NOT_FOUND]
    
    @pytest.mark.asyncio
    async def test_handles_method_not_allowed(self, test_client):
        # Act - Try POST on GET-only endpoint
        response = test_client.post("/api/v1/startups/123")
        
        # Assert
        assert response.status_code == status.HTTP_405_METHOD_NOT_ALLOWED
    
    @pytest.mark.asyncio
    async def test_handles_unsupported_media_type(self, test_client):
        # Act
        response = test_client.post(
            "/api/v1/startups",
            data="<xml>data</xml>",
            headers={"Content-Type": "application/xml"}
        )
        
        # Assert
        assert response.status_code == status.HTTP_415_UNSUPPORTED_MEDIA_TYPE