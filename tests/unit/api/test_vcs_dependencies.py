"""Test the dependency injection functions for VCs endpoint."""

import pytest
from unittest.mock import Mock, AsyncMock, patch

from src.api.v1.endpoints.vcs import (
    get_vc_repository,
    get_vc_service
)
from src.core.repositories.vc_repository import InMemoryVCRepository
from src.core.services.vc_service import VCService


class TestVCsDependencyInjection:
    """Test the dependency injection functions."""
    
    @pytest.mark.asyncio
    async def test_get_vc_repository_returns_in_memory_repository(self):
        """Test that get_vc_repository returns an InMemoryVCRepository."""
        # Act
        result = await get_vc_repository()
        
        # Assert
        assert isinstance(result, InMemoryVCRepository)
    
    @pytest.mark.asyncio
    async def test_get_vc_service_returns_service_with_dependencies(self):
        """Test that get_vc_service returns a properly configured VCService."""
        # Arrange
        mock_repository = Mock()
        mock_ai_port = Mock()
        
        # Act
        result = await get_vc_service(
            repository=mock_repository,
            ai_port=mock_ai_port
        )
        
        # Assert
        assert isinstance(result, VCService)
        assert result.repository == mock_repository
        assert result.ai_port == mock_ai_port
    
    @pytest.mark.asyncio
    async def test_get_vc_service_with_default_dependencies(self):
        """Test that get_vc_service can create service with default dependencies."""
        # We need to mock the dependency functions
        with patch('src.api.v1.endpoints.vcs.get_vc_repository') as mock_get_repo:
            with patch('src.api.v1.endpoints.vcs.get_ai_port') as mock_get_ai:
                mock_repo = Mock()
                mock_ai = Mock()
                mock_get_repo.return_value = mock_repo
                mock_get_ai.return_value = mock_ai
                
                # Act - simulating the default Depends() behavior
                result = await get_vc_service(
                    repository=mock_repo,
                    ai_port=mock_ai
                )
                
                # Assert
                assert isinstance(result, VCService)
                assert result.repository == mock_repo
                assert result.ai_port == mock_ai