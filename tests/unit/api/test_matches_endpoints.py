"""Unit tests for Matches API endpoints following TDD principles."""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from fastapi import status
from datetime import datetime
import uuid

from src.core.models.match import Match
from src.core.models.startup import Startup
from src.core.models.vc import VC
from src.core.schemas.match import Match<PERSON><PERSON>, MatchResponse


class TestMatchesEndpoints:
    """Test match creation, retrieval, and management endpoints."""
    
    @pytest.mark.asyncio
    async def test_create_match_calculates_score_automatically(
        self, test_client, auth_headers, mock_db_session
    ):
        # Arrange
        match_data = {
            "startup_id": str(uuid.uuid4()),
            "vc_id": str(uuid.uuid4())
        }
        
        # Act
        response = test_client.post(
            "/api/v1/matches",
            json=match_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["startup_id"] == match_data["startup_id"]
        assert data["vc_id"] == match_data["vc_id"]
        assert "score" in data
        assert "reasons" in data
        assert "status" in data
        assert data["status"] == "pending"
    
    @pytest.mark.asyncio
    async def test_create_match_prevents_duplicates(
        self, test_client, auth_headers, mock_db_session
    ):
        # Arrange
        match_data = {
            "startup_id": str(uuid.uuid4()),
            "vc_id": str(uuid.uuid4())
        }
        
        # Mock existing match
        mock_db_session.query().filter().first.return_value = Mock()
        
        # Act
        response = test_client.post(
            "/api/v1/matches",
            json=match_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_409_CONFLICT
        assert "Match already exists" in response.json()["detail"]
    
    @pytest.mark.asyncio
    async def test_create_match_validates_startup_exists(
        self, test_client, auth_headers, mock_db_session
    ):
        # Arrange
        match_data = {
            "startup_id": "non-existent-startup",
            "vc_id": str(uuid.uuid4())
        }
        
        # Mock startup not found
        mock_db_session.query().filter().first.side_effect = [None, Mock()]
        
        # Act
        response = test_client.post(
            "/api/v1/matches",
            json=match_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert "Startup not found" in response.json()["detail"]
    
    @pytest.mark.asyncio
    async def test_create_match_validates_vc_exists(
        self, test_client, auth_headers, mock_db_session
    ):
        # Arrange
        match_data = {
            "startup_id": str(uuid.uuid4()),
            "vc_id": "non-existent-vc"
        }
        
        # Mock VC not found
        mock_db_session.query().filter().first.side_effect = [Mock(), None]
        
        # Act
        response = test_client.post(
            "/api/v1/matches",
            json=match_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert "VC not found" in response.json()["detail"]
    
    @pytest.mark.asyncio
    async def test_list_matches_with_filters(self, test_client):
        # Act - Filter by status
        response = test_client.get("/api/v1/matches?status=approved")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        for item in data["items"]:
            assert item["status"] == "approved"
    
    @pytest.mark.asyncio
    async def test_list_matches_filters_by_score_range(self, test_client):
        # Act
        response = test_client.get("/api/v1/matches?min_score=0.7&max_score=0.9")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        for item in data["items"]:
            assert 0.7 <= item["score"] <= 0.9
    
    @pytest.mark.asyncio
    async def test_list_matches_for_specific_startup(self, test_client):
        # Arrange
        startup_id = str(uuid.uuid4())
        
        # Act
        response = test_client.get(f"/api/v1/matches?startup_id={startup_id}")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        for item in data["items"]:
            assert item["startup_id"] == startup_id
    
    @pytest.mark.asyncio
    async def test_list_matches_for_specific_vc(self, test_client):
        # Arrange
        vc_id = str(uuid.uuid4())
        
        # Act
        response = test_client.get(f"/api/v1/matches?vc_id={vc_id}")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        for item in data["items"]:
            assert item["vc_id"] == vc_id
    
    @pytest.mark.asyncio
    async def test_get_match_details_includes_entities(self, test_client):
        # Arrange
        match_id = str(uuid.uuid4())
        
        # Act
        response = test_client.get(f"/api/v1/matches/{match_id}")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "id" in data
        assert "startup" in data
        assert "vc" in data
        assert "score" in data
        assert "reasons" in data
        assert "status" in data
    
    @pytest.mark.asyncio
    async def test_update_match_status_to_approved(
        self, test_client, auth_headers
    ):
        # Arrange
        match_id = str(uuid.uuid4())
        update_data = {
            "status": "approved",
            "notes": "Great fit based on sector and stage alignment"
        }
        
        # Act
        response = test_client.patch(
            f"/api/v1/matches/{match_id}/status",
            json=update_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "approved"
        assert data["notes"] == update_data["notes"]
        assert "updated_at" in data
    
    @pytest.mark.asyncio
    async def test_update_match_status_validates_transition(
        self, test_client, auth_headers
    ):
        # Arrange - Try invalid status transition
        match_id = str(uuid.uuid4())
        update_data = {
            "status": "invalid-status"
        }
        
        # Act
        response = test_client.patch(
            f"/api/v1/matches/{match_id}/status",
            json=update_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    @pytest.mark.asyncio
    async def test_delete_match_requires_authentication(self, test_client):
        # Arrange
        match_id = str(uuid.uuid4())
        
        # Act
        response = test_client.delete(f"/api/v1/matches/{match_id}")
        
        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    @pytest.mark.asyncio
    async def test_bulk_create_matches_for_startup(
        self, test_client, auth_headers, mock_db_session
    ):
        # Arrange
        startup_id = str(uuid.uuid4())
        vc_ids = [str(uuid.uuid4()) for _ in range(3)]
        
        # Act
        response = test_client.post(
            f"/api/v1/matches/bulk/startup/{startup_id}",
            json={"vc_ids": vc_ids},
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert len(data["matches"]) == 3
        for match in data["matches"]:
            assert match["startup_id"] == startup_id
            assert match["vc_id"] in vc_ids
    
    @pytest.mark.asyncio
    async def test_recalculate_match_score(
        self, test_client, auth_headers
    ):
        # Arrange
        match_id = str(uuid.uuid4())
        
        # Act
        response = test_client.post(
            f"/api/v1/matches/{match_id}/recalculate",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "previous_score" in data
        assert "new_score" in data
        assert "score_changed" in data
    
    @pytest.mark.asyncio
    async def test_get_match_recommendations_for_startup(
        self, test_client, auth_headers
    ):
        # Arrange
        startup_id = str(uuid.uuid4())
        
        # Act
        response = test_client.get(
            f"/api/v1/matches/recommendations/startup/{startup_id}",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "recommendations" in data
        assert len(data["recommendations"]) <= 10  # Default limit
        # Recommendations should be sorted by score
        scores = [r["score"] for r in data["recommendations"]]
        assert scores == sorted(scores, reverse=True)
    
    @pytest.mark.asyncio
    async def test_get_match_recommendations_with_min_score(
        self, test_client, auth_headers
    ):
        # Arrange
        startup_id = str(uuid.uuid4())
        min_score = 0.7
        
        # Act
        response = test_client.get(
            f"/api/v1/matches/recommendations/startup/{startup_id}?min_score={min_score}",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        for recommendation in data["recommendations"]:
            assert recommendation["score"] >= min_score
    
    @pytest.mark.asyncio
    async def test_export_matches_as_csv(
        self, test_client, auth_headers
    ):
        # Act
        response = test_client.get(
            "/api/v1/matches/export?format=csv",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        assert response.headers["content-type"] == "text/csv; charset=utf-8"
        assert "attachment" in response.headers["content-disposition"]
    
    @pytest.mark.asyncio
    async def test_get_match_statistics(self, test_client, auth_headers):
        # Act
        response = test_client.get(
            "/api/v1/matches/statistics",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "total_matches" in data
        assert "average_score" in data
        assert "status_breakdown" in data
        assert "score_distribution" in data
        assert "matches_by_day" in data