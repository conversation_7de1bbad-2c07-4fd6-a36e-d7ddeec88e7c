"""Comprehensive unit tests for Startup API endpoints with full coverage."""

import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from fastapi import status
from datetime import datetime
import uuid
import json
import html
from typing import List, Optional

from src.core.models.startup import Startup
from src.core.schemas.startup import <PERSON>upC<PERSON>, StartupUpdate
from src.core.ai.models import StartupAnalysis, BusinessModel, BusinessModelType, TechnologyStack
from src.core.ports.ai_port import AIAnalysisError
from src.core.services.startup_service import StartupService
from src.core.repositories.startup_repository import StartupRepository
from src.api.errors import NotFoundError, BadRequestError


class TestStartupEndpointsComprehensive:
    """Comprehensive test suite for startup API endpoints with full coverage."""
    
    @pytest.fixture
    def mock_startup_repository(self):
        """Mock startup repository."""
        repo = Mock(spec=StartupRepository)
        repo.save = AsyncMock()
        repo.find_by_id = AsyncMock()
        repo.find_all = AsyncMock(return_value=[])
        repo.find_by_sector = AsyncMock(return_value=[])
        repo.find_by_stage = AsyncMock(return_value=[])
        repo.delete = AsyncMock(return_value=True)
        return repo
    
    @pytest.fixture
    def mock_ai_port(self):
        """Mock AI port."""
        port = Mock()
        port.analyze_startup = AsyncMock()
        port.get_usage_stats = Mock(return_value={})
        return port
    
    @pytest.fixture
    def mock_startup_service(self, mock_startup_repository, mock_ai_port):
        """Mock startup service."""
        service = Mock(spec=StartupService)
        service.repository = mock_startup_repository
        service.ai_port = mock_ai_port
        return service
    
    @pytest.fixture
    def mock_dependencies(self, test_client, mock_startup_service, mock_db_session):
        """Override dependencies with mocks."""
        from src.api.v1.endpoints.startups import get_startup_service, get_startup_repository
        from src.api.v1.deps import get_current_user_optional
        from src.api.main import app
        
        # Mock the repository dependency
        async def mock_get_repository(db=None):
            return mock_startup_service.repository
        
        # Mock the service dependency
        async def mock_get_service(repository=None, ai_port=None):
            return mock_startup_service
        
        app.dependency_overrides[get_startup_repository] = mock_get_repository
        app.dependency_overrides[get_startup_service] = mock_get_service
        
        yield
        
        app.dependency_overrides.clear()
    
    @pytest.fixture
    def mock_auth(self):
        """Mock authentication dependencies."""
        from src.api.v1.deps import get_current_user_optional
        from src.api.main import app
        
        # Mock auth to return a user
        async def mock_get_current_user_optional(credentials=None):
            return "test-user"
        
        app.dependency_overrides[get_current_user_optional] = mock_get_current_user_optional
        
        yield
        
        # Clean up only this override
        app.dependency_overrides.pop(get_current_user_optional, None)
    
    @pytest.fixture
    def sample_startup_data(self):
        """Sample startup data for testing."""
        return {
            "name": "AI Analytics Corp",
            "sector": "AI/ML",
            "stage": "Series A",
            "description": "We provide AI-powered analytics for businesses",
            "website": "https://aianalytics.com",
            "team_size": 15,
            "monthly_revenue": 75000
        }
    
    @pytest.fixture
    def sample_startup_insights(self):
        """Sample startup insights from AI analysis."""
        return StartupAnalysis(
            sectors=["AI/ML", "Analytics", "B2B SaaS"],
            technologies=TechnologyStack(
                languages=["Python", "JavaScript"],
                frameworks=["TensorFlow", "React"],
                databases=["PostgreSQL", "Redis"]
            ),
            business_model=BusinessModel(
                type=BusinessModelType.SAAS,
                revenue_streams=["Subscription", "Usage-based"],
                target_market="Enterprise B2B",
                pricing_model="Tiered subscription"
            ),
            keywords=["AI", "analytics", "business intelligence"],
            value_proposition="AI-powered analytics platform for business intelligence",
            target_customers=["Enterprise companies", "Data-driven organizations"],
            competitive_advantages=["Proprietary ML algorithms", "Real-time processing"],
            confidence_score=0.85
        )
    
    # Test Create Endpoint
    @pytest.mark.asyncio
    async def test_create_startup_success(
        self, test_client, mock_dependencies, mock_startup_service,
        sample_startup_data, auth_headers, sample_startup
    ):
        """Test successful startup creation."""
        # Arrange
        sample_startup.id = uuid.uuid4()
        sample_startup.name = html.escape(sample_startup_data["name"])
        mock_startup_service.create_startup = AsyncMock(return_value=sample_startup)
        
        # Act
        response = test_client.post(
            "/api/v1/startups",
            json=sample_startup_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["name"] == html.escape(sample_startup_data["name"])
        assert data["sector"] == sample_startup_data["sector"]
        assert data["stage"] == sample_startup_data["stage"]
        assert "id" in data
        assert "created_at" in data
        assert data["is_fundable"] is True
    
    @pytest.mark.asyncio
    async def test_create_startup_without_auth(
        self, test_client, mock_dependencies, mock_startup_service,
        sample_startup_data, sample_startup
    ):
        """Test creating startup without authentication (should still work)."""
        # Arrange
        sample_startup.id = uuid.uuid4()
        sample_startup.name = html.escape(sample_startup_data["name"])
        mock_startup_service.create_startup = AsyncMock(return_value=sample_startup)
        
        # Act
        response = test_client.post(
            "/api/v1/startups",
            json=sample_startup_data
        )
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
    
    @pytest.mark.asyncio
    async def test_create_startup_html_escaping(
        self, test_client, mock_dependencies, mock_startup_service,
        auth_headers, startup_factory
    ):
        """Test HTML escaping in startup creation."""
        # Arrange
        startup_data = {
            "name": "<script>alert('XSS')</script>",
            "sector": "Technology",
            "stage": "Seed",
            "description": "<img src=x onerror=alert('XSS')>"
        }
        escaped_startup = startup_factory(
            name=html.escape(startup_data["name"]),
            sector=startup_data["sector"],
            stage=startup_data["stage"],
            description=html.escape(startup_data["description"])
        )
        mock_startup_service.create_startup = AsyncMock(return_value=escaped_startup)
        
        # Act
        response = test_client.post(
            "/api/v1/startups",
            json=startup_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["name"] == html.escape(startup_data["name"])
        assert data["description"] == html.escape(startup_data["description"])
    
    @pytest.mark.asyncio
    async def test_create_startup_missing_required_fields(
        self, test_client, mock_dependencies
    ):
        """Test validation for missing required fields."""
        # Arrange
        startup_data = {"name": "Incomplete Startup"}
        
        # Act
        response = test_client.post("/api/v1/startups", json=startup_data)
        
        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        error_data = response.json()
        # FastAPI returns validation errors in detail field
        assert "detail" in error_data
        errors = error_data["detail"]
        assert any("sector" in error["field"] for error in errors)
        assert any("stage" in error["field"] for error in errors)
    
    @pytest.mark.asyncio
    async def test_create_startup_invalid_stage(
        self, test_client, mock_dependencies
    ):
        """Test validation for invalid stage."""
        # Arrange
        startup_data = {
            "name": "Test Startup",
            "sector": "Technology",
            "stage": "Invalid Stage"
        }
        
        # Act
        response = test_client.post("/api/v1/startups", json=startup_data)
        
        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        error_data = response.json()
        assert "detail" in error_data
    
    @pytest.mark.asyncio
    async def test_create_startup_service_value_error(
        self, test_client, mock_dependencies, mock_startup_service, sample_startup_data
    ):
        """Test handling of ValueError from service."""
        # Arrange
        mock_startup_service.create_startup = AsyncMock(
            side_effect=ValueError("Invalid startup data")
        )
        
        # Act
        response = test_client.post("/api/v1/startups", json=sample_startup_data)
        
        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Invalid startup data" in response.json()["error"]["detail"]
    
    @pytest.mark.asyncio
    async def test_create_startup_service_exception(
        self, test_client, mock_dependencies, mock_startup_service, sample_startup_data
    ):
        """Test handling of general exception from service."""
        # Arrange
        mock_startup_service.create_startup = AsyncMock(
            side_effect=Exception("Database error")
        )
        
        # Act
        response = test_client.post("/api/v1/startups", json=sample_startup_data)
        
        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert "Failed to create startup" in response.json()["error"]["detail"]
    
    # Test List Endpoint
    @pytest.mark.asyncio
    async def test_list_startups_success(
        self, test_client, mock_dependencies, startup_factory
    ):
        """Test successful startup listing."""
        # Arrange
        startups = [startup_factory() for _ in range(5)]
        mock_dependencies
        
        # Act
        response = test_client.get("/api/v1/startups")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "items" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data
        assert "pages" in data
        assert isinstance(data["items"], list)
    
    @pytest.mark.asyncio
    async def test_list_startups_with_pagination(
        self, test_client, mock_dependencies, mock_startup_service, startup_factory
    ):
        """Test pagination parameters."""
        # Arrange
        startups = [startup_factory() for _ in range(30)]
        mock_startup_service.list_startups = AsyncMock(return_value=startups)
        
        # Act
        response = test_client.get("/api/v1/startups?page=2&size=10")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["page"] == 2
        assert data["size"] == 10
        assert len(data["items"]) <= 10
        assert data["total"] == 30
        assert data["pages"] == 3
    
    @pytest.mark.asyncio
    async def test_list_startups_filter_by_sector(
        self, test_client, mock_dependencies, mock_startup_service, startup_factory
    ):
        """Test filtering by sector."""
        # Arrange
        ai_startups = [startup_factory(sector="AI/ML") for _ in range(3)]
        mock_startup_service.list_startups = AsyncMock(return_value=ai_startups)
        
        # Act
        response = test_client.get("/api/v1/startups?sector=AI/ML")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert all(item["sector"] == "AI/ML" for item in data["items"])
    
    @pytest.mark.asyncio
    async def test_list_startups_filter_by_stage(
        self, test_client, mock_dependencies, mock_startup_service, startup_factory
    ):
        """Test filtering by stage."""
        # Arrange
        seed_startups = [startup_factory(stage="Seed") for _ in range(3)]
        mock_startup_service.list_startups = AsyncMock(return_value=seed_startups)
        
        # Act
        response = test_client.get("/api/v1/startups?stage=Seed")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert all(item["stage"] == "Seed" for item in data["items"])
    
    @pytest.mark.asyncio
    async def test_list_startups_filter_by_team_size(
        self, test_client, mock_dependencies, mock_startup_service, startup_factory
    ):
        """Test filtering by team size range."""
        # Arrange
        startups = [
            startup_factory(team_size=5),
            startup_factory(team_size=15),
            startup_factory(team_size=25),
            startup_factory(team_size=50),
        ]
        mock_startup_service.list_startups = AsyncMock(return_value=startups)
        
        # Act
        response = test_client.get("/api/v1/startups?min_team_size=10&max_team_size=30")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data["items"]) == 2
        assert all(10 <= item["team_size"] <= 30 for item in data["items"])
    
    @pytest.mark.asyncio
    async def test_list_startups_filter_by_revenue(
        self, test_client, mock_dependencies, mock_startup_service, startup_factory
    ):
        """Test filtering by revenue range."""
        # Arrange
        startups = [
            startup_factory(monthly_revenue=10000),
            startup_factory(monthly_revenue=50000),
            startup_factory(monthly_revenue=100000),
            startup_factory(monthly_revenue=200000),
        ]
        mock_startup_service.list_startups = AsyncMock(return_value=startups)
        
        # Act
        response = test_client.get("/api/v1/startups?min_revenue=25000&max_revenue=150000")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data["items"]) == 2
        assert all(25000 <= item["monthly_revenue"] <= 150000 for item in data["items"])
    
    @pytest.mark.asyncio
    async def test_list_startups_filter_by_fundable(
        self, test_client, mock_dependencies, mock_startup_service, startup_factory
    ):
        """Test filtering by fundable status."""
        # Arrange
        startups = [
            startup_factory(stage="Seed"),  # fundable
            startup_factory(stage="Series A"),  # fundable
            startup_factory(stage="Pre-IPO"),  # not fundable
        ]
        mock_startup_service.list_startups = AsyncMock(return_value=startups)
        
        # Act
        response = test_client.get("/api/v1/startups?is_fundable=true")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data["items"]) == 2
        assert all(item["is_fundable"] for item in data["items"])
    
    @pytest.mark.asyncio
    async def test_list_startups_search_query(
        self, test_client, mock_dependencies, mock_startup_service, startup_factory
    ):
        """Test search by query."""
        # Arrange
        startups = [
            startup_factory(name="AI Analytics", description="AI-powered analytics"),
            startup_factory(name="FinTech Solutions", description="Financial technology"),
            startup_factory(name="Data AI Corp", description="Data processing"),
        ]
        mock_startup_service.list_startups = AsyncMock(return_value=startups)
        
        # Act
        response = test_client.get("/api/v1/startups?query=AI")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data["items"]) == 2
    
    @pytest.mark.asyncio
    async def test_list_startups_invalid_pagination(
        self, test_client, mock_dependencies
    ):
        """Test invalid pagination parameters."""
        # Test page < 1
        response = test_client.get("/api/v1/startups?page=0")
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Page must be greater than 0" in response.json()["error"]["detail"]
        
        # Test size > 100
        response = test_client.get("/api/v1/startups?size=101")
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Size must be between 1 and 100" in response.json()["error"]["detail"]
    
    # Test Get Endpoint
    @pytest.mark.asyncio
    async def test_get_startup_success(
        self, test_client, mock_dependencies, mock_startup_service, sample_startup
    ):
        """Test successful retrieval of a startup."""
        # Arrange
        startup_id = uuid.uuid4()
        sample_startup.id = startup_id
        mock_startup_service.get_startup = AsyncMock(return_value=sample_startup)
        
        # Act
        response = test_client.get(f"/api/v1/startups/{startup_id}")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["id"] == str(startup_id)
        assert data["name"] == sample_startup.name
    
    @pytest.mark.asyncio
    async def test_get_startup_not_found(
        self, test_client, mock_dependencies, mock_startup_service
    ):
        """Test 404 when startup not found."""
        # Arrange
        startup_id = uuid.uuid4()
        mock_startup_service.get_startup = AsyncMock(
            side_effect=ValueError(f"Startup with id {startup_id} not found")
        )
        
        # Act
        response = test_client.get(f"/api/v1/startups/{startup_id}")
        
        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND
        error_data = response.json()
        assert "not found" in error_data["error"]["detail"].lower()
    
    @pytest.mark.asyncio
    async def test_get_startup_invalid_uuid(
        self, test_client, mock_dependencies
    ):
        """Test handling of invalid UUID."""
        # Act
        response = test_client.get("/api/v1/startups/not-a-uuid")
        
        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    # Test Update Endpoint
    @pytest.mark.asyncio
    async def test_update_startup_success(
        self, test_client, mock_dependencies, mock_startup_service, sample_startup, auth_headers
    ):
        """Test successful startup update."""
        # Arrange
        startup_id = uuid.uuid4()
        update_data = {
            "team_size": 30,
            "monthly_revenue": 200000
        }
        updated_startup = Startup(
            id=startup_id,
            name=sample_startup.name,
            sector=sample_startup.sector,
            stage=sample_startup.stage,
            team_size=30,
            monthly_revenue=200000
        )
        mock_startup_service.update_startup = AsyncMock(return_value=updated_startup)
        
        # Act
        response = test_client.put(
            f"/api/v1/startups/{startup_id}",
            json=update_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["team_size"] == 30
        assert data["monthly_revenue"] == 200000
    
    @pytest.mark.asyncio
    async def test_update_startup_partial_update(
        self, test_client, mock_dependencies, mock_startup_service, sample_startup, auth_headers
    ):
        """Test partial update with only some fields."""
        # Arrange
        startup_id = uuid.uuid4()
        update_data = {"description": "Updated description"}
        sample_startup.description = "Updated description"
        mock_startup_service.update_startup = AsyncMock(return_value=sample_startup)
        
        # Act
        response = test_client.put(
            f"/api/v1/startups/{startup_id}",
            json=update_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["description"] == "Updated description"
    
    @pytest.mark.asyncio
    async def test_update_startup_not_found(
        self, test_client, mock_dependencies, mock_startup_service, auth_headers
    ):
        """Test 404 when updating non-existent startup."""
        # Arrange
        startup_id = uuid.uuid4()
        mock_startup_service.update_startup = AsyncMock(
            side_effect=ValueError(f"Startup with id {startup_id} not found")
        )
        
        # Act
        response = test_client.put(
            f"/api/v1/startups/{startup_id}",
            json={"team_size": 10},
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    @pytest.mark.asyncio
    async def test_update_startup_invalid_data(
        self, test_client, mock_dependencies, mock_startup_service, auth_headers
    ):
        """Test 400 when update data is invalid."""
        # Arrange
        startup_id = uuid.uuid4()
        mock_startup_service.update_startup = AsyncMock(
            side_effect=ValueError("Invalid update data")
        )
        
        # Act
        response = test_client.put(
            f"/api/v1/startups/{startup_id}",
            json={"stage": "Invalid Stage"},
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    # Test Delete Endpoint
    @pytest.mark.asyncio
    async def test_delete_startup_success(
        self, test_client, mock_dependencies, mock_startup_service, mock_auth, auth_headers
    ):
        """Test successful startup deletion."""
        # Arrange
        startup_id = uuid.uuid4()
        mock_startup_service.delete_startup = AsyncMock(return_value=True)
        
        # Act
        response = test_client.delete(
            f"/api/v1/startups/{startup_id}",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_204_NO_CONTENT
        assert response.content == b""
    
    @pytest.mark.asyncio
    async def test_delete_startup_not_found(
        self, test_client, mock_dependencies, mock_startup_service, mock_auth, auth_headers
    ):
        """Test 404 when deleting non-existent startup."""
        # Arrange
        startup_id = uuid.uuid4()
        mock_startup_service.delete_startup = AsyncMock(return_value=False)
        
        # Act
        response = test_client.delete(
            f"/api/v1/startups/{startup_id}",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    @pytest.mark.asyncio
    async def test_delete_startup_requires_auth(
        self, test_client, mock_dependencies
    ):
        """Test that deletion requires authentication."""
        # Arrange
        startup_id = uuid.uuid4()
        
        # Act - No auth headers
        response = test_client.delete(f"/api/v1/startups/{startup_id}")
        
        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Authentication required" in response.json()["error"]["detail"]
    
    # Test Analyze Endpoint
    @pytest.mark.asyncio
    async def test_analyze_startup_success(
        self, test_client, mock_dependencies, mock_startup_service, 
        sample_startup_insights, mock_auth, auth_headers
    ):
        """Test successful startup analysis."""
        # Arrange
        startup_id = uuid.uuid4()
        mock_startup_service.analyze_startup = AsyncMock(return_value=sample_startup_insights)
        
        # Act
        response = test_client.post(
            f"/api/v1/startups/{startup_id}/analyze",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["startup_id"] == str(startup_id)
        assert data["analysis"]["sectors"] == sample_startup_insights.sectors
        assert data["analysis"]["value_proposition"] == sample_startup_insights.value_proposition
        assert data["analysis"]["confidence_score"] == sample_startup_insights.confidence_score
        assert "analyzed_at" in data
    
    @pytest.mark.asyncio
    async def test_analyze_startup_with_force_refresh(
        self, test_client, mock_dependencies, mock_startup_service,
        sample_startup_insights, mock_auth, auth_headers
    ):
        """Test analysis with force_refresh parameter."""
        # Arrange
        startup_id = uuid.uuid4()
        mock_startup_service.analyze_startup = AsyncMock(return_value=sample_startup_insights)
        
        # Act
        response = test_client.post(
            f"/api/v1/startups/{startup_id}/analyze?force_refresh=true",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        mock_startup_service.analyze_startup.assert_called_with(
            startup_id,
            force_refresh=True
        )
    
    @pytest.mark.asyncio
    async def test_analyze_startup_requires_auth(
        self, test_client, mock_dependencies
    ):
        """Test that analysis requires authentication."""
        # Arrange
        startup_id = uuid.uuid4()
        
        # Act
        response = test_client.post(f"/api/v1/startups/{startup_id}/analyze")
        
        # Assert
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Authentication required for AI analysis" in response.json()["error"]["detail"]
    
    @pytest.mark.asyncio
    async def test_analyze_startup_not_found(
        self, test_client, mock_dependencies, mock_startup_service, mock_auth, auth_headers
    ):
        """Test 404 when analyzing non-existent startup."""
        # Arrange
        startup_id = uuid.uuid4()
        mock_startup_service.analyze_startup = AsyncMock(
            side_effect=ValueError(f"Startup with id {startup_id} not found")
        )
        
        # Act
        response = test_client.post(
            f"/api/v1/startups/{startup_id}/analyze",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    @pytest.mark.asyncio
    async def test_analyze_startup_ai_error(
        self, test_client, mock_dependencies, mock_startup_service, mock_auth, auth_headers
    ):
        """Test 500 when AI analysis fails."""
        # Arrange
        startup_id = uuid.uuid4()
        mock_startup_service.analyze_startup = AsyncMock(
            side_effect=Exception("OpenAI API error")
        )
        
        # Act
        response = test_client.post(
            f"/api/v1/startups/{startup_id}/analyze",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert "AI analysis failed" in response.json()["error"]["detail"]
    
    # Test Rate Limiting
    @pytest.mark.asyncio
    async def test_rate_limiting_applied(
        self, test_client, mock_dependencies, mock_redis
    ):
        """Test that rate limiting is applied to all endpoints."""
        # Arrange
        mock_redis.incr.return_value = 61  # Exceed limit
        
        # Act & Assert - Test each endpoint
        endpoints = [
            ("POST", "/api/v1/startups", {"name": "Test", "sector": "Tech", "stage": "Seed"}),
            ("GET", "/api/v1/startups", None),
            ("GET", f"/api/v1/startups/{uuid.uuid4()}", None),
            ("PUT", f"/api/v1/startups/{uuid.uuid4()}", {"team_size": 10}),
            ("DELETE", f"/api/v1/startups/{uuid.uuid4()}", None),
            ("POST", f"/api/v1/startups/{uuid.uuid4()}/analyze", None),
        ]
        
        for method, url, json_data in endpoints:
            response = test_client.request(method, url, json=json_data)
            # Rate limiting should be triggered
            assert response.status_code in [status.HTTP_429_TOO_MANY_REQUESTS, status.HTTP_401_UNAUTHORIZED, status.HTTP_422_UNPROCESSABLE_ENTITY]
    
    # Test Edge Cases
    @pytest.mark.asyncio
    async def test_create_startup_with_null_optional_fields(
        self, test_client, mock_dependencies, mock_startup_service, auth_headers, startup_factory
    ):
        """Test creating startup with null optional fields."""
        # Arrange
        startup_data = {
            "name": "Minimal Startup",
            "sector": "Technology",
            "stage": "Seed",
            "description": None,
            "website": None,
            "team_size": None,
            "monthly_revenue": None
        }
        
        minimal_startup = startup_factory(
            name="Minimal Startup",
            sector="Technology",
            stage="Seed",
            description="",
            website="",
            team_size=0,
            monthly_revenue=0.0
        )
        mock_startup_service.create_startup = AsyncMock(return_value=minimal_startup)
        
        # Act
        response = test_client.post(
            "/api/v1/startups",
            json=startup_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["description"] == ""
        assert data["website"] == ""
        assert data["team_size"] == 0
        assert data["monthly_revenue"] == 0.0
    
    @pytest.mark.asyncio
    async def test_list_startups_empty_results(
        self, test_client, mock_dependencies, mock_startup_service
    ):
        """Test listing when no startups exist."""
        # Arrange
        mock_startup_service.list_startups = AsyncMock(return_value=[])
        
        # Act
        response = test_client.get("/api/v1/startups")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["items"] == []
        assert data["total"] == 0
        assert data["pages"] == 0
    
    @pytest.mark.asyncio
    async def test_startup_response_schema_conversion(
        self, test_client, mock_dependencies, mock_startup_service, startup_factory
    ):
        """Test proper conversion between domain model and response schema."""
        # Arrange
        startup = startup_factory()
        startup.id = None  # Test with None ID
        mock_startup_service.get_startup = AsyncMock(return_value=startup)
        
        # Act
        response = test_client.get(f"/api/v1/startups/{uuid.uuid4()}")
        
        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["id"] == ""  # Should convert None to empty string