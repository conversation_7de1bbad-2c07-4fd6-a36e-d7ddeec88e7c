"""Comprehensive tests for connections API endpoints."""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from fastapi.testclient import TestClient
from fastapi import status
from datetime import datetime, timedelta
from uuid import UUID, uuid4
import json

from src.api.main import app
from src.core.models.connection import (
    Connection,
    ConnectionId,
    ConnectionMetrics,
    ConnectionStrength,
    RelationshipType,
    ConnectionPath,
    IntroductionRequest,
    IntroductionStatus
)
from src.core.services.warm_intro_service import WarmIntroService
from tests.fixtures.warm_intro_fixtures import (
    sample_user_ids,
    mock_users,
    api_test_data,
    error_scenarios
)


@pytest.fixture
def mock_warm_intro_service():
    """Create a mock WarmIntroService."""
    service = Mock(spec=WarmIntroService)
    
    # Configure async methods
    service.create_connection = AsyncMock()
    service.find_intro_paths_for_match = AsyncMock()
    service.request_introduction = AsyncMock()
    service.get_pending_intro_requests = AsyncMock()
    service.respond_to_intro_request = AsyncMock()
    service.get_connection_analytics = AsyncMock()
    
    return service


@pytest.fixture
def test_client_with_auth(mock_warm_intro_service, sample_user_ids):
    """Create test client with mocked authentication and warm intro service."""
    from src.api.v1.deps import get_current_user
    
    # Mock current user
    def mock_get_current_user():
        return sample_user_ids['alice']
    
    # Mock service creation in endpoints
    with patch('src.api.v1.endpoints.connections.WarmIntroService') as mock_service_class:
        mock_service_class.return_value = mock_warm_intro_service
        
        # Override auth dependency
        app.dependency_overrides[get_current_user] = mock_get_current_user
        
        client = TestClient(app)
        yield client
        
        # Clean up
        app.dependency_overrides.clear()


class TestCreateConnectionEndpoint:
    """Test POST /connections/ endpoint."""
    
    def test_create_connection_success(self, test_client_with_auth, mock_warm_intro_service, sample_user_ids, mock_users, api_test_data):
        """Test successful connection creation."""
        # Mock service response
        mock_connection = Connection(
            id=ConnectionId(),
            user_a_id=sample_user_ids['alice'],
            user_b_id=sample_user_ids['bob'],
            relationship_type=RelationshipType.COLLEAGUE,
            strength=ConnectionStrength.STRONG,
            metrics=ConnectionMetrics(trust_score=0.7),
            notes="Test connection"
        )
        mock_warm_intro_service.create_connection.return_value = mock_connection
        
        # Mock user repository for other user lookup
        with patch('src.api.v1.endpoints.connections.PostgresUserRepository') as mock_user_repo_class:
            mock_user_repo = Mock()
            mock_user_repo.get = AsyncMock(return_value=mock_users['bob'])
            mock_user_repo_class.return_value = mock_user_repo
            
            # Execute
            response = test_client_with_auth.post(
                "/connections/",
                json={
                    "other_user_id": str(sample_user_ids['bob']),
                    "relationship_type": "colleague",
                    "notes": "Test connection",
                    "trust_score": 0.7
                }
            )
            
            # Verify
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data['relationship_type'] == 'colleague'
            assert data['strength'] == 'strong'
            assert data['notes'] == "Test connection"
            assert data['other_user']['name'] == mock_users['bob'].name
            
            # Verify service was called correctly
            mock_warm_intro_service.create_connection.assert_called_once()
            call_args = mock_warm_intro_service.create_connection.call_args
            assert call_args.kwargs['user_a_id'] == sample_user_ids['alice']
            assert call_args.kwargs['user_b_id'] == sample_user_ids['bob']
            assert call_args.kwargs['relationship_type'] == RelationshipType.COLLEAGUE
            assert call_args.kwargs['trust_score'] == 0.7
    
    def test_create_connection_invalid_relationship_type(self, test_client_with_auth):
        """Test creation with invalid relationship type."""
        response = test_client_with_auth.post(
            "/connections/",
            json={
                "other_user_id": str(uuid4()),
                "relationship_type": "invalid_type",
                "trust_score": 0.5
            }
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_create_connection_invalid_trust_score(self, test_client_with_auth):
        """Test creation with invalid trust score."""
        response = test_client_with_auth.post(
            "/connections/",
            json={
                "other_user_id": str(uuid4()),
                "relationship_type": "colleague",
                "trust_score": 1.5  # Too high
            }
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_create_connection_service_error(self, test_client_with_auth, mock_warm_intro_service):
        """Test handling of service errors."""
        mock_warm_intro_service.create_connection.side_effect = ValueError("Connection already exists")
        
        response = test_client_with_auth.post(
            "/connections/",
            json={
                "other_user_id": str(uuid4()),
                "relationship_type": "colleague",
                "trust_score": 0.5
            }
        )
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Connection already exists" in response.json()['detail']
    
    def test_create_connection_unexpected_error(self, test_client_with_auth, mock_warm_intro_service):
        """Test handling of unexpected errors."""
        mock_warm_intro_service.create_connection.side_effect = Exception("Database error")
        
        response = test_client_with_auth.post(
            "/connections/",
            json={
                "other_user_id": str(uuid4()),
                "relationship_type": "colleague",
                "trust_score": 0.5
            }
        )
        
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert response.json()['detail'] == "Failed to create connection"


class TestGetConnectionsEndpoint:
    """Test GET /connections/ endpoint."""
    
    def test_get_connections_success(self, test_client_with_auth, sample_user_ids, mock_users):
        """Test successful connections retrieval."""
        # Mock connections
        mock_connection1 = Connection(
            id=ConnectionId(),
            user_a_id=sample_user_ids['alice'],
            user_b_id=sample_user_ids['bob'],
            relationship_type=RelationshipType.COLLEAGUE,
            strength=ConnectionStrength.STRONG,
            metrics=ConnectionMetrics(trust_score=0.8),
            notes="Colleague connection"
        )
        
        mock_connection2 = Connection(
            id=ConnectionId(),
            user_a_id=sample_user_ids['alice'],
            user_b_id=sample_user_ids['charlie'],
            relationship_type=RelationshipType.BUSINESS_PARTNER,
            strength=ConnectionStrength.MEDIUM,
            metrics=ConnectionMetrics(trust_score=0.6),
            notes="Business connection"
        )
        
        # Mock repository responses
        with patch('src.api.v1.endpoints.connections.PostgresConnectionRepository') as mock_conn_repo_class:
            with patch('src.api.v1.endpoints.connections.PostgresUserRepository') as mock_user_repo_class:
                # Setup connection repository
                mock_conn_repo = Mock()
                mock_conn_repo.search_connections = AsyncMock(return_value=[mock_connection1, mock_connection2])
                mock_conn_repo_class.return_value = mock_conn_repo
                
                # Setup user repository
                mock_user_repo = Mock()
                mock_user_repo.get = AsyncMock()
                mock_user_repo.get.side_effect = [mock_users['bob'], mock_users['charlie']]
                mock_user_repo_class.return_value = mock_user_repo
                
                # Execute
                response = test_client_with_auth.get("/connections/")
                
                # Verify
                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert len(data) == 2
                
                # Check first connection
                conn1 = data[0]
                assert conn1['relationship_type'] == 'colleague'
                assert conn1['strength'] == 'strong'
                assert conn1['other_user']['name'] == mock_users['bob'].name
                
                # Check second connection
                conn2 = data[1]
                assert conn2['relationship_type'] == 'business_partner'
                assert conn2['strength'] == 'medium'
                assert conn2['other_user']['name'] == mock_users['charlie'].name
    
    def test_get_connections_with_filters(self, test_client_with_auth):
        """Test connections retrieval with filters."""
        with patch('src.api.v1.endpoints.connections.PostgresConnectionRepository') as mock_conn_repo_class:
            mock_conn_repo = Mock()
            mock_conn_repo.search_connections = AsyncMock(return_value=[])
            mock_conn_repo_class.return_value = mock_conn_repo
            
            # Execute with filters
            response = test_client_with_auth.get(
                "/connections/?strength=strong&relationship_type=colleague"
            )
            
            # Verify
            assert response.status_code == status.HTTP_200_OK
            
            # Check that filters were passed to repository
            mock_conn_repo.search_connections.assert_called_once()
            call_args = mock_conn_repo.search_connections.call_args
            filters = call_args[0][1]  # Second argument is filters
            assert filters['strength'] == 'strong'
            assert filters['relationship_type'] == 'colleague'
    
    def test_get_connections_empty_result(self, test_client_with_auth):
        """Test connections retrieval with no results."""
        with patch('src.api.v1.endpoints.connections.PostgresConnectionRepository') as mock_conn_repo_class:
            mock_conn_repo = Mock()
            mock_conn_repo.search_connections = AsyncMock(return_value=[])
            mock_conn_repo_class.return_value = mock_conn_repo
            
            response = test_client_with_auth.get("/connections/")
            
            assert response.status_code == status.HTTP_200_OK
            assert response.json() == []


class TestFindIntroPathsEndpoints:
    """Test path finding endpoints."""
    
    def test_find_paths_to_startup_success(self, test_client_with_auth, mock_warm_intro_service):
        """Test successful path finding to startup."""
        startup_id = uuid4()
        
        # Mock service response
        mock_paths = [
            {
                'target_user': {
                    'id': str(uuid4()),
                    'name': 'Startup Founder',
                    'email': '<EMAIL>',
                    'role': 'founder'
                },
                'path': [
                    {'id': str(uuid4()), 'name': 'Alice', 'position': 0},
                    {'id': str(uuid4()), 'name': 'Bob', 'position': 1},
                    {'id': str(uuid4()), 'name': 'Founder', 'position': 2}
                ],
                'strength_score': 0.8,
                'hop_count': 2,
                'can_request_intro': True
            }
        ]
        mock_warm_intro_service.find_intro_paths_for_match.return_value = mock_paths
        
        # Execute
        response = test_client_with_auth.get(f"/connections/paths/to-startup/{startup_id}")
        
        # Verify
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data) == 1
        
        path = data[0]
        assert path['target_user']['name'] == 'Startup Founder'
        assert path['strength_score'] == 0.8
        assert path['hop_count'] == 2
        assert path['can_request_intro'] is True
        
        # Verify service was called correctly
        mock_warm_intro_service.find_intro_paths_for_match.assert_called_once()
        call_args = mock_warm_intro_service.find_intro_paths_for_match.call_args
        assert call_args.kwargs['startup_id'] == startup_id
        assert call_args.kwargs['max_depth'] == 3  # Default
    
    def test_find_paths_to_startup_with_max_depth(self, test_client_with_auth, mock_warm_intro_service):
        """Test path finding with custom max depth."""
        startup_id = uuid4()
        mock_warm_intro_service.find_intro_paths_for_match.return_value = []
        
        response = test_client_with_auth.get(f"/connections/paths/to-startup/{startup_id}?max_depth=2")
        
        assert response.status_code == status.HTTP_200_OK
        
        # Verify max_depth parameter
        call_args = mock_warm_intro_service.find_intro_paths_for_match.call_args
        assert call_args.kwargs['max_depth'] == 2
    
    def test_find_paths_to_vc_success(self, test_client_with_auth, mock_warm_intro_service):
        """Test successful path finding to VC."""
        vc_id = uuid4()
        
        mock_paths = [
            {
                'target_user': {
                    'id': str(uuid4()),
                    'name': 'VC Partner',
                    'email': '<EMAIL>',
                    'role': 'investor'
                },
                'path': [
                    {'id': str(uuid4()), 'name': 'Alice', 'position': 0},
                    {'id': str(uuid4()), 'name': 'Partner', 'position': 1}
                ],
                'strength_score': 0.9,
                'hop_count': 1,
                'can_request_intro': True
            }
        ]
        mock_warm_intro_service.find_intro_paths_for_match.return_value = mock_paths
        
        response = test_client_with_auth.get(f"/connections/paths/to-vc/{vc_id}")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data) == 1
        assert data[0]['target_user']['name'] == 'VC Partner'
        assert data[0]['hop_count'] == 1
        
        # Verify service was called with vc_id
        call_args = mock_warm_intro_service.find_intro_paths_for_match.call_args
        assert call_args.kwargs['vc_id'] == vc_id
    
    def test_find_paths_invalid_max_depth(self, test_client_with_auth):
        """Test path finding with invalid max depth."""
        startup_id = uuid4()
        
        # Max depth too high
        response = test_client_with_auth.get(f"/connections/paths/to-startup/{startup_id}?max_depth=5")
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        
        # Max depth too low
        response = test_client_with_auth.get(f"/connections/paths/to-startup/{startup_id}?max_depth=0")
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_find_paths_service_error(self, test_client_with_auth, mock_warm_intro_service):
        """Test handling of service errors in path finding."""
        startup_id = uuid4()
        mock_warm_intro_service.find_intro_paths_for_match.side_effect = Exception("Database error")
        
        response = test_client_with_auth.get(f"/connections/paths/to-startup/{startup_id}")
        
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert response.json()['detail'] == "Failed to find introduction paths"


class TestRequestIntroductionEndpoint:
    """Test POST /connections/introductions/request endpoint."""
    
    def test_request_introduction_success(self, test_client_with_auth, mock_warm_intro_service, sample_user_ids):
        """Test successful introduction request."""
        # Mock service response
        mock_request = IntroductionRequest(
            requester_id=sample_user_ids['alice'],
            target_id=sample_user_ids['charlie'],
            connector_id=sample_user_ids['bob'],
            message="Please introduce me"
        )
        mock_warm_intro_service.request_introduction.return_value = mock_request
        
        # Execute
        response = test_client_with_auth.post(
            "/connections/introductions/request",
            json={
                "target_id": str(sample_user_ids['charlie']),
                "connector_id": str(sample_user_ids['bob']),
                "message": "Please introduce me for potential collaboration"
            }
        )
        
        # Verify
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data['request_id'] == str(mock_request.id)
        assert data['status'] == 'pending'
        assert data['message'] == "Introduction request sent successfully"
        assert 'expires_at' in data
        
        # Verify service was called correctly
        mock_warm_intro_service.request_introduction.assert_called_once()
        call_args = mock_warm_intro_service.request_introduction.call_args
        assert call_args.kwargs['requester_id'] == sample_user_ids['alice']
        assert call_args.kwargs['target_id'] == sample_user_ids['charlie']
        assert call_args.kwargs['connector_id'] == sample_user_ids['bob']
    
    def test_request_introduction_short_message(self, test_client_with_auth):
        """Test introduction request with message too short."""
        response = test_client_with_auth.post(
            "/connections/introductions/request",
            json={
                "target_id": str(uuid4()),
                "connector_id": str(uuid4()),
                "message": "Short"  # Less than 10 characters
            }
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_request_introduction_service_error(self, test_client_with_auth, mock_warm_intro_service):
        """Test handling of service errors."""
        mock_warm_intro_service.request_introduction.side_effect = ValueError("Invalid connector")
        
        response = test_client_with_auth.post(
            "/connections/introductions/request",
            json={
                "target_id": str(uuid4()),
                "connector_id": str(uuid4()),
                "message": "Please introduce me for collaboration"
            }
        )
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Invalid connector" in response.json()['detail']


class TestGetPendingIntroRequestsEndpoint:
    """Test GET /connections/introductions/pending endpoint."""
    
    def test_get_pending_requests_success(self, test_client_with_auth, mock_warm_intro_service):
        """Test successful retrieval of pending requests."""
        # Mock service response
        mock_requests = [
            {
                'request': {
                    'id': str(uuid4()),
                    'message': 'Please introduce me',
                    'created_at': datetime.utcnow().isoformat(),
                    'expires_at': (datetime.utcnow() + timedelta(days=30)).isoformat()
                },
                'requester': {
                    'id': str(uuid4()),
                    'name': 'Alice Johnson',
                    'email': '<EMAIL>'
                },
                'target': {
                    'id': str(uuid4()),
                    'name': 'Charlie Brown',
                    'email': '<EMAIL>'
                },
                'connections': {
                    'requester_strength': 'strong',
                    'target_strength': 'medium'
                }
            }
        ]
        mock_warm_intro_service.get_pending_intro_requests.return_value = mock_requests
        
        # Execute
        response = test_client_with_auth.get("/connections/introductions/pending")
        
        # Verify
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data) == 1
        
        request_data = data[0]
        assert request_data['request']['message'] == 'Please introduce me'
        assert request_data['requester']['name'] == 'Alice Johnson'
        assert request_data['target']['name'] == 'Charlie Brown'
        assert request_data['connections']['requester_strength'] == 'strong'
    
    def test_get_pending_requests_empty(self, test_client_with_auth, mock_warm_intro_service):
        """Test retrieval when no pending requests exist."""
        mock_warm_intro_service.get_pending_intro_requests.return_value = []
        
        response = test_client_with_auth.get("/connections/introductions/pending")
        
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == []
    
    def test_get_pending_requests_service_error(self, test_client_with_auth, mock_warm_intro_service):
        """Test handling of service errors."""
        mock_warm_intro_service.get_pending_intro_requests.side_effect = Exception("Database error")
        
        response = test_client_with_auth.get("/connections/introductions/pending")
        
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert response.json()['detail'] == "Failed to get pending requests"


class TestRespondToIntroRequestEndpoint:
    """Test PUT /connections/introductions/{request_id}/respond endpoint."""
    
    def test_respond_accept_success(self, test_client_with_auth, mock_warm_intro_service):
        """Test successfully accepting an introduction request."""
        request_id = uuid4()
        
        # Mock service response
        mock_updated_request = IntroductionRequest(
            id=request_id,
            requester_id=uuid4(),
            target_id=uuid4(),
            connector_id=uuid4(),
            message="Please introduce me",
            status=IntroductionStatus.ACCEPTED
        )
        mock_warm_intro_service.respond_to_intro_request.return_value = mock_updated_request
        
        # Execute
        response = test_client_with_auth.put(
            f"/connections/introductions/{request_id}/respond",
            json={
                "accept": True,
                "notes": "Happy to make this introduction"
            }
        )
        
        # Verify
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data['request_id'] == str(request_id)
        assert data['status'] == 'accepted'
        assert "accepted" in data['message']
        
        # Verify service was called correctly
        mock_warm_intro_service.respond_to_intro_request.assert_called_once()
        call_args = mock_warm_intro_service.respond_to_intro_request.call_args
        assert call_args.kwargs['request_id'] == request_id
        assert call_args.kwargs['accept'] is True
        assert call_args.kwargs['notes'] == "Happy to make this introduction"
    
    def test_respond_decline_success(self, test_client_with_auth, mock_warm_intro_service):
        """Test successfully declining an introduction request."""
        request_id = uuid4()
        
        # Mock service response
        mock_updated_request = IntroductionRequest(
            id=request_id,
            requester_id=uuid4(),
            target_id=uuid4(),
            connector_id=uuid4(),
            message="Please introduce me",
            status=IntroductionStatus.DECLINED
        )
        mock_warm_intro_service.respond_to_intro_request.return_value = mock_updated_request
        
        # Execute
        response = test_client_with_auth.put(
            f"/connections/introductions/{request_id}/respond",
            json={
                "accept": False,
                "notes": "Not the right time for this introduction"
            }
        )
        
        # Verify
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data['status'] == 'declined'
        assert "declined" in data['message']
    
    def test_respond_without_notes(self, test_client_with_auth, mock_warm_intro_service):
        """Test responding without optional notes."""
        request_id = uuid4()
        
        mock_updated_request = IntroductionRequest(
            id=request_id,
            requester_id=uuid4(),
            target_id=uuid4(),
            connector_id=uuid4(),
            message="Please introduce me",
            status=IntroductionStatus.ACCEPTED
        )
        mock_warm_intro_service.respond_to_intro_request.return_value = mock_updated_request
        
        response = test_client_with_auth.put(
            f"/connections/introductions/{request_id}/respond",
            json={"accept": True}
        )
        
        assert response.status_code == status.HTTP_200_OK
        
        # Verify notes was None
        call_args = mock_warm_intro_service.respond_to_intro_request.call_args
        assert call_args.kwargs['notes'] is None
    
    def test_respond_service_error(self, test_client_with_auth, mock_warm_intro_service):
        """Test handling of service errors."""
        request_id = uuid4()
        mock_warm_intro_service.respond_to_intro_request.side_effect = ValueError("Request not found")
        
        response = test_client_with_auth.put(
            f"/connections/introductions/{request_id}/respond",
            json={"accept": True}
        )
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Request not found" in response.json()['detail']


class TestGetConnectionAnalyticsEndpoint:
    """Test GET /connections/analytics endpoint."""
    
    def test_get_analytics_success(self, test_client_with_auth, mock_warm_intro_service):
        """Test successful analytics retrieval."""
        # Mock service response
        mock_analytics = {
            'total_connections': 15,
            'strength_distribution': {
                'strong': 5,
                'medium': 7,
                'weak': 3
            },
            'relationship_distribution': {
                'colleague': 6,
                'business_partner': 4,
                'industry_peer': 3,
                'investor_founder': 2
            },
            'introduction_stats': {
                'requests_sent': 5,
                'requests_received': 3,
                'introductions_made': 8,
                'successful_intros': 6
            },
            'network_reach': {
                'depth_1': 15,
                'depth_2': 47
            },
            'key_connectors': [
                {
                    'user': {
                        'id': str(uuid4()),
                        'name': 'Super Connector',
                        'email': '<EMAIL>'
                    },
                    'connector_score': 25.5
                }
            ]
        }
        mock_warm_intro_service.get_connection_analytics.return_value = mock_analytics
        
        # Execute
        response = test_client_with_auth.get("/connections/analytics")
        
        # Verify
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        assert data['total_connections'] == 15
        assert data['strength_distribution']['strong'] == 5
        assert data['relationship_distribution']['colleague'] == 6
        assert data['introduction_stats']['successful_intros'] == 6
        assert data['network_reach']['depth_2'] == 47
        assert len(data['key_connectors']) == 1
        assert data['key_connectors'][0]['user']['name'] == 'Super Connector'
    
    def test_get_analytics_service_error(self, test_client_with_auth, mock_warm_intro_service):
        """Test handling of service errors."""
        mock_warm_intro_service.get_connection_analytics.side_effect = Exception("Database error")
        
        response = test_client_with_auth.get("/connections/analytics")
        
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert response.json()['detail'] == "Failed to get analytics"


class TestEndpointSecurity:
    """Test security aspects of endpoints."""
    
    def test_endpoints_require_authentication(self):
        """Test that all endpoints require authentication."""
        client = TestClient(app)
        
        endpoints_to_test = [
            ("POST", "/connections/"),
            ("GET", "/connections/"),
            ("GET", f"/connections/paths/to-startup/{uuid4()}"),
            ("GET", f"/connections/paths/to-vc/{uuid4()}"),
            ("POST", "/connections/introductions/request"),
            ("GET", "/connections/introductions/pending"),
            ("PUT", f"/connections/introductions/{uuid4()}/respond"),
            ("GET", "/connections/analytics")
        ]
        
        for method, endpoint in endpoints_to_test:
            response = client.request(method, endpoint)
            # Should get 401 Unauthorized or 403 Forbidden (depending on auth implementation)
            assert response.status_code in [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN]
    
    def test_invalid_uuid_parameters(self, test_client_with_auth):
        """Test handling of invalid UUID parameters."""
        # Invalid startup_id
        response = test_client_with_auth.get("/connections/paths/to-startup/invalid-uuid")
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        
        # Invalid vc_id
        response = test_client_with_auth.get("/connections/paths/to-vc/invalid-uuid")
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        
        # Invalid request_id
        response = test_client_with_auth.put(
            "/connections/introductions/invalid-uuid/respond",
            json={"accept": True}
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


class TestEndpointInputValidation:
    """Test input validation across endpoints."""
    
    def test_create_connection_missing_fields(self, test_client_with_auth):
        """Test creation with missing required fields."""
        # Missing other_user_id
        response = test_client_with_auth.post(
            "/connections/",
            json={
                "relationship_type": "colleague",
                "trust_score": 0.5
            }
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        
        # Missing relationship_type
        response = test_client_with_auth.post(
            "/connections/",
            json={
                "other_user_id": str(uuid4()),
                "trust_score": 0.5
            }
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_request_introduction_validation(self, test_client_with_auth):
        """Test introduction request validation."""
        # Missing target_id
        response = test_client_with_auth.post(
            "/connections/introductions/request",
            json={
                "connector_id": str(uuid4()),
                "message": "Please introduce me for collaboration"
            }
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        
        # Missing connector_id
        response = test_client_with_auth.post(
            "/connections/introductions/request",
            json={
                "target_id": str(uuid4()),
                "message": "Please introduce me for collaboration"
            }
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        
        # Missing message
        response = test_client_with_auth.post(
            "/connections/introductions/request",
            json={
                "target_id": str(uuid4()),
                "connector_id": str(uuid4())
            }
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_respond_to_intro_validation(self, test_client_with_auth):
        """Test introduction response validation."""
        request_id = uuid4()
        
        # Missing accept field
        response = test_client_with_auth.put(
            f"/connections/introductions/{request_id}/respond",
            json={"notes": "Some notes"}
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        
        # Invalid accept field type
        response = test_client_with_auth.put(
            f"/connections/introductions/{request_id}/respond",
            json={"accept": "yes"}  # Should be boolean
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY