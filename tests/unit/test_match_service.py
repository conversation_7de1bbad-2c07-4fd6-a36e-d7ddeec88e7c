"""Comprehensive tests for MatchService with proper coverage.

This test suite provides comprehensive coverage for all methods in MatchService,
including edge cases, error handling, and proper mocking of dependencies.
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from uuid import UUID, uuid4
from datetime import datetime
from typing import List, Dict, Any

from src.core.services.match_service import MatchService
from src.core.models.match import Match
from src.core.models.startup import Startup
from src.core.models.vc import VC
from src.core.repositories.startup_repository import StartupRepository
from src.core.repositories.vc_repository import VCRepository
from src.core.ports.ai_port import AIPort, MatchRationale, AIAnalysisError
from src.core.schemas.match import MatchStatus, MatchType
from src.core.services.matching_engine import MatchingEngine


class TestMatchService:
    """Test suite for MatchService with comprehensive coverage."""
    
    @pytest.fixture
    def mock_startup_repo(self):
        """Mock startup repository."""
        repo = Mock(spec=StartupRepository)
        repo.find_by_id = AsyncMock()
        return repo
    
    @pytest.fixture
    def mock_vc_repo(self):
        """Mock VC repository."""
        repo = Mock(spec=VCRepository)
        repo.find_by_id = AsyncMock()
        return repo
    
    @pytest.fixture
    def mock_ai_port(self):
        """Mock AI port."""
        port = Mock(spec=AIPort)
        port.generate_match_rationale = AsyncMock()
        return port
    
    @pytest.fixture
    def mock_matching_engine(self):
        """Mock matching engine."""
        with patch('src.core.services.match_service.MatchingEngine') as mock:
            engine = Mock(spec=MatchingEngine)
            # Create a mock match object for calculate_match
            mock_match = Mock()
            mock_match.score = 0.75
            mock_match.reasons = ["Industry alignment", "Stage match"]
            engine.calculate_match = Mock(return_value=mock_match)
            # Also mock the methods the service is actually calling
            engine.calculate_match_score = Mock(return_value=0.75)
            engine.generate_match_reasons = Mock(return_value=["Industry alignment", "Stage match"])
            mock.return_value = engine
            yield engine
    
    @pytest.fixture
    def match_service(self, mock_startup_repo, mock_vc_repo, mock_ai_port, mock_matching_engine):
        """Create MatchService instance with mocked dependencies."""
        service = MatchService(
            startup_repository=mock_startup_repo,
            vc_repository=mock_vc_repo,
            ai_port=mock_ai_port
        )
        # Replace the matching engine with our mock
        service.matching_engine = mock_matching_engine
        return service
    
    @pytest.fixture
    def sample_startup(self):
        """Create a sample startup."""
        return Startup(
            id=uuid4(),
            name="AI Startup",
            sector="Artificial Intelligence",
            stage="Series A",
            description="AI-powered analytics platform",
            website="https://aistartup.com",
            team_size=25,
            monthly_revenue=50000
        )
    
    @pytest.fixture
    def sample_vc(self):
        """Create a sample VC."""
        return VC(
            id=uuid4(),
            firm_name="Tech Ventures",
            website="https://techventures.com",
            thesis="Investing in AI and ML startups at early stages",
            sectors=["AI", "FinTech", "HealthTech"],
            stages=["Seed", "Series A", "Series B"],
            check_size_min=100000,
            check_size_max=10000000
        )
    
    @pytest.fixture
    def sample_match_rationale(self):
        """Create a sample match rationale from AI."""
        return MatchRationale(
            compatibility_score=0.85,
            key_alignments=["Strong AI focus", "Stage alignment", "Geographic proximity"],
            potential_concerns=["Competition with portfolio company"],
            suggested_talking_points=["AI market opportunity", "Technical differentiation"],
            confidence_level=0.9
        )
    
    # Test create_match method
    @pytest.mark.asyncio
    async def test_create_match_manual_success(self, match_service, mock_startup_repo, 
                                             mock_vc_repo, sample_startup, sample_vc):
        """Test successful manual match creation."""
        startup_id = sample_startup.id
        vc_id = sample_vc.id
        
        mock_startup_repo.find_by_id.return_value = sample_startup
        mock_vc_repo.find_by_id.return_value = sample_vc
        
        # Execute
        result = await match_service.create_match(
            startup_id=startup_id,
            vc_id=vc_id,
            match_type=MatchType.MANUAL,
            notes="Introduced at conference"
        )
        
        # Assert
        assert result.startup.id == startup_id
        assert result.vc.id == vc_id
        assert result.match_type == MatchType.MANUAL
        assert result.status == MatchStatus.PENDING
        assert result.notes == "Introduced at conference"
        assert result.score == 0.75  # From mock matching engine
        assert "Industry alignment" in result.reasons
        
        # Verify calls
        mock_startup_repo.find_by_id.assert_called_once_with(startup_id)
        mock_vc_repo.find_by_id.assert_called_once_with(vc_id)
    
    @pytest.mark.asyncio
    async def test_create_match_ai_enhanced_success(self, match_service, mock_startup_repo, 
                                                  mock_vc_repo, mock_ai_port, sample_startup, 
                                                  sample_vc, sample_match_rationale):
        """Test successful AI-enhanced match creation."""
        startup_id = sample_startup.id
        vc_id = sample_vc.id
        
        mock_startup_repo.find_by_id.return_value = sample_startup
        mock_vc_repo.find_by_id.return_value = sample_vc
        mock_ai_port.generate_match_rationale.return_value = sample_match_rationale
        
        # Execute
        result = await match_service.create_match(
            startup_id=startup_id,
            vc_id=vc_id,
            match_type=MatchType.AI_ENHANCED,
            use_ai_insights=True
        )
        
        # Assert
        assert result.score == 0.85  # From AI rationale
        assert result.reasons == sample_match_rationale.key_alignments
        assert result.match_type == MatchType.AI_ENHANCED
        
        # Verify AI was called
        mock_ai_port.generate_match_rationale.assert_called_once_with(sample_startup, sample_vc)
    
    @pytest.mark.asyncio
    async def test_create_match_already_exists(self, match_service, mock_startup_repo, 
                                             mock_vc_repo, sample_startup, sample_vc):
        """Test creating a match that already exists."""
        startup_id = sample_startup.id
        vc_id = sample_vc.id
        
        mock_startup_repo.find_by_id.return_value = sample_startup
        mock_vc_repo.find_by_id.return_value = sample_vc
        
        # Create first match
        await match_service.create_match(startup_id, vc_id)
        
        # Try to create duplicate
        with pytest.raises(ValueError, match="Match already exists"):
            await match_service.create_match(startup_id, vc_id)
    
    @pytest.mark.asyncio
    async def test_create_match_startup_not_found(self, match_service, mock_startup_repo):
        """Test match creation when startup is not found."""
        startup_id = uuid4()
        vc_id = uuid4()
        
        mock_startup_repo.find_by_id.return_value = None
        
        with pytest.raises(ValueError, match=f"Startup {startup_id} not found"):
            await match_service.create_match(startup_id, vc_id)
    
    @pytest.mark.asyncio
    async def test_create_match_vc_not_found(self, match_service, mock_startup_repo, 
                                           mock_vc_repo, sample_startup):
        """Test match creation when VC is not found."""
        startup_id = sample_startup.id
        vc_id = uuid4()
        
        mock_startup_repo.find_by_id.return_value = sample_startup
        mock_vc_repo.find_by_id.return_value = None
        
        with pytest.raises(ValueError, match=f"VC {vc_id} not found"):
            await match_service.create_match(startup_id, vc_id)
    
    # Test list_matches method
    @pytest.mark.asyncio
    async def test_list_matches_no_filters(self, match_service, mock_startup_repo, 
                                         mock_vc_repo, sample_startup, sample_vc):
        """Test listing all matches without filters."""
        # Create multiple matches
        startup_ids = [sample_startup.id, uuid4(), uuid4()]
        vc_ids = [sample_vc.id, uuid4()]
        
        mock_startup_repo.find_by_id.return_value = sample_startup
        mock_vc_repo.find_by_id.return_value = sample_vc
        
        # Create 3 matches
        for i in range(3):
            await match_service.create_match(startup_ids[i % len(startup_ids)], 
                                           vc_ids[i % len(vc_ids)])
        
        # List all matches
        result = await match_service.list_matches()
        
        assert len(result) == 3
        # Should be sorted by score descending
        assert all(result[i].score >= result[i+1].score for i in range(len(result)-1))
    
    @pytest.mark.asyncio
    async def test_list_matches_by_startup(self, match_service, mock_startup_repo, 
                                         mock_vc_repo, sample_startup, sample_vc):
        """Test filtering matches by startup."""
        startup_id = sample_startup.id
        other_startup_id = uuid4()
        other_vc_id = uuid4()
        
        # Mock different returns for different IDs
        other_startup = Mock()
        other_startup.id = other_startup_id
        other_startup.name = "Other Startup"
        
        mock_startup_repo.find_by_id.side_effect = lambda id: sample_startup if id == startup_id else other_startup
        mock_vc_repo.find_by_id.return_value = sample_vc
        
        # Create matches for different startups with different VCs to avoid duplicates
        await match_service.create_match(startup_id, sample_vc.id)
        await match_service.create_match(other_startup_id, other_vc_id)
        
        # Filter by startup
        result = await match_service.list_matches(startup_id=startup_id)
        
        assert len(result) == 1
        assert result[0].startup.id == startup_id
    
    @pytest.mark.asyncio
    async def test_list_matches_by_vc(self, match_service, mock_startup_repo, 
                                    mock_vc_repo, sample_startup, sample_vc):
        """Test filtering matches by VC."""
        vc_id = sample_vc.id
        other_vc_id = uuid4()
        other_startup_id = uuid4()
        
        # Mock different VC for second match
        other_vc = Mock()
        other_vc.id = other_vc_id
        other_vc.firm_name = "Other VC"
        
        mock_startup_repo.find_by_id.return_value = sample_startup
        mock_vc_repo.find_by_id.side_effect = lambda id: sample_vc if id == vc_id else other_vc
        
        # Create matches for different VCs with different startups to avoid duplicates
        await match_service.create_match(sample_startup.id, vc_id)
        await match_service.create_match(other_startup_id, other_vc_id)
        
        # Filter by VC
        result = await match_service.list_matches(vc_id=vc_id)
        
        assert len(result) == 1
        assert result[0].vc.id == vc_id
    
    @pytest.mark.asyncio
    async def test_list_matches_by_status(self, match_service, mock_startup_repo, 
                                        mock_vc_repo, sample_startup, sample_vc):
        """Test filtering matches by status."""
        mock_startup_repo.find_by_id.return_value = sample_startup
        mock_vc_repo.find_by_id.return_value = sample_vc
        
        # Create a match and update its status
        match = await match_service.create_match(sample_startup.id, sample_vc.id)
        await match_service.update_match(match.id, {"status": MatchStatus.REVIEWED})
        
        # Create another match (will be PENDING)
        await match_service.create_match(sample_startup.id, uuid4())
        
        # Filter by REVIEWED status
        result = await match_service.list_matches(status=MatchStatus.REVIEWED)
        
        assert len(result) == 1
        assert result[0].status == MatchStatus.REVIEWED
    
    @pytest.mark.asyncio
    async def test_list_matches_by_min_score(self, match_service, mock_startup_repo, 
                                           mock_vc_repo, mock_matching_engine, sample_startup):
        """Test filtering matches by minimum score."""
        mock_startup_repo.find_by_id.return_value = sample_startup
        
        # Create matches with different scores using different VCs
        scores = [0.9, 0.7, 0.5]
        vcs = []
        for i, score in enumerate(scores):
            # Create a unique VC for each match
            vc = VC(
                id=uuid4(),
                firm_name=f"VC {i+1}",
                website=f"https://vc{i+1}.com",
                thesis="Test thesis",
                sectors=["Tech"],
                stages=["Series A"]
            )
            vcs.append(vc)
            mock_vc_repo.find_by_id.return_value = vc
            
            # Update the mock to return the correct score
            mock_match = Mock()
            mock_match.score = score
            mock_match.reasons = ["Industry alignment", "Stage match"]
            mock_matching_engine.calculate_match.return_value = mock_match
            await match_service.create_match(sample_startup.id, vc.id)
        
        # Filter by minimum score
        result = await match_service.list_matches(min_score=0.7)
        
        assert len(result) == 2
        assert all(m.score >= 0.7 for m in result)
    
    # Test get_match method
    @pytest.mark.asyncio
    async def test_get_match_success(self, match_service, mock_startup_repo, 
                                   mock_vc_repo, sample_startup, sample_vc):
        """Test getting a specific match."""
        mock_startup_repo.find_by_id.return_value = sample_startup
        mock_vc_repo.find_by_id.return_value = sample_vc
        
        # Create a match
        created_match = await match_service.create_match(sample_startup.id, sample_vc.id)
        
        # Get the match
        result = await match_service.get_match(created_match.id)
        
        assert result.id == created_match.id
        assert result.startup.id == sample_startup.id
        assert result.vc.id == sample_vc.id
    
    @pytest.mark.asyncio
    async def test_get_match_not_found(self, match_service):
        """Test getting a match that doesn't exist."""
        non_existent_id = uuid4()
        
        with pytest.raises(ValueError, match=f"Match {non_existent_id} not found"):
            await match_service.get_match(non_existent_id)
    
    # Test update_match method
    @pytest.mark.asyncio
    async def test_update_match_success(self, match_service, mock_startup_repo, 
                                      mock_vc_repo, sample_startup, sample_vc):
        """Test updating a match."""
        mock_startup_repo.find_by_id.return_value = sample_startup
        mock_vc_repo.find_by_id.return_value = sample_vc
        
        # Create a match
        match = await match_service.create_match(sample_startup.id, sample_vc.id)
        original_updated_at = match.updated_at
        
        # Update the match
        updates = {
            "status": MatchStatus.REVIEWED,
            "notes": "Great fit confirmed",
            "score": 0.95
        }
        
        result = await match_service.update_match(match.id, updates)
        
        assert result.status == MatchStatus.REVIEWED
        assert result.notes == "Great fit confirmed"
        assert result.score == 0.95
        assert result.updated_at > original_updated_at
    
    @pytest.mark.asyncio
    async def test_update_match_not_found(self, match_service):
        """Test updating a match that doesn't exist."""
        non_existent_id = uuid4()
        
        with pytest.raises(ValueError, match=f"Match {non_existent_id} not found"):
            await match_service.update_match(non_existent_id, {"status": MatchStatus.REVIEWED})
    
    @pytest.mark.asyncio
    async def test_update_match_invalid_field(self, match_service, mock_startup_repo, 
                                            mock_vc_repo, sample_startup, sample_vc):
        """Test updating a match with invalid fields."""
        mock_startup_repo.find_by_id.return_value = sample_startup
        mock_vc_repo.find_by_id.return_value = sample_vc
        
        # Create a match
        match = await match_service.create_match(sample_startup.id, sample_vc.id)
        
        # Update with invalid field (should be ignored)
        result = await match_service.update_match(match.id, {"invalid_field": "value"})
        
        # Should succeed but not have the invalid field
        assert not hasattr(result, "invalid_field")
    
    # Test delete_match method
    @pytest.mark.asyncio
    async def test_delete_match_success(self, match_service, mock_startup_repo, 
                                      mock_vc_repo, sample_startup, sample_vc):
        """Test deleting a match."""
        mock_startup_repo.find_by_id.return_value = sample_startup
        mock_vc_repo.find_by_id.return_value = sample_vc
        
        # Create a match
        match = await match_service.create_match(sample_startup.id, sample_vc.id)
        
        # Delete the match
        result = await match_service.delete_match(match.id)
        
        assert result is True
        
        # Verify it's deleted
        with pytest.raises(ValueError):
            await match_service.get_match(match.id)
    
    @pytest.mark.asyncio
    async def test_delete_match_not_found(self, match_service):
        """Test deleting a match that doesn't exist."""
        non_existent_id = uuid4()
        
        result = await match_service.delete_match(non_existent_id)
        
        assert result is False
    
    # Test batch_match method
    @pytest.mark.asyncio
    async def test_batch_match_success(self, match_service, mock_startup_repo, 
                                     mock_vc_repo, mock_ai_port, mock_matching_engine,
                                     sample_match_rationale):
        """Test batch matching with AI."""
        # Create test data
        startup_ids = [uuid4() for _ in range(2)]
        vc_ids = [uuid4() for _ in range(2)]
        
        startups = []
        for sid in startup_ids:
            startup = Mock(spec=Startup)
            startup.id = sid
            startups.append(startup)
        
        vcs = []
        for vid in vc_ids:
            vc = Mock(spec=VC)
            vc.id = vid
            vcs.append(vc)
        
        # Mock repository responses
        mock_startup_repo.find_by_id.side_effect = startups
        mock_vc_repo.find_by_id.side_effect = vcs
        
        # Mock AI responses with different scores
        mock_ai_port.generate_match_rationale.return_value = sample_match_rationale
        
        # Execute
        result = await match_service.batch_match(
            startup_ids=startup_ids,
            vc_ids=vc_ids,
            match_type=MatchType.AI_ENHANCED,
            min_score_threshold=0.8
        )
        
        # Should create matches only above threshold (0.85 > 0.8)
        assert len(result) == 4  # 2 startups x 2 VCs
        assert all(m.score >= 0.8 for m in result)
        assert all(m.match_type == MatchType.AI_ENHANCED for m in result)
    
    @pytest.mark.asyncio
    async def test_batch_match_with_threshold_filtering(self, match_service, mock_startup_repo, 
                                                      mock_vc_repo, mock_matching_engine):
        """Test batch matching filters by score threshold."""
        startup_ids = [uuid4()]
        vc_ids = [uuid4() for _ in range(3)]
        
        startup = Mock(spec=Startup)
        startup.id = startup_ids[0]
        mock_startup_repo.find_by_id.return_value = startup
        
        vcs = []
        for vid in vc_ids:
            vc = Mock(spec=VC)
            vc.id = vid
            vcs.append(vc)
        
        mock_vc_repo.find_by_id.side_effect = vcs
        
        # Mock different scores
        scores = [0.9, 0.7, 0.5]
        match_results = []
        for score in scores:
            mock_match = Mock()
            mock_match.score = score
            mock_match.reasons = ["Test reason"]
            match_results.append(mock_match)
        mock_matching_engine.calculate_match.side_effect = match_results
        
        # Execute with high threshold
        result = await match_service.batch_match(
            startup_ids=startup_ids,
            vc_ids=vc_ids,
            match_type=MatchType.AUTOMATED,
            min_score_threshold=0.8
        )
        
        # Should only create 1 match (score 0.9)
        assert len(result) == 1
        assert result[0].score == 0.9
    
    @pytest.mark.asyncio
    async def test_batch_match_skip_existing(self, match_service, mock_startup_repo, 
                                           mock_vc_repo, sample_startup, sample_vc):
        """Test batch matching skips existing matches."""
        mock_startup_repo.find_by_id.return_value = sample_startup
        mock_vc_repo.find_by_id.return_value = sample_vc
        
        # Create an existing match
        await match_service.create_match(sample_startup.id, sample_vc.id)
        
        # Try batch match with same pair
        result = await match_service.batch_match(
            startup_ids=[sample_startup.id],
            vc_ids=[sample_vc.id],
            min_score_threshold=0.5
        )
        
        # Should not create duplicate
        assert len(result) == 0
    
    @pytest.mark.asyncio
    async def test_batch_match_ai_fallback(self, match_service, mock_startup_repo, 
                                         mock_vc_repo, mock_ai_port, mock_matching_engine):
        """Test batch matching falls back to basic engine when AI fails."""
        startup_ids = [uuid4()]
        vc_ids = [uuid4()]
        
        startup = Mock(spec=Startup)
        startup.id = startup_ids[0]
        vc = Mock(spec=VC)
        vc.id = vc_ids[0]
        
        mock_startup_repo.find_by_id.return_value = startup
        mock_vc_repo.find_by_id.return_value = vc
        
        # Mock AI failure
        mock_ai_port.generate_match_rationale.side_effect = Exception("AI service unavailable")
        
        # Execute
        result = await match_service.batch_match(
            startup_ids=startup_ids,
            vc_ids=vc_ids,
            match_type=MatchType.AI_ENHANCED,
            min_score_threshold=0.5
        )
        
        # Should fall back to basic matching
        assert len(result) == 1
        assert result[0].score == 0.75  # From mock matching engine
    
    @pytest.mark.asyncio
    async def test_batch_match_startup_not_found(self, match_service, mock_startup_repo):
        """Test batch matching with non-existent startup."""
        startup_id = uuid4()
        vc_ids = [uuid4()]
        
        mock_startup_repo.find_by_id.return_value = None
        
        with pytest.raises(ValueError, match=f"Startup {startup_id} not found"):
            await match_service.batch_match([startup_id], vc_ids)
    
    @pytest.mark.asyncio
    async def test_batch_match_vc_not_found(self, match_service, mock_startup_repo, 
                                          mock_vc_repo, sample_startup):
        """Test batch matching with non-existent VC."""
        vc_id = uuid4()
        
        mock_startup_repo.find_by_id.return_value = sample_startup
        mock_vc_repo.find_by_id.return_value = None
        
        with pytest.raises(ValueError, match=f"VC {vc_id} not found"):
            await match_service.batch_match([sample_startup.id], [vc_id])
    
    # Test get_ai_insights_for_match method
    @pytest.mark.asyncio
    async def test_get_ai_insights_success(self, match_service, mock_startup_repo, 
                                         mock_vc_repo, mock_ai_port, sample_startup, 
                                         sample_vc, sample_match_rationale):
        """Test getting AI insights for an existing match."""
        mock_startup_repo.find_by_id.return_value = sample_startup
        mock_vc_repo.find_by_id.return_value = sample_vc
        mock_ai_port.generate_match_rationale.return_value = sample_match_rationale
        
        # Create a manual match
        match = await match_service.create_match(
            sample_startup.id, 
            sample_vc.id,
            match_type=MatchType.MANUAL
        )
        original_score = match.score
        
        # Get AI insights
        result = await match_service.get_ai_insights_for_match(match.id)
        
        assert result == sample_match_rationale
        
        # Verify match was updated with AI insights
        updated_match = await match_service.get_match(match.id)
        assert updated_match.score == sample_match_rationale.compatibility_score
        assert updated_match.reasons == sample_match_rationale.key_alignments
        assert updated_match.score != original_score
    
    @pytest.mark.asyncio
    async def test_get_ai_insights_match_not_found(self, match_service):
        """Test getting AI insights for non-existent match."""
        non_existent_id = uuid4()
        
        with pytest.raises(ValueError, match=f"Match {non_existent_id} not found"):
            await match_service.get_ai_insights_for_match(non_existent_id)
    
    @pytest.mark.asyncio
    async def test_get_ai_insights_ai_failure(self, match_service, mock_startup_repo, 
                                            mock_vc_repo, mock_ai_port, sample_startup, sample_vc):
        """Test AI insights when AI service fails."""
        mock_startup_repo.find_by_id.return_value = sample_startup
        mock_vc_repo.find_by_id.return_value = sample_vc
        
        # Create a match
        match = await match_service.create_match(sample_startup.id, sample_vc.id)
        
        # Mock AI failure
        mock_ai_port.generate_match_rationale.side_effect = AIAnalysisError("AI service error")
        
        # Should propagate the error
        with pytest.raises(AIAnalysisError):
            await match_service.get_ai_insights_for_match(match.id)
    
    # Test edge cases and error conditions
    @pytest.mark.asyncio
    async def test_create_match_with_none_notes(self, match_service, mock_startup_repo, 
                                               mock_vc_repo, sample_startup, sample_vc):
        """Test creating match with None notes."""
        mock_startup_repo.find_by_id.return_value = sample_startup
        mock_vc_repo.find_by_id.return_value = sample_vc
        
        result = await match_service.create_match(
            sample_startup.id, 
            sample_vc.id,
            notes=None
        )
        
        assert result.notes is None
    
    @pytest.mark.asyncio
    async def test_list_matches_empty_repository(self, match_service):
        """Test listing matches when none exist."""
        result = await match_service.list_matches()
        
        assert result == []
    
    @pytest.mark.asyncio
    async def test_list_matches_multiple_filters(self, match_service, mock_startup_repo, 
                                               mock_vc_repo, sample_startup, sample_vc):
        """Test listing matches with multiple filters."""
        mock_startup_repo.find_by_id.return_value = sample_startup
        mock_vc_repo.find_by_id.return_value = sample_vc
        
        # Create matches with different properties
        match1 = await match_service.create_match(sample_startup.id, sample_vc.id)
        await match_service.update_match(match1.id, {"status": MatchStatus.REVIEWED})
        
        # Create another match with different startup
        other_startup_id = uuid4()
        await match_service.create_match(other_startup_id, sample_vc.id)
        
        # Apply multiple filters
        result = await match_service.list_matches(
            startup_id=sample_startup.id,
            status=MatchStatus.REVIEWED,
            min_score=0.7
        )
        
        assert len(result) == 1
        assert result[0].id == match1.id
    
    @pytest.mark.asyncio
    async def test_concurrent_match_creation(self, match_service, mock_startup_repo, 
                                           mock_vc_repo, sample_startup, sample_vc):
        """Test that concurrent match creation is handled properly."""
        mock_startup_repo.find_by_id.return_value = sample_startup
        mock_vc_repo.find_by_id.return_value = sample_vc
        
        # Create multiple matches concurrently
        import asyncio
        
        async def create_match():
            return await match_service.create_match(uuid4(), uuid4())
        
        # This should work without issues
        tasks = [create_match() for _ in range(5)]
        results = await asyncio.gather(*tasks)
        
        assert len(results) == 5
        assert len(set(m.id for m in results)) == 5  # All unique IDs
    
    # Additional edge case tests
    @pytest.mark.asyncio
    async def test_create_match_ai_enhanced_without_ai_insights(self, match_service, mock_startup_repo, 
                                                               mock_vc_repo, sample_startup, sample_vc):
        """Test AI-enhanced match creation with use_ai_insights=False."""
        mock_startup_repo.find_by_id.return_value = sample_startup
        mock_vc_repo.find_by_id.return_value = sample_vc
        
        # Create AI_ENHANCED match without AI insights
        result = await match_service.create_match(
            sample_startup.id,
            sample_vc.id,
            match_type=MatchType.AI_ENHANCED,
            use_ai_insights=False
        )
        
        # Should use matching engine instead of AI
        assert result.score == 0.75  # From mock matching engine
        assert "Industry alignment" in result.reasons
        assert result.match_type == MatchType.AI_ENHANCED
    
    @pytest.mark.asyncio
    async def test_batch_match_empty_lists(self, match_service):
        """Test batch matching with empty lists."""
        # Test with empty startup list
        result = await match_service.batch_match([], [uuid4()])
        assert result == []
        
        # Test with empty VC list
        result = await match_service.batch_match([uuid4()], [])
        assert result == []
        
        # Test with both empty
        result = await match_service.batch_match([], [])
        assert result == []
    
    @pytest.mark.asyncio
    async def test_batch_match_automated_type(self, match_service, mock_startup_repo, 
                                            mock_vc_repo, mock_matching_engine):
        """Test batch matching with AUTOMATED type uses matching engine."""
        startup_id = uuid4()
        vc_id = uuid4()
        
        startup = Mock(spec=Startup)
        startup.id = startup_id
        vc = Mock(spec=VC)
        vc.id = vc_id
        
        mock_startup_repo.find_by_id.return_value = startup
        mock_vc_repo.find_by_id.return_value = vc
        
        # Execute with AUTOMATED type
        result = await match_service.batch_match(
            [startup_id],
            [vc_id],
            match_type=MatchType.AUTOMATED,
            min_score_threshold=0.5
        )
        
        # Should use matching engine, not AI
        assert len(result) == 1
        assert result[0].match_type == MatchType.AUTOMATED
        assert result[0].score == 0.75  # From mock
        mock_matching_engine.calculate_match.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_update_match_with_datetime_field(self, match_service, mock_startup_repo, 
                                                   mock_vc_repo, sample_startup, sample_vc):
        """Test updating match with datetime fields."""
        mock_startup_repo.find_by_id.return_value = sample_startup
        mock_vc_repo.find_by_id.return_value = sample_vc
        
        # Create a match
        match = await match_service.create_match(sample_startup.id, sample_vc.id)
        
        # Update with created_at (should be allowed)
        new_created_at = datetime(2020, 1, 1)
        result = await match_service.update_match(match.id, {"created_at": new_created_at})
        
        assert result.created_at == new_created_at
    
    @pytest.mark.asyncio
    async def test_list_matches_with_zero_min_score(self, match_service, mock_startup_repo, 
                                                   mock_vc_repo, sample_startup, sample_vc):
        """Test listing matches with min_score of 0."""
        mock_startup_repo.find_by_id.return_value = sample_startup
        mock_vc_repo.find_by_id.return_value = sample_vc
        
        # Create a match
        await match_service.create_match(sample_startup.id, sample_vc.id)
        
        # List with min_score=0 (should return all)
        result = await match_service.list_matches(min_score=0.0)
        assert len(result) == 1
    
    @pytest.mark.asyncio
    async def test_get_ai_insights_updates_timestamp(self, match_service, mock_startup_repo, 
                                                   mock_vc_repo, mock_ai_port, sample_startup, 
                                                   sample_vc, sample_match_rationale):
        """Test that getting AI insights updates the match timestamp."""
        mock_startup_repo.find_by_id.return_value = sample_startup
        mock_vc_repo.find_by_id.return_value = sample_vc
        mock_ai_port.generate_match_rationale.return_value = sample_match_rationale
        
        # Create a match
        match = await match_service.create_match(sample_startup.id, sample_vc.id)
        original_updated_at = match.updated_at
        
        # Sleep a tiny bit to ensure timestamp difference
        import time
        time.sleep(0.001)
        
        # Get AI insights
        await match_service.get_ai_insights_for_match(match.id)
        
        # Check timestamp was updated
        updated_match = await match_service.get_match(match.id)
        assert updated_match.updated_at > original_updated_at
    
    @pytest.mark.asyncio
    async def test_batch_match_manual_type(self, match_service, mock_startup_repo, 
                                         mock_vc_repo, mock_matching_engine):
        """Test batch matching with MANUAL type."""
        startup_id = uuid4()
        vc_id = uuid4()
        
        startup = Mock(spec=Startup)
        startup.id = startup_id
        vc = Mock(spec=VC)
        vc.id = vc_id
        
        mock_startup_repo.find_by_id.return_value = startup
        mock_vc_repo.find_by_id.return_value = vc
        
        # Execute with MANUAL type
        result = await match_service.batch_match(
            [startup_id],
            [vc_id],
            match_type=MatchType.MANUAL,
            min_score_threshold=0.5
        )
        
        # Should use matching engine for MANUAL type
        assert len(result) == 1
        assert result[0].match_type == MatchType.MANUAL
        mock_matching_engine.calculate_match.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_batch_match_high_threshold_filters_all(self, match_service, mock_startup_repo, 
                                                         mock_vc_repo, mock_matching_engine):
        """Test batch matching with very high threshold filters all matches."""
        startup_id = uuid4()
        vc_id = uuid4()
        
        startup = Mock(spec=Startup)
        startup.id = startup_id
        vc = Mock(spec=VC)
        vc.id = vc_id
        
        mock_startup_repo.find_by_id.return_value = startup
        mock_vc_repo.find_by_id.return_value = vc
        mock_matching_engine.calculate_match_score.return_value = 0.5
        
        # Execute with very high threshold
        result = await match_service.batch_match(
            [startup_id],
            [vc_id],
            match_type=MatchType.AUTOMATED,
            min_score_threshold=0.99
        )
        
        # Should filter out all matches
        assert result == []
    
    @pytest.mark.asyncio
    async def test_dataclasses_replace_not_used(self, match_service):
        """Test that dataclasses.replace import is not actually used."""
        # This test documents that the import is unused
        # The service uses setattr for updates instead
        from src.core.services.match_service import replace
        # Just verify the import exists but isn't used in the codebase
        assert replace is not None