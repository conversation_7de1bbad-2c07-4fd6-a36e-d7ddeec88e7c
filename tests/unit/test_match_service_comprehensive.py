"""Comprehensive tests for MatchService to improve coverage."""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from uuid import uuid4
from datetime import datetime
from typing import List, Optional

from src.core.services.match_service import MatchService
from src.core.models.match import Match
from src.core.models.startup import Startup
from src.core.models.vc import VC
from src.core.schemas.match import Match<PERSON>tatus, MatchType, MatchCreate, MatchUpdate


class TestMatchService:
    """Test suite for MatchService with mocked dependencies."""
    
    @pytest.fixture
    def mock_startup_repo(self):
        """Mock startup repository."""
        repo = Mock()
        repo.get = AsyncMock()
        repo.get_all = AsyncMock()
        repo.search = AsyncMock()
        return repo
    
    @pytest.fixture
    def mock_vc_repo(self):
        """Mock VC repository."""
        repo = Mock()
        repo.get = AsyncMock()
        repo.get_all = AsyncMock()
        repo.search = AsyncMock()
        return repo
    
    @pytest.fixture
    def mock_match_repo(self):
        """Mock match repository."""
        repo = Mock()
        repo.create = AsyncMock()
        repo.get = AsyncMock()
        repo.update = AsyncMock()
        repo.delete = AsyncMock()
        repo.get_all = AsyncMock()
        repo.get_by_startup = AsyncMock()
        repo.get_by_vc = AsyncMock()
        repo.get_by_status = AsyncMock()
        return repo
    
    @pytest.fixture
    def mock_ai_service(self):
        """Mock AI service."""
        service = Mock()
        service.analyze_compatibility = AsyncMock()
        service.generate_match_insights = AsyncMock()
        service.suggest_matches = AsyncMock()
        return service
    
    @pytest.fixture
    def match_service(self, mock_startup_repo, mock_vc_repo, mock_match_repo, mock_ai_service):
        """Create MatchService instance with mocked dependencies."""
        return MatchService(
            startup_repository=mock_startup_repo,
            vc_repository=mock_vc_repo,
            match_repository=mock_match_repo,
            ai_service=mock_ai_service
        )
    
    @pytest.fixture
    def sample_startup(self):
        """Create a sample startup."""
        return Startup(
            id=uuid4(),
            name="AI Startup",
            industry="Artificial Intelligence",
            stage="Series A",
            location="San Francisco",
            description="AI-powered analytics platform",
            website="https://aistartup.com",
            founded_date=datetime(2020, 1, 1),
            team_size=25,
            funding_raised=5000000,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    
    @pytest.fixture
    def sample_vc(self):
        """Create a sample VC."""
        return VC(
            id=uuid4(),
            name="Tech Ventures",
            website="https://techventures.com",
            investment_focus=["AI", "FinTech", "HealthTech"],
            investment_stage=["Seed", "Series A", "Series B"],
            check_size_min=100000,
            check_size_max=10000000,
            portfolio_companies=["Company1", "Company2"],
            location="San Francisco",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    
    @pytest.fixture
    def sample_match(self, sample_startup, sample_vc):
        """Create a sample match."""
        return Match(
            id=uuid4(),
            startup_id=sample_startup.id,
            vc_id=sample_vc.id,
            status=MatchStatus.PENDING,
            match_type=MatchType.AI_SUGGESTED,
            score=0.85,
            reasoning="Strong alignment in AI focus and investment stage",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    
    @pytest.mark.asyncio
    async def test_create_match_success(self, match_service, mock_startup_repo, mock_vc_repo, 
                                      mock_match_repo, mock_ai_service, sample_startup, sample_vc):
        """Test successful match creation."""
        # Setup mocks
        startup_id = sample_startup.id
        vc_id = sample_vc.id
        
        mock_startup_repo.get.return_value = sample_startup
        mock_vc_repo.get.return_value = sample_vc
        
        mock_ai_service.analyze_compatibility.return_value = {
            "score": 0.85,
            "reasoning": "Strong alignment in AI focus",
            "insights": {
                "strengths": ["Industry match", "Stage alignment"],
                "concerns": ["Geographic distance"]
            }
        }
        
        created_match = Match(
            id=uuid4(),
            startup_id=startup_id,
            vc_id=vc_id,
            status=MatchStatus.PENDING,
            match_type=MatchType.AI_SUGGESTED,
            score=0.85,
            reasoning="Strong alignment in AI focus",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        mock_match_repo.create.return_value = created_match
        
        # Execute
        match_data = MatchCreate(
            startup_id=startup_id,
            vc_id=vc_id,
            match_type=MatchType.AI_SUGGESTED
        )
        
        result = await match_service.create_match(match_data)
        
        # Assert
        assert result.startup_id == startup_id
        assert result.vc_id == vc_id
        assert result.score == 0.85
        assert result.status == MatchStatus.PENDING
        
        # Verify calls
        mock_startup_repo.get.assert_called_once_with(startup_id)
        mock_vc_repo.get.assert_called_once_with(vc_id)
        mock_ai_service.analyze_compatibility.assert_called_once()
        mock_match_repo.create.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_match_startup_not_found(self, match_service, mock_startup_repo):
        """Test match creation when startup is not found."""
        startup_id = uuid4()
        vc_id = uuid4()
        
        mock_startup_repo.get.return_value = None
        
        match_data = MatchCreate(
            startup_id=startup_id,
            vc_id=vc_id,
            match_type=MatchType.AI_SUGGESTED
        )
        
        with pytest.raises(ValueError, match="Startup not found"):
            await match_service.create_match(match_data)
    
    @pytest.mark.asyncio
    async def test_update_match_status(self, match_service, mock_match_repo, sample_match):
        """Test updating match status."""
        match_id = sample_match.id
        new_status = MatchStatus.ACCEPTED
        
        mock_match_repo.get.return_value = sample_match
        
        updated_match = Match(
            **sample_match.dict(),
            status=new_status,
            updated_at=datetime.now()
        )
        mock_match_repo.update.return_value = updated_match
        
        # Execute
        result = await match_service.update_match_status(match_id, new_status)
        
        # Assert
        assert result.status == new_status
        mock_match_repo.get.assert_called_once_with(match_id)
        mock_match_repo.update.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_matches_for_startup(self, match_service, mock_match_repo, sample_startup):
        """Test retrieving matches for a startup."""
        startup_id = sample_startup.id
        
        matches = [
            Mock(id=uuid4(), startup_id=startup_id, score=0.85),
            Mock(id=uuid4(), startup_id=startup_id, score=0.75)
        ]
        
        mock_match_repo.get_by_startup.return_value = matches
        
        # Execute
        result = await match_service.get_matches_for_startup(startup_id)
        
        # Assert
        assert len(result) == 2
        assert all(m.startup_id == startup_id for m in result)
        mock_match_repo.get_by_startup.assert_called_once_with(startup_id)
    
    @pytest.mark.asyncio
    async def test_get_matches_for_vc(self, match_service, mock_match_repo, sample_vc):
        """Test retrieving matches for a VC."""
        vc_id = sample_vc.id
        
        matches = [
            Mock(id=uuid4(), vc_id=vc_id, score=0.90),
            Mock(id=uuid4(), vc_id=vc_id, score=0.80)
        ]
        
        mock_match_repo.get_by_vc.return_value = matches
        
        # Execute
        result = await match_service.get_matches_for_vc(vc_id)
        
        # Assert
        assert len(result) == 2
        assert all(m.vc_id == vc_id for m in result)
        mock_match_repo.get_by_vc.assert_called_once_with(vc_id)
    
    @pytest.mark.asyncio
    async def test_bulk_create_matches(self, match_service, mock_startup_repo, mock_vc_repo, 
                                     mock_match_repo, mock_ai_service, sample_startup):
        """Test bulk match creation for a startup."""
        startup_id = sample_startup.id
        vc_ids = [uuid4() for _ in range(3)]
        
        mock_startup_repo.get.return_value = sample_startup
        
        # Mock VCs
        vcs = [Mock(id=vc_id, name=f"VC{i}") for i, vc_id in enumerate(vc_ids)]
        mock_vc_repo.get = AsyncMock(side_effect=vcs)
        
        # Mock AI analysis
        mock_ai_service.analyze_compatibility.return_value = {
            "score": 0.80,
            "reasoning": "Good match"
        }
        
        # Mock match creation
        created_matches = []
        for vc_id in vc_ids:
            match = Mock(
                id=uuid4(),
                startup_id=startup_id,
                vc_id=vc_id,
                score=0.80
            )
            created_matches.append(match)
        
        mock_match_repo.create = AsyncMock(side_effect=created_matches)
        
        # Execute
        result = await match_service.bulk_create_matches(startup_id, vc_ids)
        
        # Assert
        assert len(result) == 3
        assert mock_ai_service.analyze_compatibility.call_count == 3
        assert mock_match_repo.create.call_count == 3
    
    @pytest.mark.asyncio
    async def test_delete_match(self, match_service, mock_match_repo, sample_match):
        """Test match deletion."""
        match_id = sample_match.id
        
        mock_match_repo.get.return_value = sample_match
        mock_match_repo.delete.return_value = True
        
        # Execute
        result = await match_service.delete_match(match_id)
        
        # Assert
        assert result is True
        mock_match_repo.get.assert_called_once_with(match_id)
        mock_match_repo.delete.assert_called_once_with(match_id)
    
    @pytest.mark.asyncio
    async def test_get_match_statistics(self, match_service, mock_match_repo):
        """Test getting match statistics."""
        # Mock matches with different statuses
        pending_matches = [Mock(status=MatchStatus.PENDING) for _ in range(5)]
        accepted_matches = [Mock(status=MatchStatus.ACCEPTED) for _ in range(3)]
        rejected_matches = [Mock(status=MatchStatus.REJECTED) for _ in range(2)]
        
        all_matches = pending_matches + accepted_matches + rejected_matches
        mock_match_repo.get_all.return_value = all_matches
        
        # Execute
        stats = await match_service.get_match_statistics()
        
        # Assert
        assert stats["total_matches"] == 10
        assert stats["pending_matches"] == 5
        assert stats["accepted_matches"] == 3
        assert stats["rejected_matches"] == 2
        assert stats["acceptance_rate"] == 0.3
    
    @pytest.mark.asyncio
    async def test_auto_match_startups_with_vcs(self, match_service, mock_startup_repo, 
                                               mock_vc_repo, mock_ai_service, mock_match_repo):
        """Test automatic matching of startups with VCs."""
        # Mock data
        startups = [Mock(id=uuid4(), industry="AI") for _ in range(2)]
        vcs = [Mock(id=uuid4(), investment_focus=["AI"]) for _ in range(2)]
        
        mock_startup_repo.get_all.return_value = startups
        mock_vc_repo.get_all.return_value = vcs
        
        # Mock AI suggestions
        mock_ai_service.suggest_matches.return_value = [
            {"startup_id": startups[0].id, "vc_id": vcs[0].id, "score": 0.90},
            {"startup_id": startups[1].id, "vc_id": vcs[1].id, "score": 0.85}
        ]
        
        # Mock match creation
        mock_match_repo.create.return_value = Mock(id=uuid4())
        
        # Execute
        result = await match_service.auto_match_all()
        
        # Assert
        assert result["matches_created"] == 2
        assert mock_ai_service.suggest_matches.called
        assert mock_match_repo.create.call_count == 2