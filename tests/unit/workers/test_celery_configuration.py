"""
Test Celery configuration and setup.
Following TDD - writing tests FIRST.
"""
import pytest
from unittest.mock import Mock, patch


class TestCeleryConfiguration:
    """Test Celery app configuration."""
    
    def test_celery_app_imports(self):
        """Test that Celery app can be imported."""
        # This should fail initially
        from src.workers.celery_app import celery_app
        assert celery_app is not None
    
    def test_celery_broker_configuration(self):
        """Test Celery broker is configured correctly."""
        from src.workers.celery_app import celery_app
        from src.core.config import settings
        
        # Should use Redis as broker
        assert celery_app.conf.broker_url == settings.celery_broker_url
        assert 'redis://' in celery_app.conf.broker_url
    
    def test_celery_result_backend_configuration(self):
        """Test Celery result backend is configured."""
        from src.workers.celery_app import celery_app
        from src.core.config import settings
        
        # Should use Redis as result backend
        assert celery_app.conf.result_backend == settings.celery_result_backend
        assert 'redis://' in celery_app.conf.result_backend
    
    def test_celery_task_serialization(self):
        """Test Celery uses JSON serialization."""
        from src.workers.celery_app import celery_app
        
        assert celery_app.conf.task_serializer == 'json'
        assert celery_app.conf.result_serializer == 'json'
        assert celery_app.conf.accept_content == ['json']
    
    def test_celery_timezone_configuration(self):
        """Test Celery timezone is UTC."""
        from src.workers.celery_app import celery_app
        
        assert celery_app.conf.timezone == 'UTC'
        assert celery_app.conf.enable_utc is True
    
    def test_celery_task_time_limits(self):
        """Test Celery task time limits are set."""
        from src.workers.celery_app import celery_app
        
        # Tasks should have reasonable time limits
        assert celery_app.conf.task_time_limit == 300  # 5 minutes hard limit
        assert celery_app.conf.task_soft_time_limit == 240  # 4 minutes soft limit
    
    def test_celery_worker_configuration(self):
        """Test Celery worker settings."""
        from src.workers.celery_app import celery_app
        
        # Worker should not prefetch too many tasks
        assert celery_app.conf.worker_prefetch_multiplier == 4
        assert celery_app.conf.worker_max_tasks_per_child == 1000


class TestCeleryTaskDiscovery:
    """Test that Celery discovers all tasks."""
    
    def test_ai_tasks_registered(self):
        """Test AI analysis tasks are registered."""
        from src.workers.celery_app import celery_app
        
        # These tasks should be auto-discovered
        assert 'src.workers.tasks.ai_tasks.analyze_startup_task' in celery_app.tasks
        assert 'src.workers.tasks.ai_tasks.analyze_vc_task' in celery_app.tasks
        assert 'src.workers.tasks.ai_tasks.batch_match_task' in celery_app.tasks
    
    def test_data_tasks_registered(self):
        """Test data enrichment tasks are registered."""
        from src.workers.celery_app import celery_app
        
        assert 'src.workers.tasks.data_tasks.scrape_vc_website_task' in celery_app.tasks
        assert 'src.workers.tasks.data_tasks.enrich_startup_data_task' in celery_app.tasks
    
    def test_notification_tasks_registered(self):
        """Test notification tasks are registered."""
        from src.workers.celery_app import celery_app
        
        assert 'src.workers.tasks.notification_tasks.send_match_notification' in celery_app.tasks
        assert 'src.workers.tasks.notification_tasks.send_batch_notifications' in celery_app.tasks
    
    def test_scheduled_tasks_registered(self):
        """Test scheduled tasks are registered."""
        from src.workers.celery_app import celery_app
        
        assert 'src.workers.tasks.scheduled_tasks.daily_match_generation' in celery_app.tasks
        assert 'src.workers.tasks.scheduled_tasks.cleanup_old_tasks' in celery_app.tasks


class TestCeleryBeatSchedule:
    """Test Celery Beat scheduling configuration."""
    
    def test_beat_schedule_exists(self):
        """Test that beat schedule is configured."""
        from src.workers.celery_app import celery_app
        
        assert hasattr(celery_app.conf, 'beat_schedule')
        assert isinstance(celery_app.conf.beat_schedule, dict)
    
    def test_daily_tasks_scheduled(self):
        """Test daily tasks are in beat schedule."""
        from src.workers.celery_app import celery_app
        
        beat_schedule = celery_app.conf.beat_schedule
        
        # Should have daily match generation
        assert 'daily-match-generation' in beat_schedule
        assert beat_schedule['daily-match-generation']['schedule'] == 86400  # 24 hours
    
    def test_cleanup_tasks_scheduled(self):
        """Test cleanup tasks are scheduled."""
        from src.workers.celery_app import celery_app
        
        beat_schedule = celery_app.conf.beat_schedule
        
        # Should have task cleanup
        assert 'cleanup-old-tasks' in beat_schedule
        assert beat_schedule['cleanup-old-tasks']['schedule'] == 3600  # 1 hour


class TestCeleryErrorHandling:
    """Test Celery error handling configuration."""
    
    def test_task_retry_configuration(self):
        """Test task retry settings."""
        from src.workers.celery_app import celery_app
        
        # Should have retry configuration
        assert celery_app.conf.task_acks_late is True
        assert celery_app.conf.task_reject_on_worker_lost is True
    
    @patch('src.workers.celery_app.Redis')
    def test_celery_handles_redis_connection_failure(self, mock_redis):
        """Test Celery handles Redis connection failures gracefully."""
        mock_redis.side_effect = ConnectionError("Redis not available")
        
        # Should not crash on import
        from src.workers import celery_app as app
        
        # Should log error but continue
        assert app is not None