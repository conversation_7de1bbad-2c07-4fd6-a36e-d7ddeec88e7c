"""
Tests for scheduled background tasks.
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
from uuid import uuid4

from src.workers.tasks.scheduled_tasks import (
    refresh_ai_insights,
    cleanup_stale_data,
    generate_analytics_report,
    update_match_scores
)
from src.workers.tasks.weekly_digest import send_all_weekly_digests
from src.core.schemas.match import MatchStatus


class TestScheduledTasks:
    """Test suite for scheduled tasks."""
    
    @pytest.fixture
    def mock_db_context(self):
        """Mock database context manager."""
        with patch('src.workers.tasks.scheduled_tasks.get_db_context') as mock:
            db_mock = Mock()
            mock.return_value.__enter__.return_value = db_mock
            mock.return_value.__exit__.return_value = None
            yield db_mock
    
    @pytest.fixture
    def mock_repositories(self, mock_db_context):
        """Mock repository instances."""
        with patch('src.workers.tasks.scheduled_tasks.PostgresStartupRepository') as startup_repo, \
             patch('src.workers.tasks.scheduled_tasks.PostgresVCRepository') as vc_repo, \
             patch('src.workers.tasks.scheduled_tasks.PostgresMatchRepository') as match_repo:
            
            # Create mock instances
            startup_repo_instance = Mock()
            vc_repo_instance = Mock()
            match_repo_instance = Mock()
            
            # Configure constructors
            startup_repo.return_value = startup_repo_instance
            vc_repo.return_value = vc_repo_instance
            match_repo.return_value = match_repo_instance
            
            yield {
                'startup': startup_repo_instance,
                'vc': vc_repo_instance,
                'match': match_repo_instance
            }
    
    def test_refresh_ai_insights_success(self, mock_repositories):
        """Test successful AI insights refresh."""
        # Create mock entities
        startup1 = Mock(id=uuid4(), metadata={})
        startup2 = Mock(id=uuid4(), metadata={
            'ai_analysis': {
                'analyzed_at': (datetime.utcnow() - timedelta(hours=7)).isoformat()
            }
        })
        startup3 = Mock(id=uuid4(), metadata={
            'ai_analysis': {
                'analyzed_at': (datetime.utcnow() - timedelta(hours=1)).isoformat()
            }
        })
        
        vc1 = Mock(id=uuid4(), metadata={})
        vc2 = Mock(id=uuid4(), metadata={
            'ai_analysis': {
                'analyzed_at': (datetime.utcnow() - timedelta(hours=8)).isoformat()
            }
        })
        
        mock_repositories['startup'].list.return_value = [startup1, startup2, startup3]
        mock_repositories['vc'].list.return_value = [vc1, vc2]
        
        with patch('src.workers.tasks.scheduled_tasks.analyze_startup_task') as mock_startup_task, \
             patch('src.workers.tasks.scheduled_tasks.analyze_vc_task') as mock_vc_task:
            
            result = refresh_ai_insights()
            
            # Should queue tasks for entities without recent analysis
            assert mock_startup_task.delay.call_count == 2  # startup1 and startup2
            assert mock_vc_task.delay.call_count == 2  # vc1 and vc2
            
            assert result['status'] == 'completed'
            assert result['tasks_queued']['startups'] == 2
            assert result['tasks_queued']['vcs'] == 2
    
    def test_cleanup_stale_data_success(self, mock_repositories):
        """Test successful stale data cleanup."""
        # Create old matches
        old_match1 = Mock(
            id=uuid4(),
            created_at=datetime.utcnow() - timedelta(days=100),
            status=MatchStatus.PENDING,
            metadata={}
        )
        old_match2 = Mock(
            id=uuid4(),
            created_at=datetime.utcnow() - timedelta(days=95),
            status=MatchStatus.PENDING,
            metadata={}
        )
        recent_match = Mock(
            id=uuid4(),
            created_at=datetime.utcnow() - timedelta(days=30),
            status=MatchStatus.PENDING,
            metadata={}
        )
        
        mock_repositories['match'].list_by_status.return_value = [
            old_match1, old_match2, recent_match
        ]
        
        with patch('src.workers.tasks.scheduled_tasks.get_cache_service') as mock_cache:
            result = cleanup_stale_data()
            
            # Should update old matches
            assert mock_repositories['match'].update.call_count == 2
            
            # Verify matches were archived
            update_calls = mock_repositories['match'].update.call_args_list
            for call in update_calls[:2]:
                assert 'metadata' in call[0][1]
                assert 'archived_at' in call[0][1]['metadata']
            
            assert result['status'] == 'completed'
            assert result['cleanup_stats']['old_matches_archived'] == 2
    
    def test_generate_analytics_report_success(self, mock_repositories):
        """Test successful analytics report generation."""
        # Create mock data
        startups = [Mock(metadata={'ai_analysis': {}}), Mock(metadata={}), Mock(metadata={})]
        vcs = [Mock(metadata={'ai_analysis': {}}), Mock(metadata={})]
        matches = [
            Mock(
                created_at=datetime.utcnow() - timedelta(hours=12),
                score=0.85,
                status=MatchStatus.PENDING,
                metadata={}
            ),
            Mock(
                created_at=datetime.utcnow() - timedelta(days=2),
                score=0.7,
                status=MatchStatus.CONTACTED,
                metadata={'ai_insights': {}}
            ),
            Mock(
                created_at=datetime.utcnow() - timedelta(hours=6),
                score=0.9,
                status=MatchStatus.INTERESTED,
                metadata={'ai_insights': {}}
            )
        ]
        
        mock_repositories['startup'].list.return_value = startups
        mock_repositories['vc'].list.return_value = vcs
        mock_repositories['match'].list.return_value = matches
        
        with patch('src.workers.tasks.scheduled_tasks.get_cache_service') as mock_cache:
            mock_cache_instance = Mock()
            mock_cache.return_value = mock_cache_instance
            
            result = generate_analytics_report()
            
            # Verify report was generated
            assert result['status'] == 'completed'
            assert 'report_date' in result
            assert 'key_metrics' in result
            
            # Verify metrics calculation
            metrics = result['key_metrics']
            assert metrics['total_entities'] == 5  # 3 startups + 2 vcs
            assert metrics['new_matches'] == 2  # 2 matches from yesterday
            assert metrics['ai_coverage'] == 0.4  # 2/5 entities have AI analysis
            
            # Verify report was cached
            mock_cache_instance.set.assert_called_once()
            cache_key = mock_cache_instance.set.call_args[0][0]
            assert cache_key.startswith('analytics:daily:')
    
    def test_update_match_scores_success(self, mock_repositories):
        """Test successful match score updates."""
        # Create matches with entities that have AI analysis
        startup_with_ai = Mock(id=uuid4(), metadata={'ai_analysis': {'score': 0.8}})
        vc_with_ai = Mock(id=uuid4(), metadata={'ai_analysis': {'score': 0.7}})
        
        match1 = Mock(
            id=uuid4(),
            startup_id=startup_with_ai.id,
            vc_id=uuid4(),
            created_at=datetime.utcnow() - timedelta(days=3),
            status=MatchStatus.PENDING,
            metadata={}
        )
        match2 = Mock(
            id=uuid4(),
            startup_id=uuid4(),
            vc_id=vc_with_ai.id,
            created_at=datetime.utcnow() - timedelta(days=2),
            status=MatchStatus.INTERESTED,
            metadata={}
        )
        
        mock_repositories['match'].list.return_value = [match1, match2]
        mock_repositories['startup'].get_by_id.return_value = startup_with_ai
        mock_repositories['vc'].get_by_id.return_value = vc_with_ai
        
        result = update_match_scores()
        
        # Should update both matches
        assert mock_repositories['match'].update.call_count == 2
        
        # Verify metadata was updated
        update_calls = mock_repositories['match'].update.call_args_list
        for call in update_calls:
            assert 'metadata' in call[0][1]
            assert 'score_updated' in call[0][1]['metadata']
        
        assert result['status'] == 'completed'
        assert result['matches_updated'] == 2
    
    def test_send_all_weekly_digests_success(self):
        """Test successful weekly digest distribution."""
        with patch('src.workers.tasks.weekly_digest.get_db_context') as mock_context:
            db_mock = Mock()
            mock_context.return_value.__enter__.return_value = db_mock
            mock_context.return_value.__exit__.return_value = None
            
            with patch('src.workers.tasks.weekly_digest.PostgresStartupRepository') as startup_repo, \
                 patch('src.workers.tasks.weekly_digest.PostgresVCRepository') as vc_repo, \
                 patch('src.workers.tasks.weekly_digest.send_weekly_digest_task') as mock_task:
                
                # Create mock entities
                startup1 = Mock(id=uuid4(), active=True, email='<EMAIL>')
                startup2 = Mock(id=uuid4(), active=True, email='<EMAIL>')
                startup3 = Mock(id=uuid4(), active=False, email='<EMAIL>')  # Inactive
                startup4 = Mock(id=uuid4(), active=True, email=None)  # No email
                
                vc1 = Mock(id=uuid4(), active=True, email='<EMAIL>')
                vc2 = Mock(id=uuid4(), active=True, email='<EMAIL>')
                
                startup_repo_instance = Mock()
                vc_repo_instance = Mock()
                
                startup_repo.return_value = startup_repo_instance
                vc_repo.return_value = vc_repo_instance
                
                startup_repo_instance.list.return_value = [startup1, startup2, startup3, startup4]
                vc_repo_instance.list.return_value = [vc1, vc2]
                
                result = send_all_weekly_digests()
                
                # Should queue tasks only for active entities with emails
                assert mock_task.delay.call_count == 4  # 2 startups + 2 vcs
                
                assert result['status'] == 'completed'
                assert result['tasks_queued']['startups'] == 2
                assert result['tasks_queued']['vcs'] == 2
                assert result['tasks_queued']['errors'] == 0
    
    def test_refresh_ai_insights_error_handling(self, mock_repositories):
        """Test error handling in AI insights refresh."""
        mock_repositories['startup'].list.side_effect = Exception("Database error")
        
        with pytest.raises(Exception) as exc_info:
            refresh_ai_insights()
        
        assert "Database error" in str(exc_info.value)
    
    def test_task_idempotency(self, mock_repositories):
        """Test that tasks are idempotent."""
        # Run cleanup twice with same data
        mock_repositories['match'].list_by_status.return_value = []
        
        with patch('src.workers.tasks.scheduled_tasks.get_cache_service'):
            result1 = cleanup_stale_data()
            result2 = cleanup_stale_data()
            
            assert result1['status'] == 'completed'
            assert result2['status'] == 'completed'
            assert result1['cleanup_stats'] == result2['cleanup_stats']