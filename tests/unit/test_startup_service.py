"""Test suite for StartupService following TDD principles.

Tests are written first to define the expected behavior of the service layer.
Each test focuses on ONE specific behavior.
"""
import pytest
from uuid import UUID, uuid4
from unittest.mock import Mock, AsyncMock
from datetime import datetime

from src.core.models.startup import Startup
from src.core.models.vc import VC
from src.core.ai.models import StartupAnalysis, BusinessModel, BusinessModelType, TechnologyStack
from src.core.repositories.startup_repository import StartupRepository
from src.core.services.startup_service import StartupService
from src.core.ports.ai_port import AIPort, StartupInsights, AIAnalysisError


class TestStartupServiceCreation:
    """Tests for startup creation functionality."""
    
    @pytest.mark.asyncio
    async def test_create_startup_assigns_id_when_not_provided(self):
        """Test that create_startup assigns an ID when creating a new startup."""
        # Arrange
        repository = Mock(StartupRepository)
        repository.save = AsyncMock(side_effect=lambda s: s)
        ai_analyzer = Mock(AIPort)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        startup_data = Startup(
            name="TechCo",
            sector="B2B SaaS",
            stage="Seed",
            description="AI-powered analytics platform"
        )
        
        # Act
        created_startup = await service.create_startup(startup_data)
        
        # Assert
        assert created_startup.id is not None
        assert isinstance(created_startup.id, UUID)
        repository.save.assert_called_once()
        
    @pytest.mark.asyncio
    async def test_create_startup_preserves_existing_id(self):
        """Test that create_startup preserves ID if already provided."""
        # Arrange
        existing_id = uuid4()
        repository = Mock(StartupRepository)
        repository.save = AsyncMock(side_effect=lambda s: s)
        ai_analyzer = Mock(AIPort)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        startup_data = Startup(
            id=existing_id,
            name="TechCo",
            sector="B2B SaaS",
            stage="Seed"
        )
        
        # Act
        created_startup = await service.create_startup(startup_data)
        
        # Assert
        assert created_startup.id == existing_id
        repository.save.assert_called_once_with(startup_data)
        
    @pytest.mark.asyncio
    async def test_create_startup_validates_required_fields(self):
        """Test that create_startup validates required fields."""
        # Arrange
        repository = Mock(StartupRepository)
        ai_analyzer = Mock(AIPort)
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act & Assert
        with pytest.raises(ValueError, match="Startup name is required"):
            await service.create_startup(Startup(name="", sector="SaaS", stage="Seed"))
            
    @pytest.mark.asyncio
    async def test_create_startup_with_all_fields(self):
        """Test that create_startup works with all fields populated."""
        # Arrange
        repository = Mock(StartupRepository)
        repository.save = AsyncMock(side_effect=lambda s: s)
        ai_analyzer = Mock(AIPort)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        startup_data = Startup(
            name="FullDataCo",
            sector="AI/ML",
            stage="Series A",
            description="Complete AI solution",
            website="https://fulldataco.com",
            team_size=25,
            monthly_revenue=100000.0
        )
        
        # Act
        created_startup = await service.create_startup(startup_data)
        
        # Assert
        assert created_startup.id is not None
        assert created_startup.name == "FullDataCo"
        assert created_startup.website == "https://fulldataco.com"
        assert created_startup.team_size == 25
        assert created_startup.monthly_revenue == 100000.0
        repository.save.assert_called_once()
        
    @pytest.mark.asyncio
    async def test_create_startup_handles_repository_error(self):
        """Test that create_startup handles repository save errors."""
        # Arrange
        repository = Mock(StartupRepository)
        repository.save = AsyncMock(side_effect=Exception("Database error"))
        ai_analyzer = Mock(AIPort)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        startup_data = Startup(
            name="ErrorCo",
            sector="Tech",
            stage="Seed"
        )
        
        # Act & Assert
        with pytest.raises(Exception, match="Database error"):
            await service.create_startup(startup_data)


class TestStartupServiceRetrieval:
    """Tests for startup retrieval functionality."""
    
    @pytest.mark.asyncio
    async def test_get_startup_returns_startup_when_found(self):
        """Test that get_startup returns the startup when it exists."""
        # Arrange
        startup_id = uuid4()
        expected_startup = Startup(
            id=startup_id,
            name="FoundCo",
            sector="Fintech",
            stage="Series A"
        )
        
        repository = Mock(StartupRepository)
        repository.find_by_id = AsyncMock(return_value=expected_startup)
        ai_analyzer = Mock(AIPort)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act
        found_startup = await service.get_startup(startup_id)
        
        # Assert
        assert found_startup == expected_startup
        repository.find_by_id.assert_called_once_with(startup_id)
        
    @pytest.mark.asyncio
    async def test_get_startup_raises_when_not_found(self):
        """Test that get_startup raises exception when startup doesn't exist."""
        # Arrange
        startup_id = uuid4()
        repository = Mock(StartupRepository)
        repository.find_by_id = AsyncMock(return_value=None)
        ai_analyzer = Mock(AIPort)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act & Assert
        with pytest.raises(ValueError, match=f"Startup with id {startup_id} not found"):
            await service.get_startup(startup_id)


class TestStartupServiceListing:
    """Tests for startup listing functionality."""
    
    @pytest.mark.asyncio
    async def test_list_startups_returns_all_startups(self):
        """Test that list_startups returns all startups from repository."""
        # Arrange
        startups = [
            Startup(id=uuid4(), name="Startup1", sector="AI", stage="Seed"),
            Startup(id=uuid4(), name="Startup2", sector="Fintech", stage="Series A"),
            Startup(id=uuid4(), name="Startup3", sector="B2B SaaS", stage="Pre-seed")
        ]
        
        repository = Mock(StartupRepository)
        repository.find_all = AsyncMock(return_value=startups)
        ai_analyzer = Mock(AIPort)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act
        result = await service.list_startups()
        
        # Assert
        assert result == startups
        repository.find_all.assert_called_once()
        
    @pytest.mark.asyncio
    async def test_list_startups_with_sector_filter(self):
        """Test that list_startups filters by sector when specified."""
        # Arrange
        fintech_startups = [
            Startup(id=uuid4(), name="FinCo1", sector="Fintech", stage="Seed"),
            Startup(id=uuid4(), name="FinCo2", sector="Fintech", stage="Series A")
        ]
        
        repository = Mock(StartupRepository)
        repository.find_by_sector = AsyncMock(return_value=fintech_startups)
        ai_analyzer = Mock(AIPort)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act
        result = await service.list_startups(sector="Fintech")
        
        # Assert
        assert result == fintech_startups
        repository.find_by_sector.assert_called_once_with("Fintech")
        
    @pytest.mark.asyncio
    async def test_list_startups_with_stage_filter(self):
        """Test that list_startups filters by stage when specified."""
        # Arrange
        seed_startups = [
            Startup(id=uuid4(), name="SeedCo1", sector="AI", stage="Seed"),
            Startup(id=uuid4(), name="SeedCo2", sector="B2B SaaS", stage="Seed")
        ]
        
        repository = Mock(StartupRepository)
        repository.find_by_stage = AsyncMock(return_value=seed_startups)
        ai_analyzer = Mock(AIPort)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act
        result = await service.list_startups(stage="Seed")
        
        # Assert
        assert result == seed_startups
        repository.find_by_stage.assert_called_once_with("Seed")
        
    @pytest.mark.asyncio
    async def test_list_startups_with_both_filters_sector_takes_precedence(self):
        """Test that list_startups with both filters uses sector first."""
        # Arrange
        fintech_startups = [
            Startup(id=uuid4(), name="FinCo1", sector="Fintech", stage="Seed")
        ]
        
        repository = Mock(StartupRepository)
        repository.find_by_sector = AsyncMock(return_value=fintech_startups)
        repository.find_by_stage = AsyncMock()  # Should not be called
        ai_analyzer = Mock(AIPort)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act
        result = await service.list_startups(sector="Fintech", stage="Seed")
        
        # Assert
        assert result == fintech_startups
        repository.find_by_sector.assert_called_once_with("Fintech")
        repository.find_by_stage.assert_not_called()
        
    @pytest.mark.asyncio
    async def test_list_startups_empty_result(self):
        """Test that list_startups handles empty results correctly."""
        # Arrange
        repository = Mock(StartupRepository)
        repository.find_all = AsyncMock(return_value=[])
        ai_analyzer = Mock(AIPort)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act
        result = await service.list_startups()
        
        # Assert
        assert result == []
        repository.find_all.assert_called_once()
        
    @pytest.mark.asyncio
    async def test_list_startups_handles_repository_error(self):
        """Test that list_startups propagates repository errors."""
        # Arrange
        repository = Mock(StartupRepository)
        repository.find_all = AsyncMock(side_effect=Exception("Database connection error"))
        ai_analyzer = Mock(AIPort)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act & Assert
        with pytest.raises(Exception, match="Database connection error"):
            await service.list_startups()


class TestStartupServiceAnalysis:
    """Tests for AI-powered startup analysis functionality."""
    
    @pytest.mark.asyncio
    async def test_analyze_startup_returns_analysis_for_existing_startup(self):
        """Test that analyze_startup returns AI analysis for an existing startup."""
        # Arrange
        startup_id = uuid4()
        startup = Startup(
            id=startup_id,
            name="AITech",
            sector="AI/ML",
            stage="Seed",
            description="AI-powered predictive analytics for e-commerce"
        )
        
        expected_analysis = StartupInsights(
            key_technologies=["Python", "TensorFlow", "AWS"],
            market_opportunity="Large and growing e-commerce analytics market",
            competitive_advantages=["Advanced ML algorithms", "Real-time processing"],
            team_strengths=["Strong technical team", "Domain expertise"],
            risk_factors=["Competition from established players"],
            growth_potential_score=0.85,
            innovation_score=0.90,
            market_fit_score=0.80
        )
        
        repository = Mock(StartupRepository)
        repository.find_by_id = AsyncMock(return_value=startup)
        
        ai_analyzer = Mock(AIPort)
        ai_analyzer.analyze_startup = AsyncMock(return_value=expected_analysis)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act
        analysis = await service.analyze_startup(startup_id)
        
        # Assert
        assert analysis == expected_analysis
        repository.find_by_id.assert_called_once_with(startup_id)
        ai_analyzer.analyze_startup.assert_called_once_with(startup, use_cache=True)
        
    @pytest.mark.asyncio
    async def test_analyze_startup_raises_when_startup_not_found(self):
        """Test that analyze_startup raises exception when startup doesn't exist."""
        # Arrange
        startup_id = uuid4()
        repository = Mock(StartupRepository)
        repository.find_by_id = AsyncMock(return_value=None)
        ai_analyzer = Mock(AIPort)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act & Assert
        with pytest.raises(ValueError, match=f"Startup with id {startup_id} not found"):
            await service.analyze_startup(startup_id)
            
    @pytest.mark.asyncio
    async def test_analyze_startup_with_force_refresh_bypasses_cache(self):
        """Test that analyze_startup bypasses cache when force_refresh is True."""
        # Arrange
        startup_id = uuid4()
        startup = Startup(
            id=startup_id,
            name="CacheCo",
            sector="B2B",
            stage="Seed",
            description="Some description"
        )
        
        repository = Mock(StartupRepository)
        repository.find_by_id = AsyncMock(return_value=startup)
        
        ai_analyzer = Mock(AIPort)
        ai_analyzer.analyze_startup = AsyncMock(return_value=Mock())
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act
        await service.analyze_startup(startup_id, force_refresh=True)
        
        # Assert
        ai_analyzer.analyze_startup.assert_called_once_with(startup, use_cache=False)
        
    @pytest.mark.asyncio
    async def test_analyze_startup_handles_ai_service_errors(self):
        """Test that analyze_startup properly handles AI service errors."""
        # Arrange
        startup_id = uuid4()
        startup = Startup(
            id=startup_id,
            name="ErrorCo",
            sector="Tech",
            stage="Seed"
        )
        
        repository = Mock(StartupRepository)
        repository.find_by_id = AsyncMock(return_value=startup)
        
        ai_analyzer = Mock(AIPort)
        ai_analyzer.analyze_startup = AsyncMock(
            side_effect=Exception("AI service unavailable")
        )
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act & Assert
        with pytest.raises(Exception, match="AI service unavailable"):
            await service.analyze_startup(startup_id)
            
    @pytest.mark.asyncio
    async def test_analyze_startup_with_ai_analysis_error(self):
        """Test that analyze_startup handles AIAnalysisError specifically."""
        # Arrange
        from src.core.ports.ai_port import AIAnalysisError
        
        startup_id = uuid4()
        startup = Startup(
            id=startup_id,
            name="AnalysisErrorCo",
            sector="Tech",
            stage="Seed"
        )
        
        repository = Mock(StartupRepository)
        repository.find_by_id = AsyncMock(return_value=startup)
        
        ai_analyzer = Mock(AIPort)
        ai_analyzer.analyze_startup = AsyncMock(
            side_effect=AIAnalysisError("Model failed to analyze")
        )
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act & Assert
        with pytest.raises(AIAnalysisError, match="Model failed to analyze"):
            await service.analyze_startup(startup_id)


class TestStartupServiceUpdate:
    """Tests for startup update functionality."""
    
    @pytest.mark.asyncio
    async def test_update_startup_modifies_existing_startup(self):
        """Test that update_startup modifies and saves an existing startup."""
        # Arrange
        startup_id = uuid4()
        existing_startup = Startup(
            id=startup_id,
            name="OldName",
            sector="OldSector",
            stage="Seed"
        )
        
        repository = Mock(StartupRepository)
        repository.find_by_id = AsyncMock(return_value=existing_startup)
        repository.save = AsyncMock(side_effect=lambda s: s)
        ai_analyzer = Mock(AIPort)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        updates = {
            "name": "NewName",
            "sector": "NewSector"
        }
        
        # Act
        updated_startup = await service.update_startup(startup_id, updates)
        
        # Assert
        assert updated_startup.name == "NewName"
        assert updated_startup.sector == "NewSector"
        assert updated_startup.stage == "Seed"  # Unchanged
        repository.save.assert_called_once()
        
    @pytest.mark.asyncio
    async def test_update_startup_raises_when_not_found(self):
        """Test that update_startup raises exception when startup doesn't exist."""
        # Arrange
        startup_id = uuid4()
        repository = Mock(StartupRepository)
        repository.find_by_id = AsyncMock(return_value=None)
        ai_analyzer = Mock(AIPort)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act & Assert
        with pytest.raises(ValueError, match=f"Startup with id {startup_id} not found"):
            await service.update_startup(startup_id, {"name": "NewName"})
            
    @pytest.mark.asyncio
    async def test_update_startup_with_invalid_field_in_updates(self):
        """Test that update_startup handles invalid fields in updates."""
        # Arrange
        startup_id = uuid4()
        existing_startup = Startup(
            id=startup_id,
            name="OldName",
            sector="OldSector",
            stage="Seed"
        )
        
        repository = Mock(StartupRepository)
        repository.find_by_id = AsyncMock(return_value=existing_startup)
        ai_analyzer = Mock(AIPort)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        updates = {
            "invalid_field": "SomeValue",
            "name": "NewName"
        }
        
        # Act & Assert
        with pytest.raises(TypeError):
            await service.update_startup(startup_id, updates)
            
    @pytest.mark.asyncio
    async def test_update_startup_empty_updates(self):
        """Test that update_startup handles empty updates dictionary."""
        # Arrange
        startup_id = uuid4()
        existing_startup = Startup(
            id=startup_id,
            name="OldName",
            sector="OldSector",
            stage="Seed"
        )
        
        repository = Mock(StartupRepository)
        repository.find_by_id = AsyncMock(return_value=existing_startup)
        repository.save = AsyncMock(side_effect=lambda s: s)
        ai_analyzer = Mock(AIPort)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act
        updated_startup = await service.update_startup(startup_id, {})
        
        # Assert
        assert updated_startup.name == "OldName"
        assert updated_startup.sector == "OldSector"
        assert updated_startup.stage == "Seed"
        repository.save.assert_called_once()
        
    @pytest.mark.asyncio
    async def test_update_startup_handles_repository_save_error(self):
        """Test that update_startup handles repository save errors."""
        # Arrange
        startup_id = uuid4()
        existing_startup = Startup(
            id=startup_id,
            name="OldName",
            sector="OldSector",
            stage="Seed"
        )
        
        repository = Mock(StartupRepository)
        repository.find_by_id = AsyncMock(return_value=existing_startup)
        repository.save = AsyncMock(side_effect=Exception("Save failed"))
        ai_analyzer = Mock(AIPort)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act & Assert
        with pytest.raises(Exception, match="Save failed"):
            await service.update_startup(startup_id, {"name": "NewName"})


class TestStartupServiceDeletion:
    """Tests for startup deletion functionality."""
    
    @pytest.mark.asyncio
    async def test_delete_startup_removes_existing_startup(self):
        """Test that delete_startup removes a startup from repository."""
        # Arrange
        startup_id = uuid4()
        repository = Mock(StartupRepository)
        repository.delete = AsyncMock(return_value=True)
        ai_analyzer = Mock(AIPort)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act
        result = await service.delete_startup(startup_id)
        
        # Assert
        assert result is True
        repository.delete.assert_called_once_with(startup_id)
        
    @pytest.mark.asyncio
    async def test_delete_startup_returns_false_when_not_found(self):
        """Test that delete_startup returns False when startup doesn't exist."""
        # Arrange
        startup_id = uuid4()
        repository = Mock(StartupRepository)
        repository.delete = AsyncMock(return_value=False)
        ai_analyzer = Mock(AIPort)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act
        result = await service.delete_startup(startup_id)
        
        # Assert
        assert result is False
        repository.delete.assert_called_once_with(startup_id)
        
    @pytest.mark.asyncio
    async def test_delete_startup_handles_repository_error(self):
        """Test that delete_startup handles repository errors."""
        # Arrange
        startup_id = uuid4()
        repository = Mock(StartupRepository)
        repository.delete = AsyncMock(side_effect=Exception("Database error"))
        ai_analyzer = Mock(AIPort)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act & Assert
        with pytest.raises(Exception, match="Database error"):
            await service.delete_startup(startup_id)


class TestStartupServiceBatchAnalysis:
    """Tests for batch startup analysis functionality."""
    
    @pytest.mark.asyncio
    async def test_analyze_startup_batch_analyzes_multiple_startups(self):
        """Test that analyze_startup_batch processes multiple startups correctly."""
        # Arrange
        startup_ids = [uuid4(), uuid4(), uuid4()]
        startups = [
            Startup(id=startup_ids[0], name="Startup1", sector="AI", stage="Seed"),
            Startup(id=startup_ids[1], name="Startup2", sector="Fintech", stage="Series A"),
            Startup(id=startup_ids[2], name="Startup3", sector="B2B SaaS", stage="Pre-seed")
        ]
        
        expected_insights = [
            StartupInsights(
                key_technologies=["Python", "TensorFlow"],
                market_opportunity="Large AI market",
                competitive_advantages=["First mover"],
                team_strengths=["Strong AI team"],
                risk_factors=["Competition"],
                growth_potential_score=0.8,
                innovation_score=0.9,
                market_fit_score=0.7
            ),
            StartupInsights(
                key_technologies=["React", "Node.js"],
                market_opportunity="Growing fintech sector",
                competitive_advantages=["Unique algorithm"],
                team_strengths=["Financial expertise"],
                risk_factors=["Regulation"],
                growth_potential_score=0.85,
                innovation_score=0.8,
                market_fit_score=0.9
            ),
            StartupInsights(
                key_technologies=["AWS", "Kubernetes"],
                market_opportunity="B2B SaaS expansion",
                competitive_advantages=["Cost effective"],
                team_strengths=["Sales experience"],
                risk_factors=["Market saturation"],
                growth_potential_score=0.7,
                innovation_score=0.6,
                market_fit_score=0.8
            )
        ]
        
        repository = Mock(StartupRepository)
        repository.find_by_id = AsyncMock(side_effect=startups)
        
        ai_analyzer = Mock(AIPort)
        ai_analyzer.batch_analyze_startups = AsyncMock(return_value=expected_insights)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act
        results = await service.analyze_startup_batch(startup_ids)
        
        # Assert
        assert results == expected_insights
        assert repository.find_by_id.call_count == 3
        repository.find_by_id.assert_any_call(startup_ids[0])
        repository.find_by_id.assert_any_call(startup_ids[1])
        repository.find_by_id.assert_any_call(startup_ids[2])
        ai_analyzer.batch_analyze_startups.assert_called_once_with(
            startups,
            max_concurrent=5
        )
        
    @pytest.mark.asyncio
    async def test_analyze_startup_batch_with_custom_max_concurrent(self):
        """Test that analyze_startup_batch respects custom max_concurrent parameter."""
        # Arrange
        startup_ids = [uuid4(), uuid4()]
        startups = [
            Startup(id=startup_ids[0], name="Startup1", sector="AI", stage="Seed"),
            Startup(id=startup_ids[1], name="Startup2", sector="Fintech", stage="Series A")
        ]
        
        repository = Mock(StartupRepository)
        repository.find_by_id = AsyncMock(side_effect=startups)
        
        ai_analyzer = Mock(AIPort)
        ai_analyzer.batch_analyze_startups = AsyncMock(return_value=[Mock(), Mock()])
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act
        await service.analyze_startup_batch(startup_ids, max_concurrent=10)
        
        # Assert
        ai_analyzer.batch_analyze_startups.assert_called_once_with(
            startups,
            max_concurrent=10
        )
        
    @pytest.mark.asyncio
    async def test_analyze_startup_batch_raises_when_startup_not_found(self):
        """Test that analyze_startup_batch raises when any startup is not found."""
        # Arrange
        startup_ids = [uuid4(), uuid4()]
        
        repository = Mock(StartupRepository)
        repository.find_by_id = AsyncMock(
            side_effect=[
                Startup(id=startup_ids[0], name="Startup1", sector="AI", stage="Seed"),
                None  # Second startup not found
            ]
        )
        
        ai_analyzer = Mock(AIPort)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act & Assert
        with pytest.raises(ValueError, match=f"Startup with id {startup_ids[1]} not found"):
            await service.analyze_startup_batch(startup_ids)
            
    @pytest.mark.asyncio
    async def test_analyze_startup_batch_empty_list(self):
        """Test that analyze_startup_batch handles empty list correctly."""
        # Arrange
        repository = Mock(StartupRepository)
        ai_analyzer = Mock(AIPort)
        ai_analyzer.batch_analyze_startups = AsyncMock(return_value=[])
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act
        results = await service.analyze_startup_batch([])
        
        # Assert
        assert results == []
        repository.find_by_id.assert_not_called()
        ai_analyzer.batch_analyze_startups.assert_called_once_with([], max_concurrent=5)
        
    @pytest.mark.asyncio
    async def test_analyze_startup_batch_handles_ai_service_errors(self):
        """Test that analyze_startup_batch properly propagates AI service errors."""
        # Arrange
        startup_ids = [uuid4()]
        startup = Startup(id=startup_ids[0], name="Startup1", sector="AI", stage="Seed")
        
        repository = Mock(StartupRepository)
        repository.find_by_id = AsyncMock(return_value=startup)
        
        ai_analyzer = Mock(AIPort)
        ai_analyzer.batch_analyze_startups = AsyncMock(
            side_effect=Exception("AI batch analysis failed")
        )
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act & Assert
        with pytest.raises(Exception, match="AI batch analysis failed"):
            await service.analyze_startup_batch(startup_ids)


class TestStartupServiceAIUsageStats:
    """Tests for AI usage statistics functionality."""
    
    def test_get_ai_usage_stats_returns_stats_from_ai_port(self):
        """Test that get_ai_usage_stats delegates to AI port correctly."""
        # Arrange
        expected_stats = {
            "total_tokens": 15000,
            "total_cost": 1.50,
            "requests_count": 25,
            "average_tokens_per_request": 600,
            "cache_hit_rate": 0.75
        }
        
        repository = Mock(StartupRepository)
        ai_analyzer = Mock(AIPort)
        ai_analyzer.get_usage_stats = Mock(return_value=expected_stats)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act
        stats = service.get_ai_usage_stats()
        
        # Assert
        assert stats == expected_stats
        ai_analyzer.get_usage_stats.assert_called_once()
        
    def test_get_ai_usage_stats_handles_empty_stats(self):
        """Test that get_ai_usage_stats handles empty statistics correctly."""
        # Arrange
        repository = Mock(StartupRepository)
        ai_analyzer = Mock(AIPort)
        ai_analyzer.get_usage_stats = Mock(return_value={})
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act
        stats = service.get_ai_usage_stats()
        
        # Assert
        assert stats == {}
        ai_analyzer.get_usage_stats.assert_called_once()
        
    def test_get_ai_usage_stats_handles_none_values(self):
        """Test that get_ai_usage_stats handles None values in statistics."""
        # Arrange
        stats_with_none = {
            "total_tokens": None,
            "total_cost": 0.0,
            "requests_count": 0,
            "cache_hit_rate": None
        }
        
        repository = Mock(StartupRepository)
        ai_analyzer = Mock(AIPort)
        ai_analyzer.get_usage_stats = Mock(return_value=stats_with_none)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act
        stats = service.get_ai_usage_stats()
        
        # Assert
        assert stats == stats_with_none
        ai_analyzer.get_usage_stats.assert_called_once()
        
    def test_get_ai_usage_stats_with_error(self):
        """Test that get_ai_usage_stats handles errors from AI port."""
        # Arrange
        repository = Mock(StartupRepository)
        ai_analyzer = Mock(AIPort)
        ai_analyzer.get_usage_stats = Mock(side_effect=Exception("Stats unavailable"))
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act & Assert
        with pytest.raises(Exception, match="Stats unavailable"):
            service.get_ai_usage_stats()


class TestStartupServiceEdgeCases:
    """Additional edge case tests for comprehensive coverage."""
    
    @pytest.mark.asyncio
    async def test_create_startup_with_none_id_explicitly(self):
        """Test creating startup with id=None explicitly."""
        # Arrange
        repository = Mock(StartupRepository)
        repository.save = AsyncMock(side_effect=lambda s: s)
        ai_analyzer = Mock(AIPort)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        startup_data = Startup(
            id=None,  # Explicitly None
            name="NullIdCo",
            sector="Tech",
            stage="Seed"
        )
        
        # Act
        created_startup = await service.create_startup(startup_data)
        
        # Assert
        assert created_startup.id is not None
        assert isinstance(created_startup.id, UUID)
        
    @pytest.mark.asyncio
    async def test_list_startups_with_none_filters(self):
        """Test list_startups with None values for filters."""
        # Arrange
        all_startups = [
            Startup(id=uuid4(), name="Startup1", sector="AI", stage="Seed"),
            Startup(id=uuid4(), name="Startup2", sector="Fintech", stage="Series A")
        ]
        
        repository = Mock(StartupRepository)
        repository.find_all = AsyncMock(return_value=all_startups)
        ai_analyzer = Mock(AIPort)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act
        result = await service.list_startups(sector=None, stage=None)
        
        # Assert
        assert result == all_startups
        repository.find_all.assert_called_once()
        
    @pytest.mark.asyncio
    async def test_analyze_startup_with_default_cache_parameter(self):
        """Test that analyze_startup uses cache by default when force_refresh not specified."""
        # Arrange
        startup_id = uuid4()
        startup = Startup(
            id=startup_id,
            name="DefaultCacheCo",
            sector="Tech",
            stage="Seed"
        )
        
        expected_insights = StartupInsights(
            key_technologies=["Python"],
            market_opportunity="Growing market",
            competitive_advantages=["First mover"],
            team_strengths=["Experienced"],
            risk_factors=["Funding"],
            growth_potential_score=0.8,
            innovation_score=0.7,
            market_fit_score=0.85
        )
        
        repository = Mock(StartupRepository)
        repository.find_by_id = AsyncMock(return_value=startup)
        
        ai_analyzer = Mock(AIPort)
        ai_analyzer.analyze_startup = AsyncMock(return_value=expected_insights)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act - call without force_refresh parameter
        result = await service.analyze_startup(startup_id)
        
        # Assert
        assert result == expected_insights
        ai_analyzer.analyze_startup.assert_called_once_with(startup, use_cache=True)
        
    @pytest.mark.asyncio
    async def test_update_startup_with_all_valid_fields(self):
        """Test updating all valid fields of a startup."""
        # Arrange
        startup_id = uuid4()
        existing_startup = Startup(
            id=startup_id,
            name="OldName",
            sector="OldSector",
            stage="Seed",
            description="Old description",
            website="https://old.com",
            team_size=5,
            monthly_revenue=1000.0
        )
        
        repository = Mock(StartupRepository)
        repository.find_by_id = AsyncMock(return_value=existing_startup)
        repository.save = AsyncMock(side_effect=lambda s: s)
        ai_analyzer = Mock(AIPort)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        updates = {
            "name": "NewName",
            "sector": "NewSector",
            "stage": "Series A",
            "description": "New description",
            "website": "https://new.com",
            "team_size": 50,
            "monthly_revenue": 50000.0
        }
        
        # Act
        updated_startup = await service.update_startup(startup_id, updates)
        
        # Assert
        assert updated_startup.name == "NewName"
        assert updated_startup.sector == "NewSector"
        assert updated_startup.stage == "Series A"
        assert updated_startup.description == "New description"
        assert updated_startup.website == "https://new.com"
        assert updated_startup.team_size == 50
        assert updated_startup.monthly_revenue == 50000.0
        assert updated_startup.id == startup_id  # ID should not change
        
    @pytest.mark.asyncio
    async def test_analyze_startup_batch_with_single_startup(self):
        """Test batch analysis with a single startup."""
        # Arrange
        startup_id = uuid4()
        startup = Startup(id=startup_id, name="SingleBatch", sector="Tech", stage="Seed")
        
        expected_insights = [
            StartupInsights(
                key_technologies=["Node.js"],
                market_opportunity="Niche market",
                competitive_advantages=["Unique approach"],
                team_strengths=["Domain expertise"],
                risk_factors=["Small market"],
                growth_potential_score=0.6,
                innovation_score=0.8,
                market_fit_score=0.7
            )
        ]
        
        repository = Mock(StartupRepository)
        repository.find_by_id = AsyncMock(return_value=startup)
        
        ai_analyzer = Mock(AIPort)
        ai_analyzer.batch_analyze_startups = AsyncMock(return_value=expected_insights)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act
        results = await service.analyze_startup_batch([startup_id])
        
        # Assert
        assert results == expected_insights
        repository.find_by_id.assert_called_once_with(startup_id)
        ai_analyzer.batch_analyze_startups.assert_called_once_with(
            [startup],
            max_concurrent=5
        )
        
    @pytest.mark.asyncio
    async def test_analyze_startup_batch_with_large_batch(self):
        """Test batch analysis with many startups."""
        # Arrange
        startup_ids = [uuid4() for _ in range(20)]
        startups = [
            Startup(id=sid, name=f"Startup{i}", sector="Tech", stage="Seed")
            for i, sid in enumerate(startup_ids)
        ]
        
        repository = Mock(StartupRepository)
        repository.find_by_id = AsyncMock(side_effect=startups)
        
        ai_analyzer = Mock(AIPort)
        ai_analyzer.batch_analyze_startups = AsyncMock(
            return_value=[Mock() for _ in range(20)]
        )
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act
        results = await service.analyze_startup_batch(startup_ids, max_concurrent=10)
        
        # Assert
        assert len(results) == 20
        assert repository.find_by_id.call_count == 20
        ai_analyzer.batch_analyze_startups.assert_called_once_with(
            startups,
            max_concurrent=10
        )
        
    @pytest.mark.asyncio
    async def test_service_initialization(self):
        """Test that service initializes correctly with dependencies."""
        # Arrange
        repository = Mock(StartupRepository)
        ai_analyzer = Mock(AIPort)
        
        # Act
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Assert
        assert service.repository is repository
        assert service.ai_port is ai_analyzer
        
    @pytest.mark.asyncio
    async def test_create_startup_validation_triggers(self):
        """Test that validation is triggered during startup creation."""
        # Arrange
        repository = Mock(StartupRepository)
        repository.save = AsyncMock(side_effect=lambda s: s)
        ai_analyzer = Mock(AIPort)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Create a startup with valid name to ensure validation runs
        startup_data = Startup(
            name="ValidName",
            sector="Tech",
            stage="Seed"
        )
        
        # Act
        created_startup = await service.create_startup(startup_data)
        
        # Assert - if we get here, validation passed
        assert created_startup.name == "ValidName"
        repository.save.assert_called_once()
        
    @pytest.mark.asyncio
    async def test_analyze_startup_batch_maintains_order(self):
        """Test that batch analysis maintains the order of results."""
        # Arrange
        startup_ids = [uuid4(), uuid4(), uuid4()]
        startups = []
        expected_insights = []
        
        for i, sid in enumerate(startup_ids):
            startup = Startup(
                id=sid,
                name=f"OrderedStartup{i}",
                sector="Tech",
                stage="Seed"
            )
            startups.append(startup)
            
            insight = StartupInsights(
                key_technologies=[f"Tech{i}"],
                market_opportunity=f"Market{i}",
                competitive_advantages=[f"Advantage{i}"],
                team_strengths=[f"Strength{i}"],
                risk_factors=[f"Risk{i}"],
                growth_potential_score=0.5 + i * 0.1,
                innovation_score=0.6 + i * 0.1,
                market_fit_score=0.7 + i * 0.1
            )
            expected_insights.append(insight)
        
        repository = Mock(StartupRepository)
        repository.find_by_id = AsyncMock(side_effect=startups)
        
        ai_analyzer = Mock(AIPort)
        ai_analyzer.batch_analyze_startups = AsyncMock(return_value=expected_insights)
        
        service = StartupService(repository=repository, ai_port=ai_analyzer)
        
        # Act
        results = await service.analyze_startup_batch(startup_ids)
        
        # Assert
        assert len(results) == 3
        for i, result in enumerate(results):
            assert result.key_technologies == [f"Tech{i}"]
            assert result.market_opportunity == f"Market{i}"