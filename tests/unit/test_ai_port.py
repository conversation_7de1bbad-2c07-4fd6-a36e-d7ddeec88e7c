"""Tests for AI port and <PERSON><PERSON>hain adapter.

These tests verify that:
1. The port interface is properly defined
2. The LangChain adapter correctly implements the port
3. Domain objects are properly translated
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch
from uuid import uuid4

from src.core.ports.ai_port import (
    AIPort, StartupInsights, VCInsights, MatchRationale, AIAnalysisError
)
from src.adapters.ai.langchain_adapter import LangChainAIAdapter
from src.core.models.startup import Startup
from src.core.models.vc import VC
from src.core.ai.models_v2 import (
    StartupAnalysis, VCThesisAnalysis, InvestmentFocus, Stage
)


@pytest.fixture
def sample_startup():
    """Create a sample startup for testing."""
    startup = Startup(
        name="AI Startup",
        description="AI-powered analytics platform",
        sector="AI/ML",
        stage="Series A"
    )
    startup.id = uuid4()
    return startup


@pytest.fixture
def sample_vc():
    """Create a sample VC for testing."""
    return VC(
        id=uuid4(),
        firm_name="Tech Ventures",
        website="https://techventures.com",
        sectors=["AI/ML", "FinTech"],
        stages=["Series A", "Series B"],
        check_size_min=1000000,
        check_size_max=10000000
    )


@pytest.fixture
def mock_startup_analysis():
    """Create a mock StartupAnalysis from LangChain."""
    return StartupAnalysis(
        company_summary="AI analytics platform",
        key_technologies=["Machine Learning", "NLP", "Computer Vision"],
        market_opportunity="$10B market for AI analytics",
        competitive_advantages=["First mover", "Patent portfolio"],
        team_strengths=["Ex-Google team", "PhD founders"],
        risk_factors=["Competition", "Technical complexity"],
        growth_potential=8.5,
        innovation_score=9.0,
        market_fit_score=7.5,
        sectors=["AI/ML", "Analytics"],
        target_customers=["Enterprises", "SMBs"],
        revenue_model="SaaS subscription"
    )


@pytest.fixture
def mock_vc_analysis():
    """Create a mock VCThesisAnalysis from LangChain."""
    return VCThesisAnalysis(
        thesis_summary="Focus on AI-first companies transforming industries",
        investment_focus=InvestmentFocus(
            sectors=["AI/ML", "FinTech", "HealthTech"],
            stages=[Stage.SERIES_A, Stage.SERIES_B],
            themes=["AI transformation", "Industry disruption"]
        ),
        check_size_range={"min": 2000000, "max": 15000000},
        portfolio_themes=["Enterprise AI", "Consumer AI"],
        investment_criteria=[
            "Strong technical team",
            "Clear path to $100M revenue",
            "Defensible technology"
        ],
        notable_investments=["AI Corp", "ML Solutions"],
        investment_pace="15-20 deals per year"
    )


class TestAIPortInterface:
    """Test the AI port interface definition."""
    
    def test_port_is_abstract(self):
        """Test that AIPort cannot be instantiated directly."""
        with pytest.raises(TypeError):
            AIPort()
    
    def test_port_defines_required_methods(self):
        """Test that AIPort defines all required abstract methods."""
        required_methods = [
            'analyze_startup',
            'analyze_vc',
            'generate_match_rationale',
            'batch_analyze_startups',
            'get_usage_stats',
            'reset_usage_stats'
        ]
        
        for method_name in required_methods:
            assert hasattr(AIPort, method_name)
            method = getattr(AIPort, method_name)
            assert hasattr(method, '__isabstractmethod__')
            assert method.__isabstractmethod__


class TestStartupInsights:
    """Test the StartupInsights domain object."""
    
    def test_startup_insights_creation(self):
        """Test creating StartupInsights."""
        insights = StartupInsights(
            key_technologies=["AI", "ML"],
            market_opportunity="Large market",
            competitive_advantages=["First mover"],
            team_strengths=["Experienced"],
            risk_factors=["Competition"],
            growth_potential_score=0.8,
            innovation_score=0.9,
            market_fit_score=0.7
        )
        
        assert insights.key_technologies == ["AI", "ML"]
        assert insights.growth_potential_score == 0.8
    
    def test_overall_score_calculation(self):
        """Test overall score calculation."""
        insights = StartupInsights(
            key_technologies=[],
            market_opportunity="",
            competitive_advantages=[],
            team_strengths=[],
            risk_factors=[],
            growth_potential_score=0.8,
            innovation_score=0.9,
            market_fit_score=0.7
        )
        
        expected_score = (0.8 + 0.9 + 0.7) / 3
        assert insights.overall_score() == pytest.approx(expected_score)


class TestLangChainAdapter:
    """Test the LangChain adapter implementation."""
    
    @pytest.fixture
    def adapter(self):
        """Create adapter with mocked analyzer."""
        with patch('src.adapters.ai.langchain_adapter.AIAnalyzerService') as mock:
            adapter = LangChainAIAdapter(
                openai_api_key="test-key",
                model_name="gpt-4",
                temperature=0.3
            )
            adapter.analyzer = Mock()
            return adapter
    
    @pytest.mark.asyncio
    async def test_analyze_startup_success(
        self, adapter, sample_startup, mock_startup_analysis
    ):
        """Test successful startup analysis."""
        # Setup mock
        adapter.analyzer.analyze_startup = AsyncMock(
            return_value=mock_startup_analysis
        )
        
        # Execute
        insights = await adapter.analyze_startup(sample_startup)
        
        # Verify
        assert isinstance(insights, StartupInsights)
        assert insights.key_technologies == ["Machine Learning", "NLP", "Computer Vision"]
        assert insights.growth_potential_score == 0.85  # 8.5 / 10
        assert insights.innovation_score == 0.9  # 9.0 / 10
        assert insights.market_fit_score == 0.75  # 7.5 / 10
        
        # Verify analyzer was called correctly
        adapter.analyzer.analyze_startup.assert_called_once_with(
            sample_startup,
            use_cache=True
        )
    
    @pytest.mark.asyncio
    async def test_analyze_startup_failure(self, adapter, sample_startup):
        """Test startup analysis failure handling."""
        # Setup mock to raise exception
        adapter.analyzer.analyze_startup = AsyncMock(
            side_effect=Exception("API error")
        )
        
        # Execute and verify exception
        with pytest.raises(AIAnalysisError) as exc_info:
            await adapter.analyze_startup(sample_startup)
        
        assert "Failed to analyze startup" in str(exc_info.value)
        assert exc_info.value.original_error is not None
    
    @pytest.mark.asyncio
    async def test_analyze_vc_success(
        self, adapter, sample_vc, mock_vc_analysis
    ):
        """Test successful VC analysis."""
        # Setup mock
        adapter.analyzer.extract_vc_thesis = AsyncMock(
            return_value=mock_vc_analysis
        )
        
        # Execute
        website_content = "VC website content..."
        insights = await adapter.analyze_vc(
            sample_vc,
            website_content=website_content
        )
        
        # Verify
        assert isinstance(insights, VCInsights)
        assert insights.thesis_summary == "Focus on AI-first companies transforming industries"
        assert insights.preferred_sectors == ["AI/ML", "FinTech", "HealthTech"]
        assert insights.preferred_stages == ["series_a", "series_b"]
        assert insights.typical_check_size == {"min": 2000000, "max": 15000000}
        
        # Verify analyzer was called
        adapter.analyzer.extract_vc_thesis.assert_called_once_with(
            sample_vc,
            website_content,
            use_cache=True
        )
    
    @pytest.mark.asyncio
    async def test_analyze_vc_requires_website_content(self, adapter, sample_vc):
        """Test that VC analysis requires website content."""
        with pytest.raises(AIAnalysisError) as exc_info:
            await adapter.analyze_vc(sample_vc, website_content=None)
        
        assert "Website content is required" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_generate_match_rationale(
        self, adapter, sample_startup, sample_vc
    ):
        """Test match rationale generation."""
        # Create mock insights
        startup_insights = StartupInsights(
            key_technologies=["AI", "ML"],
            market_opportunity="Large AI market",
            competitive_advantages=["First mover"],
            team_strengths=["Experienced team"],
            risk_factors=["Competition"],
            growth_potential_score=0.8,
            innovation_score=0.9,
            market_fit_score=0.7
        )
        
        vc_insights = VCInsights(
            thesis_summary="AI focus",
            preferred_sectors=["AI/ML"],
            preferred_stages=["Series A"],
            typical_check_size={"min": 1000000, "max": 10000000},
            portfolio_focus=["Enterprise AI"],
            investment_criteria=["Strong team"],
            exclusion_criteria=[]
        )
        
        # Execute with pre-computed insights
        rationale = await adapter.generate_match_rationale(
            sample_startup,
            sample_vc,
            startup_insights=startup_insights,
            vc_insights=vc_insights
        )
        
        # Verify
        assert isinstance(rationale, MatchRationale)
        assert 0 <= rationale.compatibility_score <= 1
        assert len(rationale.key_alignments) > 0
        assert len(rationale.suggested_talking_points) > 0
        assert rationale.confidence_level == 0.85
    
    @pytest.mark.asyncio
    async def test_batch_analyze_startups(
        self, adapter, sample_startup, mock_startup_analysis
    ):
        """Test batch startup analysis."""
        # Setup mock
        adapter.analyzer.batch_analyze_startups = AsyncMock(
            return_value=[mock_startup_analysis, mock_startup_analysis]
        )
        
        # Execute
        startups = [sample_startup, sample_startup]
        insights_list = await adapter.batch_analyze_startups(startups)
        
        # Verify
        assert len(insights_list) == 2
        for insights in insights_list:
            assert isinstance(insights, StartupInsights)
            assert insights.key_technologies == ["Machine Learning", "NLP", "Computer Vision"]
    
    def test_get_usage_stats(self, adapter):
        """Test getting usage statistics."""
        # Setup mock
        mock_stats = {
            "total_tokens": 1000,
            "total_cost": 0.05,
            "cache_stats": {"hits": 10, "misses": 5}
        }
        adapter.analyzer.get_usage_stats = Mock(return_value=mock_stats)
        
        # Execute
        stats = adapter.get_usage_stats()
        
        # Verify
        assert stats == mock_stats
        adapter.analyzer.get_usage_stats.assert_called_once()
    
    def test_reset_usage_stats(self, adapter):
        """Test resetting usage statistics."""
        # Setup mock
        adapter.analyzer.reset_usage_stats = Mock()
        
        # Execute
        adapter.reset_usage_stats()
        
        # Verify
        adapter.analyzer.reset_usage_stats.assert_called_once()


class TestCompatibilityCalculation:
    """Test the compatibility calculation logic."""
    
    @pytest.fixture
    def adapter(self):
        """Create adapter for testing private methods."""
        with patch('src.adapters.ai.langchain_adapter.AIAnalyzerService'):
            return LangChainAIAdapter()
    
    def test_calculate_compatibility_perfect_match(self, adapter, sample_startup, sample_vc):
        """Test compatibility calculation for perfect match."""
        startup_insights = StartupInsights(
            key_technologies=["AI"],
            market_opportunity="Large",
            competitive_advantages=[],
            team_strengths=[],
            risk_factors=[],
            growth_potential_score=0.8,
            innovation_score=0.9,
            market_fit_score=0.7
        )
        
        vc_insights = VCInsights(
            thesis_summary="AI focus",
            preferred_sectors=["AI/ML"],
            preferred_stages=["Series A"],
            typical_check_size={"min": 1000000, "max": 10000000},
            portfolio_focus=[],
            investment_criteria=[],
            exclusion_criteria=[]
        )
        
        # Add funding amount to startup
        setattr(sample_startup, 'funding_amount', 5000000)
        
        score = adapter._calculate_compatibility(
            sample_startup, sample_vc, startup_insights, vc_insights
        )
        
        # Should have high score due to sector, stage, and funding alignment
        assert score > 0.7
    
    def test_find_alignments(self, adapter):
        """Test finding alignments between startup and VC."""
        startup_insights = StartupInsights(
            key_technologies=["AI", "ML", "Cloud"],
            market_opportunity="Large AI market",
            competitive_advantages=["First mover"],
            team_strengths=["Experienced"],
            risk_factors=[],
            growth_potential_score=0.8,
            innovation_score=0.9,
            market_fit_score=0.7
        )
        
        vc_insights = VCInsights(
            thesis_summary="AI focus",
            preferred_sectors=["AI/ML"],
            preferred_stages=["Series A"],
            typical_check_size={"min": 1000000, "max": 10000000},
            portfolio_focus=["AI", "Cloud"],
            investment_criteria=[],
            exclusion_criteria=[]
        )
        
        alignments = adapter._find_alignments(startup_insights, vc_insights)
        
        assert len(alignments) > 0
        assert any("Technology alignment" in a for a in alignments)
        assert any("market opportunity" in a for a in alignments)