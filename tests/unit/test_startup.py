import pytest
from src.core.models.startup import Startup

def test_startup_creation():
    startup = Startup(
        name="TechCo",
        sector="B2B SaaS",
        stage="Seed",
        description="AI-powered sales automation",
        website="https://techco.com",
        team_size=5,
        monthly_revenue=50000
    )
    
    assert startup.name == "TechCo"
    assert startup.sector == "B2B SaaS"
    assert startup.is_fundable()
    
def test_startup_validation():
    with pytest.raises(ValueError):
        Startup(name="", sector="B2B SaaS", stage="Seed")
        
def test_startup_sectors_extraction():
    startup = Startup(
        name="AITech",
        sector="Technology",
        stage="Seed",
        description="We use machine learning for B2B sales automation"
    )
    assert "AI/ML" in startup.extract_sectors()
    assert "B2B SaaS" in startup.extract_sectors()