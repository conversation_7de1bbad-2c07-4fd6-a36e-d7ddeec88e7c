"""Unit tests for repository interfaces following TDD principles.

These tests are written FIRST before implementation (Red phase).
They test behavior, not implementation details.
"""
import pytest
from typing import List, Optional
from uuid import UUID, uuid4
from datetime import datetime

from src.core.models.startup import Startup
from src.core.models.vc import VC
from src.core.repositories.startup_repository import StartupRepository
from src.core.repositories.vc_repository import VCRepository


class TestStartupRepository:
    """Test cases for StartupRepository interface."""
    
    @pytest.fixture
    def startup_repo(self) -> StartupRepository:
        """Fixture to provide a StartupRepository instance."""
        # This will fail initially as StartupRepository doesn't exist yet
        from src.core.repositories.startup_repository import InMemoryStartupRepository
        return InMemoryStartupRepository()
    
    @pytest.fixture
    def sample_startup(self) -> Startup:
        """Fixture providing a sample startup."""
        return Startup(
            name="TechCo",
            sector="B2B SaaS",
            stage="Seed",
            description="AI-powered analytics platform",
            website="https://techco.example.com",
            team_size=10,
            monthly_revenue=50000.0
        )
    
    async def test_save_startup_assigns_id_when_none_provided(self, startup_repo: StartupRepository, sample_startup: Startup):
        """Test that saving a startup without ID assigns a new ID."""
        # Act
        saved_startup = await startup_repo.save(sample_startup)
        
        # Assert
        assert hasattr(saved_startup, 'id')
        assert saved_startup.id is not None
        assert isinstance(saved_startup.id, UUID)
        assert saved_startup.name == sample_startup.name
    
    async def test_save_startup_preserves_existing_id(self, startup_repo: StartupRepository, sample_startup: Startup):
        """Test that saving a startup with existing ID preserves it."""
        # Arrange
        startup_with_id = sample_startup
        startup_with_id.id = uuid4()
        original_id = startup_with_id.id
        
        # Act
        saved_startup = await startup_repo.save(startup_with_id)
        
        # Assert
        assert saved_startup.id == original_id
    
    async def test_find_by_id_returns_saved_startup(self, startup_repo: StartupRepository, sample_startup: Startup):
        """Test finding a startup by ID after saving."""
        # Arrange
        saved_startup = await startup_repo.save(sample_startup)
        
        # Act
        found_startup = await startup_repo.find_by_id(saved_startup.id)
        
        # Assert
        assert found_startup is not None
        assert found_startup.id == saved_startup.id
        assert found_startup.name == saved_startup.name
        assert found_startup.sector == saved_startup.sector
    
    async def test_find_by_id_returns_none_when_not_found(self, startup_repo: StartupRepository):
        """Test that find_by_id returns None for non-existent ID."""
        # Act
        result = await startup_repo.find_by_id(uuid4())
        
        # Assert
        assert result is None
    
    async def test_find_by_sector_returns_matching_startups(self, startup_repo: StartupRepository):
        """Test finding startups by sector."""
        # Arrange
        saas_startup1 = Startup(name="SaaS1", sector="B2B SaaS", stage="Seed")
        saas_startup2 = Startup(name="SaaS2", sector="B2B SaaS", stage="Series A")
        fintech_startup = Startup(name="FinTech1", sector="Fintech", stage="Seed")
        
        await startup_repo.save(saas_startup1)
        await startup_repo.save(saas_startup2)
        await startup_repo.save(fintech_startup)
        
        # Act
        saas_startups = await startup_repo.find_by_sector("B2B SaaS")
        
        # Assert
        assert len(saas_startups) == 2
        assert all(s.sector == "B2B SaaS" for s in saas_startups)
        startup_names = {s.name for s in saas_startups}
        assert startup_names == {"SaaS1", "SaaS2"}
    
    async def test_find_by_stage_returns_matching_startups(self, startup_repo: StartupRepository):
        """Test finding startups by funding stage."""
        # Arrange
        seed_startup1 = Startup(name="Seed1", sector="B2B SaaS", stage="Seed")
        seed_startup2 = Startup(name="Seed2", sector="Fintech", stage="Seed")
        series_a_startup = Startup(name="SeriesA1", sector="B2B SaaS", stage="Series A")
        
        await startup_repo.save(seed_startup1)
        await startup_repo.save(seed_startup2)
        await startup_repo.save(series_a_startup)
        
        # Act
        seed_startups = await startup_repo.find_by_stage("Seed")
        
        # Assert
        assert len(seed_startups) == 2
        assert all(s.stage == "Seed" for s in seed_startups)
        startup_names = {s.name for s in seed_startups}
        assert startup_names == {"Seed1", "Seed2"}
    
    async def test_find_all_returns_all_saved_startups(self, startup_repo: StartupRepository):
        """Test retrieving all startups."""
        # Arrange
        startups = [
            Startup(name="Startup1", sector="B2B SaaS", stage="Seed"),
            Startup(name="Startup2", sector="Fintech", stage="Series A"),
            Startup(name="Startup3", sector="AI/ML", stage="Pre-seed")
        ]
        
        for startup in startups:
            await startup_repo.save(startup)
        
        # Act
        all_startups = await startup_repo.find_all()
        
        # Assert
        assert len(all_startups) == 3
        startup_names = {s.name for s in all_startups}
        assert startup_names == {"Startup1", "Startup2", "Startup3"}
    
    async def test_delete_removes_startup(self, startup_repo: StartupRepository):
        """Test deleting a startup."""
        # Arrange
        startup = await startup_repo.save(Startup(name="ToDelete", sector="B2B SaaS", stage="Seed"))
        
        # Act
        deleted = await startup_repo.delete(startup.id)
        
        # Assert
        assert deleted is True
        assert await startup_repo.find_by_id(startup.id) is None
    
    async def test_delete_returns_false_when_not_found(self, startup_repo: StartupRepository):
        """Test deleting non-existent startup returns False."""
        # Act
        deleted = await startup_repo.delete(uuid4())
        
        # Assert
        assert deleted is False
    
    @pytest.mark.parametrize("sector,expected_count", [
        ("B2B SaaS", 2),
        ("Fintech", 1),
        ("AI/ML", 1),
        ("Healthcare", 0)
    ])
    async def test_startup_repository_sector_filtering(self, startup_repo: StartupRepository, sector: str, expected_count: int):
        """Parametrized test for sector filtering."""
        # Arrange
        startups = [
            Startup(name="SaaS1", sector="B2B SaaS", stage="Seed"),
            Startup(name="SaaS2", sector="B2B SaaS", stage="Series A"),
            Startup(name="Fintech1", sector="Fintech", stage="Seed"),
            Startup(name="AI1", sector="AI/ML", stage="Pre-seed")
        ]
        
        for startup in startups:
            await startup_repo.save(startup)
        
        # Act
        results = await startup_repo.find_by_sector(sector)
        
        # Assert
        assert len(results) == expected_count


class TestVCRepository:
    """Test cases for VCRepository interface."""
    
    @pytest.fixture
    def vc_repo(self) -> VCRepository:
        """Fixture to provide a VCRepository instance."""
        # This will fail initially as VCRepository doesn't exist yet
        from src.core.repositories.vc_repository import InMemoryVCRepository
        return InMemoryVCRepository()
    
    @pytest.fixture
    def sample_vc(self) -> VC:
        """Fixture providing a sample VC."""
        return VC(
            firm_name="AI Ventures",
            website="https://aiventures.example.com",
            thesis="Investing in AI-first B2B SaaS companies",
            check_size_min=100000,
            check_size_max=2000000,
            sectors=["B2B SaaS", "AI/ML"],
            stages=["Seed", "Series A"]
        )
    
    async def test_save_vc_assigns_id_when_none_provided(self, vc_repo: VCRepository, sample_vc: VC):
        """Test that saving a VC without ID assigns a new ID."""
        # Act
        saved_vc = await vc_repo.save(sample_vc)
        
        # Assert
        assert hasattr(saved_vc, 'id')
        assert saved_vc.id is not None
        assert isinstance(saved_vc.id, UUID)
        assert saved_vc.firm_name == sample_vc.firm_name
    
    async def test_find_by_id_returns_saved_vc(self, vc_repo: VCRepository, sample_vc: VC):
        """Test finding a VC by ID after saving."""
        # Arrange
        saved_vc = await vc_repo.save(sample_vc)
        
        # Act
        found_vc = await vc_repo.find_by_id(saved_vc.id)
        
        # Assert
        assert found_vc is not None
        assert found_vc.id == saved_vc.id
        assert found_vc.firm_name == saved_vc.firm_name
        assert found_vc.sectors == saved_vc.sectors
    
    async def test_find_by_sector_returns_matching_vcs(self, vc_repo: VCRepository):
        """Test finding VCs that invest in a specific sector."""
        # Arrange
        saas_vc = VC(firm_name="SaaS Capital", sectors=["B2B SaaS"], stages=["Seed", "Series A"])
        multi_sector_vc = VC(firm_name="Diversified VC", sectors=["B2B SaaS", "Fintech", "AI/ML"], stages=["Seed"])
        fintech_vc = VC(firm_name="Fintech Partners", sectors=["Fintech"], stages=["Series A", "Series B"])
        
        await vc_repo.save(saas_vc)
        await vc_repo.save(multi_sector_vc)
        await vc_repo.save(fintech_vc)
        
        # Act
        saas_vcs = await vc_repo.find_by_sector("B2B SaaS")
        
        # Assert
        assert len(saas_vcs) == 2
        vc_names = {vc.firm_name for vc in saas_vcs}
        assert vc_names == {"SaaS Capital", "Diversified VC"}
    
    async def test_find_by_stage_returns_matching_vcs(self, vc_repo: VCRepository):
        """Test finding VCs that invest in a specific stage."""
        # Arrange
        seed_vc = VC(firm_name="Seed Fund", sectors=["B2B SaaS"], stages=["Pre-seed", "Seed"])
        growth_vc = VC(firm_name="Growth Partners", sectors=["B2B SaaS"], stages=["Series B", "Series C"])
        multi_stage_vc = VC(firm_name="Full Stack VC", sectors=["Fintech"], stages=["Seed", "Series A", "Series B"])
        
        await vc_repo.save(seed_vc)
        await vc_repo.save(growth_vc)
        await vc_repo.save(multi_stage_vc)
        
        # Act
        seed_stage_vcs = await vc_repo.find_by_stage("Seed")
        
        # Assert
        assert len(seed_stage_vcs) == 2
        vc_names = {vc.firm_name for vc in seed_stage_vcs}
        assert vc_names == {"Seed Fund", "Full Stack VC"}
    
    async def test_find_by_check_size_range_returns_matching_vcs(self, vc_repo: VCRepository):
        """Test finding VCs by check size range."""
        # Arrange
        small_check_vc = VC(
            firm_name="Micro VC",
            sectors=["B2B SaaS"],
            stages=["Pre-seed"],
            check_size_min=10000,
            check_size_max=100000
        )
        medium_check_vc = VC(
            firm_name="Mid VC",
            sectors=["B2B SaaS"],
            stages=["Seed"],
            check_size_min=100000,
            check_size_max=1000000
        )
        large_check_vc = VC(
            firm_name="Big VC",
            sectors=["B2B SaaS"],
            stages=["Series A"],
            check_size_min=1000000,
            check_size_max=10000000
        )
        
        await vc_repo.save(small_check_vc)
        await vc_repo.save(medium_check_vc)
        await vc_repo.save(large_check_vc)
        
        # Act
        vcs_for_500k = await vc_repo.find_by_check_size_range(500000)
        
        # Assert
        assert len(vcs_for_500k) == 1
        assert vcs_for_500k[0].firm_name == "Mid VC"
    
    async def test_find_all_returns_all_saved_vcs(self, vc_repo: VCRepository):
        """Test retrieving all VCs."""
        # Arrange
        vcs = [
            VC(firm_name="VC1", sectors=["B2B SaaS"], stages=["Seed"]),
            VC(firm_name="VC2", sectors=["Fintech"], stages=["Series A"]),
            VC(firm_name="VC3", sectors=["AI/ML"], stages=["Pre-seed"])
        ]
        
        for vc in vcs:
            await vc_repo.save(vc)
        
        # Act
        all_vcs = await vc_repo.find_all()
        
        # Assert
        assert len(all_vcs) == 3
        vc_names = {vc.firm_name for vc in all_vcs}
        assert vc_names == {"VC1", "VC2", "VC3"}
    
    async def test_delete_removes_vc(self, vc_repo: VCRepository):
        """Test deleting a VC."""
        # Arrange
        vc = await vc_repo.save(VC(firm_name="ToDelete", sectors=["B2B SaaS"], stages=["Seed"]))
        
        # Act
        deleted = await vc_repo.delete(vc.id)
        
        # Assert
        assert deleted is True
        assert await vc_repo.find_by_id(vc.id) is None