import pytest
from src.core.models.vc import VC

def test_vc_creation():
    vc = VC(
        firm_name="Sequoia Capital",
        website="https://sequoiacap.com",
        thesis="Early stage B2B SaaS with AI focus",
        check_size_min=1_000_000,
        check_size_max=10_000_000,
        sectors=["B2B SaaS", "AI/ML"],
        stages=["Seed", "Series A"]
    )
    
    assert vc.firm_name == "Sequoia Capital"
    assert vc.can_invest_in_stage("Seed")
    assert not vc.can_invest_in_stage("Series D")
    
def test_vc_check_size_match():
    vc = VC(
        firm_name="Test VC",
        check_size_min=500_000,
        check_size_max=2_000_000
    )
    
    assert vc.matches_funding_amount(1_000_000)
    assert not vc.matches_funding_amount(5_000_000)