# VC Matching Platform - Test Suite

## Overview

This test suite provides comprehensive coverage for the VC Matching Platform, following Test-Driven Development (TDD) principles. The tests are organized into unit tests and integration tests, covering all major components including the AI analyzer service, FastAPI endpoints, and the matching engine.

## Test Structure

```
tests/
├── conftest.py           # Shared fixtures and test configuration
├── run_tests.py          # Test runner script with various options
├── unit/                 # Unit tests (no external dependencies)
│   ├── ai/              # AI service tests
│   │   ├── test_analyzer.py
│   │   └── test_cache.py
│   ├── api/             # API endpoint tests
│   │   ├── test_startups_endpoints.py
│   │   ├── test_vcs_endpoints.py
│   │   ├── test_matches_endpoints.py
│   │   ├── test_middleware.py
│   │   └── test_error_handling.py
│   └── existing tests...
└── integration/          # Integration tests
    ├── test_ai_matching_integration.py
    └── test_end_to_end.py
```

## Running Tests

### Basic Commands

```bash
# Run all tests
pytest

# Run with coverage report
pytest --cov=src --cov-report=html

# Run only unit tests
pytest -m unit

# Run only integration tests
pytest -m integration

# Run specific test file
pytest tests/unit/ai/test_analyzer.py

# Run tests in watch mode (requires pytest-watch)
ptw
```

### Using the Test Runner Script

```bash
# Make the script executable
chmod +x tests/run_tests.py

# Run unit tests with coverage
./tests/run_tests.py --unit --coverage

# Run API tests in verbose mode
./tests/run_tests.py --api -v

# Run tests in watch mode
./tests/run_tests.py --watch

# Run only previously failed tests
./tests/run_tests.py --failed
```

## Test Categories

Tests are marked with pytest markers for easy filtering:

- `@pytest.mark.unit` - Unit tests with no external dependencies
- `@pytest.mark.integration` - Integration tests that may use external services
- `@pytest.mark.api` - API endpoint tests
- `@pytest.mark.ai` - AI service tests
- `@pytest.mark.auth` - Authentication tests
- `@pytest.mark.slow` - Slow running tests (excluded by default)

## Key Testing Patterns

### 1. AAA Pattern (Arrange, Act, Assert)

All tests follow the AAA pattern for clarity:

```python
def test_startup_matches_vc_when_sectors_align():
    # Arrange
    startup = Startup(name="TechCo", sector="B2B SaaS", stage="Seed")
    vc = VC(firm_name="AI Ventures", sectors=["B2B SaaS"], stages=["Seed"])
    
    # Act
    match = MatchingEngine().calculate_match(startup, vc)
    
    # Assert
    assert match.score > 0.8
    assert "sector alignment" in match.reasons
```

### 2. Mocking External Dependencies

External services are mocked at boundaries:

```python
@pytest.fixture
def mock_ai_analyzer(mock_openai, mock_ai_cache):
    analyzer = AIAnalyzerService(cache=mock_ai_cache)
    analyzer.llm = mock_openai
    return analyzer
```

### 3. Async Testing

Async functions are tested with `pytest-asyncio`:

```python
@pytest.mark.asyncio
async def test_analyze_startup_returns_cached_result():
    # Test async functions
    result = await analyzer.analyze_startup(startup)
    assert result is not None
```

### 4. Parametrized Tests

Multiple scenarios tested efficiently:

```python
@pytest.mark.parametrize("stage,expected", [
    ("Seed", True),
    ("Series D", False),
])
def test_vc_stage_matching(stage, expected):
    # Test multiple cases
```

## Coverage Requirements

- Minimum coverage: 80% for business logic
- Focus areas:
  - Domain models and business logic
  - API endpoints and error handling
  - AI service integration
  - Matching algorithm

## Fixtures

Key fixtures available in `conftest.py`:

- `mock_redis` - Mocked Redis client
- `mock_db_session` - Mocked database session
- `mock_ai_analyzer` - Mocked AI analyzer service
- `sample_startup` - Pre-configured startup instance
- `sample_vc` - Pre-configured VC instance
- `test_client` - FastAPI test client with mocked dependencies
- `auth_headers` - Authentication headers for protected endpoints

## Best Practices

1. **Test Behavior, Not Implementation**
   - Focus on what the code does, not how it does it
   - Tests should survive refactoring

2. **Keep Tests Fast**
   - Unit tests should run in <100ms
   - Use mocks for external dependencies
   - Mark slow tests with `@pytest.mark.slow`

3. **Test One Thing**
   - Each test should verify one specific behavior
   - Use descriptive test names that explain the scenario

4. **Use Factories for Test Data**
   - `startup_factory` and `vc_factory` fixtures create test data
   - Avoid hardcoding test data

5. **Handle Async Properly**
   - Use `pytest-asyncio` for async tests
   - Mock async functions with `AsyncMock`

## Continuous Integration

The test suite is designed to run in CI/CD pipelines:

```yaml
# Example GitHub Actions workflow
- name: Run tests
  run: |
    pip install -r requirements-test.txt
    pytest --cov=src --cov-report=xml
    
- name: Upload coverage
  uses: codecov/codecov-action@v3
```

## Troubleshooting

### Common Issues

1. **Import Errors**
   - Ensure PYTHONPATH includes the project root
   - Run tests from project root: `python -m pytest`

2. **Async Warnings**
   - Use `pytest-asyncio` and mark async tests properly
   - Configure `asyncio_mode = auto` in pytest.ini

3. **Mock Not Working**
   - Check patch paths match import statements
   - Use `spec` parameter for better mock validation

4. **Flaky Tests**
   - Avoid time-dependent assertions
   - Mock datetime/time functions
   - Use proper async handling

## Contributing

When adding new tests:

1. Write failing test first (Red)
2. Implement minimal code to pass (Green)
3. Refactor while keeping tests green
4. Ensure new code has >80% coverage
5. Run full test suite before committing

## Performance Testing

For performance-critical code:

```python
@pytest.mark.benchmark
def test_matching_performance(benchmark):
    result = benchmark(matching_engine.calculate_match, startup, vc)
    assert result.score >= 0
```

## Security Testing

Security considerations are tested:

- SQL injection attempts
- XSS prevention
- Authentication bypass attempts
- Rate limiting effectiveness

See `test_error_handling.py` for examples.