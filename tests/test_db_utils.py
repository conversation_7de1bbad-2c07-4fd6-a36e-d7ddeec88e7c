"""Test database utilities for using migrations in tests."""

import logging
from contextlib import contextmanager
from typing import Generator

from sqlalchemy import create_engine, text
from sqlalchemy.engine import Engine
from sqlalchemy.ext.asyncio import AsyncEngine, create_async_engine, AsyncSession
from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy.pool import StaticPool

from src.database.setup import Base
from src.database.migrations import get_alembic_config, is_database_initialized
from alembic import command

logger = logging.getLogger(__name__)


def create_test_engine(database_url: str = "sqlite:///:memory:") -> Engine:
    """Create a test database engine.
    
    Args:
        database_url: Database URL (default: in-memory SQLite)
        
    Returns:
        SQLAlchemy Engine
    """
    # Use StaticPool for SQLite to avoid connection issues in tests
    if database_url.startswith("sqlite"):
        return create_engine(
            database_url,
            connect_args={"check_same_thread": False},
            poolclass=StaticPool,
        )
    else:
        return create_engine(database_url)


def create_async_test_engine(database_url: str = "sqlite+aiosqlite:///:memory:") -> AsyncEngine:
    """Create an async test database engine.
    
    Args:
        database_url: Async database URL
        
    Returns:
        SQLAlchemy AsyncEngine
    """
    # Use StaticPool for SQLite to avoid connection issues in tests
    if "sqlite" in database_url:
        return create_async_engine(
            database_url,
            connect_args={"check_same_thread": False},
            poolclass=StaticPool,
        )
    else:
        return create_async_engine(database_url)


def init_test_db_with_migrations(engine: Engine) -> None:
    """Initialize test database using migrations.
    
    For test databases, we can safely use create_all() since
    we're working with temporary databases that are torn down
    after tests.
    
    Args:
        engine: SQLAlchemy engine
    """
    # Import all models to ensure they're registered
    from src.database.models import startup, vc, match, user  # noqa
    
    # For tests, we use create_all() for speed and simplicity
    # This is safe because test databases are temporary
    Base.metadata.create_all(bind=engine)
    
    # If using PostgreSQL in tests, we might want to use real migrations
    if "postgresql" in str(engine.url):
        try:
            # Check if we can use Alembic
            config = get_alembic_config()
            
            # Stamp the database as up-to-date
            with engine.begin() as connection:
                config.attributes['connection'] = connection
                command.stamp(config, "head")
        except Exception as e:
            logger.warning(f"Could not stamp test database with Alembic: {e}")


async def init_async_test_db_with_migrations(engine: AsyncEngine) -> None:
    """Initialize async test database.
    
    Args:
        engine: SQLAlchemy AsyncEngine
    """
    # Import all models to ensure they're registered
    from src.database.models import startup, vc, match, user  # noqa
    
    # For async tests, use create_all()
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)


@contextmanager
def test_db_session(database_url: str = "sqlite:///:memory:") -> Generator[Session, None, None]:
    """Create a test database session as a context manager.
    
    Args:
        database_url: Database URL for testing
        
    Yields:
        SQLAlchemy Session
    """
    engine = create_test_engine(database_url)
    init_test_db_with_migrations(engine)
    
    SessionLocal = sessionmaker(bind=engine)
    session = SessionLocal()
    
    try:
        yield session
    finally:
        session.close()
        # Clean up
        Base.metadata.drop_all(bind=engine)
        engine.dispose()


@contextmanager  
def test_db_engine(database_url: str = "sqlite:///:memory:") -> Generator[Engine, None, None]:
    """Create a test database engine as a context manager.
    
    Args:
        database_url: Database URL for testing
        
    Yields:
        SQLAlchemy Engine
    """
    engine = create_test_engine(database_url)
    init_test_db_with_migrations(engine)
    
    try:
        yield engine
    finally:
        # Clean up
        Base.metadata.drop_all(bind=engine)
        engine.dispose()


# Convenience functions for backward compatibility
def setup_test_db(engine: Engine) -> None:
    """Set up test database (backward compatibility).
    
    Args:
        engine: SQLAlchemy engine
    """
    init_test_db_with_migrations(engine)


async def setup_async_test_db(engine: AsyncEngine) -> None:
    """Set up async test database (backward compatibility).
    
    Args:
        engine: SQLAlchemy AsyncEngine  
    """
    await init_async_test_db_with_migrations(engine)