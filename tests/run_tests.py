#!/usr/bin/env python3
"""Test runner script with various test execution options."""

import sys
import subprocess
import argparse
from pathlib import Path

def run_command(cmd: list) -> int:
    """Run a command and return the exit code."""
    print(f"Running: {' '.join(cmd)}")
    print("-" * 80)
    result = subprocess.run(cmd)
    print("-" * 80)
    return result.returncode

def main():
    parser = argparse.ArgumentParser(description="Run tests for VC Matching Platform")
    parser.add_argument("--unit", action="store_true", help="Run only unit tests")
    parser.add_argument("--integration", action="store_true", help="Run only integration tests")
    parser.add_argument("--api", action="store_true", help="Run only API tests")
    parser.add_argument("--ai", action="store_true", help="Run only AI service tests")
    parser.add_argument("--coverage", action="store_true", help="Generate coverage report")
    parser.add_argument("--watch", action="store_true", help="Run tests in watch mode")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--failed", action="store_true", help="Run only previously failed tests")
    parser.add_argument("--slow", action="store_true", help="Include slow tests")
    parser.add_argument("tests", nargs="*", help="Specific test files or directories")
    
    args = parser.parse_args()
    
    # Base pytest command
    cmd = ["pytest"]
    
    # Add verbosity
    if args.verbose:
        cmd.append("-vv")
    else:
        cmd.append("-v")
    
    # Add markers based on arguments
    markers = []
    if args.unit:
        markers.append("unit")
    if args.integration:
        markers.append("integration")
    if args.api:
        markers.append("api")
    if args.ai:
        markers.append("ai")
    if not args.slow:
        markers.append("not slow")
    
    if markers:
        cmd.extend(["-m", " and ".join(markers)])
    
    # Add coverage if requested
    if args.coverage:
        cmd.extend([
            "--cov=src",
            "--cov-report=term-missing",
            "--cov-report=html",
            "--cov-fail-under=80"
        ])
    
    # Add watch mode
    if args.watch:
        # Use pytest-watch if available
        cmd = ["ptw"] + cmd[1:]  # Replace pytest with ptw
    
    # Run only failed tests
    if args.failed:
        cmd.append("--lf")
    
    # Add specific test paths
    if args.tests:
        cmd.extend(args.tests)
    
    # Run the tests
    exit_code = run_command(cmd)
    
    # Open coverage report if generated
    if args.coverage and exit_code == 0:
        coverage_path = Path("htmlcov/index.html")
        if coverage_path.exists():
            print(f"\nCoverage report generated at: {coverage_path.absolute()}")
            if sys.platform == "darwin":  # macOS
                subprocess.run(["open", str(coverage_path)])
            elif sys.platform == "linux":
                subprocess.run(["xdg-open", str(coverage_path)])
    
    return exit_code

if __name__ == "__main__":
    sys.exit(main())