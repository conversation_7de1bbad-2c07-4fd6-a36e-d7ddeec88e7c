global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    environment: 'staging'
    project: 'vc-matching-platform'

scrape_configs:
  # FastAPI metrics
  - job_name: 'fastapi'
    static_configs:
      - targets: ['api:8000']
    metrics_path: '/metrics'

  # PostgreSQL exporter (if added)
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  # Redis exporter (if added)
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  # Celery/Flower metrics
  - job_name: 'celery'
    static_configs:
      - targets: ['flower:5555']
    metrics_path: '/metrics'

  # Nginx exporter (if added)
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']

  # Node exporter for system metrics (if added)
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']