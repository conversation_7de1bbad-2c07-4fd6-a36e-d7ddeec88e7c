# Database Connection Setup Guide

## Overview
This guide explains how to set up and troubleshoot PostgreSQL connections for the VC Matching Platform in both local development and Docker environments.

## Current Status ✅
- **Local Development**: Working with async PostgreSQL connection
- **Docker Environment**: Ready to deploy
- **Connection String**: `postgresql+asyncpg://postgres:postgres@db:5432/vc_matching_platform`

## Quick Start

### Local Development (Recommended for immediate work)
```bash
# 1. Use local environment configuration
cp .env.local .env

# 2. Make sure PostgreSQL is running
brew services start postgresql

# 3. Install dependencies
pip install -r requirements.txt

# 4. Run database setup
python scripts/setup_database.py

# 5. Test the connection
python test_async_db_connection.py
```

### Docker Environment
```bash
# 1. Use Docker environment configuration  
cp .env.docker .env

# 2. Start Docker services
docker-compose up -d db redis

# 3. Run migrations
docker-compose exec api alembic upgrade head

# 4. Test connection inside container
docker-compose exec api python scripts/setup_database.py
```

## Environment Files

### `.env` (Local Development)
```env
DATABASE_URL=postgresql+asyncpg://thecostcokid@localhost:5432/vc_matching_platform
REDIS_URL=redis://localhost:6379
```

### `.env.docker` (Docker Environment)
```env
DATABASE_URL=postgresql+asyncpg://postgres:postgres@db:5432/vc_matching_platform
REDIS_URL=redis://redis:6379
```

## Key Changes Made

### 1. Added AsyncPG Driver
- **Issue**: Missing `asyncpg` dependency for async PostgreSQL connections
- **Fix**: Added `asyncpg==0.29.0` to `requirements.txt`
- **Impact**: Enables proper async database operations with FastAPI

### 2. Fixed Connection Strings
- **Issue**: Mismatched connection strings between local and Docker environments
- **Fix**: Created separate `.env.local` and `.env.docker` configurations
- **Impact**: Clear separation between development environments

### 3. Connection String Format
- **Before**: `postgresql://user:pass@host:port/db`
- **After**: `postgresql+asyncpg://user:pass@host:port/db`
- **Why**: Explicitly specifies the async driver for SQLAlchemy

### 4. Docker Networking
- **Issue**: App trying to connect to `db:5432` when Docker not running
- **Fix**: Environment-specific configurations and clear setup instructions
- **Impact**: No more "nodename nor servname provided" errors

## Testing Scripts

### `test_async_db_connection.py`
Comprehensive async connection test with detailed error reporting:
```bash
python test_async_db_connection.py
```

### `scripts/setup_database.py`
Complete database setup and verification:
```bash
python scripts/setup_database.py
```

## Troubleshooting

### Error: "nodename nor servname provided, or not known"
- **Cause**: Trying to connect to Docker service names without Docker running
- **Fix**: Use `.env.local` for local development or start Docker services

### Error: "asyncpg module not found"
- **Cause**: Missing async PostgreSQL driver
- **Fix**: `pip install asyncpg==0.29.0`

### Error: "database does not exist"
- **Local**: `createdb vc_matching_platform`
- **Docker**: Database auto-created by Docker Compose

### Error: "connection refused"
- **Local**: `brew services start postgresql`
- **Docker**: `docker-compose up -d db`

## Database Schema

Current tables created:
- `alembic_version` - Migration tracking
- `startups` - Startup information
- `vcs` - Venture Capital information  
- `matches` - Startup-VC matching records
- `users` - Authentication users

## Next Steps

1. **Start Application**: Database is ready, you can now run the FastAPI app
2. **Test Endpoints**: Create/retrieve startups and VCs via API
3. **Add Redis**: Configure Redis connection for caching
4. **Authentication**: Set up JWT authentication system

## Validation Commands

```bash
# Check local connection
python test_async_db_connection.py

# Full setup verification
python scripts/setup_database.py

# Check tables exist
psql vc_matching_platform -c "\dt"

# Check Docker services
docker-compose ps
```

## Configuration Reference

### SQLAlchemy Settings (src/core/config.py)
- `database_pool_size`: 5 connections
- `database_max_overflow`: 10 additional connections
- `database_echo`: False (set to True for SQL debugging)

### Docker Compose (docker-compose.yml)
- PostgreSQL 15 Alpine image
- Health checks enabled
- Persistent volume for data
- Service dependencies configured

---

✅ **Status**: Database connection is now working and ready for development!