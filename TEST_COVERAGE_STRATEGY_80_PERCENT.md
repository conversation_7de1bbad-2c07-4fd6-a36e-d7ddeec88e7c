# Test Coverage Strategy: Achieving 80% Coverage

## Executive Summary
- **Current Coverage**: 47.3% (1,101/2,329 lines)
- **Coverage with Exclusions**: 52.9% (1,101/2,081 lines)
- **Target Coverage**: 80% (1,664/2,081 lines)
- **Lines to Cover**: 563 additional lines
- **Timeline**: 3-4 weeks with focused effort

## Phase 1: Immediate Actions (Week 1)
### 1.1 Configuration Updates
The `pytest_updated.ini` already excludes old files, which improves coverage to 52.9%.

### 1.2 Remove Deprecated Files (248 lines saved)
```bash
# Files to remove:
rm src/database/repositories/vc_repository_old.py      # 132 lines
rm src/database/repositories/match_repository_old.py   # 116 lines
```

## Phase 2: High-Impact Test Priorities (Weeks 2-3)

### Priority 1: Core Services (191 missing lines) - Expected +9% coverage
These are the business-critical services with the lowest coverage:

#### 1. match_service.py (87 missing lines, 19% coverage)
- **Impact**: Core matching functionality
- **Effort**: Medium
- **Test file**: `tests/unit/test_match_service_enhanced.py`
- **Key methods to test**:
  - `create_match()` with various scenarios
  - `update_match_status()`
  - `get_matches_for_startup()`
  - `get_matches_for_vc()`
  - Error handling and edge cases

#### 2. vc_service.py (52 missing lines, 24% coverage)
- **Impact**: VC management core
- **Effort**: Low-Medium
- **Test file**: `tests/unit/test_vc_service_enhanced.py`
- **Key methods to test**:
  - CRUD operations
  - Search and filter functionality
  - Validation logic

#### 3. startup_service.py (31 missing lines, 34% coverage)
- **Impact**: Startup management core
- **Effort**: Low
- **Test file**: `tests/unit/test_startup_service_enhanced.py`

#### 4. matching_engine.py (24 missing lines, 23% coverage)
- **Impact**: AI matching logic
- **Effort**: Medium
- **Test file**: `tests/unit/test_matching_engine_enhanced.py`

### Priority 2: API Endpoints (160 missing lines) - Expected +7.5% coverage
User-facing endpoints that need comprehensive testing:

#### 1. startups.py (59 missing lines, 31% coverage)
- **Test file**: Enhance existing `test_startups_endpoints.py`
- **Focus**: Error cases, validation, pagination

#### 2. matches.py (55 missing lines, 34% coverage)
- **Test file**: Enhance existing `test_matches_endpoints.py`
- **Focus**: Authorization, filters, bulk operations

#### 3. vcs.py (46 missing lines, 36% coverage)
- **Test file**: Enhance existing `test_vcs_endpoints.py`
- **Focus**: Search functionality, error handling

### Priority 3: Database Repositories (175 missing lines) - Expected +8% coverage
Critical data layer with async operations:

#### 1. match_repository.py (74 missing lines, 51% coverage)
- **Test file**: `tests/unit/database/test_match_repository_async.py`
- **Focus**: Async operations, complex queries

#### 2. startup_repository.py (53 missing lines, 57% coverage)
- **Test file**: Enhance existing tests with async coverage
- **Focus**: Search, filters, pagination

#### 3. vc_repository.py (48 missing lines, 62% coverage)
- **Test file**: Enhance existing tests
- **Focus**: Complex queries, error scenarios

### Priority 4: AI Components (212 missing lines) - Expected +10% coverage

#### 1. langchain_adapter.py (80 missing lines, 22% coverage)
- **Test file**: `tests/unit/adapters/test_langchain_adapter.py`
- **Strategy**: Mock LangChain components heavily

#### 2. analyzer.py (69 missing lines, 27% coverage)
- **Test file**: Enhance existing `test_analyzer.py`
- **Focus**: Mock AI calls, test analysis logic

#### 3. cache.py (63 missing lines, 22% coverage)
- **Test file**: Enhance existing `test_cache.py`
- **Focus**: Cache operations, TTL, invalidation

## Phase 3: Additional Improvements (Week 4)

### Lower Priority Files (60 missing lines) - Expected +3% coverage
- **api/errors.py** (28 missing lines) - Error handling
- **api/main.py** (25 missing lines) - FastAPI setup
- **core/ai/chains.py** (20 missing lines) - AI chains

## Test Implementation Strategy

### 1. Testing Patterns
```python
# Use comprehensive fixtures in conftest.py
@pytest.fixture
def mock_async_session():
    """Mock for async database sessions"""
    session = AsyncMock()
    session.execute = AsyncMock()
    session.commit = AsyncMock()
    return session

@pytest.fixture
def mock_ai_service():
    """Mock for AI services"""
    service = AsyncMock()
    service.analyze_compatibility = AsyncMock(return_value={'score': 0.85})
    return service
```

### 2. Coverage Tracking Script
```python
# scripts/track_coverage.py
import subprocess
import json

def run_coverage_check():
    # Run tests with coverage
    result = subprocess.run(
        ['pytest', '--cov=src', '--cov-report=json'],
        capture_output=True
    )
    
    # Parse coverage data
    with open('coverage.json', 'r') as f:
        data = json.load(f)
    
    print(f"Current coverage: {data['totals']['percent_covered']:.1f}%")
    print(f"Files below 50%:")
    for file, stats in data['files'].items():
        if stats['summary']['percent_covered'] < 50:
            print(f"  - {file}: {stats['summary']['percent_covered']:.1f}%")
```

## Estimated Coverage Timeline

| Week | Focus Area | Files | Lines to Cover | Expected Coverage |
|------|------------|-------|----------------|-------------------|
| 1 | Setup & Services | 4 | 191 | 52.9% → 62% |
| 2 | API Endpoints | 3 | 160 | 62% → 69.5% |
| 3 | Repositories & AI | 6 | 175 | 69.5% → 77.5% |
| 4 | Final Push | 3+ | 60+ | 77.5% → 80%+ |

## Success Metrics

1. **Weekly Progress**: Minimum 7-8% coverage increase per week
2. **File Coverage**: No critical file below 60% coverage
3. **Test Quality**: All tests use proper mocking, no flaky tests
4. **CI/CD**: All tests pass in under 5 minutes

## Files to Exclude or Remove

### Already Excluded (in pytest_updated.ini)
- `*/*_old.py` - Old repository files
- `*/examples.py` - Example files
- `src/core/ai/config.py` - Configuration
- `src/core/ai/exceptions.py` - Simple exceptions
- `src/core/ai/streaming.py` - Streaming utilities
- `src/core/ai/rate_limiter.py` - Rate limiting
- `src/database/setup.py` - Database setup

### Files to Remove
1. `src/database/repositories/vc_repository_old.py` (132 lines)
2. `src/database/repositories/match_repository_old.py` (116 lines)

## Implementation Checklist

- [ ] Remove old repository files
- [ ] Run coverage report with updated configuration
- [ ] Create enhanced test files for services (Priority 1)
- [ ] Enhance API endpoint tests (Priority 2)
- [ ] Add async repository tests (Priority 3)
- [ ] Mock AI components thoroughly (Priority 4)
- [ ] Set up coverage tracking automation
- [ ] Configure CI/CD with coverage gates

## Best Practices

1. **Mock Everything External**
   - Database connections
   - AI/LLM calls
   - External APIs
   - File I/O operations

2. **Test Patterns**
   - Use `pytest.mark.asyncio` for async tests
   - Group related tests in classes
   - Use parametrized tests for multiple scenarios
   - Test both success and failure paths

3. **Coverage Goals**
   - Aim for 80% line coverage
   - Focus on critical paths first
   - Don't test for the sake of coverage
   - Ensure tests are meaningful

## Monitoring and Maintenance

1. **Daily**: Run `pytest --cov=src --cov-report=term-missing`
2. **Weekly**: Review coverage trends
3. **Per PR**: Enforce no coverage decrease
4. **Monthly**: Review and update test strategy