# Database Setup Guide

## Prerequisites

You need PostgreSQL installed and running on your system.

### macOS (using Homebrew)
```bash
# Install PostgreSQL
brew install postgresql@14

# Start PostgreSQL service
brew services start postgresql@14

# Create the database
createdb vc_matching_platform
```

### Ubuntu/Debian
```bash
# Install PostgreSQL
sudo apt-get update
sudo apt-get install postgresql postgresql-contrib

# Start PostgreSQL service
sudo systemctl start postgresql

# Create the database
sudo -u postgres createdb vc_matching_platform
```

### Windows
1. Download PostgreSQL from https://www.postgresql.org/download/windows/
2. Run the installer
3. Use pgAdmin or command line to create `vc_matching_platform` database

## Configuration

1. Update the `.env` file with your database credentials:
```env
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/vc_matching_platform
```

2. Test the connection:
```bash
python test_db_connection.py
```

3. Initialize the database:
```bash
python scripts/init_db.py
```

## Common Connection Strings

### Local development (default)
```
postgresql://postgres:postgres@localhost:5432/vc_matching_platform
```

### With custom user
```
postgresql://myuser:mypassword@localhost:5432/vc_matching_platform
```

### Docker PostgreSQL
```
********************************************/vc_matching_platform
```

## Troubleshooting

### "FATAL: database does not exist"
Create the database:
```bash
createdb vc_matching_platform
```

### "FATAL: role does not exist"
Create the user:
```bash
createuser -s postgres
```

### "could not connect to server"
Make sure PostgreSQL is running:
```bash
# macOS
brew services start postgresql@14

# Linux
sudo systemctl start postgresql

# Check status
pg_isready
```