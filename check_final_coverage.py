#!/usr/bin/env python3
"""Check final test coverage for the project."""

import subprocess
import re

# Run pytest with coverage, suppressing warnings
cmd = ["python3", "-m", "pytest", "tests/unit/", "--cov=src", "--cov-report=term", "-q", "--tb=no"]
result = subprocess.run(cmd, capture_output=True, text=True, env={'PYTHONPATH': '.'})

output = result.stdout + result.stderr

# Extract total coverage
total_match = re.search(r'TOTAL\s+\d+\s+\d+\s+(\d+)%', output)

if total_match:
    total_coverage = int(total_match.group(1))
    print(f"Current Overall Test Coverage: {total_coverage}%")
    print(f"Target Coverage: 80%")
    
    if total_coverage >= 80:
        print("\n✅ CONGRATULATIONS! We've achieved the 80% coverage goal!")
    else:
        print(f"\n📊 Progress: {total_coverage}% / 80% (need {80 - total_coverage}% more)")
else:
    print("Unable to determine coverage. Showing raw output:")
    print(output)

# Show module breakdown
print("\n" + "="*70)
print("Module Coverage Summary:")
print("="*70)

# Extract our target modules
target_modules = [
    "match_service.py",
    "matching_engine.py", 
    "matches.py",
    "startups.py",
    "vcs.py",
    "startup_service.py",
    "vc_service.py",
    "match_repository.py",
    "startup_repository.py",
    "vc_repository.py"
]

for line in output.split('\n'):
    for module in target_modules:
        if module in line and '%' in line:
            print(line.strip())