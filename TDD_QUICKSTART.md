# TDD Quick Start Guide

## 🚀 Start Here - Adding a New Feature

### Step 1: Initialize TDD Tracking
```bash
cd /Users/<USER>/Documents/BiLat/vc-matching-platform
python scripts/tdd_checkpoint.py --feature your_feature_name --start
```

### Step 2: Write Failing Test FIRST
```bash
# Create test file
touch tests/unit/test_your_feature.py

# Track it
python scripts/tdd_checkpoint.py --feature your_feature_name --test-file tests/unit/test_your_feature.py
```

### Step 3: Verify Test Fails
```bash
# Run test - it MUST fail
PYTHONPATH=. pytest tests/unit/test_your_feature.py -v

# Verify and track
python scripts/tdd_checkpoint.py --feature your_feature_name --verify-fail
```

### Step 4: Write Minimal Implementation
```bash
# Create implementation
touch src/core/services/your_feature.py

# Track it
python scripts/tdd_checkpoint.py --feature your_feature_name --impl-file src/core/services/your_feature.py
```

### Step 5: Make Test Pass
```bash
# Run test - should pass now
PYTHONPATH=. pytest tests/unit/test_your_feature.py -v

# Verify and track
python scripts/tdd_checkpoint.py --feature your_feature_name --verify-pass
```

### Step 6: Refactor
```bash
# Refactor code while keeping tests green
# Then mark as refactored
python scripts/tdd_checkpoint.py --feature your_feature_name --refactored
```

### Step 7: Check Coverage
```bash
# Run coverage check
PYTHONPATH=. pytest --cov=src --cov-report=term-missing

# Verify coverage
python scripts/tdd_checkpoint.py --feature your_feature_name --verify-coverage
```

### Step 8: Verify All Tests
```bash
# Run all tests
PYTHONPATH=. pytest tests/ -v

# Verify all green
python scripts/tdd_checkpoint.py --feature your_feature_name --verify-all
```

### Step 9: Complete Feature
```bash
# Mark feature complete
python scripts/tdd_checkpoint.py --feature your_feature_name --complete --report
```

## 🤖 Agent Usage Quick Reference

### Domain Modeling
```
Task: "Using the ddd_expert agent, design domain model for [feature]"
```

### Writing Tests
```
Task: "Using the tdd_specialist agent, write failing tests for [feature]"
```

### API Implementation
```
Task: "Using the fastapi_architect agent, implement endpoints for [feature]"
```

### AI Integration
```
Task: "Using the ai_langchain_expert agent, add AI analysis to [feature]"
```

## 🔍 Common Commands

### Check TDD Compliance
```bash
python scripts/check_tdd_compliance.py
```

### Run Tests with Coverage
```bash
PYTHONPATH=. pytest tests/ --cov=src --cov-report=html
open htmlcov/index.html  # View coverage report
```

### Install Git Hook
```bash
python scripts/check_tdd_compliance.py --install-hook
```

### Check Feature Status
```bash
python scripts/tdd_checkpoint.py --feature your_feature_name --report
```

## ❌ What NOT to Do

1. **NEVER** write code without a failing test first
2. **NEVER** commit if coverage drops below 80%
3. **NEVER** skip the refactor phase
4. **NEVER** mark tests as "TODO" and move on

## ✅ TDD Checklist for Every Feature

- [ ] Feature tracked in TDD checkpoint system
- [ ] Test file created before implementation
- [ ] Test fails initially (Red)
- [ ] Minimal code to pass test (Green)
- [ ] Refactored for clarity (Refactor)
- [ ] Coverage ≥ 80%
- [ ] All tests pass
- [ ] Handoff document created if switching agents

## 🚨 If You're Stuck

1. **Test won't fail initially**
   - Your test might be testing the wrong thing
   - Check test is actually running the code
   - Ensure assertions are correct

2. **Coverage below 80%**
   ```bash
   # See what's not covered
   PYTHONPATH=. pytest --cov=src --cov-report=term-missing
   ```

3. **Need help with test design**
   ```
   Task: "Using the tdd_specialist agent, help design tests for [specific behavior]"
   ```

## 📊 Current Project Status
- Coverage: 39% → Target: 80%
- Focus Areas: Repository pattern, business logic extraction
- Priority: Increase test coverage for existing code

Remember: **No code without a test!** 🧪