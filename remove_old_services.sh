#!/bin/bash
# Safe removal script for old service files
# Created: $(date)

set -e  # Exit on error

echo "=== Safe Removal of Old Service Files ==="
echo

# Step 1: Create backup
echo "Step 1: Creating backup..."
BACKUP_DIR="backup/old_services_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

# Copy files to backup
cp src/core/services/match_service_old.py "$BACKUP_DIR/" || { echo "Failed to backup match_service_old.py"; exit 1; }
cp src/core/services/startup_service_old.py "$BACKUP_DIR/" || { echo "Failed to backup startup_service_old.py"; exit 1; }
cp src/core/services/vc_service_old.py "$BACKUP_DIR/" || { echo "Failed to backup vc_service_old.py"; exit 1; }

# Create manifest
cat > "$BACKUP_DIR/MANIFEST.txt" << EOF
Backup created on: $(date)
Files backed up:
- match_service_old.py (208 lines)
- startup_service_old.py (166 lines)
- vc_service_old.py (176 lines)
Total: 550 lines
Reason: Deprecated files with 0% coverage, replaced by new implementations
EOF

echo "Backup created in: $BACKUP_DIR"
ls -la "$BACKUP_DIR"
echo

# Step 2: Final verification
echo "Step 2: Final verification - checking for imports..."
IMPORT_CHECK=$(grep -r "match_service_old\|startup_service_old\|vc_service_old" . --include="*.py" 2>/dev/null | grep -v "check_coverage_improvement.py" | grep -v "remove_old_services.sh" || true)

if [ -n "$IMPORT_CHECK" ]; then
    echo "WARNING: Found references to old service files:"
    echo "$IMPORT_CHECK"
    echo
    read -p "Do you want to continue with removal? (y/N) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Removal cancelled."
        exit 1
    fi
else
    echo "No imports found. Safe to proceed."
fi
echo

# Step 3: Remove files
echo "Step 3: Removing old service files..."
rm -f src/core/services/match_service_old.py && echo "Removed match_service_old.py"
rm -f src/core/services/startup_service_old.py && echo "Removed startup_service_old.py"
rm -f src/core/services/vc_service_old.py && echo "Removed vc_service_old.py"
echo

# Step 4: Verify removal
echo "Step 4: Verifying removal..."
if [ -f "src/core/services/match_service_old.py" ] || [ -f "src/core/services/startup_service_old.py" ] || [ -f "src/core/services/vc_service_old.py" ]; then
    echo "ERROR: Some files were not removed!"
    exit 1
else
    echo "All old service files successfully removed."
fi
echo

# Step 5: Show current service directory
echo "Step 5: Current service directory contents:"
ls -la src/core/services/
echo

echo "=== Removal Complete ==="
echo
echo "Next steps:"
echo "1. Run tests: pytest -v"
echo "2. Update check_coverage_improvement.py to remove references to old files"
echo "3. Update TEST_COVERAGE_IMPROVEMENT_PLAN.md"
echo
echo "To rollback if needed:"
echo "cp $BACKUP_DIR/*.py src/core/services/"
echo