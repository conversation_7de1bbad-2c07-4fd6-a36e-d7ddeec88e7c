#!/usr/bin/env python3
"""
Simple test to verify data persistence works.
"""

import asyncio
import uuid
from datetime import datetime
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

from src.database.setup import get_async_db, get_async_session_factory
from src.database.models.startup import Startup as StartupModel
from src.database.models.vc import VC as VCModel
from src.database.models.match import Match as MatchModel


async def test_persistence():
    """Test that we can save and retrieve data from PostgreSQL."""
    
    print("\n🧪 Testing Data Persistence in PostgreSQL\n")
    
    AsyncSessionLocal = get_async_session_factory()
    async with AsyncSessionLocal() as session:
        # Test 1: Create and save a startup
        print("1️⃣ Creating a startup...")
        startup = StartupModel(
            id=uuid.uuid4(),
            name=f"TestStartup_{datetime.now().strftime('%Y%m%d%H%M%S')}",
            sector="B2B SaaS",
            stage="Seed",
            description="A test startup for persistence verification",
            website="https://test.example.com",
            team_size=10,
            monthly_revenue=50000.0,
            created_at=datetime.utcnow()
        )
        
        session.add(startup)
        await session.commit()
        startup_id = startup.id
        print(f"✅ Created startup with ID: {startup_id}")
        
        # Test 2: Retrieve the startup
        print("\n2️⃣ Retrieving the startup...")
        result = await session.execute(
            text("SELECT * FROM startups WHERE id = :id"),
            {"id": str(startup_id)}
        )
        row = result.first()
        if row:
            print(f"✅ Retrieved startup: {row.name}")
            print(f"   Sector: {row.sector}")
            print(f"   Stage: {row.stage}")
            print(f"   Team Size: {row.team_size}")
        else:
            print("❌ Failed to retrieve startup")
            return False
        
        # Test 3: Create and save a VC
        print("\n3️⃣ Creating a VC...")
        vc = VCModel(
            id=uuid.uuid4(),
            firm_name=f"TestVC_{datetime.now().strftime('%Y%m%d%H%M%S')}",
            sectors=["B2B SaaS", "AI/ML"],
            stages=["Seed", "Series A"],
            thesis="We invest in B2B SaaS companies with AI capabilities",
            website="https://testvc.example.com",
            created_at=datetime.utcnow()
        )
        
        session.add(vc)
        await session.commit()
        vc_id = vc.id
        print(f"✅ Created VC with ID: {vc_id}")
        
        # Test 4: Create a match
        print("\n4️⃣ Creating a match between startup and VC...")
        match = MatchModel(
            id=uuid.uuid4(),
            startup_id=startup_id,
            vc_id=vc_id,
            score=0.85,
            reasons=["Sector alignment", "Stage match", "Strong team"],
            created_at=datetime.utcnow()
        )
        
        session.add(match)
        await session.commit()
        print(f"✅ Created match with score: {match.score}")
        
        # Test 5: Count all records
        print("\n5️⃣ Counting records in database...")
        
        startup_count = await session.execute(text("SELECT COUNT(*) FROM startups"))
        vc_count = await session.execute(text("SELECT COUNT(*) FROM vcs"))
        match_count = await session.execute(text("SELECT COUNT(*) FROM matches"))
        
        print(f"✅ Total startups: {startup_count.scalar()}")
        print(f"✅ Total VCs: {vc_count.scalar()}")
        print(f"✅ Total matches: {match_count.scalar()}")
        
        # Test 6: Update the startup
        print("\n6️⃣ Updating the startup...")
        await session.execute(
            text("UPDATE startups SET team_size = :size WHERE id = :id"),
            {"size": 15, "id": str(startup_id)}
        )
        await session.commit()
        
        # Verify update
        result = await session.execute(
            text("SELECT team_size FROM startups WHERE id = :id"),
            {"id": str(startup_id)}
        )
        new_size = result.scalar()
        if new_size == 15:
            print(f"✅ Successfully updated team size to {new_size}")
        else:
            print("❌ Update failed")
            return False
        
        print("\n🎉 All data persistence tests passed!")
        print("✅ PostgreSQL is working correctly for data storage and retrieval")
        
        # Update WORKING_FEATURES.md
        update_working_features()
        
        return True


def update_working_features():
    """Update the WORKING_FEATURES.md file with current status."""
    try:
        with open("WORKING_FEATURES.md", "r") as f:
            content = f.read()
        
        # Update database connection status
        content = content.replace("- [ ] PostgreSQL database connection", "- [x] PostgreSQL database connection")
        content = content.replace("- [ ] Save startup to PostgreSQL", "- [x] Save startup to PostgreSQL")
        content = content.replace("- [ ] Retrieve startup by ID", "- [x] Retrieve startup by ID")
        content = content.replace("- [ ] Save VC to PostgreSQL", "- [x] Save VC to PostgreSQL")
        content = content.replace("- [ ] Create match between startup and VC", "- [x] Create match between startup and VC")
        content = content.replace("- [ ] Save match to database", "- [x] Save match to database")
        content = content.replace("- Database connected: ❌", "- Database connected: ✅")
        content = content.replace("- Can save data: ❌", "- Can save data: ✅")
        content = content.replace("- Can retrieve data: ❌", "- Can retrieve data: ✅")
        
        with open("WORKING_FEATURES.md", "w") as f:
            f.write(content)
        
        print("\n✅ Updated WORKING_FEATURES.md with current status")
    except Exception as e:
        print(f"\n⚠️  Could not update WORKING_FEATURES.md: {e}")


if __name__ == "__main__":
    success = asyncio.run(test_persistence())
    if success:
        print("\n✅ Data persistence is verified and working!")
    else:
        print("\n❌ Data persistence test failed")
        sys.exit(1)