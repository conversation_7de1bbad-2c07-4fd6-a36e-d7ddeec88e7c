#!/usr/bin/env python3
"""Initialize the database with tables."""

import sys
from pathlib import Path

# Add project root to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.database.setup import init_db, get_engine
from src.core.config import settings


def main():
    """Initialize database tables."""
    print(f"Initializing database: {settings.database_url}")
    
    try:
        # Create tables
        init_db()
        print("✅ Database tables created successfully!")
        
        # Test connection
        engine = get_engine()
        with engine.connect() as conn:
            result = conn.execute("SELECT 1")
            print("✅ Database connection test successful!")
            
    except Exception as e:
        print(f"❌ Error initializing database: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()