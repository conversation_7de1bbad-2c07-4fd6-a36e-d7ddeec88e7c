#!/usr/bin/env python3
"""Apply vector search migration directly to the database."""

import sys
import os
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment
from dotenv import load_dotenv
load_dotenv('.env.local')


def apply_vector_migration():
    """Apply the vector search migration."""
    print("🚀 Applying vector search migration...")
    print("=" * 60)
    
    # Connect to database
    conn = psycopg2.connect(
        host="localhost",
        database="vc_matching_platform",
        user="thecostcokid",
        password=""
    )
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cur = conn.cursor()
    
    try:
        # Enable pgvector extension if available
        print("📦 Checking for pgvector extension...")
        try:
            cur.execute("CREATE EXTENSION IF NOT EXISTS vector")
            print("  ✅ Extension enabled")
        except Exception as e:
            print("  ⚠️  pgvector not installed, using text arrays for embeddings")
            print("      To install: brew install pgvector")
        
        # Add vector columns to startups table
        print("\n🏢 Adding vector columns to startups table...")
        try:
            cur.execute("""
                ALTER TABLE startups 
                ADD COLUMN IF NOT EXISTS description_vector text,
                ADD COLUMN IF NOT EXISTS combined_vector text,
                ADD COLUMN IF NOT EXISTS embedding_updated_at timestamp with time zone
            """)
            print("  ✅ Startup columns added")
        except Exception as e:
            print(f"  ⚠️  Startup columns might already exist: {str(e)}")
        
        # Add vector columns to vcs table
        print("\n💼 Adding vector columns to vcs table...")
        try:
            cur.execute("""
                ALTER TABLE vcs
                ADD COLUMN IF NOT EXISTS thesis_vector text,
                ADD COLUMN IF NOT EXISTS combined_vector text,
                ADD COLUMN IF NOT EXISTS embedding_updated_at timestamp with time zone
            """)
            print("  ✅ VC columns added")
        except Exception as e:
            print(f"  ⚠️  VC columns might already exist: {str(e)}")
        
        # Create text_to_vector function (simplified for text arrays)
        print("\n🔧 Creating array similarity functions...")
        cur.execute("""
            CREATE OR REPLACE FUNCTION cosine_similarity(a float[], b float[])
            RETURNS float
            LANGUAGE plpgsql
            AS $$
            DECLARE
                dot_product float := 0;
                norm_a float := 0;
                norm_b float := 0;
                i int;
            BEGIN
                IF array_length(a, 1) != array_length(b, 1) THEN
                    RETURN 0;
                END IF;
                
                FOR i IN 1..array_length(a, 1) LOOP
                    dot_product := dot_product + (a[i] * b[i]);
                    norm_a := norm_a + (a[i] * a[i]);
                    norm_b := norm_b + (b[i] * b[i]);
                END LOOP;
                
                IF norm_a = 0 OR norm_b = 0 THEN
                    RETURN 0;
                END IF;
                
                RETURN dot_product / (sqrt(norm_a) * sqrt(norm_b));
            END;
            $$;
        """)
        
        cur.execute("""
            CREATE OR REPLACE FUNCTION text_to_float_array(text_input text)
            RETURNS float[]
            LANGUAGE plpgsql
            AS $$
            BEGIN
                RETURN string_to_array(
                    regexp_replace(text_input, '[\[\]]', '', 'g'),
                    ','
                )::float[];
            EXCEPTION
                WHEN OTHERS THEN
                    RETURN NULL;
            END;
            $$;
        """)
        print("  ✅ Functions created")
        
        # Create hybrid search function
        print("\n🔍 Creating hybrid_startup_search function...")
        cur.execute("""
            CREATE OR REPLACE FUNCTION hybrid_startup_search(
                query_embedding text,
                keyword_query text,
                keyword_weight float DEFAULT 0.4,
                semantic_weight float DEFAULT 0.6,
                min_score float DEFAULT 0.1,
                result_limit int DEFAULT 50
            )
            RETURNS TABLE (
                startup_id uuid,
                startup_name varchar,
                sector varchar,
                stage varchar,
                description text,
                monthly_revenue numeric,
                keyword_score float,
                semantic_score float,
                hybrid_score float
            )
            LANGUAGE plpgsql
            AS $$
            BEGIN
                RETURN QUERY
                WITH keyword_matches AS (
                    SELECT 
                        s.id,
                        COALESCE(
                            ts_rank_cd(
                                to_tsvector('english', COALESCE(s.description, '')),
                                plainto_tsquery('english', keyword_query)
                            ), 0
                        ) as kw_score
                    FROM startups s
                    WHERE keyword_query = '' 
                       OR to_tsvector('english', COALESCE(s.description, '')) @@ plainto_tsquery('english', keyword_query)
                ),
                semantic_matches AS (
                    SELECT 
                        s.id,
                        CASE 
                            WHEN s.combined_vector IS NOT NULL AND query_embedding != '' THEN
                                cosine_similarity(
                                    text_to_float_array(s.combined_vector),
                                    text_to_float_array(query_embedding)
                                )
                            ELSE 0
                        END as sem_score
                    FROM startups s
                    WHERE s.combined_vector IS NOT NULL
                )
                SELECT 
                    s.id as startup_id,
                    s.name as startup_name,
                    s.sector,
                    s.stage,
                    s.description,
                    s.monthly_revenue,
                    COALESCE(k.kw_score, 0) as keyword_score,
                    COALESCE(sm.sem_score, 0) as semantic_score,
                    (keyword_weight * COALESCE(k.kw_score, 0) + 
                     semantic_weight * COALESCE(sm.sem_score, 0)) as hybrid_score
                FROM startups s
                LEFT JOIN keyword_matches k ON s.id = k.id
                LEFT JOIN semantic_matches sm ON s.id = sm.id
                WHERE (keyword_weight * COALESCE(k.kw_score, 0) + 
                       semantic_weight * COALESCE(sm.sem_score, 0)) >= min_score
                ORDER BY hybrid_score DESC, s.monthly_revenue DESC
                LIMIT result_limit;
            END;
            $$;
        """)
        print("  ✅ Search function created")
        
        # Verify the migration
        print("\n🔍 Verifying migration...")
        
        # Check if columns exist
        cur.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'startups' 
            AND column_name IN ('description_vector', 'combined_vector', 'embedding_updated_at')
        """)
        startup_cols = [row[0] for row in cur.fetchall()]
        
        cur.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'vcs' 
            AND column_name IN ('thesis_vector', 'combined_vector', 'embedding_updated_at')
        """)
        vc_cols = [row[0] for row in cur.fetchall()]
        
        print(f"\n✅ Migration completed successfully!")
        print(f"  Startup columns: {', '.join(startup_cols)}")
        print(f"  VC columns: {', '.join(vc_cols)}")
        
        # Update alembic version table to mark this migration as complete
        print("\n📝 Updating alembic version...")
        cur.execute("""
            CREATE TABLE IF NOT EXISTS alembic_version (
                version_num varchar(32) NOT NULL,
                CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num)
            )
        """)
        
        cur.execute("""
            INSERT INTO alembic_version (version_num) 
            VALUES ('006_add_vector_search')
            ON CONFLICT (version_num) DO NOTHING
        """)
        print("  ✅ Alembic version updated")
        
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")
        raise
    finally:
        cur.close()
        conn.close()


if __name__ == "__main__":
    apply_vector_migration()