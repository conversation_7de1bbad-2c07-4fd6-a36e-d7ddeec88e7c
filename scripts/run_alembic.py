#!/usr/bin/env python3
"""Run Alembic migrations with proper environment."""

import os
import sys
from dotenv import load_dotenv

# Load local environment
load_dotenv('.env.local')

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import subprocess

if __name__ == "__main__":
    # Run alembic upgrade
    subprocess.run(["alembic", "upgrade", "head"])