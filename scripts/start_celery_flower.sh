#!/bin/bash
# Start Flower (Celery monitoring tool) for VC Matching Platform

echo "Starting Flower (Celery monitoring)..."

# Set Python path
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Load environment variables if .env exists
if [ -f .env ]; then
    echo "Loading environment variables from .env"
    export $(cat .env | grep -v '^#' | xargs)
fi

# Flower port
FLOWER_PORT=${FLOWER_PORT:-5555}

# Basic auth for Flower (optional)
FLOWER_BASIC_AUTH=${FLOWER_BASIC_AUTH:-}

echo "Configuration:"
echo "  Port: $FLOWER_PORT"
echo "  Redis URL: ${REDIS_URL:-redis://localhost:6379/0}"

# Build Flower command
FLOWER_CMD="celery -A src.workers.celery_app flower --port=$FLOWER_PORT"

# Add basic auth if configured
if [ ! -z "$FLOWER_BASIC_AUTH" ]; then
    echo "  Basic Auth: Enabled"
    FLOWER_CMD="$FLOWER_CMD --basic_auth=$FLOWER_BASIC_AUTH"
else
    echo "  Basic Auth: Disabled"
fi

# Start Flower
echo "Flower will be available at http://localhost:$FLOWER_PORT"
$FLOWER_CMD