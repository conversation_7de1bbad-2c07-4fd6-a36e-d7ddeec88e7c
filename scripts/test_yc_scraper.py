#!/usr/bin/env python3
"""Test script to verify YC scraper is working with real data."""

import asyncio
import sys
import os

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.scrapers.yc_scraper import YCCompanyScraper, discover_new_yc_companies


async def test_yc_scraper():
    """Test the YC scraper functionality."""
    print("Testing YC Company Scraper...")
    print("=" * 50)
    
    scraper = YCCompanyScraper()
    
    # Test 1: Fetch a small sample
    print("\n1. Fetching 5 YC companies...")
    companies = await scraper.scrape_companies(limit=5)
    
    if companies:
        print(f"✅ Successfully fetched {len(companies)} companies")
        
        # Display first company details
        if companies[0]:
            company = companies[0]
            print(f"\nExample company:")
            print(f"  Name: {company.get('name')}")
            print(f"  Batch: {company.get('batch')}")
            print(f"  Sector: {company.get('sector')}")
            print(f"  Stage: {company.get('stage')}")
            print(f"  Description: {company.get('description', '')[:100]}...")
            print(f"  Website: {company.get('website')}")
            print(f"  Source: {company.get('source')}")
    else:
        print("❌ No companies fetched - scraper may be returning mock data")
    
    # Test 2: Discover recent companies
    print("\n2. Discovering recent YC companies...")
    recent_companies = await discover_new_yc_companies()
    
    if recent_companies:
        print(f"✅ Found {len(recent_companies)} recent companies")
        
        # Show batch distribution
        batches = {}
        for company in recent_companies:
            batch = company.get('batch', 'Unknown')
            batches[batch] = batches.get(batch, 0) + 1
        
        print("\nBatch distribution:")
        for batch, count in sorted(batches.items(), reverse=True):
            print(f"  {batch}: {count} companies")
    else:
        print("❌ No recent companies found")
    
    # Test 3: Check if we're getting real or mock data
    print("\n3. Verifying data source...")
    if companies and companies[0].get('source') == 'yc_api':
        print("✅ Data source is YC API (real data)")
    elif companies and companies[0].get('source') == 'yc_fallback':
        print("⚠️  Data source is fallback (mock data)")
    else:
        print("❓ Unknown data source")
    
    print("\n" + "=" * 50)
    print("Test complete!")


if __name__ == "__main__":
    asyncio.run(test_yc_scraper())