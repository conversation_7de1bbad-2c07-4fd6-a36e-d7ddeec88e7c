#!/usr/bin/env python3
"""Audit authentication on all API endpoints."""

import os
import re
from pathlib import Path

# Add parent directory to path
import sys
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def audit_endpoints(directory: str):
    """Audit all API endpoints for authentication."""
    endpoint_files = Path(directory).glob("*.py")
    
    results = {
        "protected": [],
        "optional": [],
        "unprotected": []
    }
    
    for file_path in endpoint_files:
        if file_path.name in ["__init__.py", "deps.py", "error_handlers.py"]:
            continue
            
        with open(file_path, 'r') as f:
            content = f.read()
            
        # Find all router decorators and their functions
        router_pattern = r'@router\.(get|post|put|delete|patch)\s*\([^)]*\).*?\n.*?async def (\w+)\s*\([^)]*\):'
        matches = re.finditer(router_pattern, content, re.DOTALL)
        
        for match in matches:
            method = match.group(1).upper()
            func_name = match.group(2)
            
            # Get the function definition
            func_start = match.start()
            func_end = content.find('\n@router', func_start + 1)
            if func_end == -1:
                func_end = len(content)
            func_content = content[func_start:func_end]
            
            # Check authentication
            auth_status = "unprotected"
            if "get_current_user" in func_content and "get_current_user_optional" not in func_content:
                auth_status = "protected"
            elif "get_current_user_optional" in func_content:
                auth_status = "optional"
            elif "require_api_key" in func_content:
                auth_status = "protected"
            
            # Extract path
            path_match = re.search(r'@router\.' + method.lower() + r'\s*\(\s*["\']([^"\']+)', func_content)
            path = path_match.group(1) if path_match else "/"
            
            endpoint_info = {
                "file": file_path.name,
                "method": method,
                "path": f"/api/v1/{file_path.stem}{path}",
                "function": func_name,
                "auth_status": auth_status
            }
            
            results[auth_status].append(endpoint_info)
    
    return results


def main():
    """Main function."""
    print("🔐 API Authentication Audit")
    print("=" * 60)
    
    results = audit_endpoints("src/api/v1/endpoints")
    
    # Show unprotected endpoints (high priority)
    print("\n❌ UNPROTECTED ENDPOINTS (Need Authentication):")
    if results["unprotected"]:
        for ep in sorted(results["unprotected"], key=lambda x: x["path"]):
            print(f"  {ep['method']:6} {ep['path']:50} ({ep['file']}:{ep['function']})")
    else:
        print("  None found ✅")
    
    # Show optional auth endpoints
    print(f"\n⚠️  OPTIONAL AUTH ENDPOINTS ({len(results['optional'])}):")
    for ep in sorted(results["optional"], key=lambda x: x["path"])[:10]:
        print(f"  {ep['method']:6} {ep['path']:50} ({ep['file']}:{ep['function']})")
    if len(results["optional"]) > 10:
        print(f"  ... and {len(results['optional']) - 10} more")
    
    # Show protected endpoints
    print(f"\n✅ PROTECTED ENDPOINTS ({len(results['protected'])}):")
    for ep in sorted(results["protected"], key=lambda x: x["path"])[:10]:
        print(f"  {ep['method']:6} {ep['path']:50} ({ep['file']}:{ep['function']})")
    if len(results["protected"]) > 10:
        print(f"  ... and {len(results['protected']) - 10} more")
    
    # Summary
    total = len(results["protected"]) + len(results["optional"]) + len(results["unprotected"])
    print(f"\n📊 Summary:")
    print(f"  Total endpoints: {total}")
    print(f"  Protected: {len(results['protected'])} ({len(results['protected'])/total*100:.1f}%)")
    print(f"  Optional: {len(results['optional'])} ({len(results['optional'])/total*100:.1f}%)")
    print(f"  Unprotected: {len(results['unprotected'])} ({len(results['unprotected'])/total*100:.1f}%)")
    
    # Recommendations
    print(f"\n💡 Recommendations:")
    print(f"  1. Add 'get_current_user' dependency to {len(results['unprotected'])} unprotected endpoints")
    print(f"  2. Review {len(results['optional'])} optional auth endpoints - should they require auth?")
    print(f"  3. Consider which endpoints should remain public (health checks, etc.)")


if __name__ == "__main__":
    main()