#!/usr/bin/env python3
"""
TDD Compliance Checker
Ensures test-driven development practices are followed
"""
import os
import sys
import subprocess
import json
from pathlib import Path
from typing import List, Dict, Tuple
from datetime import datetime

class TDDComplianceChecker:
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.src_dir = project_root / "src"
        self.test_dir = project_root / "tests"
        self.violations = []
        
    def check_test_exists_for_module(self, module_path: Path) -> bool:
        """Check if a test file exists for a given module"""
        if "__init__.py" in str(module_path):
            return True
            
        # Convert src path to expected test path
        relative_path = module_path.relative_to(self.src_dir)
        test_path = self.test_dir / relative_path
        test_path = test_path.with_name(f"test_{test_path.name}")
        
        return test_path.exists()
    
    def check_test_written_first(self, module_path: Path) -> bool:
        """Check if test was created before implementation using git history"""
        try:
            # Get creation date of implementation file
            impl_result = subprocess.run(
                ["git", "log", "--follow", "--format=%at", "--", str(module_path)],
                capture_output=True, text=True, cwd=self.project_root
            )
            
            if not impl_result.stdout.strip():
                return True  # New file, not yet committed
                
            impl_timestamps = [int(ts) for ts in impl_result.stdout.strip().split('\n')]
            impl_created = min(impl_timestamps)
            
            # Get creation date of test file
            relative_path = module_path.relative_to(self.src_dir)
            test_path = self.test_dir / relative_path
            test_path = test_path.with_name(f"test_{test_path.name}")
            
            if not test_path.exists():
                return False
                
            test_result = subprocess.run(
                ["git", "log", "--follow", "--format=%at", "--", str(test_path)],
                capture_output=True, text=True, cwd=self.project_root
            )
            
            if not test_result.stdout.strip():
                return False  # Test exists but not committed
                
            test_timestamps = [int(ts) for ts in test_result.stdout.strip().split('\n')]
            test_created = min(test_timestamps)
            
            return test_created <= impl_created
            
        except Exception:
            return True  # Give benefit of doubt if git history unavailable
    
    def get_coverage_for_module(self, module_path: Path) -> float:
        """Get coverage percentage for a specific module"""
        try:
            # Run coverage for specific module
            relative_path = module_path.relative_to(self.project_root)
            module_name = str(relative_path).replace('/', '.').replace('.py', '')
            
            result = subprocess.run(
                ["pytest", f"--cov={module_name}", "--cov-report=json", "--quiet"],
                capture_output=True, cwd=self.project_root
            )
            
            if result.returncode == 0 and Path("coverage.json").exists():
                with open("coverage.json", "r") as f:
                    data = json.load(f)
                    files = data.get("files", {})
                    for file_path, file_data in files.items():
                        if str(relative_path) in file_path:
                            return file_data["summary"]["percent_covered"]
            return 0.0
        except Exception:
            return 0.0
    
    def check_all_modules(self) -> Tuple[bool, List[Dict]]:
        """Check all Python modules for TDD compliance"""
        compliant = True
        issues = []
        
        for module_path in self.src_dir.rglob("*.py"):
            if "__pycache__" in str(module_path):
                continue
                
            module_name = str(module_path.relative_to(self.project_root))
            
            # Check 1: Test exists
            has_test = self.check_test_exists_for_module(module_path)
            if not has_test:
                compliant = False
                issues.append({
                    "module": module_name,
                    "issue": "No test file found",
                    "severity": "high"
                })
            
            # Check 2: Test written first (only for files with history)
            if has_test:
                test_first = self.check_test_written_first(module_path)
                if not test_first:
                    compliant = False
                    issues.append({
                        "module": module_name,
                        "issue": "Test written after implementation",
                        "severity": "medium"
                    })
            
            # Check 3: Coverage threshold
            coverage = self.get_coverage_for_module(module_path)
            if coverage < 80:
                compliant = False
                issues.append({
                    "module": module_name,
                    "issue": f"Coverage {coverage:.1f}% below 80% threshold",
                    "severity": "medium" if coverage > 60 else "high"
                })
        
        return compliant, issues
    
    def generate_report(self, issues: List[Dict]) -> str:
        """Generate a formatted compliance report"""
        report = ["TDD Compliance Report", "=" * 50, ""]
        
        if not issues:
            report.append("✅ All modules are TDD compliant!")
        else:
            report.append(f"❌ Found {len(issues)} TDD violations:")
            report.append("")
            
            # Group by severity
            high_severity = [i for i in issues if i["severity"] == "high"]
            medium_severity = [i for i in issues if i["severity"] == "medium"]
            
            if high_severity:
                report.append("HIGH SEVERITY:")
                for issue in high_severity:
                    report.append(f"  - {issue['module']}: {issue['issue']}")
                report.append("")
            
            if medium_severity:
                report.append("MEDIUM SEVERITY:")
                for issue in medium_severity:
                    report.append(f"  - {issue['module']}: {issue['issue']}")
                report.append("")
        
        # Overall statistics
        total_modules = sum(1 for _ in self.src_dir.rglob("*.py") 
                          if "__pycache__" not in str(_))
        compliant_modules = total_modules - len(set(i["module"] for i in issues))
        
        report.extend([
            "Statistics:",
            f"  Total modules: {total_modules}",
            f"  Compliant modules: {compliant_modules}",
            f"  Compliance rate: {(compliant_modules/total_modules*100):.1f}%"
        ])
        
        return "\n".join(report)
    
    def create_git_hook(self):
        """Create pre-commit hook for TDD compliance"""
        hook_path = self.project_root / ".git" / "hooks" / "pre-commit"
        hook_content = '''#!/usr/bin/env python3
import subprocess
import sys

# Run TDD compliance check
result = subprocess.run([sys.executable, "scripts/check_tdd_compliance.py", "--quiet"])
sys.exit(result.returncode)
'''
        hook_path.write_text(hook_content)
        hook_path.chmod(0o755)
        print(f"✅ Created git pre-commit hook at {hook_path}")

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Check TDD compliance")
    parser.add_argument("--quiet", action="store_true", help="Only show violations")
    parser.add_argument("--install-hook", action="store_true", 
                       help="Install git pre-commit hook")
    args = parser.parse_args()
    
    project_root = Path(__file__).parent.parent
    checker = TDDComplianceChecker(project_root)
    
    if args.install_hook:
        checker.create_git_hook()
        return
    
    compliant, issues = checker.check_all_modules()
    
    if not args.quiet or issues:
        report = checker.generate_report(issues)
        print(report)
    
    sys.exit(0 if compliant else 1)

if __name__ == "__main__":
    main()