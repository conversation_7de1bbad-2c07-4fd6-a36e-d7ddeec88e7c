#!/usr/bin/env python3
"""
TDD Checkpoint Tracker
Tracks and enforces TDD checkpoints for features
"""
import json
import os
import subprocess
from dataclasses import dataclass, asdict
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, List

@dataclass
class TDDCheckpoint:
    """Track TDD compliance for a feature"""
    feature_name: str
    test_written_first: bool = False
    test_initially_failed: bool = False
    minimal_code_written: bool = False
    test_now_passes: bool = False
    refactored: bool = False
    coverage_above_80: bool = False
    all_tests_green: bool = False
    
    # Metadata
    test_files: List[str] = None
    implementation_files: List[str] = None
    initial_coverage: float = 0.0
    final_coverage: float = 0.0
    started_at: str = None
    completed_at: str = None
    
    def __post_init__(self):
        if self.test_files is None:
            self.test_files = []
        if self.implementation_files is None:
            self.implementation_files = []
        if self.started_at is None:
            self.started_at = datetime.now().isoformat()
    
    @property
    def is_complete(self) -> bool:
        """Check if all checkpoints are satisfied"""
        return all([
            self.test_written_first,
            self.test_initially_failed,
            self.minimal_code_written,
            self.test_now_passes,
            self.refactored,
            self.coverage_above_80,
            self.all_tests_green
        ])
    
    def save(self, checkpoint_dir: Path):
        """Save checkpoint to JSON file"""
        checkpoint_dir.mkdir(exist_ok=True)
        file_path = checkpoint_dir / f"{self.feature_name}.json"
        with open(file_path, 'w') as f:
            json.dump(asdict(self), f, indent=2)
    
    @classmethod
    def load(cls, feature_name: str, checkpoint_dir: Path) -> Optional['TDDCheckpoint']:
        """Load checkpoint from JSON file"""
        file_path = checkpoint_dir / f"{feature_name}.json"
        if file_path.exists():
            with open(file_path, 'r') as f:
                data = json.load(f)
                return cls(**data)
        return None

class TDDTracker:
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.checkpoint_dir = project_root / "tests" / "checkpoints"
        self.checkpoint_dir.mkdir(exist_ok=True)
    
    def start_feature(self, feature_name: str) -> TDDCheckpoint:
        """Start tracking a new feature"""
        checkpoint = TDDCheckpoint(feature_name=feature_name)
        checkpoint.initial_coverage = self.get_current_coverage()
        checkpoint.save(self.checkpoint_dir)
        return checkpoint
    
    def verify_test_written_first(self, checkpoint: TDDCheckpoint, test_file: str) -> bool:
        """Verify that test file exists and was written first"""
        test_path = Path(test_file)
        if not test_path.exists():
            return False
        
        # Check if any implementation files exist yet
        impl_files = list(self.project_root.glob(f"src/**/*{checkpoint.feature_name}*.py"))
        if impl_files:
            # Implementation already exists
            return False
        
        checkpoint.test_files.append(str(test_path))
        checkpoint.test_written_first = True
        checkpoint.save(self.checkpoint_dir)
        return True
    
    def verify_test_fails(self, checkpoint: TDDCheckpoint) -> bool:
        """Run tests and verify they fail"""
        result = subprocess.run(
            ["pytest"] + checkpoint.test_files + ["-v"],
            capture_output=True,
            cwd=self.project_root
        )
        
        if result.returncode != 0:  # Tests failed as expected
            checkpoint.test_initially_failed = True
            checkpoint.save(self.checkpoint_dir)
            return True
        return False
    
    def track_implementation(self, checkpoint: TDDCheckpoint, impl_file: str):
        """Track implementation file"""
        checkpoint.implementation_files.append(impl_file)
        checkpoint.minimal_code_written = True
        checkpoint.save(self.checkpoint_dir)
    
    def verify_tests_pass(self, checkpoint: TDDCheckpoint) -> bool:
        """Verify that tests now pass"""
        result = subprocess.run(
            ["pytest"] + checkpoint.test_files + ["-v"],
            capture_output=True,
            cwd=self.project_root
        )
        
        if result.returncode == 0:  # Tests pass
            checkpoint.test_now_passes = True
            checkpoint.save(self.checkpoint_dir)
            return True
        return False
    
    def verify_all_tests_green(self, checkpoint: TDDCheckpoint) -> bool:
        """Verify all project tests pass"""
        result = subprocess.run(
            ["pytest", "tests/", "-v"],
            capture_output=True,
            cwd=self.project_root
        )
        
        if result.returncode == 0:
            checkpoint.all_tests_green = True
            checkpoint.save(self.checkpoint_dir)
            return True
        return False
    
    def get_current_coverage(self) -> float:
        """Get current test coverage percentage"""
        try:
            result = subprocess.run(
                ["pytest", "--cov=src", "--cov-report=json", "--quiet"],
                capture_output=True,
                cwd=self.project_root
            )
            
            coverage_file = self.project_root / "coverage.json"
            if coverage_file.exists():
                with open(coverage_file, 'r') as f:
                    data = json.load(f)
                    return data["totals"]["percent_covered"]
        except Exception:
            pass
        return 0.0
    
    def verify_coverage(self, checkpoint: TDDCheckpoint) -> bool:
        """Verify coverage is above threshold"""
        current_coverage = self.get_current_coverage()
        checkpoint.final_coverage = current_coverage
        
        if current_coverage >= 80:
            checkpoint.coverage_above_80 = True
            checkpoint.save(self.checkpoint_dir)
            return True
        return False
    
    def mark_refactored(self, checkpoint: TDDCheckpoint):
        """Mark that refactoring is complete"""
        checkpoint.refactored = True
        checkpoint.save(self.checkpoint_dir)
    
    def complete_feature(self, checkpoint: TDDCheckpoint):
        """Mark feature as complete"""
        checkpoint.completed_at = datetime.now().isoformat()
        checkpoint.save(self.checkpoint_dir)
    
    def generate_report(self, checkpoint: TDDCheckpoint) -> str:
        """Generate checkpoint report"""
        report = [
            f"TDD Checkpoint Report: {checkpoint.feature_name}",
            "=" * 50,
            ""
        ]
        
        checkpoints = [
            ("Test written first", checkpoint.test_written_first),
            ("Test initially failed", checkpoint.test_initially_failed),
            ("Minimal code written", checkpoint.minimal_code_written),
            ("Test now passes", checkpoint.test_now_passes),
            ("Refactored", checkpoint.refactored),
            ("Coverage above 80%", checkpoint.coverage_above_80),
            ("All tests green", checkpoint.all_tests_green)
        ]
        
        for name, status in checkpoints:
            symbol = "✅" if status else "❌"
            report.append(f"{symbol} {name}")
        
        report.extend([
            "",
            f"Test files: {len(checkpoint.test_files)}",
            f"Implementation files: {len(checkpoint.implementation_files)}",
            f"Coverage: {checkpoint.initial_coverage:.1f}% → {checkpoint.final_coverage:.1f}%",
            f"Started: {checkpoint.started_at}",
            f"Completed: {checkpoint.completed_at or 'In Progress'}"
        ])
        
        if not checkpoint.is_complete:
            report.extend([
                "",
                "⚠️  Feature not TDD compliant yet!"
            ])
        
        return "\n".join(report)

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Track TDD checkpoints")
    parser.add_argument("--feature", required=True, help="Feature name")
    parser.add_argument("--start", action="store_true", help="Start new feature")
    parser.add_argument("--test-file", help="Mark test file written")
    parser.add_argument("--verify-fail", action="store_true", help="Verify tests fail")
    parser.add_argument("--impl-file", help="Track implementation file")
    parser.add_argument("--verify-pass", action="store_true", help="Verify tests pass")
    parser.add_argument("--refactored", action="store_true", help="Mark as refactored")
    parser.add_argument("--verify-coverage", action="store_true", help="Verify coverage")
    parser.add_argument("--verify-all", action="store_true", help="Verify all tests")
    parser.add_argument("--complete", action="store_true", help="Complete feature")
    parser.add_argument("--report", action="store_true", help="Show report")
    
    args = parser.parse_args()
    
    project_root = Path(__file__).parent.parent
    tracker = TDDTracker(project_root)
    
    # Load or create checkpoint
    checkpoint = TDDCheckpoint.load(args.feature, tracker.checkpoint_dir)
    if checkpoint is None:
        if not args.start:
            print(f"❌ No checkpoint found for feature '{args.feature}'")
            print("   Use --start to begin tracking")
            return
        checkpoint = tracker.start_feature(args.feature)
        print(f"✅ Started tracking feature: {args.feature}")
    
    # Process commands
    if args.test_file:
        if tracker.verify_test_written_first(checkpoint, args.test_file):
            print("✅ Test written first")
        else:
            print("❌ Test not written first or implementation already exists")
    
    if args.verify_fail:
        if tracker.verify_test_fails(checkpoint):
            print("✅ Test initially failed")
        else:
            print("❌ Test did not fail - invalid test")
    
    if args.impl_file:
        tracker.track_implementation(checkpoint, args.impl_file)
        print(f"✅ Tracked implementation: {args.impl_file}")
    
    if args.verify_pass:
        if tracker.verify_tests_pass(checkpoint):
            print("✅ Tests now pass")
        else:
            print("❌ Tests still failing")
    
    if args.refactored:
        tracker.mark_refactored(checkpoint)
        print("✅ Marked as refactored")
    
    if args.verify_coverage:
        if tracker.verify_coverage(checkpoint):
            print(f"✅ Coverage {checkpoint.final_coverage:.1f}% above threshold")
        else:
            print(f"❌ Coverage {checkpoint.final_coverage:.1f}% below 80% threshold")
    
    if args.verify_all:
        if tracker.verify_all_tests_green(checkpoint):
            print("✅ All tests green")
        else:
            print("❌ Some tests failing")
    
    if args.complete:
        tracker.complete_feature(checkpoint)
        print(f"✅ Feature '{args.feature}' marked complete")
    
    if args.report or True:  # Always show report
        print("\n" + tracker.generate_report(checkpoint))

if __name__ == "__main__":
    main()