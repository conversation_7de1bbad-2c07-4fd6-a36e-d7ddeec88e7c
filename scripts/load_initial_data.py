#!/usr/bin/env python3
"""Script to load initial YC company data into the database."""

import asyncio
import sys
import os
from datetime import datetime
import argparse
from dotenv import load_dotenv

# Load local environment
load_dotenv('.env.local')

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from src.database.setup import get_session_factory
from src.core.services.scraper_service import ScraperService
from src.database.repositories.startup_repository import PostgresStartupRepository
from src.scrapers.yc_scraper import YCCompanyScraper


async def load_yc_data(limit: int = 100, generate_embeddings: bool = False):
    """Load YC company data into the database."""
    print(f"Loading YC company data (limit: {limit})...")
    print("=" * 60)
    
    # Initialize database session
    SessionLocal = get_session_factory()
    db = SessionLocal()
    
    try:
        # Initialize repositories and services
        startup_repo = PostgresStartupRepository(db)
        scraper_service = ScraperService(startup_repo)
        
        # Start scraping
        print(f"\n🔍 Scraping {limit} YC companies...")
        results = await scraper_service.scrape_yc_companies(limit=limit)
        
        print(f"\n📊 Results:")
        print(f"  Total scraped: {results['total_scraped']}")
        print(f"  New companies: {results['new_companies']} ✅")
        print(f"  Updated companies: {results['updated_companies']} 🔄")
        print(f"  Errors: {results['errors']} ❌")
        
        if results['new_companies'] > 0:
            print(f"\n✨ New companies added:")
            for company in results['companies'][:10]:  # Show first 10
                if company['action'] == 'created':
                    print(f"  - {company['name']} (ID: {company['id']})")
            
            if results['new_companies'] > 10:
                print(f"  ... and {results['new_companies'] - 10} more")
        
        # Generate embeddings if requested
        if generate_embeddings and results['new_companies'] > 0:
            print(f"\n🧠 Generating embeddings for new companies...")
            # This would trigger the embedding generation task
            # For now, we'll add this in a follow-up
            print("  ⚠️  Embedding generation not yet implemented")
        
        print(f"\n✅ Data loading complete!")
        
        # Show database stats
        # Note: count() method doesn't exist, we'll use a different approach
        all_startups = await startup_repo.find_all()
        total_startups = len(all_startups)
        print(f"\n📈 Database stats:")
        print(f"  Total startups in database: {total_startups}")
        
    except Exception as e:
        print(f"\n❌ Error loading data: {str(e)}")
        raise
    finally:
        db.close()


async def verify_data_quality():
    """Verify the quality of loaded data."""
    print("\n🔍 Verifying data quality...")
    print("=" * 60)
    
    # Initialize database session
    SessionLocal = get_session_factory()
    db = SessionLocal()
    
    try:
        startup_repo = PostgresStartupRepository(db)
        
        # Get all startups
        all_startups = await startup_repo.find_all()
        
        # Analyze data quality
        quality_issues = {
            'missing_description': 0,
            'missing_website': 0,
            'missing_metadata': 0,
            'unknown_sector': 0,
            'unknown_stage': 0
        }
        
        for startup in all_startups:
            if not startup.description or len(startup.description) < 10:
                quality_issues['missing_description'] += 1
            if not startup.website:
                quality_issues['missing_website'] += 1
            if not hasattr(startup, 'metadata') or not startup.metadata:
                quality_issues['missing_metadata'] += 1
            if startup.sector == "Other" or not startup.sector:
                quality_issues['unknown_sector'] += 1
            if not startup.stage:
                quality_issues['unknown_stage'] += 1
        
        print(f"\n📊 Data Quality Report:")
        print(f"  Total companies: {len(all_startups)}")
        print(f"\n  Issues found:")
        for issue, count in quality_issues.items():
            percentage = (count / len(all_startups) * 100) if all_startups else 0
            status = "✅" if percentage < 10 else "⚠️" if percentage < 30 else "❌"
            print(f"  {status} {issue.replace('_', ' ').title()}: {count} ({percentage:.1f}%)")
        
        # Show sector distribution
        sectors = {}
        for startup in all_startups:
            sector = startup.sector
            sectors[sector] = sectors.get(sector, 0) + 1
        
        print(f"\n📊 Sector Distribution:")
        for sector, count in sorted(sectors.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"  {sector}: {count}")
        
    except Exception as e:
        print(f"\n❌ Error verifying data: {str(e)}")
        raise
    finally:
        db.close()


async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Load initial YC company data")
    parser.add_argument(
        "--limit",
        type=int,
        default=100,
        help="Number of companies to load (default: 100)"
    )
    parser.add_argument(
        "--generate-embeddings",
        action="store_true",
        help="Generate embeddings for loaded companies"
    )
    parser.add_argument(
        "--verify-only",
        action="store_true",
        help="Only verify existing data quality"
    )
    
    args = parser.parse_args()
    
    if args.verify_only:
        await verify_data_quality()
    else:
        await load_yc_data(
            limit=args.limit,
            generate_embeddings=args.generate_embeddings
        )
        await verify_data_quality()


if __name__ == "__main__":
    asyncio.run(main())