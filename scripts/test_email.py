#!/usr/bin/env python3
"""Test email sending functionality."""

import sys
import os
import asyncio
from uuid import uuid4

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment
from dotenv import load_dotenv
load_dotenv('.env.local')

from src.core.services.email_service import get_email_service


async def test_email_service():
    """Test the email service with different templates."""
    print("📧 Testing Email Service")
    print("=" * 60)
    
    email_service = get_email_service()
    
    # Test recipient email (change this to your email)
    test_email = os.getenv('TEST_EMAIL', '<EMAIL>')
    
    print(f"\n🔍 Email Configuration:")
    print(f"  From: {email_service.from_email}")
    print(f"  SendGrid configured: {email_service.use_sendgrid}")
    print(f"  Environment: {email_service.environment}")
    print(f"  Test recipient: {test_email}")
    
    # Test 1: Match notification
    print(f"\n1️⃣ Testing match notification email...")
    result = email_service.send_email(
        to_email=test_email,
        template_name='match_notification',
        data={
            'recipient_name': 'Test User',
            'match_name': 'Awesome Startup Inc.',
            'match_id': str(uuid4()),
            'score': 85,
            'reasons': 'Strong technical fit, Same industry focus, Geographic match'
        }
    )
    print(f"  Result: {result}")
    
    # Test 2: Weekly digest
    print(f"\n2️⃣ Testing weekly digest email...")
    result = email_service.send_email(
        to_email=test_email,
        template_name='weekly_digest',
        data={
            'recipient_name': 'Test User',
            'new_matches': 5,
            'updated_profiles': 12,
            'top_match': '92%'
        }
    )
    print(f"  Result: {result}")
    
    # Test 3: Analysis complete
    print(f"\n3️⃣ Testing analysis complete email...")
    result = email_service.send_email(
        to_email=test_email,
        template_name='analysis_complete',
        data={
            'recipient_name': 'Test User',
            'entity_type': 'startup',
            'entity_id': str(uuid4()),
            'insights': [
                'Strong AI/ML capabilities detected',
                'Market size estimated at $2.5B',
                'Key competitors identified: CompA, CompB',
                'Recommended focus on B2B segment',
                'High growth potential in healthcare vertical'
            ]
        }
    )
    print(f"  Result: {result}")
    
    # Test 4: Bulk email
    print(f"\n4️⃣ Testing bulk email...")
    bulk_result = email_service.send_bulk_emails(
        template_name='weekly_digest',
        recipients=[
            {
                'email': test_email,
                'data': {
                    'recipient_name': 'User 1',
                    'new_matches': 3,
                    'updated_profiles': 8,
                    'top_match': '88%'
                }
            },
            {
                'email': f'second_{test_email}',
                'data': {
                    'recipient_name': 'User 2',
                    'new_matches': 7,
                    'updated_profiles': 15,
                    'top_match': '94%'
                }
            }
        ]
    )
    print(f"  Result: {bulk_result}")
    
    print("\n✅ Email testing complete!")
    
    if not email_service.use_sendgrid:
        print("\n⚠️  Note: SendGrid is not configured. To send real emails:")
        print("  1. Sign up for SendGrid: https://sendgrid.com")
        print("  2. Create an API key")
        print("  3. Add to .env.local: SENDGRID_API_KEY=your-api-key")
        print("  4. Update EMAIL_FROM and EMAIL_FROM_NAME")


if __name__ == "__main__":
    asyncio.run(test_email_service())