#!/usr/bin/env python3
"""Generate embeddings for all startups and VCs in the database."""

import asyncio
import sys
import os
from datetime import datetime
import psycopg2
from psycopg2.extras import <PERSON><PERSON>
import openai
from typing import List
import numpy as np

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment
from dotenv import load_dotenv
load_dotenv('.env.local')

# Initialize OpenAI
openai.api_key = os.getenv('OPENAI_API_KEY')


async def get_embedding(text: str, model: str = "text-embedding-3-small") -> List[float]:
    """Get embedding for a text using OpenAI."""
    try:
        # Clean the text
        text = text.replace("\n", " ").strip()
        if not text:
            return [0.0] * 1536  # Return zero vector for empty text
        
        # Get embedding from OpenAI
        response = await asyncio.to_thread(
            openai.embeddings.create,
            input=[text],
            model=model
        )
        
        return response.data[0].embedding
    except Exception as e:
        print(f"❌ Error getting embedding: {str(e)}")
        return [0.0] * 1536  # Return zero vector on error


async def generate_startup_embeddings():
    """Generate embeddings for all startups."""
    print("\n🚀 Generating embeddings for startups...")
    print("=" * 60)
    
    # Connect to database
    conn = psycopg2.connect(
        host="localhost",
        database="vc_matching_platform",
        user="thecostcokid",
        password=""
    )
    cur = conn.cursor()
    
    try:
        # Get all startups without embeddings
        cur.execute("""
            SELECT id, name, description, sector, stage
            FROM startups
            WHERE description_vector IS NULL 
               OR description_vector = '[]'
               OR LENGTH(description_vector::text) < 10
        """)
        
        startups = cur.fetchall()
        print(f"Found {len(startups)} startups without embeddings")
        
        # Generate embeddings
        updated_count = 0
        for startup in startups:
            startup_id, name, description, sector, stage = startup
            
            # Create combined text for embedding
            text_to_embed = f"{name}. {sector} company at {stage} stage. {description or ''}"
            
            print(f"  Generating embedding for: {name}...", end='')
            embedding = await get_embedding(text_to_embed)
            
            # Store as text array in PostgreSQL
            embedding_str = '[' + ','.join(map(str, embedding)) + ']'
            
            # Update database
            cur.execute("""
                UPDATE startups 
                SET description_vector = %s,
                    updated_at = %s
                WHERE id = %s
            """, (embedding_str, datetime.utcnow(), startup_id))
            
            updated_count += 1
            print(" ✅")
            
            # Commit every 10 updates
            if updated_count % 10 == 0:
                conn.commit()
                print(f"  Progress: {updated_count}/{len(startups)}")
        
        # Final commit
        conn.commit()
        print(f"\n✅ Generated embeddings for {updated_count} startups")
        
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")
        conn.rollback()
        raise
    finally:
        cur.close()
        conn.close()


async def generate_vc_embeddings():
    """Generate embeddings for all VCs."""
    print("\n🏢 Generating embeddings for VCs...")
    print("=" * 60)
    
    # Connect to database
    conn = psycopg2.connect(
        host="localhost",
        database="vc_matching_platform",
        user="thecostcokid",
        password=""
    )
    cur = conn.cursor()
    
    try:
        # Get all VCs without embeddings
        cur.execute("""
            SELECT id, firm_name, thesis, sectors, stages
            FROM vcs
            WHERE thesis_vector IS NULL 
               OR thesis_vector = '[]'
               OR LENGTH(thesis_vector::text) < 10
        """)
        
        vcs = cur.fetchall()
        print(f"Found {len(vcs)} VCs without embeddings")
        
        # Generate embeddings
        updated_count = 0
        for vc in vcs:
            vc_id, firm_name, thesis, sectors, stages = vc
            
            # Create combined text for embedding
            sectors_text = ', '.join(sectors) if sectors else ''
            stages_text = ', '.join(stages) if stages else ''
            text_to_embed = f"{firm_name}. Investment thesis: {thesis or 'Not specified'}. Sectors: {sectors_text}. Stages: {stages_text}"
            
            print(f"  Generating embedding for: {firm_name}...", end='')
            embedding = await get_embedding(text_to_embed)
            
            # Store as text array in PostgreSQL
            embedding_str = '[' + ','.join(map(str, embedding)) + ']'
            
            # Update database
            cur.execute("""
                UPDATE vcs 
                SET thesis_vector = %s,
                    updated_at = %s
                WHERE id = %s
            """, (embedding_str, datetime.utcnow(), vc_id))
            
            updated_count += 1
            print(" ✅")
            
            # Commit every 10 updates
            if updated_count % 10 == 0:
                conn.commit()
                print(f"  Progress: {updated_count}/{len(vcs)}")
        
        # Final commit
        conn.commit()
        print(f"\n✅ Generated embeddings for {updated_count} VCs")
        
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")
        conn.rollback()
        raise
    finally:
        cur.close()
        conn.close()


async def verify_embeddings():
    """Verify that embeddings were generated correctly."""
    print("\n🔍 Verifying embeddings...")
    print("=" * 60)
    
    conn = psycopg2.connect(
        host="localhost",
        database="vc_matching_platform",
        user="thecostcokid",
        password=""
    )
    cur = conn.cursor()
    
    try:
        # Check startups
        cur.execute("""
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN description_vector IS NOT NULL 
                     AND description_vector != '[]' 
                     AND LENGTH(description_vector::text) > 10 THEN 1 END) as with_embeddings
            FROM startups
        """)
        total_startups, startups_with_embeddings = cur.fetchone()
        
        # Check VCs
        cur.execute("""
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN thesis_vector IS NOT NULL 
                     AND thesis_vector != '[]' 
                     AND LENGTH(thesis_vector::text) > 10 THEN 1 END) as with_embeddings
            FROM vcs
        """)
        total_vcs, vcs_with_embeddings = cur.fetchone()
        
        print(f"\n📊 Embedding Status:")
        print(f"  Startups: {startups_with_embeddings}/{total_startups} ({startups_with_embeddings/total_startups*100:.1f}%)")
        print(f"  VCs: {vcs_with_embeddings}/{total_vcs} ({vcs_with_embeddings/total_vcs*100:.1f}%)")
        
        # Test hybrid search
        if startups_with_embeddings > 0:
            print(f"\n🧪 Testing hybrid search...")
            
            # Get a sample query embedding
            test_query = "AI machine learning startups"
            query_embedding = await get_embedding(test_query)
            query_embedding_str = '[' + ','.join(map(str, query_embedding)) + ']'
            
            # Test the hybrid search function
            cur.execute("""
                SELECT COUNT(*) FROM hybrid_startup_search(
                    %s::text,
                    %s,
                    0.4,
                    0.6,
                    0.1
                )
            """, (query_embedding_str, test_query))
            
            result_count = cur.fetchone()[0]
            print(f"  ✅ Hybrid search returned {result_count} results for '{test_query}'")
        
    except Exception as e:
        print(f"\n❌ Error verifying: {str(e)}")
    finally:
        cur.close()
        conn.close()


async def main():
    """Main function."""
    print("🧠 Embedding Generation for VC Matching Platform")
    print("=" * 60)
    
    # Check OpenAI API key
    if not os.getenv('OPENAI_API_KEY') or os.getenv('OPENAI_API_KEY') == 'your-key-here':
        print("❌ Error: Please set a valid OPENAI_API_KEY in .env.local")
        return
    
    # Generate embeddings
    await generate_startup_embeddings()
    await generate_vc_embeddings()
    
    # Verify
    await verify_embeddings()
    
    print("\n✅ Embedding generation complete!")
    print("\n💡 Next steps:")
    print("  1. Test the hybrid search endpoints")
    print("  2. Monitor embedding generation costs")
    print("  3. Set up periodic embedding updates")


if __name__ == "__main__":
    asyncio.run(main())