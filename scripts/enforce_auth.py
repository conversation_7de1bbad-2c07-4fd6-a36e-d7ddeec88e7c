#!/usr/bin/env python3
"""Enforce authentication on API endpoints that should be protected."""

import os
import re
from pathlib import Path
import sys

sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


# Endpoints that should remain public (no auth required)
PUBLIC_ENDPOINTS = {
    # Health checks
    ("health.py", "health_check"),
    ("health.py", "detailed_health_check"),
    ("health.py", "redis_health"),
    ("health.py", "celery_health"),
    ("health.py", "check_dependencies"),
    
    # Auth endpoints
    ("auth.py", "register"),
    ("auth.py", "login"),
    ("auth.py", "refresh_token"),
    
    # Public discovery/search
    ("discovery.py", "get_discovery_stats"),
    ("market_intelligence.py", "get_market_overview"),
    ("signals.py", "get_signal_types"),
    
    # Public read operations (debatable - you may want to protect these)
    ("startups.py", "list_startups"),
    ("startups.py", "get_startup"),
    ("vcs.py", "list_vcs"),
    ("vcs.py", "get_vc"),
}

# Endpoints that should have optional auth (enhanced features when authenticated)
OPTIONAL_ENDPOINTS = {
    # Discovery endpoints - work without auth but better with it
    ("discovery.py", "discover_startups_for_vc"),
    ("discovery.py", "find_investors_for_startup"),
    ("hybrid_discovery.py", "discover_startups"),
    ("hybrid_discovery.py", "discover_vcs"),
    ("cached_discovery.py", "search_cached"),
}


def should_be_protected(file_name: str, function_name: str) -> bool:
    """Check if an endpoint should be protected."""
    # Check if it's explicitly public
    if (file_name, function_name) in PUBLIC_ENDPOINTS:
        return False
    
    # Check if it should be optional
    if (file_name, function_name) in OPTIONAL_ENDPOINTS:
        return False
    
    # All write operations should be protected
    if any(op in function_name for op in ["create", "update", "delete", "extract", "scrape", "request", "respond", "complete"]):
        return True
    
    # Personal data access should be protected
    if any(word in function_name for word in ["my_", "analytics", "stats"]):
        return True
    
    # Admin operations
    if any(word in function_name for word in ["clear", "check"]):
        return True
    
    return False


def update_endpoint_auth(file_path: Path) -> int:
    """Update authentication in an endpoint file."""
    with open(file_path, 'r') as f:
        content = f.read()
    
    updates = 0
    modified_content = content
    
    # Find all function definitions with optional auth
    pattern = r'(async def (\w+)\([\s\S]*?)(current_user: Optional\[str\] = Depends\(get_current_user_optional\))'
    
    for match in re.finditer(pattern, content):
        func_name = match.group(2)
        
        if should_be_protected(file_path.name, func_name):
            # Replace optional auth with required auth
            old_text = match.group(3)
            new_text = 'current_user: str = Depends(get_current_user)'
            
            modified_content = modified_content.replace(old_text, new_text, 1)
            updates += 1
            print(f"  ✅ Updated {func_name} to require authentication")
    
    # Also need to update the import if we're adding get_current_user
    if updates > 0 and "get_current_user," not in modified_content:
        # Update the import statement
        import_pattern = r'(from src\.api\.v1\.deps import \([\s\S]*?)(get_current_user_optional)'
        import_match = re.search(import_pattern, modified_content)
        
        if import_match:
            old_import = import_match.group(2)
            new_import = "get_current_user_optional, get_current_user"
            modified_content = modified_content.replace(old_import, new_import, 1)
    
    if updates > 0:
        with open(file_path, 'w') as f:
            f.write(modified_content)
    
    return updates


def main():
    """Main function."""
    print("🔐 Enforcing Authentication on API Endpoints")
    print("=" * 60)
    
    endpoints_dir = Path("src/api/v1/endpoints")
    total_updates = 0
    
    for file_path in endpoints_dir.glob("*.py"):
        if file_path.name in ["__init__.py", "deps.py", "error_handlers.py"]:
            continue
        
        print(f"\n📄 Processing {file_path.name}...")
        updates = update_endpoint_auth(file_path)
        total_updates += updates
        
        if updates == 0:
            print(f"  ℹ️  No updates needed")
    
    print(f"\n✅ Total endpoints updated: {total_updates}")
    print("\n💡 Next steps:")
    print("  1. Review the changes to ensure they're correct")
    print("  2. Update any tests that might be affected")
    print("  3. Test the API endpoints to ensure authentication works")
    print("  4. Consider adding role-based access control (RBAC)")


if __name__ == "__main__":
    main()