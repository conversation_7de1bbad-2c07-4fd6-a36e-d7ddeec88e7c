-- Reset VCs table with sample data
TRUNCATE TABLE vcs CASCADE;

INSERT INTO vcs (id, firm_name, website, thesis, sectors, stages, check_size_min, check_size_max, portfolio_companies, partners, created_at, updated_at) VALUES
('e98fe67d-7f82-493c-a780-b124a3a39ebf', 'Sequoia Capital', 'https://www.sequoiacap.com', 'We partner with daring founders from idea to IPO and beyond. We focus on companies that can define categories and create lasting value.', '["AI/ML", "SaaS", "Fintech", "Healthcare"]', '["Seed", "Series A", "Series B"]', 1000000, 100000000, '["Airbnb", "Stripe", "WhatsApp", "Zoom"]', '["<PERSON><PERSON><PERSON>", "<PERSON> Lin", "<PERSON>"]', NOW(), NOW()),
('a12de67d-7f82-493c-a780-b124a3a39ebf', '<PERSON><PERSON><PERSON>', 'https://a16z.com', 'We invest in bold entrepreneurs building the future through technology across AI, bio, crypto, and American dynamism.', '["AI/ML", "Crypto", "Biotech", "Enterprise"]', '["Seed", "Series A", "Series B", "Series C"]', 500000, 150000000, '["Coinbase", "Lyft", "Slack", "GitHub"]', '["Marc Andreessen", "Ben Horowitz", "<PERSON>"]', NOW(), NOW()),
('b34fe67d-7f82-493c-a780-b124a3a39ebf', 'Lightspeed Venture Partners', 'https://lsvp.com', 'We partner with extraordinary entrepreneurs to build enduring technology companies at every stage and around the world.', '["Enterprise", "Consumer", "Healthcare", "Fintech"]', '["Seed", "Series A", "Series B"]', 2000000, 50000000, '["Snapchat", "Nutanix", "AppDynamics"]', '["Ravi Mhatre", "Jeremy Liew", "Nicole Quinn"]', NOW(), NOW());

-- Reset startups table with sample data
TRUNCATE TABLE startups CASCADE;

INSERT INTO startups (id, name, sector, stage, description, website, team_size, monthly_revenue, created_at, updated_at) VALUES
('550e8400-e29b-41d4-a716-446655440001', 'TechStartup AI', 'AI/ML', 'Seed', 'Building next-generation AI models for enterprise automation. Our platform helps companies reduce operational costs by 70%.', 'https://techstartupai.com', 15, 50000, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440002', 'HealthTech Pro', 'Healthcare', 'Series A', 'AI-powered diagnostic tools for early disease detection. FDA approved and deployed in 50+ hospitals.', 'https://healthtechpro.com', 45, 250000, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440003', 'FinanceFlow', 'Fintech', 'Series B', 'Modern payment infrastructure for global B2B transactions. Processing $100M+ monthly.', 'https://financeflow.com', 120, 2000000, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440004', 'EduPlatform', 'EdTech', 'Seed', 'Personalized learning platform using AI to adapt to each student. 10,000+ active users.', 'https://eduplatform.com', 8, 30000, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440005', 'GreenEnergy Tech', 'Climate Tech', 'Series A', 'Smart grid optimization software reducing energy waste by 40%. Deployed in 3 major cities.', 'https://greenenergy.tech', 35, 180000, NOW(), NOW());