#!/bin/bash

# VC Matching Platform - Complete Setup and Run Script
# This script handles environment setup and starts all components
# Usage: ./scripts/setup-and-run.sh [docker|local|hybrid] [--reset]

set -e  # Exit on error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="VC Matching Platform"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENV_FILE="$PROJECT_ROOT/.env"
ENV_EXAMPLE="$PROJECT_ROOT/.env.example"
ENV_STAGING="$PROJECT_ROOT/.env.staging"

# Default mode
MODE=${1:-docker}
RESET_FLAG=${2:-}

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "\n${PURPLE}🚀 $1${NC}"
    echo -e "${PURPLE}$(printf '=%.0s' {1..50})${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if port is available
port_available() {
    ! nc -z localhost "$1" 2>/dev/null
}

# Function to wait for service
wait_for_service() {
    local host=$1
    local port=$2
    local service_name=$3
    local max_attempts=30
    local attempt=1

    print_info "Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if nc -z "$host" "$port" 2>/dev/null; then
            print_status "$service_name is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed to start within $((max_attempts * 2)) seconds"
    return 1
}

# Function to generate secure secret key
generate_secret_key() {
    python3 -c "import secrets; print(secrets.token_urlsafe(32))"
}

# Function to setup environment file
setup_environment() {
    print_header "Setting up environment configuration"
    
    if [ "$RESET_FLAG" = "--reset" ] && [ -f "$ENV_FILE" ]; then
        print_warning "Resetting existing .env file"
        rm "$ENV_FILE"
    fi
    
    if [ ! -f "$ENV_FILE" ]; then
        if [ -f "$ENV_STAGING" ]; then
            print_info "Creating .env from .env.staging template"
            cp "$ENV_STAGING" "$ENV_FILE"
        elif [ -f "$ENV_EXAMPLE" ]; then
            print_info "Creating .env from .env.example template"
            cp "$ENV_EXAMPLE" "$ENV_FILE"
        else
            print_error "No environment template found!"
            exit 1
        fi
    fi
    
    # Generate secure secret key if needed
    if grep -q "your-staging-secret-key-please-change-this\|CHANGE-THIS-REQUIRED" "$ENV_FILE"; then
        print_info "Generating secure secret key..."
        SECRET_KEY=$(generate_secret_key)
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            sed -i '' "s/SECRET_KEY=.*/SECRET_KEY=$SECRET_KEY/" "$ENV_FILE"
        else
            # Linux
            sed -i "s/SECRET_KEY=.*/SECRET_KEY=$SECRET_KEY/" "$ENV_FILE"
        fi
        print_status "Generated new secret key"
    fi
    
    # Check for required OpenAI API key
    if grep -q "your-openai-api-key-here" "$ENV_FILE"; then
        print_warning "OpenAI API key not set in .env file"
        echo -e "${YELLOW}Please edit $ENV_FILE and add your OpenAI API key:${NC}"
        echo -e "${CYAN}OPENAI_API_KEY=sk-your-actual-key-here${NC}"

        read -p "Do you want to enter your OpenAI API key now? (y/n/s for skip): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            read -p "Enter your OpenAI API key: " openai_key
            if [ -n "$openai_key" ]; then
                if [[ "$OSTYPE" == "darwin"* ]]; then
                    sed -i '' "s/OPENAI_API_KEY=.*/OPENAI_API_KEY=$openai_key/" "$ENV_FILE"
                else
                    sed -i "s/OPENAI_API_KEY=.*/OPENAI_API_KEY=$openai_key/" "$ENV_FILE"
                fi
                print_status "OpenAI API key updated"
            else
                print_warning "Empty API key provided, skipping"
            fi
        elif [[ $REPLY =~ ^[Ss]$ ]]; then
            print_info "Skipping OpenAI API key setup for now"
        else
            print_warning "You can update the OpenAI API key later in $ENV_FILE"
        fi
    fi
    
    print_status "Environment configuration ready"
}

# Function to check prerequisites
check_prerequisites() {
    print_header "Checking prerequisites"
    
    case $MODE in
        "docker")
            if ! command_exists docker; then
                print_error "Docker is not installed. Please install Docker and try again."
                exit 1
            fi
            
            if ! command_exists docker-compose; then
                print_error "docker-compose is not installed. Please install it and try again."
                exit 1
            fi
            
            if ! docker info > /dev/null 2>&1; then
                print_error "Docker is not running. Please start Docker and try again."
                exit 1
            fi
            
            print_status "Docker and docker-compose are ready"
            ;;
            
        "local"|"hybrid")
            if ! command_exists python3; then
                print_error "Python 3 is not installed. Please install Python 3.9+ and try again."
                exit 1
            fi
            
            python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')" 2>/dev/null || echo "unknown")
            if ! python3 -c "import sys; exit(0 if sys.version_info >= (3, 9) else 1)" 2>/dev/null; then
                print_error "Python 3.9+ is required. Found Python $python_version"
                print_info "Your Python version should work, but the version check failed."
                print_info "Continuing anyway..."
            fi
            
            if ! command_exists pip && ! command_exists pip3; then
                print_error "pip is not installed. Please install pip and try again."
                exit 1
            fi

            # Set pip command preference
            if command_exists pip3; then
                PIP_CMD="pip3"
            else
                PIP_CMD="pip"
            fi
            
            print_status "Python $python_version and pip are ready"
            
            if [ "$MODE" = "hybrid" ]; then
                if ! command_exists docker; then
                    print_error "Docker is required for hybrid mode. Please install Docker."
                    exit 1
                fi
                print_status "Docker is ready for hybrid mode"
            fi
            ;;
    esac
}

# Function to show usage
show_usage() {
    echo -e "${CYAN}Usage: $0 [MODE] [OPTIONS]${NC}"
    echo -e "\n${YELLOW}MODES:${NC}"
    echo -e "  docker  - Run everything in Docker containers (default)"
    echo -e "  local   - Run everything locally (requires Python, PostgreSQL, Redis)"
    echo -e "  hybrid  - Run services in Docker, app locally (recommended for development)"
    echo -e "\n${YELLOW}OPTIONS:${NC}"
    echo -e "  --reset - Reset environment configuration"
    echo -e "\n${YELLOW}EXAMPLES:${NC}"
    echo -e "  $0                    # Run with Docker (default)"
    echo -e "  $0 docker             # Run with Docker"
    echo -e "  $0 local              # Run everything locally"
    echo -e "  $0 hybrid             # Hybrid mode (services in Docker, app local)"
    echo -e "  $0 docker --reset     # Reset environment and run with Docker"
}

# Main execution starts here
main() {
    cd "$PROJECT_ROOT"
    
    print_header "$PROJECT_NAME - Setup and Run"
    print_info "Mode: $MODE"
    
    # Show usage if help requested
    if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
        show_usage
        exit 0
    fi
    
    # Validate mode
    if [[ ! "$MODE" =~ ^(docker|local|hybrid)$ ]]; then
        print_error "Invalid mode: $MODE"
        show_usage
        exit 1
    fi
    
    # Run setup steps
    check_prerequisites
    setup_environment
    
    print_header "Starting $PROJECT_NAME in $MODE mode"
    
    case $MODE in
        "docker")
            run_docker_mode
            ;;
        "local")
            run_local_mode
            ;;
        "hybrid")
            run_hybrid_mode
            ;;
    esac
}

# Function to run Docker mode
run_docker_mode() {
    print_info "Running in Docker mode - all services in containers"

    # Stop existing containers
    print_info "Stopping existing containers..."
    docker-compose down 2>/dev/null || true

    # Build and start services
    print_info "Building and starting Docker services..."
    docker-compose up -d --build

    # Wait for services
    wait_for_service localhost 5432 "PostgreSQL"
    wait_for_service localhost 6379 "Redis"
    wait_for_service localhost 8000 "API Server"

    show_service_urls "docker"
}

# Function to run local mode
run_local_mode() {
    print_info "Running in local mode - all services locally"

    # Check for local services
    check_local_services

    # Setup Python environment
    setup_python_environment

    # Setup database
    setup_local_database

    # Start services
    start_local_services

    show_service_urls "local"
}

# Function to run hybrid mode
run_hybrid_mode() {
    print_info "Running in hybrid mode - services in Docker, app locally"

    # Start only database and Redis in Docker
    print_info "Starting database and Redis in Docker..."
    docker-compose up -d db redis

    # Wait for services
    wait_for_service localhost 5432 "PostgreSQL"
    wait_for_service localhost 6379 "Redis"

    # Setup Python environment
    setup_python_environment

    # Update environment for local connection
    update_env_for_hybrid

    # Setup database
    setup_local_database

    # Start local services
    start_local_services

    show_service_urls "hybrid"
}

# Function to check local services
check_local_services() {
    print_header "Checking local services"

    if [ "$MODE" = "local" ]; then
        local has_issues=false

        # Check PostgreSQL
        if ! command_exists psql; then
            print_warning "PostgreSQL client (psql) not found."
            print_info "You can install it with: brew install postgresql (macOS) or apt-get install postgresql-client (Ubuntu)"
            has_issues=true
        fi

        # Check Redis
        if ! command_exists redis-cli; then
            print_warning "Redis client (redis-cli) not found."
            print_info "You can install it with: brew install redis (macOS) or apt-get install redis-tools (Ubuntu)"
            has_issues=true
        fi

        # Check if services are running
        if ! nc -z localhost 5432 2>/dev/null; then
            print_warning "PostgreSQL not running on localhost:5432"
            print_info "Please start PostgreSQL: brew services start postgresql (macOS)"
            has_issues=true
        else
            print_status "PostgreSQL is running on localhost:5432"
        fi

        if ! nc -z localhost 6379 2>/dev/null; then
            print_warning "Redis not running on localhost:6379"
            print_info "Please start Redis: brew services start redis (macOS)"
            has_issues=true
        else
            print_status "Redis is running on localhost:6379"
        fi

        if [ "$has_issues" = true ]; then
            print_warning "Some local services are not available."
            print_info "Consider using hybrid mode instead: ./run.sh hybrid"
            read -p "Continue anyway? (y/n): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                print_info "Exiting. Try: ./run.sh hybrid"
                exit 1
            fi
        fi
    fi
}

# Function to setup Python environment
setup_python_environment() {
    print_header "Setting up Python environment"

    # Check if virtual environment exists
    if [ ! -d "venv" ]; then
        print_info "Creating Python virtual environment..."
        python3 -m venv venv
        print_status "Virtual environment created"
    fi

    # Activate virtual environment
    print_info "Activating virtual environment..."
    source venv/bin/activate

    # Set pip command preference
    if command_exists pip3; then
        PIP_CMD="pip3"
    else
        PIP_CMD="pip"
    fi

    # Upgrade pip
    print_info "Upgrading pip..."
    $PIP_CMD install --upgrade pip > /dev/null 2>&1

    # Install dependencies
    print_info "Installing Python dependencies..."
    $PIP_CMD install -r requirements.txt > /dev/null 2>&1
    print_status "Dependencies installed"
}

# Function to update environment for hybrid mode
update_env_for_hybrid() {
    print_info "Updating environment for hybrid mode..."

    # Update database URL for local connection
    if [[ "$OSTYPE" == "darwin"* ]]; then
        sed -i '' 's|DATABASE_URL=postgresql://.*@db:|DATABASE_URL=postgresql://postgres:postgres@localhost:|' "$ENV_FILE"
        sed -i '' 's|REDIS_URL=redis://redis:|REDIS_URL=redis://localhost:|' "$ENV_FILE"
    else
        sed -i 's|DATABASE_URL=postgresql://.*@db:|DATABASE_URL=postgresql://postgres:postgres@localhost:|' "$ENV_FILE"
        sed -i 's|REDIS_URL=redis://redis:|REDIS_URL=redis://localhost:|' "$ENV_FILE"
    fi

    print_status "Environment updated for hybrid mode"
}

# Function to setup local database
setup_local_database() {
    print_header "Setting up database"

    # Load environment variables
    if [ -f "$ENV_FILE" ]; then
        export $(cat "$ENV_FILE" | grep -v '^#' | xargs) 2>/dev/null || true
    fi

    # Run database migrations
    print_info "Running database migrations..."
    if command_exists alembic; then
        alembic upgrade head
        print_status "Database migrations completed"
    else
        print_warning "Alembic not found, skipping migrations"
    fi
}

# Function to start local services
start_local_services() {
    print_header "Starting local services"

    # Load environment variables
    if [ -f "$ENV_FILE" ]; then
        export $(cat "$ENV_FILE" | grep -v '^#' | xargs) 2>/dev/null || true
    fi

    # Create log directory
    mkdir -p logs

    # Start API server
    print_info "Starting API server..."
    nohup uvicorn src.api.main:app --reload --host 0.0.0.0 --port 8000 > logs/api.log 2>&1 &
    API_PID=$!
    echo $API_PID > logs/api.pid

    # Wait for API to start
    sleep 3
    if wait_for_service localhost 8000 "API Server"; then
        print_status "API server started (PID: $API_PID)"
    else
        print_error "Failed to start API server"
        exit 1
    fi

    # Start Celery worker
    print_info "Starting Celery worker..."
    nohup celery -A src.workers.celery_app worker --loglevel=info > logs/celery-worker.log 2>&1 &
    WORKER_PID=$!
    echo $WORKER_PID > logs/celery-worker.pid
    print_status "Celery worker started (PID: $WORKER_PID)"

    # Start Celery beat
    print_info "Starting Celery beat scheduler..."
    nohup celery -A src.workers.celery_app beat --loglevel=info > logs/celery-beat.log 2>&1 &
    BEAT_PID=$!
    echo $BEAT_PID > logs/celery-beat.pid
    print_status "Celery beat started (PID: $BEAT_PID)"

    # Start Flower (optional)
    if command_exists flower; then
        print_info "Starting Flower monitoring..."
        nohup celery -A src.workers.celery_app flower --port=5555 > logs/flower.log 2>&1 &
        FLOWER_PID=$!
        echo $FLOWER_PID > logs/flower.pid
        print_status "Flower started (PID: $FLOWER_PID)"
    fi

    # Create stop script
    create_stop_script
}

# Function to create stop script
create_stop_script() {
    cat > stop-services.sh << 'EOF'
#!/bin/bash
# Stop all local services

echo "Stopping VC Matching Platform services..."

# Function to stop service by PID file
stop_service() {
    local service_name=$1
    local pid_file="logs/${service_name}.pid"

    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            echo "Stopping $service_name (PID: $pid)..."
            kill "$pid"
            rm "$pid_file"
        else
            echo "$service_name is not running"
            rm "$pid_file" 2>/dev/null || true
        fi
    else
        echo "No PID file found for $service_name"
    fi
}

# Stop all services
stop_service "api"
stop_service "celery-worker"
stop_service "celery-beat"
stop_service "flower"

echo "All services stopped"
EOF

    chmod +x stop-services.sh
    print_status "Created stop-services.sh script"
}

# Function to show service URLs
show_service_urls() {
    local mode=$1

    print_header "Service URLs and Information"

    case $mode in
        "docker")
            echo -e "${GREEN}🌐 Service URLs:${NC}"
            echo -e "  ${CYAN}API Documentation:${NC} http://localhost:8000/docs"
            echo -e "  ${CYAN}API ReDoc:${NC}         http://localhost:8000/redoc"
            echo -e "  ${CYAN}Flower Monitor:${NC}    http://localhost:5555"
            echo -e "  ${CYAN}Database:${NC}          localhost:5432"
            echo -e "  ${CYAN}Redis:${NC}             localhost:6379"

            echo -e "\n${YELLOW}📋 Management Commands:${NC}"
            echo -e "  ${CYAN}View logs:${NC}         docker-compose logs -f [service]"
            echo -e "  ${CYAN}Stop services:${NC}     docker-compose down"
            echo -e "  ${CYAN}Restart:${NC}           docker-compose restart [service]"
            ;;

        "local"|"hybrid")
            echo -e "${GREEN}🌐 Service URLs:${NC}"
            echo -e "  ${CYAN}API Documentation:${NC} http://localhost:8000/docs"
            echo -e "  ${CYAN}API ReDoc:${NC}         http://localhost:8000/redoc"
            if [ -f "logs/flower.pid" ]; then
                echo -e "  ${CYAN}Flower Monitor:${NC}    http://localhost:5555"
            fi
            echo -e "  ${CYAN}Database:${NC}          localhost:5432"
            echo -e "  ${CYAN}Redis:${NC}             localhost:6379"

            echo -e "\n${YELLOW}📋 Management Commands:${NC}"
            echo -e "  ${CYAN}View API logs:${NC}     tail -f logs/api.log"
            echo -e "  ${CYAN}View worker logs:${NC}  tail -f logs/celery-worker.log"
            echo -e "  ${CYAN}Stop services:${NC}     ./stop-services.sh"

            if [ "$mode" = "hybrid" ]; then
                echo -e "  ${CYAN}Stop Docker:${NC}       docker-compose down"
            fi
            ;;
    esac

    echo -e "\n${GREEN}🧪 Testing:${NC}"
    echo -e "  ${CYAN}Run tests:${NC}         pytest tests/ --cov=src"
    echo -e "  ${CYAN}Health check:${NC}      curl http://localhost:8000/health"

    echo -e "\n${GREEN}✅ $PROJECT_NAME is running in $mode mode!${NC}"

    if grep -q "your-openai-api-key-here" "$ENV_FILE" 2>/dev/null; then
        echo -e "\n${YELLOW}⚠️  Remember to set your OpenAI API key in $ENV_FILE${NC}"
    fi
}

# Check if script is being sourced or executed
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
