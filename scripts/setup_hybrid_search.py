#!/usr/bin/env python3
"""Script to setup hybrid search infrastructure."""

import sys
import os
import asyncio
import logging
from pathlib import Path

# Add project root to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from alembic import command
from alembic.config import Config
from sqlalchemy import text

from src.database.setup import get_database
from src.core.config import get_settings
from src.workers.tasks.embedding_tasks import generate_missing_embeddings_task

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def run_migration():
    """Run the vector search migration."""
    logger.info("Running database migration for vector search...")
    
    # Get alembic config
    alembic_cfg = Config("alembic.ini")
    
    try:
        # Run migration
        command.upgrade(alembic_cfg, "head")
        logger.info("✅ Migration completed successfully")
        return True
    except Exception as e:
        logger.error(f"❌ Migration failed: {e}")
        return False


def check_vector_extension():
    """Check if pgvector extension is installed."""
    logger.info("Checking pgvector extension...")
    
    with get_database() as db:
        try:
            # Check extension
            result = db.execute(text("SELECT * FROM pg_extension WHERE extname = 'vector'"))
            if result.fetchone():
                logger.info("✅ pgvector extension is installed")
                
                # Test vector operations
                test_result = db.execute(text("SELECT '[1,2,3]'::vector"))
                if test_result.fetchone():
                    logger.info("✅ Vector operations are working")
                    return True
            else:
                logger.error("❌ pgvector extension is not installed")
                logger.info("Please install pgvector in your PostgreSQL database:")
                logger.info("  1. Install pgvector package for your OS")
                logger.info("  2. Connect to your database as superuser")
                logger.info("  3. Run: CREATE EXTENSION vector;")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error checking pgvector: {e}")
            return False


def check_entities_count():
    """Check how many entities need embeddings."""
    logger.info("Checking entities that need embeddings...")
    
    with get_database() as db:
        try:
            # Count startups
            startup_result = db.execute(text("""
                SELECT COUNT(*) as count 
                FROM startups 
                WHERE combined_vector IS NULL
            """))
            startup_count = startup_result.fetchone().count
            
            # Count VCs
            vc_result = db.execute(text("""
                SELECT COUNT(*) as count 
                FROM vcs 
                WHERE combined_vector IS NULL
            """))
            vc_count = vc_result.fetchone().count
            
            logger.info(f"📊 Startups needing embeddings: {startup_count}")
            logger.info(f"📊 VCs needing embeddings: {vc_count}")
            
            return startup_count, vc_count
            
        except Exception as e:
            logger.error(f"❌ Error counting entities: {e}")
            return 0, 0


def queue_embedding_generation(batch_size: int = 20):
    """Queue background task to generate embeddings."""
    logger.info(f"Queueing embedding generation task (batch size: {batch_size})...")
    
    try:
        # Queue the task
        result = generate_missing_embeddings_task.delay("all", batch_size)
        logger.info(f"✅ Task queued with ID: {result.id}")
        logger.info("Monitor progress with: celery -A src.workers.celery_app inspect active")
        return True
    except Exception as e:
        logger.error(f"❌ Error queueing task: {e}")
        logger.info("Make sure Celery is running:")
        logger.info("  celery -A src.workers.celery_app worker --loglevel=info")
        return False


async def generate_sample_embeddings():
    """Generate embeddings for a few sample entities."""
    logger.info("Generating sample embeddings...")
    
    from src.core.ai.embeddings import HybridEmbeddingService
    from src.core.models.startup import Startup as StartupDomain
    
    settings = get_settings()
    
    if not settings.OPENAI_API_KEY:
        logger.error("❌ OPENAI_API_KEY not set in environment")
        return False
    
    embedding_service = HybridEmbeddingService(settings.OPENAI_API_KEY)
    
    with get_database() as db:
        try:
            # Get a sample startup
            result = db.execute(text("""
                SELECT * FROM startups 
                WHERE combined_vector IS NULL 
                LIMIT 1
            """))
            startup_data = result.fetchone()
            
            if startup_data:
                # Create domain object
                startup = StartupDomain(
                    name=startup_data.name,
                    sector=startup_data.sector,
                    stage=startup_data.stage,
                    description=startup_data.description or "",
                    team_size=startup_data.team_size or 0,
                    monthly_revenue=startup_data.monthly_revenue or 0
                )
                startup.id = str(startup_data.id)
                
                # Generate embeddings
                logger.info(f"Generating embeddings for startup: {startup.name}")
                embeddings = await embedding_service.generate_startup_embeddings(startup)
                
                # Update database
                await embedding_service.update_startup_embeddings(
                    db, str(startup_data.id), embeddings
                )
                
                logger.info("✅ Sample embedding generated successfully")
                
                # Test search
                test_result = db.execute(text("""
                    SELECT 
                        name,
                        1 - (text_to_vector(combined_vector) <=> text_to_vector(combined_vector)) as self_similarity
                    FROM startups 
                    WHERE id = :startup_id
                """), {"startup_id": startup_data.id})
                
                test_row = test_result.fetchone()
                if test_row:
                    logger.info(f"✅ Vector search test passed (self-similarity: {test_row.self_similarity:.4f})")
                
                return True
            else:
                logger.info("No startups found to test")
                return True
                
        except Exception as e:
            logger.error(f"❌ Error generating sample embeddings: {e}")
            return False


def main():
    """Main setup function."""
    logger.info("🚀 Setting up Hybrid Search Infrastructure")
    logger.info("=" * 50)
    
    # Step 1: Run migration
    if not run_migration():
        logger.error("Failed to run migration. Exiting.")
        return 1
    
    # Step 2: Check pgvector
    if not check_vector_extension():
        logger.error("pgvector extension not available. Please install it first.")
        return 1
    
    # Step 3: Check entities
    startup_count, vc_count = check_entities_count()
    total_count = startup_count + vc_count
    
    if total_count == 0:
        logger.info("✅ All entities already have embeddings!")
        return 0
    
    # Step 4: Generate sample embeddings
    logger.info("\nGenerating sample embeddings to test the system...")
    loop = asyncio.get_event_loop()
    if not loop.run_until_complete(generate_sample_embeddings()):
        logger.error("Sample embedding generation failed")
        return 1
    
    # Step 5: Ask about full generation
    logger.info("\n" + "=" * 50)
    logger.info(f"Ready to generate embeddings for {total_count} entities")
    logger.info("This will use OpenAI API and incur costs.")
    logger.info(f"Estimated cost: ${total_count * 0.00002:.2f} (using text-embedding-3-small)")
    
    response = input("\nProceed with full embedding generation? (y/N): ")
    
    if response.lower() == 'y':
        # Queue the background task
        if queue_embedding_generation():
            logger.info("\n✅ Setup completed successfully!")
            logger.info("Embeddings are being generated in the background.")
            logger.info("\nNext steps:")
            logger.info("1. Monitor Celery tasks: celery -A src.workers.celery_app flower")
            logger.info("2. Test hybrid search: GET /api/v1/hybrid/search-status")
            logger.info("3. Try discovery: POST /api/v1/hybrid/discover-startups")
        else:
            logger.error("Failed to queue embedding generation")
            return 1
    else:
        logger.info("\n✅ Setup completed (without full embedding generation)")
        logger.info("You can generate embeddings later using:")
        logger.info("  python -c \"from src.workers.tasks.embedding_tasks import generate_missing_embeddings_task; generate_missing_embeddings_task.delay('all', 20)\"")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())