#!/usr/bin/env python3
"""Test and verify Celery scheduled tasks configuration."""

import sys
import os
from datetime import datetime, timedelta

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.workers import celery_app
from src.workers.tasks.scheduled_tasks import (
    refresh_ai_insights,
    cleanup_stale_data,
    generate_analytics_report,
    update_match_scores
)
from src.workers.tasks.weekly_digest import send_all_weekly_digests


def test_scheduled_tasks():
    """Test scheduled tasks configuration."""
    print("🕒 Testing Celery Scheduled Tasks")
    print("=" * 60)
    
    # Check beat schedule configuration
    print("\n📅 Configured Schedule:")
    beat_schedule = celery_app.conf.beat_schedule
    
    for task_name, config in beat_schedule.items():
        schedule = config['schedule']
        task = config['task']
        queue = config.get('options', {}).get('queue', 'default')
        
        # Convert seconds to human-readable format
        if schedule < 3600:
            interval = f"{int(schedule/60)} minutes"
        elif schedule < 86400:
            interval = f"{int(schedule/3600)} hours"
        else:
            interval = f"{int(schedule/86400)} days"
        
        print(f"\n  {task_name}:")
        print(f"    Task: {task}")
        print(f"    Interval: {interval}")
        print(f"    Queue: {queue}")
    
    # Test individual tasks can be called
    print("\n\n🧪 Testing Task Execution (Dry Run):")
    
    # Test 1: AI Insights Refresh (should check for entities needing refresh)
    print("\n1️⃣ Testing AI Insights Refresh...")
    try:
        # Call synchronously for testing
        result = refresh_ai_insights()
        print(f"  ✅ Success: {result}")
    except Exception as e:
        print(f"  ❌ Error: {str(e)}")
    
    # Test 2: Analytics Report Generation
    print("\n2️⃣ Testing Analytics Report Generation...")
    try:
        result = generate_analytics_report()
        print(f"  ✅ Success: Generated report for {result.get('report_date')}")
        if 'key_metrics' in result:
            print(f"     Total entities: {result['key_metrics']['total_entities']}")
            print(f"     New matches: {result['key_metrics']['new_matches']}")
            print(f"     AI coverage: {result['key_metrics']['ai_coverage']:.1%}")
    except Exception as e:
        print(f"  ❌ Error: {str(e)}")
    
    # Test 3: Weekly Digest Distribution
    print("\n3️⃣ Testing Weekly Digest Distribution...")
    try:
        result = send_all_weekly_digests()
        print(f"  ✅ Success: {result}")
    except Exception as e:
        print(f"  ❌ Error: {str(e)}")
    
    # Test 4: Cleanup Task
    print("\n4️⃣ Testing Cleanup Task...")
    try:
        result = cleanup_stale_data()
        print(f"  ✅ Success: {result}")
    except Exception as e:
        print(f"  ❌ Error: {str(e)}")
    
    print("\n\n📊 Schedule Summary:")
    print("  - AI insights refresh every 6 hours")
    print("  - Analytics report generated daily")
    print("  - Weekly digest emails sent every 7 days")
    print("  - Data cleanup runs daily")
    print("  - Old task cleanup runs hourly")
    
    print("\n✅ Scheduled tasks configuration complete!")
    
    print("\n💡 To run Celery Beat scheduler:")
    print("  celery -A src.workers.celery_app beat --loglevel=info")
    
    print("\n💡 To run Celery workers:")
    print("  # All queues:")
    print("  celery -A src.workers.celery_app worker --loglevel=info")
    print("\n  # Specific queue:")
    print("  celery -A src.workers.celery_app worker -Q scheduled --loglevel=info")


if __name__ == "__main__":
    test_scheduled_tasks()