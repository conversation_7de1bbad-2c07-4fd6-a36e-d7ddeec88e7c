#!/usr/bin/env python3
"""Simple script to load YC data directly into the database."""

import asyncio
import sys
import os
import psycopg2
from psycopg2.extras import <PERSON>son
from datetime import datetime
import uuid
import json

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.scrapers.yc_scraper import YCCompanyScraper


async def load_yc_data_directly():
    """Load YC company data directly into PostgreSQL."""
    print("Loading YC company data...")
    print("=" * 60)
    
    # Connect directly to PostgreSQL
    conn = psycopg2.connect(
        host="localhost",
        database="vc_matching_platform",
        user="thecostcokid",
        password=""
    )
    cur = conn.cursor()
    
    try:
        # First, check if tables exist
        cur.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'startups'
            );
        """)
        table_exists = cur.fetchone()[0]
        
        if not table_exists:
            print("❌ Table 'startups' does not exist. Please run migrations first.")
            return
        
        # Scrape YC companies
        scraper = YCCompanyScraper()
        companies = await scraper.scrape_companies(limit=50)
        
        print(f"\n✅ Fetched {len(companies)} companies from YC API")
        
        # Insert companies
        new_count = 0
        updated_count = 0
        
        for company in companies:
            # Check if company already exists by name
            cur.execute("SELECT id FROM startups WHERE name = %s", (company['name'],))
            existing = cur.fetchone()
            
            if existing:
                # Update existing
                cur.execute("""
                    UPDATE startups 
                    SET description = %s, website = %s, team_size = %s, updated_at = %s
                    WHERE id = %s
                """, (
                    company.get('description', ''),
                    company.get('website', ''),
                    company.get('team_size', 0),
                    datetime.utcnow(),
                    existing[0]
                ))
                updated_count += 1
            else:
                # Insert new
                startup_id = str(uuid.uuid4())
                metadata = {
                    'yc_batch': company.get('batch'),
                    'yc_id': company.get('yc_id'),
                    'yc_profile_url': company.get('yc_profile_url'),
                    'tags': company.get('tags', []),
                    'location': company.get('all_locations'),
                    'is_hiring': company.get('is_hiring', False),
                    'top_company': company.get('top_company', False),
                    'founded': company.get('founded'),
                    'source': company.get('source', 'yc_api')
                }
                
                cur.execute("""
                    INSERT INTO startups 
                    (id, name, sector, stage, description, website, team_size, monthly_revenue, metadata, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    startup_id,
                    company['name'],
                    company['sector'],
                    company['stage'],
                    company.get('description', ''),
                    company.get('website', ''),
                    company.get('team_size', 0),
                    0,  # monthly_revenue
                    Json(metadata),  # metadata as JSON
                    datetime.utcnow(),
                    datetime.utcnow()
                ))
                new_count += 1
                print(f"  Added: {company['name']} ({company['batch']}) - {company['sector']}")
        
        # Commit the transaction
        conn.commit()
        
        print(f"\n📊 Results:")
        print(f"  New companies: {new_count} ✅")
        print(f"  Updated companies: {updated_count} 🔄")
        
        # Show total count
        cur.execute("SELECT COUNT(*) FROM startups")
        total = cur.fetchone()[0]
        print(f"\n📈 Total startups in database: {total}")
        
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")
        conn.rollback()
        raise
    finally:
        cur.close()
        conn.close()


if __name__ == "__main__":
    asyncio.run(load_yc_data_directly())