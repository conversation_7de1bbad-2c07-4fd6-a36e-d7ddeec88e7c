#!/bin/bash
# Start Celery worker for VC Matching Platform

echo "Starting Celery worker..."

# Set Python path
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Load environment variables if .env exists
if [ -f .env ]; then
    echo "Loading environment variables from .env"
    export $(cat .env | grep -v '^#' | xargs)
fi

# Default to INFO log level if not set
export LOG_LEVEL=${LOG_LEVEL:-INFO}

# Worker name (useful for multiple workers)
WORKER_NAME=${WORKER_NAME:-worker1}

# Number of concurrent worker processes
CONCURRENCY=${CONCURRENCY:-4}

# Queue names to process (comma-separated)
QUEUES=${QUEUES:-default,ai_analysis,data_enrichment,notifications,scheduled}

echo "Configuration:"
echo "  Worker Name: $WORKER_NAME"
echo "  Concurrency: $CONCURRENCY"
echo "  Queues: $QUEUES"
echo "  Log Level: $LOG_LEVEL"
echo "  Redis URL: ${REDIS_URL:-redis://localhost:6379/0}"

# Start Celery worker
celery -A src.workers.celery_app worker \
    --loglevel=$LOG_LEVEL \
    --hostname=$WORKER_NAME@%h \
    --concurrency=$CONCURRENCY \
    --queues=$QUEUES \
    --pool=prefork \
    --time-limit=300 \
    --soft-time-limit=240 \
    --max-tasks-per-child=1000 \
    --without-gossip \
    --without-mingle \
    --without-heartbeat