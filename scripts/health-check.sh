#!/bin/bash

# Health check script for VC Matching Platform
# Checks all services and reports their status

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== VC Matching Platform Health Check ===${NC}\n"

# Function to check service
check_service() {
    local name=$1
    local url=$2
    local expected_code=${3:-200}
    
    if curl -s -o /dev/null -w "%{http_code}" "$url" | grep -q "^${expected_code}$"; then
        echo -e "${GREEN}✅ ${name}: Healthy${NC}"
        return 0
    else
        echo -e "${RED}❌ ${name}: Unhealthy${NC}"
        return 1
    fi
}

# Function to check docker container
check_container() {
    local name=$1
    local container=$2
    
    if docker ps | grep -q "$container.*Up"; then
        echo -e "${GREEN}✅ ${name}: Running${NC}"
        return 0
    else
        echo -e "${RED}❌ ${name}: Not Running${NC}"
        return 1
    fi
}

# Check Docker services
echo -e "${YELLOW}Docker Services:${NC}"
check_container "PostgreSQL" "postgres"
check_container "Redis" "redis"
check_container "API" "api"
check_container "Celery Worker" "celery"
check_container "Celery Beat" "celery-beat"
check_container "Flower" "flower"

echo ""

# Check HTTP endpoints
echo -e "${YELLOW}HTTP Endpoints:${NC}"
check_service "API Health" "http://localhost:8000/health"
check_service "API Docs" "http://localhost:8000/api/v1/docs"
check_service "Flower" "http://localhost:5555"

echo ""

# Check database connectivity
echo -e "${YELLOW}Database Connectivity:${NC}"
if docker-compose exec -T db pg_isready -U postgres > /dev/null 2>&1; then
    echo -e "${GREEN}✅ PostgreSQL: Accepting connections${NC}"
else
    echo -e "${RED}❌ PostgreSQL: Not accepting connections${NC}"
fi

# Check Redis connectivity
if docker-compose exec -T redis redis-cli ping | grep -q "PONG"; then
    echo -e "${GREEN}✅ Redis: Responding to ping${NC}"
else
    echo -e "${RED}❌ Redis: Not responding${NC}"
fi

echo ""

# Check Celery workers
echo -e "${YELLOW}Celery Status:${NC}"
if docker-compose exec -T celery celery -A src.workers.celery_app inspect ping > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Celery Workers: Responsive${NC}"
    
    # Get worker stats
    active_tasks=$(docker-compose exec -T celery celery -A src.workers.celery_app inspect active | grep -c "empty" || echo "0")
    echo -e "${BLUE}   Active tasks: ${active_tasks}${NC}"
else
    echo -e "${RED}❌ Celery Workers: Not responsive${NC}"
fi

echo ""

# Memory and disk usage
echo -e "${YELLOW}Resource Usage:${NC}"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" | head -10

echo ""

# Recent errors
echo -e "${YELLOW}Recent Errors (last 10):${NC}"
docker-compose logs --tail=1000 | grep -i "error" | tail -10 || echo "No recent errors found"

echo -e "\n${BLUE}=== Health Check Complete ===${NC}"