#!/bin/bash
# Start Celery Beat scheduler for VC Matching Platform

echo "Starting Celery Beat scheduler..."

# Set Python path
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Load environment variables if .env exists
if [ -f .env ]; then
    echo "Loading environment variables from .env"
    export $(cat .env | grep -v '^#' | xargs)
fi

# Default to INFO log level if not set
export LOG_LEVEL=${LOG_LEVEL:-INFO}

# Schedule file location
SCHEDULE_FILE=${SCHEDULE_FILE:-celerybeat-schedule}

echo "Configuration:"
echo "  Log Level: $LOG_LEVEL"
echo "  Schedule File: $SCHEDULE_FILE"
echo "  Redis URL: ${REDIS_URL:-redis://localhost:6379/0}"

# Create directory for schedule file if it doesn't exist
mkdir -p $(dirname $SCHEDULE_FILE)

# Start Celery Beat
celery -A src.workers.celery_app beat \
    --loglevel=$LOG_LEVEL \
    --schedule=$SCHEDULE_FILE