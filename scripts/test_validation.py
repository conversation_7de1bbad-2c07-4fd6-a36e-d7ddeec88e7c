#!/usr/bin/env python3
"""Test data validation and deduplication functionality."""

import sys
import os
import asyncio
from uuid import uuid4

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.services.validation_service import get_validation_service


def test_validation_service():
    """Test the validation service."""
    print("🔍 Testing Data Validation Service")
    print("=" * 60)
    
    validation_service = get_validation_service()
    
    # Test 1: URL validation
    print("\n1️⃣ Testing URL validation...")
    test_urls = [
        ("https://example.com", True),
        ("http://example.com", True),
        ("example.com", True),  # Should add https://
        ("https://example.com/path", True),
        ("invalid url", False),
        ("https://test.com", False),  # Spam domain
        ("", True),  # Empty is ok
        (None, True),  # None is ok
    ]
    
    for url, expected in test_urls:
        is_valid, cleaned = validation_service.validate_url(url)
        status = "✅" if is_valid == expected else "❌"
        print(f"  {status} {url} -> {cleaned if is_valid else 'Invalid'}")
    
    # Test 2: Email validation
    print("\n2️⃣ Testing email validation...")
    test_emails = [
        ("<EMAIL>", False),  # Spam domain
        ("<EMAIL>", True),
        ("invalid.email", False),
        ("<EMAIL>", False),  # Spam domain
        ("<EMAIL>", True),  # Should lowercase
        ("", True),  # Empty is ok
        (None, True),  # None is ok
    ]
    
    for email, expected in test_emails:
        is_valid, cleaned = validation_service.validate_email(email)
        status = "✅" if is_valid == expected else "❌"
        print(f"  {status} {email} -> {cleaned if is_valid else 'Invalid'}")
    
    # Test 3: Startup validation
    print("\n3️⃣ Testing startup data validation...")
    test_startups = [
        {
            "data": {"name": "A", "description": "Too short"},
            "should_fail": True,
            "reason": "Name too short, description too short"
        },
        {
            "data": {"name": "Valid Startup", "website": "invalid url"},
            "should_fail": True,
            "reason": "Invalid website URL"
        },
        {
            "data": {"name": "Valid Startup", "team_size": -5},
            "should_fail": True,
            "reason": "Negative team size"
        },
        {
            "data": {
                "name": "Valid Startup Inc",
                "website": "validstartup.com",
                "stage": "Series A",  # Should normalize to series-a
                "team_size": 50,
                "monthly_revenue": 100000
            },
            "should_fail": False,
            "reason": "All valid"
        }
    ]
    
    for test in test_startups:
        errors = validation_service.validate_startup_data(test["data"])
        has_errors = bool(errors)
        status = "✅" if has_errors == test["should_fail"] else "❌"
        print(f"  {status} {test['reason']}")
        if errors:
            print(f"      Errors: {errors}")
    
    # Test 4: VC validation
    print("\n4️⃣ Testing VC data validation...")
    test_vcs = [
        {
            "data": {"firm_name": "V", "thesis": "Too short"},
            "should_fail": True,
            "reason": "Name too short, thesis too short"
        },
        {
            "data": {"firm_name": "Valid VC", "min_check_size": 1000000, "max_check_size": 500000},
            "should_fail": True,
            "reason": "Min check > Max check"
        },
        {
            "data": {
                "firm_name": "Sequoia Capital",
                "website": "sequoiacap.com",
                "thesis": "We invest in early-stage companies with strong technical founders",
                "min_check_size": 500000,
                "max_check_size": 5000000,
                "sectors": ["ai/ml", "fintech", "saas"],
                "stages": ["seed", "series-a"]
            },
            "should_fail": False,
            "reason": "All valid"
        }
    ]
    
    for test in test_vcs:
        errors = validation_service.validate_vc_data(test["data"])
        has_errors = bool(errors)
        status = "✅" if has_errors == test["should_fail"] else "❌"
        print(f"  {status} {test['reason']}")
        if errors:
            print(f"      Errors: {errors}")
    
    # Test 5: Duplicate detection
    print("\n5️⃣ Testing duplicate detection...")
    
    # Startup duplicates
    new_startup = {"name": "OpenAI", "website": "openai.com", "sector": "ai/ml", "stage": "growth"}
    existing_startups = [
        {"name": "OpenAI", "website": "openai.com", "sector": "ai/ml", "stage": "growth"},
        {"name": "Open AI", "website": "different.com", "sector": "ai/ml", "stage": "growth"},
        {"name": "Anthropic", "website": "anthropic.com", "sector": "ai/ml", "stage": "growth"},
    ]
    
    duplicates = validation_service.find_duplicate_startups(new_startup, existing_startups)
    print(f"  Startup duplicates found: {len(duplicates)}")
    for dup in duplicates:
        print(f"    - Score: {dup['score']:.1f}, Reason: {dup['reason']}")
    
    # VC duplicates
    new_vc = {"firm_name": "Sequoia Capital", "website": "sequoiacap.com"}
    existing_vcs = [
        {"firm_name": "Sequoia Capital", "website": "sequoiacap.com"},
        {"firm_name": "Sequoia", "website": "different.com"},
        {"firm_name": "Andreessen Horowitz", "website": "a16z.com"},
    ]
    
    duplicates = validation_service.find_duplicate_vcs(new_vc, existing_vcs)
    print(f"\n  VC duplicates found: {len(duplicates)}")
    for dup in duplicates:
        print(f"    - Score: {dup['score']:.1f}, Reason: {dup['reason']}")
    
    print("\n✅ Validation testing complete!")


if __name__ == "__main__":
    test_validation_service()