#!/usr/bin/env python3
"""
Database setup and connection verification script.
This script handles database initialization for both local and Docker environments.
"""

import sys
import os
import asyncio
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.database.setup import get_async_engine, async_init_db, get_async_db
from src.core.config import settings
from sqlalchemy import text


async def verify_connection():
    """Verify database connection is working."""
    print(f"🔍 Testing connection to: {settings.async_database_url}")
    
    try:
        engine = get_async_engine()
        
        # Test basic connection
        async with engine.begin() as conn:
            result = await conn.execute(text("SELECT 1"))
            print("✅ Database connection successful!")
            
            # Check PostgreSQL version
            result = await conn.execute(text("SELECT version()"))  
            version = result.scalar()
            print(f"✅ PostgreSQL version: {version}")
        
        return True
        
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False


async def initialize_database():
    """Initialize database tables."""
    print("\n🔧 Initializing database tables...")
    
    try:
        await async_init_db()
        print("✅ Database tables created successfully!")
        
        # Verify tables exist
        engine = get_async_engine()
        async with engine.begin() as conn:
            result = await conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                ORDER BY table_name
            """))
            tables = [row[0] for row in result]
            print(f"✅ Tables available: {', '.join(tables)}")
            
        return True
        
    except Exception as e:
        print(f"❌ Table initialization failed: {e}")
        return False


async def test_session_factory():
    """Test that session factory works correctly."""
    print("\n🧪 Testing session factory...")
    
    try:
        async for session in get_async_db():
            # Test basic query
            result = await session.execute(text("SELECT COUNT(*) FROM startups"))
            count = result.scalar()
            print(f"✅ Session factory working - Startup count: {count}")
            
            # Test another table
            result = await session.execute(text("SELECT COUNT(*) FROM vcs"))
            count = result.scalar()
            print(f"✅ VC count: {count}")
            
            # Test matches table
            result = await session.execute(text("SELECT COUNT(*) FROM matches"))
            count = result.scalar()
            print(f"✅ Matches count: {count}")
            
            break
            
        return True
        
    except Exception as e:
        print(f"❌ Session factory test failed: {e}")
        return False


def print_setup_instructions():
    """Print setup instructions based on current environment."""
    print("\n" + "="*60)
    print("📋 DATABASE SETUP INSTRUCTIONS")
    print("="*60)
    
    if "localhost" in settings.database_url:
        print("🏠 LOCAL DEVELOPMENT MODE")
        print("\n1. Install PostgreSQL:")
        print("   brew install postgresql")
        print("   brew services start postgresql")
        
        print("\n2. Create database:")
        print("   createdb vc_matching_platform")
        
        print("\n3. Install Python dependencies:")
        print("   pip install -r requirements.txt")
        
        print("\n4. Run this setup script:")
        print("   python scripts/setup_database.py")
        
    else:
        print("🐳 DOCKER MODE")
        print("\n1. Start Docker services:")
        print("   docker-compose up -d db redis")
        
        print("\n2. Run database migrations:")
        print("   docker-compose exec api alembic upgrade head")
        
        print("\n3. Verify connection:")
        print("   docker-compose exec api python scripts/setup_database.py")
        
    print("\n" + "="*60)


async def main():
    """Main setup function."""
    print("🚀 VC Matching Platform - Database Setup")
    print("="*50)
    
    # Step 1: Verify connection
    if not await verify_connection():
        print_setup_instructions()
        sys.exit(1)
    
    # Step 2: Initialize database
    if not await initialize_database():
        sys.exit(1)
    
    # Step 3: Test session factory
    if not await test_session_factory():
        sys.exit(1)
    
    print("\n🎉 Database setup completed successfully!")
    print("✅ Ready for development!")
    
    # Update WORKING_FEATURES.md
    try:
        features_file = project_root / "WORKING_FEATURES.md"
        if features_file.exists():
            content = features_file.read_text()
            updated_content = content.replace(
                "- [ ] PostgreSQL database connection",
                "- [x] PostgreSQL database connection"
            ).replace(
                "Database connected: ❌",
                "Database connected: ✅"
            )
            features_file.write_text(updated_content)
            print("✅ Updated WORKING_FEATURES.md")
    except Exception as e:
        print(f"⚠️  Could not update WORKING_FEATURES.md: {e}")


if __name__ == "__main__":
    asyncio.run(main())