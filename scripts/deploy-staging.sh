#!/bin/bash

# Staging Deployment Script
# This script deploys the VC Matching Platform to staging environment

set -e  # Exit on error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="vc-matching-platform"
COMPOSE_FILE="docker-compose.yml"
COMPOSE_STAGING_FILE="docker-compose.staging.yml"
ENV_FILE=".env"
ENV_STAGING_FILE=".env.staging"

echo -e "${GREEN}🚀 Starting staging deployment for ${PROJECT_NAME}${NC}"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker is not running. Please start Docker and try again.${NC}"
    exit 1
fi

# Check if docker-compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ docker-compose is not installed. Please install it and try again.${NC}"
    exit 1
fi

# Check for required files
echo -e "${YELLOW}📋 Checking required files...${NC}"
required_files=(
    "$COMPOSE_FILE"
    "$COMPOSE_STAGING_FILE"
    "Dockerfile"
    "requirements.txt"
    "alembic.ini"
)

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo -e "${RED}❌ Missing required file: $file${NC}"
        exit 1
    fi
done

# Setup environment file
if [ ! -f "$ENV_FILE" ]; then
    if [ -f "$ENV_STAGING_FILE" ]; then
        echo -e "${YELLOW}📝 Creating .env from .env.staging${NC}"
        cp "$ENV_STAGING_FILE" "$ENV_FILE"
        echo -e "${YELLOW}⚠️  Please update the .env file with your actual values${NC}"
    else
        echo -e "${RED}❌ No .env file found. Please create one from .env.staging${NC}"
        exit 1
    fi
fi

# Create necessary directories
echo -e "${YELLOW}📁 Creating necessary directories...${NC}"
mkdir -p nginx/conf.d
mkdir -p monitoring
mkdir -p scripts/postgres

# Stop existing containers
echo -e "${YELLOW}🛑 Stopping existing containers...${NC}"
docker-compose -f "$COMPOSE_FILE" -f "$COMPOSE_STAGING_FILE" down

# Remove old containers and volumes (optional - comment out to preserve data)
# echo -e "${YELLOW}🗑️  Removing old containers and volumes...${NC}"
# docker-compose -f "$COMPOSE_FILE" -f "$COMPOSE_STAGING_FILE" down -v

# Build images
echo -e "${YELLOW}🔨 Building Docker images...${NC}"
docker-compose -f "$COMPOSE_FILE" -f "$COMPOSE_STAGING_FILE" build --no-cache

# Start services
echo -e "${YELLOW}🚀 Starting services...${NC}"
docker-compose -f "$COMPOSE_FILE" -f "$COMPOSE_STAGING_FILE" up -d

# Wait for services to be healthy
echo -e "${YELLOW}⏳ Waiting for services to be healthy...${NC}"
sleep 10

# Check service health
services=("db" "redis" "api" "celery" "celery-beat" "flower" "nginx")
all_healthy=true

for service in "${services[@]}"; do
    if docker-compose -f "$COMPOSE_FILE" -f "$COMPOSE_STAGING_FILE" ps | grep -q "${service}.*Up"; then
        echo -e "${GREEN}✅ ${service} is running${NC}"
    else
        echo -e "${RED}❌ ${service} is not running${NC}"
        all_healthy=false
    fi
done

if [ "$all_healthy" = true ]; then
    echo -e "${GREEN}✅ All services are running!${NC}"
    
    # Display service URLs
    echo -e "\n${GREEN}🌐 Service URLs:${NC}"
    echo -e "  API:        http://localhost/api/v1/docs"
    echo -e "  Flower:     http://localhost/flower/"
    echo -e "  Health:     http://localhost/health"
    
    if docker-compose -f "$COMPOSE_FILE" -f "$COMPOSE_STAGING_FILE" ps | grep -q "prometheus.*Up"; then
        echo -e "  Prometheus: http://localhost:9090"
    fi
    
    if docker-compose -f "$COMPOSE_FILE" -f "$COMPOSE_STAGING_FILE" ps | grep -q "grafana.*Up"; then
        echo -e "  Grafana:    http://localhost:3000"
    fi
    
    # Show logs
    echo -e "\n${YELLOW}📋 Recent logs:${NC}"
    docker-compose -f "$COMPOSE_FILE" -f "$COMPOSE_STAGING_FILE" logs --tail=20 api
    
    echo -e "\n${GREEN}🎉 Staging deployment complete!${NC}"
    echo -e "${YELLOW}📝 To view logs: docker-compose -f ${COMPOSE_FILE} -f ${COMPOSE_STAGING_FILE} logs -f [service]${NC}"
    echo -e "${YELLOW}🛑 To stop: docker-compose -f ${COMPOSE_FILE} -f ${COMPOSE_STAGING_FILE} down${NC}"
else
    echo -e "${RED}❌ Some services failed to start. Check logs with:${NC}"
    echo -e "docker-compose -f ${COMPOSE_FILE} -f ${COMPOSE_STAGING_FILE} logs"
    exit 1
fi