version: '3.8'

# Staging-specific overrides
services:
  # API with staging configurations
  api:
    environment:
      - ENVIRONMENT=staging
      - LOG_LEVEL=INFO
      - WORKERS=2  # Fewer workers for staging
      - USE_ASYNC_DB=${USE_ASYNC_DB:-true}  # Enable async in staging
      - RATE_LIMIT_ENABLED=true
      - RATE_LIMIT_PER_MINUTE=100
    restart: unless-stopped
    labels:
      - "com.vc-matching.service=api"
      - "com.vc-matching.environment=staging"

  # Celery with staging queue configurations
  celery:
    environment:
      - LOG_LEVEL=INFO
      - CELERY_TASK_ALWAYS_EAGER=false
      - CELERY_TASK_EAGER_PROPAGATES=false
    restart: unless-stopped
    deploy:
      replicas: 2  # Run 2 workers in staging
    command: >
      celery -A src.workers.celery_app worker 
      --loglevel=info 
      --concurrency=2
      --queues=default,ai_analysis,data_enrichment,scraping
    labels:
      - "com.vc-matching.service=celery-worker"
      - "com.vc-matching.environment=staging"

  # Enable nginx in staging
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx/staging.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - api
    restart: unless-stopped
    labels:
      - "com.vc-matching.service=nginx"
      - "com.vc-matching.environment=staging"

  # PostgreSQL with staging configurations
  db:
    environment:
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-staging-password-change-me}
    volumes:
      - postgres_data_staging:/var/lib/postgresql/data
      - ./scripts/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    restart: unless-stopped

  # Redis with persistence
  redis:
    command: >
      redis-server 
      --appendonly yes 
      --appendfsync everysec
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
    volumes:
      - redis_data_staging:/data
    restart: unless-stopped

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: vc_matching_prometheus
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
    restart: unless-stopped
    labels:
      - "com.vc-matching.service=prometheus"
      - "com.vc-matching.environment=staging"

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: vc_matching_grafana
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
    ports:
      - "3000:3000"
    restart: unless-stopped
    depends_on:
      - prometheus
    labels:
      - "com.vc-matching.service=grafana"
      - "com.vc-matching.environment=staging"

volumes:
  postgres_data_staging:
  redis_data_staging:
  nginx_logs:
  prometheus_data:
  grafana_data: