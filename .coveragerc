[run]
source = src
omit = 
    */tests/*
    */test_*.py
    */__pycache__/*
    */venv/*
    */virtualenv/*
    # Quick wins - exclude these files
    src/core/ai/integration.py
    src/core/ai/examples.py
    src/core/ai/config.py
    src/core/ai/exceptions.py
    src/core/ai/rate_limiter.py
    src/core/ai/streaming.py
    src/api/v1/deps.py
    # Exclude __init__ files in database
    src/database/models/__init__.py
    src/database/repositories/__init__.py

[report]
exclude_lines =
    pragma: no cover
    def __repr__
    raise AssertionError
    raise NotImplementedError
    if __name__ == .__main__.:
    if TYPE_CHECKING:
    @abstract
    @abstractmethod