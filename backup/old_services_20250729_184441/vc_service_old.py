"""Service layer for VC business logic.

This service encapsulates all business logic related to VCs,
coordinating between repositories and external services like AI analysis.
Following DDD principles, it returns domain objects, not HTTP responses.
"""
from typing import List, Optional, Dict, Any
from uuid import UUID, uuid4
from dataclasses import replace

from src.core.models.vc import VC
from src.core.repositories.vc_repository import VCRepository
from src.core.ai.analyzer import AIAnalyzerService
from src.core.ai.models import VCThesisAnalysis


class VCService:
    """Service for managing VC-related business logic.
    
    This service coordinates between the repository layer and AI services,
    ensuring proper separation of concerns and encapsulation of business rules.
    """
    
    def __init__(
        self,
        repository: VCRepository,
        ai_analyzer: AIAnalyzerService
    ):
        """Initialize the VC service with required dependencies.
        
        Args:
            repository: Repository for VC data persistence
            ai_analyzer: AI service for analyzing VCs
        """
        self.repository = repository
        self.ai_analyzer = ai_analyzer
    
    async def create_vc(self, vc: VC) -> VC:
        """Create a new VC.
        
        Assigns an ID if not provided and saves to repository.
        
        Args:
            vc: VC domain object to create
            
        Returns:
            Created VC with ID assigned
            
        Raises:
            ValueError: If VC validation fails
        """
        # Validate required fields
        if not vc.firm_name:
            raise ValueError("VC firm name is required")
        
        # Assign ID if not provided
        if vc.id is None:
            vc = replace(vc, id=uuid4())
        
        # Save to repository
        saved_vc = await self.repository.save(vc)
        return saved_vc
    
    async def get_vc(self, vc_id: UUID) -> VC:
        """Retrieve a VC by ID.
        
        Args:
            vc_id: UUID of the VC to retrieve
            
        Returns:
            The VC if found
            
        Raises:
            ValueError: If VC not found
        """
        vc = await self.repository.find_by_id(vc_id)
        if vc is None:
            raise ValueError(f"VC with id {vc_id} not found")
        return vc
    
    async def list_vcs(
        self,
        sector: Optional[str] = None,
        stage: Optional[str] = None
    ) -> List[VC]:
        """List VCs with optional filters.
        
        Args:
            sector: Filter by investment sector if provided
            stage: Filter by investment stage if provided
            
        Returns:
            List of VCs matching the filters
        """
        if sector:
            return await self.repository.find_by_sector(sector)
        elif stage:
            return await self.repository.find_by_stage(stage)
        else:
            return await self.repository.find_all()
    
    async def extract_thesis(
        self,
        vc_id: UUID,
        website_content: str,
        force_refresh: bool = False
    ) -> VCThesisAnalysis:
        """Extract VC investment thesis using AI from website content.
        
        Updates the VC with extracted information and saves it.
        
        Args:
            vc_id: UUID of the VC to analyze
            website_content: Scraped content from VC's website
            force_refresh: Whether to bypass cache
            
        Returns:
            AI analysis of the VC's investment thesis
            
        Raises:
            ValueError: If VC not found
            Exception: If AI analysis fails
        """
        # First, get the VC
        vc = await self.get_vc(vc_id)
        
        # Perform AI analysis
        analysis = await self.ai_analyzer.extract_vc_thesis(
            vc,
            website_content,
            use_cache=not force_refresh
        )
        
        # Update VC with extracted information
        # Note: This is already done in the AIAnalyzerService.extract_vc_thesis method
        # but we ensure it's saved to the repository
        await self.repository.save(vc)
        
        return analysis
    
    async def update_vc(
        self,
        vc_id: UUID,
        updates: Dict[str, Any]
    ) -> VC:
        """Update an existing VC.
        
        Args:
            vc_id: UUID of the VC to update
            updates: Dictionary of fields to update
            
        Returns:
            Updated VC
            
        Raises:
            ValueError: If VC not found
        """
        # Get existing VC
        vc = await self.get_vc(vc_id)
        
        # Apply updates
        updated_vc = replace(vc, **updates)
        
        # Save updated VC
        saved_vc = await self.repository.save(updated_vc)
        return saved_vc
    
    async def delete_vc(self, vc_id: UUID) -> bool:
        """Delete a VC.
        
        Args:
            vc_id: UUID of the VC to delete
            
        Returns:
            True if deleted, False if not found
        """
        return await self.repository.delete(vc_id)