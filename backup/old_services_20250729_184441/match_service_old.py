"""Service layer for match operations.

This service encapsulates the business logic for creating, managing,
and analyzing matches between startups and VCs.
"""

from typing import List, Optional, Dict, Any
from uuid import UUID, uuid4
from datetime import datetime

from src.core.models.match import Match
from src.core.models.startup import Startup
from src.core.models.vc import VC
from src.core.repositories.startup_repository import StartupRepository
from src.core.repositories.vc_repository import VCRepository
from src.core.schemas.match import MatchStatus, MatchType


class MatchService:
    """Service for managing matches between startups and VCs."""
    
    def __init__(
        self,
        startup_repository: StartupRepository,
        vc_repository: VCRepository
    ):
        self.startup_repo = startup_repository
        self.vc_repo = vc_repository
        # In-memory storage for matches (would be a repository in production)
        self._matches: Dict[UUID, Match] = {}
    
    async def create_match(
        self,
        startup_id: UUID,
        vc_id: UUID,
        match_type: MatchType = MatchType.MANUAL,
        notes: Optional[str] = None
    ) -> Match:
        """Create a new match between a startup and VC."""
        # Check if match already exists
        existing = self._find_existing_match(startup_id, vc_id)
        if existing:
            raise ValueError("Match already exists between this startup and VC")
        
        # Get startup and VC
        startup = await self.startup_repo.get(startup_id)
        if not startup:
            raise ValueError(f"Startup with id {startup_id} not found")
        
        vc = await self.vc_repo.get(vc_id)
        if not vc:
            raise ValueError(f"VC with id {vc_id} not found")
        
        # Calculate match score and reasons
        score, reasons = self._calculate_match_score(startup, vc)
        
        # Create match
        match = Match(
            startup=startup,
            vc=vc,
            score=score,
            reasons=reasons
        )
        match.id = uuid4()
        match.status = MatchStatus.PENDING
        match.match_type = match_type
        match.notes = notes
        match.created_at = datetime.utcnow()
        match.updated_at = datetime.utcnow()
        
        # Store match
        self._matches[match.id] = match
        
        return match
    
    async def list_matches(
        self,
        startup_id: Optional[UUID] = None,
        vc_id: Optional[UUID] = None,
        status: Optional[MatchStatus] = None,
        min_score: Optional[float] = None
    ) -> List[Match]:
        """List matches with optional filters."""
        matches = list(self._matches.values())
        
        # Apply filters
        if startup_id:
            matches = [m for m in matches if m.startup.id == startup_id]
        
        if vc_id:
            matches = [m for m in matches if m.vc.id == vc_id]
        
        if status:
            matches = [m for m in matches if getattr(m, 'status', MatchStatus.PENDING) == status]
        
        if min_score is not None:
            matches = [m for m in matches if m.score >= min_score]
        
        # Sort by score descending
        matches.sort(key=lambda m: m.score, reverse=True)
        
        return matches
    
    async def get_match(self, match_id: UUID) -> Match:
        """Get a specific match by ID."""
        match = self._matches.get(match_id)
        if not match:
            raise ValueError(f"Match with id {match_id} not found")
        return match
    
    async def update_match(self, match_id: UUID, update_data: Dict[str, Any]) -> Match:
        """Update a match."""
        match = await self.get_match(match_id)
        
        # Update allowed fields
        if 'status' in update_data:
            match.status = update_data['status']
        
        if 'notes' in update_data:
            match.notes = update_data['notes']
        
        match.updated_at = datetime.utcnow()
        
        return match
    
    async def delete_match(self, match_id: UUID) -> bool:
        """Delete a match."""
        if match_id in self._matches:
            del self._matches[match_id]
            return True
        return False
    
    async def batch_match(
        self,
        startup_ids: List[UUID],
        vc_ids: List[UUID],
        match_type: MatchType = MatchType.AI_ENHANCED,
        min_score_threshold: float = 0.0
    ) -> List[Match]:
        """Create matches for multiple startups and VCs."""
        matches = []
        
        for startup_id in startup_ids:
            for vc_id in vc_ids:
                try:
                    # Check if match already exists
                    if self._find_existing_match(startup_id, vc_id):
                        continue
                    
                    # Create match
                    match = await self.create_match(
                        startup_id=startup_id,
                        vc_id=vc_id,
                        match_type=match_type
                    )
                    
                    # Only include if above threshold
                    if match.score >= min_score_threshold:
                        matches.append(match)
                    else:
                        # Remove match if below threshold
                        await self.delete_match(match.id)
                
                except ValueError:
                    # Skip if startup or VC not found
                    continue
        
        return matches
    
    def _find_existing_match(self, startup_id: UUID, vc_id: UUID) -> Optional[Match]:
        """Check if a match already exists between startup and VC."""
        for match in self._matches.values():
            if match.startup.id == startup_id and match.vc.id == vc_id:
                return match
        return None
    
    def _calculate_match_score(self, startup: Startup, vc: VC) -> tuple[float, List[str]]:
        """Calculate match score between startup and VC."""
        score = 0.0
        reasons = []
        
        # Stage matching (0-40 points)
        if vc.can_invest_in_stage(startup.stage):
            score += 0.4
            reasons.append("Stage match")
        else:
            reasons.append("Stage mismatch")
        
        # Funding amount matching (0-20 points)
        if startup.funding_amount and vc.matches_funding_amount(startup.funding_amount):
            score += 0.2
            reasons.append("Funding amount in range")
        
        # Sector alignment (0-40 points)
        startup_sectors = startup.extract_sectors()
        if vc.sectors:
            matching_sectors = set(startup_sectors) & set(vc.sectors)
            if matching_sectors:
                sector_score = len(matching_sectors) / max(len(vc.sectors), 1)
                score += 0.4 * sector_score
                reasons.append(f"Sector alignment: {', '.join(matching_sectors)}")
            else:
                reasons.append("No sector match")
        else:
            # If VC has no specific sectors, give partial credit
            score += 0.2
            reasons.append("VC invests across sectors")
        
        return min(score, 1.0), reasons