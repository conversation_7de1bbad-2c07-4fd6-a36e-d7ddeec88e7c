"""Service layer for Startup business logic.

This service encapsulates all business logic related to startups,
coordinating between repositories and external services like AI analysis.
Following DDD principles, it returns domain objects, not HTTP responses.
"""
from typing import List, Optional, Dict, Any
from uuid import UUID, uuid4
from dataclasses import replace

from src.core.models.startup import Startup
from src.core.repositories.startup_repository import StartupRepository
from src.core.ai.analyzer import AIAnalyzerService
from src.core.ai.models import StartupAnalysis


class StartupService:
    """Service for managing startup-related business logic.
    
    This service coordinates between the repository layer and AI services,
    ensuring proper separation of concerns and encapsulation of business rules.
    """
    
    def __init__(
        self,
        repository: StartupRepository,
        ai_analyzer: AIAnalyzerService
    ):
        """Initialize the startup service with required dependencies.
        
        Args:
            repository: Repository for startup data persistence
            ai_analyzer: AI service for analyzing startups
        """
        self.repository = repository
        self.ai_analyzer = ai_analyzer
    
    async def create_startup(self, startup: Startup) -> Startup:
        """Create a new startup.
        
        Assigns an ID if not provided and saves to repository.
        
        Args:
            startup: Startup domain object to create
            
        Returns:
            Created startup with ID assigned
            
        Raises:
            ValueError: If startup validation fails
        """
        # Validation is handled by the domain model's __post_init__
        # This will raise ValueError if name is empty
        _ = startup.name  # Trigger validation
        
        # Assign ID if not provided
        if startup.id is None:
            startup = replace(startup, id=uuid4())
        
        # Save to repository
        saved_startup = await self.repository.save(startup)
        return saved_startup
    
    async def get_startup(self, startup_id: UUID) -> Startup:
        """Retrieve a startup by ID.
        
        Args:
            startup_id: UUID of the startup to retrieve
            
        Returns:
            The startup if found
            
        Raises:
            ValueError: If startup not found
        """
        startup = await self.repository.find_by_id(startup_id)
        if startup is None:
            raise ValueError(f"Startup with id {startup_id} not found")
        return startup
    
    async def list_startups(
        self,
        sector: Optional[str] = None,
        stage: Optional[str] = None
    ) -> List[Startup]:
        """List startups with optional filters.
        
        Args:
            sector: Filter by sector if provided
            stage: Filter by funding stage if provided
            
        Returns:
            List of startups matching the filters
        """
        if sector:
            return await self.repository.find_by_sector(sector)
        elif stage:
            return await self.repository.find_by_stage(stage)
        else:
            return await self.repository.find_all()
    
    async def analyze_startup(
        self,
        startup_id: UUID,
        force_refresh: bool = False
    ) -> StartupAnalysis:
        """Analyze a startup using AI to extract key information.
        
        Args:
            startup_id: UUID of the startup to analyze
            force_refresh: Whether to bypass cache
            
        Returns:
            AI analysis of the startup
            
        Raises:
            ValueError: If startup not found
            Exception: If AI analysis fails
        """
        # First, get the startup
        startup = await self.get_startup(startup_id)
        
        # Perform AI analysis
        analysis = await self.ai_analyzer.analyze_startup(
            startup,
            use_cache=not force_refresh
        )
        
        return analysis
    
    async def update_startup(
        self,
        startup_id: UUID,
        updates: Dict[str, Any]
    ) -> Startup:
        """Update an existing startup.
        
        Args:
            startup_id: UUID of the startup to update
            updates: Dictionary of fields to update
            
        Returns:
            Updated startup
            
        Raises:
            ValueError: If startup not found
        """
        # Get existing startup
        startup = await self.get_startup(startup_id)
        
        # Apply updates
        updated_startup = replace(startup, **updates)
        
        # Save updated startup
        saved_startup = await self.repository.save(updated_startup)
        return saved_startup
    
    async def delete_startup(self, startup_id: UUID) -> bool:
        """Delete a startup.
        
        Args:
            startup_id: UUID of the startup to delete
            
        Returns:
            True if deleted, False if not found
        """
        return await self.repository.delete(startup_id)