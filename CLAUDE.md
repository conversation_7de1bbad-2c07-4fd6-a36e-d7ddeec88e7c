# Claude Code Integration Guide

This file provides context and instructions for Claude Code when working on the VC-Startup Matching Platform.

## Project Overview
A bilateral matching platform connecting startups with VCs through AI-powered analysis. Unlike competitors, VCs can actively search for startups matching their thesis.

## Specialized Sub-Agents

This project includes specialized sub-agents for different aspects of development. Use the Task tool to invoke them:

### Available Agents:
1. **ddd_expert** - Domain-Driven Design for clean architecture
2. **tdd_specialist** - Test-Driven Development with pytest
3. **fastapi_architect** - FastAPI best practices and patterns
4. **ai_langchain_expert** - AI/LLM integration strategies
5. **web_scraping_expert** - Playwright and scraping techniques
6. **background_tasks_expert** - Celery and async task processing
7. **database_performance_expert** - PostgreSQL optimization
8. **devops_deployment_expert** - Docker and deployment
9. **test_coverage_expert** - Coverage analysis and improvement strategies
10. **refactoring_expert** - Code cleanup and structural improvements

### Using Sub-Agents:
```
Task: "Using the ddd_expert agent, design a domain model for investment preferences"
```

## Current Status
- ✅ Core domain models (Startup, VC, Match)
- ✅ Matching engine with scoring algorithm
- ✅ TDD approach with pytest
- ✅ Sub-agent system for specialized tasks

## Testing Commands
```bash
# Run all tests
PYTHONPATH=. pytest tests/ -v

# Run specific test file
PYTHONPATH=. pytest tests/unit/test_startup.py -v

# Run with coverage
PYTHONPATH=. pytest tests/ --cov=src --cov-report=html
```

## Project Conventions
- Always write tests first (TDD)
- Domain models have no external dependencies
- Use type hints everywhere
- Follow DDD principles for complex business logic
- Use dependency injection in FastAPI

## Next Development Phases
1. API Layer - Use fastapi_architect agent
2. AI Integration - Use ai_langchain_expert agent
3. Database Layer - Use database_performance_expert agent
4. Web Scraping - Use web_scraping_expert agent

## Important Files
- `agents/prompts/` - Sub-agent prompt definitions
- `agents/USAGE_GUIDE.md` - Detailed agent usage instructions
- `src/core/models/` - Pure domain models
- `src/core/services/` - Business logic
- `tests/` - All test files (run these frequently)

When in doubt about which agent to use, check `agents/USAGE_GUIDE.md` or use the agent_loader to suggest one based on the task.