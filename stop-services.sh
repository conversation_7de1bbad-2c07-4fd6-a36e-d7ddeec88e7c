#!/bin/bash
# Stop all local services

echo "Stopping VC Matching Platform services..."

# Function to stop service by PID file
stop_service() {
    local service_name=$1
    local pid_file="logs/${service_name}.pid"

    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            echo "Stopping $service_name (PID: $pid)..."
            kill "$pid"
            rm "$pid_file"
        else
            echo "$service_name is not running"
            rm "$pid_file" 2>/dev/null || true
        fi
    else
        echo "No PID file found for $service_name"
    fi
}

# Stop all services
stop_service "api"
stop_service "celery-worker"
stop_service "celery-beat"
stop_service "flower"

echo "All services stopped"
