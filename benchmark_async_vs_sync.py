"""Performance benchmark comparing sync vs async database operations."""

import asyncio
import time
from uuid import uuid4
from statistics import mean, stdev
import logging
from typing import List, Tuple

from src.database.setup import get_session_factory, get_engine
from src.database.async_setup import AsyncSessionLocal, async_engine
from src.database.repositories.startup_repository import StartupRepository
from src.database.repositories.async_startup_repository import AsyncStartupRepository
from src.core.models.startup import Startup as StartupDomainModel

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BenchmarkResults:
    """Store benchmark results."""
    
    def __init__(self, name: str):
        self.name = name
        self.times: List[float] = []
    
    def add_time(self, elapsed: float):
        self.times.append(elapsed)
    
    def get_stats(self) -> dict:
        if not self.times:
            return {"mean": 0, "stdev": 0, "min": 0, "max": 0}
        
        return {
            "mean": mean(self.times),
            "stdev": stdev(self.times) if len(self.times) > 1 else 0,
            "min": min(self.times),
            "max": max(self.times),
            "total": sum(self.times)
        }


async def benchmark_sync_operations(num_operations: int) -> BenchmarkResults:
    """Benchmark synchronous database operations."""
    results = BenchmarkResults("Sync Operations")
    
    SessionLocal = get_session_factory()
    with SessionLocal() as session:
        repo = StartupRepository(session)
        
        logger.info(f"Starting sync benchmark with {num_operations} operations...")
        
        # Benchmark creates
        created_ids = []
        for i in range(num_operations):
            startup = StartupDomainModel(
                id=uuid4(),
                name=f"Sync Startup {i}",
                sector="Technology",
                stage="Series A",
                description=f"Benchmark sync startup {i}",
                website=f"https://sync-{i}.com",
                team_size=10 + i,
                monthly_revenue=50000.0 * (i + 1)
            )
            
            start = time.time()
            created = repo.save(startup)
            elapsed = time.time() - start
            results.add_time(elapsed)
            created_ids.append(created.id)
        
        # Benchmark reads
        read_results = BenchmarkResults("Sync Reads")
        for startup_id in created_ids[:10]:  # Read first 10
            start = time.time()
            _ = repo.find_by_id(startup_id)
            elapsed = time.time() - start
            read_results.add_time(elapsed)
        
        # Benchmark searches
        search_results = BenchmarkResults("Sync Searches")
        for i in range(5):
            start = time.time()
            _ = repo.find_by_sector("Technology")
            elapsed = time.time() - start
            search_results.add_time(elapsed)
        
        # Cleanup
        for startup_id in created_ids:
            repo.delete(startup_id)
    
    return results, read_results, search_results


async def benchmark_async_operations(num_operations: int) -> BenchmarkResults:
    """Benchmark asynchronous database operations."""
    results = BenchmarkResults("Async Operations")
    
    async with AsyncSessionLocal() as session:
        repo = AsyncStartupRepository(session)
        
        logger.info(f"Starting async benchmark with {num_operations} operations...")
        
        # Benchmark creates
        created_ids = []
        for i in range(num_operations):
            startup = StartupDomainModel(
                id=uuid4(),
                name=f"Async Startup {i}",
                sector="Technology",
                stage="Series A",
                description=f"Benchmark async startup {i}",
                website=f"https://async-{i}.com",
                team_size=10 + i,
                monthly_revenue=50000.0 * (i + 1)
            )
            
            start = time.time()
            created = await repo.save(startup)
            elapsed = time.time() - start
            results.add_time(elapsed)
            created_ids.append(created.id)
        
        # Benchmark reads
        read_results = BenchmarkResults("Async Reads")
        for startup_id in created_ids[:10]:  # Read first 10
            start = time.time()
            _ = await repo.find_by_id(startup_id)
            elapsed = time.time() - start
            read_results.add_time(elapsed)
        
        # Benchmark searches
        search_results = BenchmarkResults("Async Searches")
        for i in range(5):
            start = time.time()
            _ = await repo.find_by_sector("Technology")
            elapsed = time.time() - start
            search_results.add_time(elapsed)
        
        # Cleanup
        for startup_id in created_ids:
            await repo.delete(startup_id)
    
    return results, read_results, search_results


async def benchmark_concurrent_async(num_concurrent: int) -> float:
    """Benchmark concurrent async operations."""
    async with AsyncSessionLocal() as session:
        repo = AsyncStartupRepository(session)
        
        async def create_startup(index: int):
            startup = StartupDomainModel(
                id=uuid4(),
                name=f"Concurrent Startup {index}",
                sector="Technology",
                stage="Series B",
                description=f"Concurrent test {index}",
                website=f"https://concurrent-{index}.com",
                team_size=20,
                monthly_revenue=100000.0
            )
            created = await repo.save(startup)
            # Clean up immediately
            await repo.delete(created.id)
            return created
        
        start = time.time()
        # Run concurrent operations
        tasks = [create_startup(i) for i in range(num_concurrent)]
        await asyncio.gather(*tasks)
        elapsed = time.time() - start
        
        return elapsed


def print_comparison(sync_stats: dict, async_stats: dict, operation: str):
    """Print comparison between sync and async stats."""
    improvement = ((sync_stats["mean"] - async_stats["mean"]) / sync_stats["mean"]) * 100
    
    print(f"\n{operation}:")
    print(f"  Sync:  mean={sync_stats['mean']:.4f}s, stdev={sync_stats['stdev']:.4f}s")
    print(f"  Async: mean={async_stats['mean']:.4f}s, stdev={async_stats['stdev']:.4f}s")
    print(f"  Improvement: {improvement:.1f}%")


async def main():
    """Run the benchmark."""
    print("=" * 60)
    print("Async vs Sync Database Performance Benchmark")
    print("=" * 60)
    
    num_operations = 50
    
    # Run sync benchmark
    print("\nRunning synchronous benchmark...")
    sync_create, sync_read, sync_search = await benchmark_sync_operations(num_operations)
    
    # Run async benchmark
    print("\nRunning asynchronous benchmark...")
    async_create, async_read, async_search = await benchmark_async_operations(num_operations)
    
    # Print results
    print("\n" + "=" * 60)
    print("RESULTS")
    print("=" * 60)
    
    # Compare creates
    sync_create_stats = sync_create.get_stats()
    async_create_stats = async_create.get_stats()
    print_comparison(sync_create_stats, async_create_stats, "CREATE Operations")
    
    # Compare reads
    sync_read_stats = sync_read.get_stats()
    async_read_stats = async_read.get_stats()
    print_comparison(sync_read_stats, async_read_stats, "READ Operations")
    
    # Compare searches
    sync_search_stats = sync_search.get_stats()
    async_search_stats = async_search.get_stats()
    print_comparison(sync_search_stats, async_search_stats, "SEARCH Operations")
    
    # Test concurrent operations
    print("\n" + "=" * 60)
    print("CONCURRENCY TEST")
    print("=" * 60)
    
    concurrent_counts = [10, 50, 100]
    for count in concurrent_counts:
        elapsed = await benchmark_concurrent_async(count)
        ops_per_second = count / elapsed
        print(f"\n{count} concurrent operations:")
        print(f"  Total time: {elapsed:.2f}s")
        print(f"  Operations/second: {ops_per_second:.1f}")
    
    # Summary
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    
    total_sync_time = sync_create_stats["total"] + sync_read_stats["total"] + sync_search_stats["total"]
    total_async_time = async_create_stats["total"] + async_read_stats["total"] + async_search_stats["total"]
    overall_improvement = ((total_sync_time - total_async_time) / total_sync_time) * 100
    
    print(f"\nTotal sync time: {total_sync_time:.2f}s")
    print(f"Total async time: {total_async_time:.2f}s")
    print(f"Overall improvement: {overall_improvement:.1f}%")
    print(f"Performance multiplier: {total_sync_time / total_async_time:.2f}x")
    
    # Cleanup
    await async_engine.dispose()
    get_engine().dispose()


if __name__ == "__main__":
    asyncio.run(main())