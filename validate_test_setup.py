#!/usr/bin/env python3
"""
Quick validation script to check if the integration test environment is ready.

This script performs pre-flight checks before running the main integration test suite.
"""

import sys
import asyncio
import importlib
from pathlib import Path

# Add project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    BOLD = '\033[1m'
    END = '\033[0m'


async def validate_setup():
    """Validate the test setup."""
    print(f"{Colors.BOLD}{Colors.BLUE}🔍 Validating Integration Test Setup{Colors.END}")
    print("=" * 50)
    
    issues = []
    
    # Check required modules
    required_modules = [
        'httpx',
        'sqlalchemy',
        'asyncpg',
        'fastapi',
        'pydantic'
    ]
    
    print(f"{Colors.BLUE}📦 Checking required modules...{Colors.END}")
    for module in required_modules:
        try:
            importlib.import_module(module)
            print(f"{Colors.GREEN}✅ {module}{Colors.END}")
        except ImportError:
            print(f"{Colors.RED}❌ {module} - Not installed{Colors.END}")
            issues.append(f"Missing module: {module}")
    
    # Check project structure
    print(f"\n{Colors.BLUE}📁 Checking project structure...{Colors.END}")
    required_paths = [
        'src/api/main.py',
        'src/core/config.py',
        'src/database/setup.py',
        'src/database/models/startup.py',
        'src/database/models/vc.py',
        'src/database/models/match.py',
        'src/api/v1/endpoints/startups.py',
        'src/api/v1/endpoints/vcs.py',
        'src/api/v1/endpoints/matches.py'
    ]
    
    for path in required_paths:
        file_path = Path(path)
        if file_path.exists():
            print(f"{Colors.GREEN}✅ {path}{Colors.END}")
        else:
            print(f"{Colors.RED}❌ {path} - Missing{Colors.END}")
            issues.append(f"Missing file: {path}")
    
    # Check configuration
    print(f"\n{Colors.BLUE}⚙️  Checking configuration...{Colors.END}")
    try:
        from src.core.config import settings
        print(f"{Colors.GREEN}✅ Configuration loaded{Colors.END}")
        print(f"   Database URL: {settings.database_url[:50]}...")
        print(f"   Debug mode: {settings.debug}")
        print(f"   API prefix: {settings.api_v1_prefix}")
    except Exception as e:
        print(f"{Colors.RED}❌ Configuration error: {str(e)}{Colors.END}")
        issues.append(f"Configuration error: {str(e)}")
    
    # Check database connection
    print(f"\n{Colors.BLUE}🗄️  Checking database connection...{Colors.END}")
    try:
        from src.database.setup import get_async_db
        from sqlalchemy import text
        
        async for db in get_async_db():
            result = await db.execute(text("SELECT 1"))
            result.scalar()
            print(f"{Colors.GREEN}✅ Database connection successful{Colors.END}")
            break
            
    except Exception as e:
        print(f"{Colors.RED}❌ Database connection failed: {str(e)}{Colors.END}")
        issues.append(f"Database connection error: {str(e)}")
    
    # Check FastAPI app
    print(f"\n{Colors.BLUE}🚀 Checking FastAPI app...{Colors.END}")
    try:
        from src.api.main import app
        print(f"{Colors.GREEN}✅ FastAPI app imported successfully{Colors.END}")
        print(f"   App title: {app.title}")
        print(f"   Routes: {len(app.routes)} routes configured")
    except Exception as e:
        print(f"{Colors.RED}❌ FastAPI app error: {str(e)}{Colors.END}")
        issues.append(f"FastAPI app error: {str(e)}")
    
    # Summary
    print(f"\n{Colors.BOLD}📊 Validation Summary{Colors.END}")
    print("=" * 50)
    
    if not issues:
        print(f"{Colors.GREEN}{Colors.BOLD}🎉 All checks passed! Ready to run integration tests.{Colors.END}")
        print(f"\n{Colors.BLUE}To run the integration tests:{Colors.END}")
        print(f"  python test_integration_data_persistence.py")
        return True
    else:
        print(f"{Colors.RED}{Colors.BOLD}❌ {len(issues)} issues found:{Colors.END}")
        for i, issue in enumerate(issues, 1):
            print(f"  {i}. {issue}")
        
        print(f"\n{Colors.YELLOW}Please fix these issues before running integration tests.{Colors.END}")
        print(f"\n{Colors.BLUE}Quick fixes:{Colors.END}")
        print(f"  • Install missing modules: pip install -r requirements-integration-test.txt")
        print(f"  • Check database is running: pg_ctl status")
        print(f"  • Verify environment variables in .env file")
        return False


if __name__ == "__main__":
    success = asyncio.run(validate_setup())
    sys.exit(0 if success else 1)