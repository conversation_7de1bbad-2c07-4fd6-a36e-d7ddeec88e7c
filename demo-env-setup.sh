#!/bin/bash

# Demo script to show environment setup functionality
# This demonstrates the environment configuration without starting services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(pwd)"
ENV_FILE="$PROJECT_ROOT/.env.demo"
ENV_STAGING="$PROJECT_ROOT/.env.staging"

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_header() {
    echo -e "\n${PURPLE}🚀 $1${NC}"
    echo -e "${PURPLE}$(printf '=%.0s' {1..50})${NC}"
}

generate_secret_key() {
    python3 -c "import secrets; print(secrets.token_urlsafe(32))"
}

print_header "Environment Setup Demo"
print_info "This demo shows how the setup script configures your environment"

# Clean up any existing demo file
if [ -f "$ENV_FILE" ]; then
    rm "$ENV_FILE"
    print_info "Cleaned up previous demo environment file"
fi

print_header "Step 1: Copy Environment Template"

if [ -f "$ENV_STAGING" ]; then
    print_info "Copying .env.staging to .env.demo..."
    cp "$ENV_STAGING" "$ENV_FILE"
    print_status "Environment template copied"
else
    print_warning ".env.staging not found"
    exit 1
fi

print_header "Step 2: Generate Secure Secret Key"

if grep -q "your-staging-secret-key-please-change-this" "$ENV_FILE"; then
    print_info "Generating secure secret key..."
    SECRET_KEY=$(generate_secret_key)
    
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' "s/SECRET_KEY=.*/SECRET_KEY=$SECRET_KEY/" "$ENV_FILE"
    else
        # Linux
        sed -i "s/SECRET_KEY=.*/SECRET_KEY=$SECRET_KEY/" "$ENV_FILE"
    fi
    
    print_status "Generated new secret key: ${SECRET_KEY:0:20}..."
else
    print_info "Secret key already configured"
fi

print_header "Step 3: Show Environment Configuration"

print_info "Current environment configuration:"
echo -e "${CYAN}----------------------------------------${NC}"

# Show key configuration values (without sensitive data)
grep -E "^(ENVIRONMENT|DEBUG|DATABASE_URL|REDIS_URL|OPENAI_API_KEY)" "$ENV_FILE" | while read line; do
    key=$(echo "$line" | cut -d'=' -f1)
    value=$(echo "$line" | cut -d'=' -f2-)
    
    case $key in
        "SECRET_KEY"|"OPENAI_API_KEY")
            if [[ $value == *"your-"* ]] || [[ $value == *"change"* ]]; then
                echo -e "${YELLOW}$key=${NC}${RED}[NEEDS CONFIGURATION]${NC}"
            else
                echo -e "${GREEN}$key=${NC}${GREEN}[CONFIGURED]${NC}"
            fi
            ;;
        *)
            echo -e "${CYAN}$key=${NC}$value"
            ;;
    esac
done

echo -e "${CYAN}----------------------------------------${NC}"

print_header "Step 4: Configuration Status"

# Check what still needs configuration
needs_config=false

if grep -q "your-openai-api-key-here" "$ENV_FILE"; then
    print_warning "OpenAI API key needs to be set"
    needs_config=true
fi

if grep -q "staging-db-password-change-me" "$ENV_FILE"; then
    print_warning "Database password should be changed for production"
fi

if [ "$needs_config" = true ]; then
    print_info "Some configuration is still needed"
else
    print_status "All critical configuration appears to be set"
fi

print_header "Step 5: What the Real Script Would Do Next"

echo -e "${CYAN}In the real setup script, this would happen next:${NC}"
echo -e "  1. ${YELLOW}Prompt for OpenAI API key${NC} (if not set)"
echo -e "  2. ${YELLOW}Check prerequisites${NC} (Python, Docker, etc.)"
echo -e "  3. ${YELLOW}Set up Python virtual environment${NC}"
echo -e "  4. ${YELLOW}Install dependencies${NC} (pip install -r requirements.txt)"
echo -e "  5. ${YELLOW}Start services${NC} (API, Celery, databases)"
echo -e "  6. ${YELLOW}Run database migrations${NC} (alembic upgrade head)"
echo -e "  7. ${YELLOW}Display service URLs${NC} and management commands"

print_header "Demo Environment File Created"

print_info "Demo environment file created at: $ENV_FILE"
print_info "You can examine it with: cat $ENV_FILE"

echo -e "\n${CYAN}To run the actual setup:${NC}"
echo -e "  ${YELLOW}./run.sh${NC}                 # Interactive mode"
echo -e "  ${YELLOW}./run.sh local${NC}           # Local mode (if you have PostgreSQL/Redis)"
echo -e "  ${YELLOW}./run.sh hybrid${NC}          # Hybrid mode (requires Docker)"
echo -e "  ${YELLOW}./run.sh docker${NC}          # Docker mode (requires Docker)"

print_header "Cleanup"

read -p "Remove demo environment file? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    rm "$ENV_FILE"
    print_status "Demo environment file removed"
else
    print_info "Demo environment file kept at $ENV_FILE"
fi

print_status "Environment setup demo completed! 🎉"
