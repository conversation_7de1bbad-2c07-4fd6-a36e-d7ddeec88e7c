"""Add vector search support for hybrid keyword + semantic matching

Revision ID: 006_add_vector_search
Revises: 005
Create Date: 2025-08-01 12:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID, JSONB


# revision identifiers, used by Alembic.
revision: str = '006_add_vector_search'
down_revision: Union[str, None] = '005'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Add vector search support for hybrid matching."""

    # Enable pgvector extension
    op.execute("CREATE EXTENSION IF NOT EXISTS vector")

    # Add vector columns to startups table
    op.add_column('startups',
        sa.Column('description_vector', sa.Text(), nullable=True,
                  comment='OpenAI embedding of startup description'))
    op.add_column('startups',
        sa.Column('combined_vector', sa.Text(), nullable=True,
                  comment='OpenAI embedding of name + description + sector + stage'))

    # Add vector columns to vcs table
    op.add_column('vcs',
        sa.Column('thesis_vector', sa.Text(), nullable=True,
                  comment='OpenAI embedding of investment thesis'))
    op.add_column('vcs',
        sa.Column('combined_vector', sa.Text(), nullable=True,
                  comment='OpenAI embedding of thesis + sectors + stages'))

    # Add metadata columns for tracking
    op.add_column('startups',
        sa.Column('embedding_updated_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('vcs',
        sa.Column('embedding_updated_at', sa.DateTime(timezone=True), nullable=True))

    # Create function to cast text to vector (for safer storage)
    op.execute("""
        CREATE OR REPLACE FUNCTION text_to_vector(text_input text)
        RETURNS vector(1536)
        LANGUAGE plpgsql
        AS $$
        BEGIN
            RETURN text_input::vector(1536);
        EXCEPTION
            WHEN OTHERS THEN
                RETURN NULL;
        END;
        $$;
    """)

    # Create vector indexes (will work after embeddings are populated)
    # Using ivfflat for good balance of speed and recall
    # op.execute("""
    #     CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_startups_description_vector
    #     ON startups USING ivfflat ((text_to_vector(description_vector)) vector_cosine_ops)
    #     WITH (lists = 100)
    #     WHERE description_vector IS NOT NULL
    # """)

    # op.execute("""
    #     CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_startups_combined_vector
    #     ON startups USING ivfflat ((text_to_vector(combined_vector)) vector_cosine_ops)
    #     WITH (lists = 100)
    #     WHERE combined_vector IS NOT NULL
    # """)

    # op.execute("""
    #     CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_vcs_thesis_vector
    #     ON vcs USING ivfflat ((text_to_vector(thesis_vector)) vector_cosine_ops)
    #     WITH (lists = 100)
    #     WHERE thesis_vector IS NOT NULL
    # """)

    # op.execute("""
    #     CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_vcs_combined_vector
    #     ON vcs USING ivfflat ((text_to_vector(combined_vector)) vector_cosine_ops)
    #     WITH (lists = 100)
    #     WHERE combined_vector IS NOT NULL
    # """)

    # Create hybrid search function for startups
    op.execute("""
        CREATE OR REPLACE FUNCTION hybrid_startup_search(
            query_embedding text,
            keyword_query text,
            keyword_weight float DEFAULT 0.4,
            semantic_weight float DEFAULT 0.6,
            min_score float DEFAULT 0.1,
            result_limit int DEFAULT 50
        )
        RETURNS TABLE (
            startup_id uuid,
            startup_name varchar,
            sector varchar,
            stage varchar,
            description text,
            monthly_revenue numeric,
            keyword_score float,
            semantic_score float,
            hybrid_score float
        )
        LANGUAGE plpgsql
        AS $$
        BEGIN
            RETURN QUERY
            WITH keyword_matches AS (
                SELECT
                    s.id,
                    COALESCE(
                        ts_rank_cd(
                            to_tsvector('english', COALESCE(s.description, '')),
                            plainto_tsquery('english', keyword_query)
                        ), 0
                    ) as kw_score
                FROM startups s
                WHERE keyword_query = ''
                   OR to_tsvector('english', COALESCE(s.description, '')) @@ plainto_tsquery('english', keyword_query)
            ),
            semantic_matches AS (
                SELECT
                    s.id,
                    CASE
                        WHEN s.combined_vector IS NOT NULL AND query_embedding != '' THEN
                            1 - (text_to_vector(s.combined_vector) <=> text_to_vector(query_embedding))
                        ELSE 0
                    END as sem_score
                FROM startups s
                WHERE s.combined_vector IS NOT NULL
            )
            SELECT
                s.id as startup_id,
                s.name as startup_name,
                s.sector,
                s.stage,
                s.description,
                s.monthly_revenue,
                COALESCE(k.kw_score, 0) as keyword_score,
                COALESCE(sm.sem_score, 0) as semantic_score,
                (keyword_weight * COALESCE(k.kw_score, 0) +
                 semantic_weight * COALESCE(sm.sem_score, 0)) as hybrid_score
            FROM startups s
            LEFT JOIN keyword_matches k ON s.id = k.id
            LEFT JOIN semantic_matches sm ON s.id = sm.id
            WHERE (keyword_weight * COALESCE(k.kw_score, 0) +
                   semantic_weight * COALESCE(sm.sem_score, 0)) >= min_score
            ORDER BY hybrid_score DESC, s.monthly_revenue DESC
            LIMIT result_limit;
        END;
        $$;
    """)

    # Create match explanation view
    op.execute("""
        CREATE OR REPLACE VIEW startup_match_explanations AS
        SELECT
            s.id as startup_id,
            s.name as startup_name,
            s.sector,
            s.stage,
            s.description,
            s.description_vector IS NOT NULL as has_description_embedding,
            s.combined_vector IS NOT NULL as has_combined_embedding,
            s.embedding_updated_at,
            LENGTH(s.description) as description_length,
            array_length(string_to_array(s.description, ' '), 1) as word_count
        FROM startups s;
    """)


def downgrade() -> None:
    """Remove vector search support."""

    # Drop views and functions
    op.execute("DROP VIEW IF EXISTS startup_match_explanations")
    op.execute("DROP FUNCTION IF EXISTS hybrid_startup_search")
    op.execute("DROP FUNCTION IF EXISTS text_to_vector")

    # Drop indexes
    op.execute("DROP INDEX IF EXISTS idx_startups_description_vector")
    op.execute("DROP INDEX IF EXISTS idx_startups_combined_vector")
    op.execute("DROP INDEX IF EXISTS idx_vcs_thesis_vector")
    op.execute("DROP INDEX IF EXISTS idx_vcs_combined_vector")

    # Remove columns
    op.drop_column('vcs', 'embedding_updated_at')
    op.drop_column('startups', 'embedding_updated_at')
    op.drop_column('vcs', 'combined_vector')
    op.drop_column('vcs', 'thesis_vector')
    op.drop_column('startups', 'combined_vector')
    op.drop_column('startups', 'description_vector')

    # Disable extension (optional - might be used by other tables)
    # op.execute("DROP EXTENSION IF EXISTS vector")