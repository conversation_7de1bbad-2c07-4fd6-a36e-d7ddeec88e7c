"""Add performance indexes for discovery feature

Revision ID: 004
Revises: 003
Create Date: 2025-08-01 23:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '004'
down_revision = '003'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add performance indexes for the discovery feature."""
    
    # GIN indexes for JSON fields (PostgreSQL)
    # These enable fast containment queries (@> operator)
    # Cast JSON to JSONB for GIN indexing with jsonb_path_ops operator class for better performance
    op.execute("CREATE INDEX IF NOT EXISTS idx_vc_sectors_gin ON vcs USING GIN ((sectors::jsonb) jsonb_path_ops)")
    op.execute("CREATE INDEX IF NOT EXISTS idx_vc_stages_gin ON vcs USING GIN ((stages::jsonb) jsonb_path_ops)")
    op.execute("CREATE INDEX IF NOT EXISTS idx_vc_portfolio_gin ON vcs USING GIN ((portfolio_companies::jsonb) jsonb_path_ops)")
    op.execute("CREATE INDEX IF NOT EXISTS idx_startup_metadata_gin ON startups USING GIN ((metadata::jsonb) jsonb_path_ops)")
    
    # Full-text search indexes for better text matching
    # These enable fast text search on thesis and descriptions
    op.execute("CREATE INDEX IF NOT EXISTS idx_vc_thesis_fts ON vcs USING GIN (to_tsvector('english', COALESCE(thesis, '')))")
    op.execute("CREATE INDEX IF NOT EXISTS idx_startup_description_fts ON startups USING GIN (to_tsvector('english', COALESCE(description, '')))")
    
    # Composite indexes for common discovery queries
    # These enable fast filtering on multiple columns simultaneously
    op.create_index('idx_startup_sector_stage', 'startups', ['sector', 'stage'])
    op.create_index('idx_startup_stage_revenue', 'startups', ['stage', 'monthly_revenue'])
    op.create_index('idx_startup_sector_revenue', 'startups', ['sector', 'monthly_revenue'])
    op.create_index('idx_startup_sector_stage_revenue', 'startups', ['sector', 'stage', 'monthly_revenue'])
    
    # VC composite indexes for discovery queries
    op.create_index('idx_vc_check_size_range', 'vcs', ['check_size_min', 'check_size_max'])
    
    # Partial indexes for common filtered queries
    # These are smaller and faster for specific query patterns
    op.execute("""
        CREATE INDEX IF NOT EXISTS idx_startups_active 
        ON startups (sector, stage, monthly_revenue) 
        WHERE monthly_revenue > 0
    """)
    
    op.execute("""
        CREATE INDEX IF NOT EXISTS idx_vcs_active 
        ON vcs (check_size_min, check_size_max) 
        WHERE check_size_min IS NOT NULL AND check_size_max IS NOT NULL
    """)
    
    # Expression indexes for calculated values
    # These enable fast queries on calculated funding needs
    op.execute("""
        CREATE INDEX IF NOT EXISTS idx_startup_funding_tier 
        ON startups ((
            CASE 
                WHEN stage = 'Pre-seed' THEN 1
                WHEN stage = 'Seed' THEN 2
                WHEN stage = 'Series A' THEN 3
                WHEN stage = 'Series B' THEN 4
                WHEN stage = 'Series C' THEN 5
                WHEN stage = 'Growth' THEN 6
                ELSE 0
            END
        ))
    """)
    
    # Statistics update to help query planner
    op.execute("ANALYZE startups")
    op.execute("ANALYZE vcs")


def downgrade() -> None:
    """Remove performance indexes."""
    
    # Drop expression indexes
    op.execute("DROP INDEX IF EXISTS idx_startup_funding_tier")
    
    # Drop partial indexes
    op.execute("DROP INDEX IF EXISTS idx_vcs_active")
    op.execute("DROP INDEX IF EXISTS idx_startups_active")
    
    # Drop composite indexes
    op.drop_index('idx_vc_check_size_range', 'vcs')
    op.drop_index('idx_startup_sector_stage_revenue', 'startups')
    op.drop_index('idx_startup_sector_revenue', 'startups')
    op.drop_index('idx_startup_stage_revenue', 'startups')
    op.drop_index('idx_startup_sector_stage', 'startups')
    
    # Drop full-text search indexes
    op.execute("DROP INDEX IF EXISTS idx_startup_description_fts")
    op.execute("DROP INDEX IF EXISTS idx_vc_thesis_fts")
    
    # Drop GIN indexes
    op.execute("DROP INDEX IF EXISTS idx_startup_metadata_gin")
    op.execute("DROP INDEX IF EXISTS idx_vc_portfolio_gin")
    op.execute("DROP INDEX IF EXISTS idx_vc_stages_gin")
    op.execute("DROP INDEX IF EXISTS idx_vc_sectors_gin")