"""Initial schema creation matching database models

Revision ID: 001
Revises: 
Create Date: 2025-08-01 22:57:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import uuid


# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create startups table
    op.create_table('startups',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('name', sa.String(255), nullable=False),
        sa.Column('sector', sa.String(100), nullable=False),
        sa.Column('stage', sa.String(50), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('website', sa.String(255), nullable=True),
        sa.Column('team_size', sa.Integer(), nullable=True, default=0),
        sa.Column('monthly_revenue', sa.Float(), nullable=True, default=0),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('metadata', sa.JSON(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create vcs table
    op.create_table('vcs',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('firm_name', sa.String(255), nullable=False),
        sa.Column('website', sa.String(255), nullable=True),
        sa.Column('thesis', sa.Text(), nullable=True),
        sa.Column('check_size_min', sa.Float(), nullable=True),
        sa.Column('check_size_max', sa.Float(), nullable=True),
        sa.Column('sectors', sa.JSON(), nullable=True),
        sa.Column('stages', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create users table
    op.create_table('users',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False, default=uuid.uuid4),
        sa.Column('email', sa.String(255), nullable=False),
        sa.Column('username', sa.String(100), nullable=False),
        sa.Column('hashed_password', sa.String(255), nullable=False),
        sa.Column('full_name', sa.String(255), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True, default=True),
        sa.Column('is_superuser', sa.Boolean(), nullable=True, default=False),
        sa.Column('roles', sa.JSON(), nullable=True, default=list),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('last_login', sa.DateTime(), nullable=True),
        sa.Column('metadata', sa.JSON(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_users_email', 'users', ['email'], unique=True)
    op.create_index('ix_users_username', 'users', ['username'], unique=True)
    
    # Create matches table
    op.create_table('matches',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('startup_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('vc_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('score', sa.Float(), nullable=False),
        sa.Column('reasons', sa.JSON(), nullable=True),
        sa.Column('status', sa.String(50), nullable=True, default='pending'),
        sa.Column('match_type', sa.String(50), nullable=True),
        sa.Column('notes', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['startup_id'], ['startups.id'], ),
        sa.ForeignKeyConstraint(['vc_id'], ['vcs.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for performance
    op.create_index('idx_match_startup_id', 'matches', ['startup_id'])
    op.create_index('idx_match_vc_id', 'matches', ['vc_id'])
    op.create_index('idx_match_startup_vc', 'matches', ['startup_id', 'vc_id'])
    op.create_index('idx_match_score', 'matches', ['score'])
    op.create_index('idx_match_status', 'matches', ['status'])
    op.create_index('idx_match_created_at', 'matches', ['created_at'])
    
    # Startup indexes
    op.create_index('idx_startup_sector', 'startups', ['sector'])
    op.create_index('idx_startup_stage', 'startups', ['stage'])
    op.create_index('idx_startup_name', 'startups', ['name'])
    
    # VC indexes
    op.create_index('idx_vc_firm_name', 'vcs', ['firm_name'])


def downgrade() -> None:
    # Drop indexes
    op.drop_index('idx_vc_firm_name', 'vcs')
    op.drop_index('idx_startup_name', 'startups')
    op.drop_index('idx_startup_stage', 'startups')
    op.drop_index('idx_startup_sector', 'startups')
    op.drop_index('idx_match_created_at', 'matches')
    op.drop_index('idx_match_status', 'matches')
    op.drop_index('idx_match_score', 'matches')
    op.drop_index('idx_match_startup_vc', 'matches')
    op.drop_index('idx_match_vc_id', 'matches')
    op.drop_index('idx_match_startup_id', 'matches')
    op.drop_index('ix_users_username', 'users')
    op.drop_index('ix_users_email', 'users')
    
    # Drop tables
    op.drop_table('matches')
    op.drop_table('users')
    op.drop_table('vcs')
    op.drop_table('startups')