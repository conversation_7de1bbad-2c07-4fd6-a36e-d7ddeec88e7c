"""Add portfolio_companies and partners fields to VCs table

Revision ID: 003
Revises: 002
Create Date: 2025-08-01 23:45:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = '003'
down_revision = '002'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add portfolio_companies and partners columns to vcs table
    op.add_column('vcs', sa.Column('portfolio_companies', sa.JSON(), nullable=True))
    op.add_column('vcs', sa.Column('partners', sa.JSON(), nullable=True))


def downgrade() -> None:
    # Remove the columns
    op.drop_column('vcs', 'partners')
    op.drop_column('vcs', 'portfolio_companies')