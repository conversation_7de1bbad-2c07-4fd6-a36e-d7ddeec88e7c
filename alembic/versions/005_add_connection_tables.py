"""Add connection tables for warm intro finder

Revision ID: 005
Revises: 004
Create Date: 2025-08-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = '005'
down_revision = '004'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add tables for warm intro finder feature."""
    
    # Create relationship type enum
    # op.execute("CREATE TYPE relationship_type_enum AS ENUM ('colleague', 'business_partner', 'mentor_mentee', 'investor_founder', 'industry_peer', 'referral', 'social')")
    
    # Create connection strength enum
    # op.execute("CREATE TYPE connection_strength_enum AS ENUM ('strong', 'medium', 'weak')")
    
    # Create introduction status enum
    # op.execute("CREATE TYPE introduction_status_enum AS ENUM ('pending', 'accepted', 'declined', 'completed', 'expired')")
    
    # Create connections table
    op.create_table('connections',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_a_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_b_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('relationship_type', postgresql.ENUM('colleague', 'business_partner', 'mentor_mentee', 'investor_founder', 'industry_peer', 'referral', 'social', name='relationship_type_enum'), nullable=False),
        sa.Column('strength', postgresql.ENUM('strong', 'medium', 'weak', name='connection_strength_enum'), nullable=False),
        sa.Column('metrics', sa.JSON(), nullable=True),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.Column('tags', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for connections
    op.create_index('idx_connections_active_users', 'connections', ['user_a_id', 'user_b_id', 'is_active'])
    op.create_index('idx_connections_relationship', 'connections', ['relationship_type', 'is_active'])
    op.create_index('idx_connections_strength', 'connections', ['strength', 'is_active'])
    op.create_index('idx_connections_users', 'connections', ['user_a_id', 'user_b_id'])
    op.create_index('idx_connections_user_a', 'connections', ['user_a_id'])
    op.create_index('idx_connections_user_b', 'connections', ['user_b_id'])
    
    # Create introduction_requests table
    op.create_table('introduction_requests',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('requester_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('target_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('connector_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('status', postgresql.ENUM('pending', 'accepted', 'declined', 'completed', 'expired', name='introduction_status_enum'), nullable=False),
        sa.Column('message', sa.Text(), nullable=False),
        sa.Column('connector_notes', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('expires_at', sa.DateTime(), nullable=True),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for introduction_requests
    op.create_index('idx_intro_requests_connector', 'introduction_requests', ['connector_id'])
    op.create_index('idx_intro_requests_expiry', 'introduction_requests', ['expires_at', 'status'])
    op.create_index('idx_intro_requests_requester', 'introduction_requests', ['requester_id'])
    op.create_index('idx_intro_requests_status', 'introduction_requests', ['status', 'connector_id'])
    op.create_index('idx_intro_requests_users', 'introduction_requests', ['requester_id', 'target_id', 'connector_id'])
    
    # Create function for path finding (using recursive CTE)
    op.execute("""
        CREATE OR REPLACE FUNCTION find_connection_paths(
            p_source_user_id UUID,
            p_target_user_id UUID,
            p_max_depth INTEGER DEFAULT 3
        )
        RETURNS TABLE (
            path UUID[],
            depth INTEGER,
            strength_score NUMERIC
        ) AS $$
        BEGIN
            RETURN QUERY
            WITH RECURSIVE connection_paths AS (
                -- Base case: direct connections from source
                SELECT 
                    CASE 
                        WHEN c.user_a_id = p_source_user_id THEN ARRAY[c.user_a_id, c.user_b_id]
                        ELSE ARRAY[c.user_b_id, c.user_a_id]
                    END as path,
                    1 as depth,
                    CASE c.strength 
                        WHEN 'strong' THEN 1.0
                        WHEN 'medium' THEN 0.6
                        WHEN 'weak' THEN 0.3
                    END as strength_score
                FROM connections c
                WHERE c.is_active = true
                  AND (c.user_a_id = p_source_user_id OR c.user_b_id = p_source_user_id)
                
                UNION ALL
                
                -- Recursive case: extend paths
                SELECT 
                    cp.path || CASE 
                        WHEN c.user_a_id = cp.path[array_length(cp.path, 1)] THEN c.user_b_id
                        ELSE c.user_a_id
                    END,
                    cp.depth + 1,
                    cp.strength_score * CASE c.strength 
                        WHEN 'strong' THEN 1.0
                        WHEN 'medium' THEN 0.6
                        WHEN 'weak' THEN 0.3
                    END
                FROM connection_paths cp
                JOIN connections c ON (
                    c.is_active = true
                    AND (
                        (c.user_a_id = cp.path[array_length(cp.path, 1)] AND NOT c.user_b_id = ANY(cp.path))
                        OR 
                        (c.user_b_id = cp.path[array_length(cp.path, 1)] AND NOT c.user_a_id = ANY(cp.path))
                    )
                )
                WHERE cp.depth < p_max_depth
            )
            SELECT DISTINCT ON (cp.path[array_length(cp.path, 1)], cp.depth) 
                cp.path,
                cp.depth,
                cp.strength_score
            FROM connection_paths cp
            WHERE cp.path[array_length(cp.path, 1)] = p_target_user_id
            ORDER BY cp.path[array_length(cp.path, 1)], cp.depth, cp.strength_score DESC;
        END;
        $$ LANGUAGE plpgsql;
    """)
    
    # Create index to support the path finding function
    op.execute("""
        CREATE INDEX idx_connections_path_finding 
        ON connections (user_a_id, user_b_id, strength) 
        WHERE is_active = true;
    """)


def downgrade() -> None:
    """Remove warm intro finder tables."""
    
    # Drop function
    op.execute("DROP FUNCTION IF EXISTS find_connection_paths")
    
    # Drop indexes
    op.drop_index('idx_connections_path_finding', 'connections')
    op.drop_index('idx_intro_requests_users', 'introduction_requests')
    op.drop_index('idx_intro_requests_status', 'introduction_requests')
    op.drop_index('idx_intro_requests_requester', 'introduction_requests')
    op.drop_index('idx_intro_requests_expiry', 'introduction_requests')
    op.drop_index('idx_intro_requests_connector', 'introduction_requests')
    
    op.drop_index('idx_connections_user_b', 'connections')
    op.drop_index('idx_connections_user_a', 'connections')
    op.drop_index('idx_connections_users', 'connections')
    op.drop_index('idx_connections_strength', 'connections')
    op.drop_index('idx_connections_relationship', 'connections')
    op.drop_index('idx_connections_active_users', 'connections')
    
    # Drop tables
    op.drop_table('introduction_requests')
    op.drop_table('connections')
    
    # Drop enums
    op.execute("DROP TYPE introduction_status_enum")
    op.execute("DROP TYPE connection_strength_enum")
    op.execute("DROP TYPE relationship_type_enum")