# Integration Test Requirements for BiLat VC Matching Platform
# This file contains additional dependencies needed for running integration tests

# Core testing framework
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-mock>=3.11.1

# HTTP client for API testing
httpx>=0.24.1

# Database testing utilities
psycopg2-binary>=2.9.7
asyncpg>=0.28.0

# Test data generation
factory-boy>=3.3.0
faker>=19.3.0

# Test reporting and coverage (optional)
pytest-html>=3.2.0
pytest-cov>=4.1.0

# Performance testing (optional)
pytest-benchmark>=4.0.0

# Test utilities
freezegun>=1.2.2  # For time manipulation in tests
responses>=0.23.3  # For mocking HTTP responses

# Environment variable management for tests
python-dotenv>=1.0.0

# JSON schema validation for API responses
jsonschema>=4.19.0

# Colorama for colored console output (already included in main requirements)
# colorama>=0.4.6

# UUID utilities
uuid>=1.30

# Date/time utilities
python-dateutil>=2.8.2