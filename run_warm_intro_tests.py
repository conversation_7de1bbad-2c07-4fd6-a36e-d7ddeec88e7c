#!/usr/bin/env python3
"""Test runner for warm intro functionality tests."""

import sys
import subprocess
import os


def run_tests():
    """Run all warm intro tests with coverage."""
    print("🧪 Running Warm Intro Tests with Coverage")
    print("=" * 60)
    
    # Test files to run
    test_files = [
        "tests/unit/models/test_connection_models.py",
        "tests/unit/services/test_warm_intro_service.py",
    ]
    
    # Coverage modules
    coverage_modules = [
        "src",
    ]
    
    # Build pytest command
    cmd = [
        "python3", "-m", "pytest",
        "-v",  # Verbose
        "--tb=short",  # Short traceback
        "--cov=" + ",".join(coverage_modules),  # Coverage modules
        "--cov-report=term-missing",  # Show missing lines
        "--cov-report=html:htmlcov_warm_intro",  # HTML report
        "--cov-report=term:skip-covered",  # Skip 100% covered files in terminal
        "--cov-fail-under=80",  # Fail if coverage < 80%
    ] + test_files
    
    # Set PYTHONPATH
    env = os.environ.copy()
    env["PYTHONPATH"] = "."
    
    print(f"Running command: {' '.join(cmd)}\n")
    
    # Run tests
    result = subprocess.run(cmd, env=env)
    
    if result.returncode == 0:
        print("\n✅ All tests passed!")
        print("📊 Coverage report generated in htmlcov_warm_intro/index.html")
    else:
        print("\n❌ Tests failed!")
        sys.exit(1)
    
    return result.returncode


def run_individual_test_suites():
    """Run individual test suites for debugging."""
    print("\n📝 Individual Test Suite Results:")
    print("-" * 60)
    
    test_suites = [
        ("Domain Models", "tests/unit/models/test_connection_models.py"),
        ("Service Layer", "tests/unit/services/test_warm_intro_service.py"),
    ]
    
    for name, test_file in test_suites:
        print(f"\n🔍 Testing {name}...")
        cmd = ["python3", "-m", "pytest", test_file, "-v", "--tb=short"]
        
        env = os.environ.copy()
        env["PYTHONPATH"] = "."
        
        result = subprocess.run(cmd, env=env, capture_output=True, text=True)
        
        if result.returncode == 0:
            # Count passed tests
            output_lines = result.stdout.split('\n')
            passed_count = len([l for l in output_lines if " PASSED" in l])
            print(f"  ✅ {name}: {passed_count} tests passed")
        else:
            print(f"  ❌ {name}: FAILED")
            # Show first few lines of error
            error_lines = result.stdout.split('\n')[:10]
            for line in error_lines:
                if "FAILED" in line or "ERROR" in line:
                    print(f"     {line}")


def check_coverage_by_module():
    """Check coverage for each module."""
    print("\n📊 Coverage by Module:")
    print("-" * 60)
    
    modules = {
        "Connection Models": "src.core.models.connection",
        "Warm Intro Service": "src.core.services.warm_intro_service",
        "Connection Repository": "src.database.repositories.connection_repository",
    }
    
    for name, module in modules.items():
        cmd = [
            "python3", "-m", "pytest",
            "tests/unit/",
            f"--cov={module}",
            "--cov-report=term-missing:skip-covered",
            "-q"  # Quiet mode
        ]
        
        env = os.environ.copy()
        env["PYTHONPATH"] = "."
        
        result = subprocess.run(cmd, env=env, capture_output=True, text=True)
        
        # Extract coverage percentage
        for line in result.stdout.split('\n'):
            if module in line and "%" in line:
                parts = line.split()
                for i, part in enumerate(parts):
                    if "%" in part:
                        coverage = part
                        print(f"  {name}: {coverage} coverage")
                        break


if __name__ == "__main__":
    print("🚀 Warm Intro Test Suite Runner")
    print("================================\n")
    
    # Check if pytest is installed
    try:
        import pytest
        import pytest_cov
    except ImportError:
        print("❌ Error: pytest and pytest-cov are required")
        print("Run: pip install pytest pytest-cov pytest-asyncio")
        sys.exit(1)
    
    # Run main test suite
    exit_code = run_tests()
    
    if exit_code == 0:
        # Show individual results only if main suite passes
        run_individual_test_suites()
        check_coverage_by_module()
    
    sys.exit(exit_code)