#!/usr/bin/env python3
"""
Simple integration test to verify data persistence through API endpoints.
"""

import requests
import json
from datetime import datetime
from colorama import init, Fore, Style

# Initialize colorama
init(autoreset=True)

BASE_URL = "http://localhost:8000/api/v1"

def test_startup_persistence():
    """Test creating and retrieving a startup."""
    print(f"\n{Fore.CYAN}Testing Startup Persistence:{Style.RESET_ALL}")
    
    # Create a startup
    startup_data = {
        "name": f"TestStartup_{datetime.now().strftime('%Y%m%d%H%M%S')}",
        "sector": "B2B SaaS",
        "stage": "Seed",
        "description": "Test startup for persistence verification",
        "website": "https://test.example.com",
        "team_size": 10,
        "monthly_revenue": 25000
    }
    
    # POST request to create startup
    response = requests.post(f"{BASE_URL}/startups", json=startup_data)
    if response.status_code == 201:
        created_startup = response.json()
        startup_id = created_startup["id"]
        print(f"{Fore.GREEN}✅ Created startup with ID: {startup_id}{Style.RESET_ALL}")
        
        # GET request to retrieve the startup
        response = requests.get(f"{BASE_URL}/startups/{startup_id}")
        if response.status_code == 200:
            retrieved_startup = response.json()
            if retrieved_startup["name"] == startup_data["name"]:
                print(f"{Fore.GREEN}✅ Successfully retrieved startup: {retrieved_startup['name']}{Style.RESET_ALL}")
                print(f"   Sector: {retrieved_startup['sector']}")
                print(f"   Stage: {retrieved_startup['stage']}")
                print(f"   Team Size: {retrieved_startup['team_size']}")
                return True
            else:
                print(f"{Fore.RED}❌ Retrieved startup data doesn't match{Style.RESET_ALL}")
        else:
            print(f"{Fore.RED}❌ Failed to retrieve startup: {response.status_code}{Style.RESET_ALL}")
    else:
        print(f"{Fore.RED}❌ Failed to create startup: {response.status_code}{Style.RESET_ALL}")
        print(f"   Response: {response.text}")
    
    return False

def test_vc_persistence():
    """Test creating and retrieving a VC."""
    print(f"\n{Fore.CYAN}Testing VC Persistence:{Style.RESET_ALL}")
    
    # Create a VC
    vc_data = {
        "firm_name": f"TestVC_{datetime.now().strftime('%Y%m%d%H%M%S')}",
        "sectors": ["B2B SaaS", "AI/ML"],
        "stages": ["Seed", "Series A"],
        "thesis": "We invest in AI-powered B2B SaaS companies",
        "website": "https://testvc.example.com",
        "fund_size": 100000000,
        "typical_check_size": 1500000
    }
    
    # POST request to create VC
    response = requests.post(f"{BASE_URL}/vcs", json=vc_data)
    if response.status_code == 201:
        created_vc = response.json()
        vc_id = created_vc["id"]
        print(f"{Fore.GREEN}✅ Created VC with ID: {vc_id}{Style.RESET_ALL}")
        
        # GET request to retrieve the VC
        response = requests.get(f"{BASE_URL}/vcs/{vc_id}")
        if response.status_code == 200:
            retrieved_vc = response.json()
            if retrieved_vc["firm_name"] == vc_data["firm_name"]:
                print(f"{Fore.GREEN}✅ Successfully retrieved VC: {retrieved_vc['firm_name']}{Style.RESET_ALL}")
                print(f"   Sectors: {', '.join(retrieved_vc['sectors'])}")
                print(f"   Stages: {', '.join(retrieved_vc['stages'])}")
                print(f"   Fund Size: ${retrieved_vc['fund_size']:,}")
                return True
            else:
                print(f"{Fore.RED}❌ Retrieved VC data doesn't match{Style.RESET_ALL}")
        else:
            print(f"{Fore.RED}❌ Failed to retrieve VC: {response.status_code}{Style.RESET_ALL}")
    else:
        print(f"{Fore.RED}❌ Failed to create VC: {response.status_code}{Style.RESET_ALL}")
        print(f"   Response: {response.text}")
    
    return False

def test_list_operations():
    """Test listing all startups and VCs."""
    print(f"\n{Fore.CYAN}Testing List Operations:{Style.RESET_ALL}")
    
    # List all startups
    response = requests.get(f"{BASE_URL}/startups")
    if response.status_code == 200:
        startups = response.json()
        print(f"{Fore.GREEN}✅ Listed {len(startups)} startups{Style.RESET_ALL}")
        if startups:
            print(f"   Latest: {startups[-1]['name']}")
    else:
        print(f"{Fore.RED}❌ Failed to list startups: {response.status_code}{Style.RESET_ALL}")
    
    # List all VCs
    response = requests.get(f"{BASE_URL}/vcs")
    if response.status_code == 200:
        vcs = response.json()
        print(f"{Fore.GREEN}✅ Listed {len(vcs)} VCs{Style.RESET_ALL}")
        if vcs:
            print(f"   Latest: {vcs[-1]['firm_name']}")
    else:
        print(f"{Fore.RED}❌ Failed to list VCs: {response.status_code}{Style.RESET_ALL}")

def test_update_operations():
    """Test updating startup and VC data."""
    print(f"\n{Fore.CYAN}Testing Update Operations:{Style.RESET_ALL}")
    
    # Create a startup to update
    startup_data = {
        "name": f"UpdateTest_{datetime.now().strftime('%Y%m%d%H%M%S')}",
        "sector": "Fintech",
        "stage": "Pre-seed",
        "team_size": 5
    }
    
    response = requests.post(f"{BASE_URL}/startups", json=startup_data)
    if response.status_code == 201:
        startup_id = response.json()["id"]
        
        # Update the startup
        update_data = {
            "team_size": 15,
            "stage": "Seed",
            "monthly_revenue": 50000
        }
        
        response = requests.put(f"{BASE_URL}/startups/{startup_id}", json=update_data)
        if response.status_code == 200:
            updated_startup = response.json()
            if updated_startup["team_size"] == 15 and updated_startup["stage"] == "Seed":
                print(f"{Fore.GREEN}✅ Successfully updated startup{Style.RESET_ALL}")
                print(f"   Team size: {startup_data['team_size']} → {updated_startup['team_size']}")
                print(f"   Stage: {startup_data['stage']} → {updated_startup['stage']}")
                return True
            else:
                print(f"{Fore.RED}❌ Update not applied correctly{Style.RESET_ALL}")
        else:
            print(f"{Fore.RED}❌ Failed to update startup: {response.status_code}{Style.RESET_ALL}")
    else:
        print(f"{Fore.RED}❌ Failed to create startup for update test{Style.RESET_ALL}")
    
    return False

def test_match_operations():
    """Test creating and retrieving matches."""
    print(f"\n{Fore.CYAN}Testing Match Operations:{Style.RESET_ALL}")
    
    # First, get existing startups and VCs
    startups_response = requests.get(f"{BASE_URL}/startups")
    vcs_response = requests.get(f"{BASE_URL}/vcs")
    
    if startups_response.status_code == 200 and vcs_response.status_code == 200:
        startups = startups_response.json()
        vcs = vcs_response.json()
        
        if startups and vcs:
            # Create a match
            match_data = {
                "startup_id": startups[0]["id"],
                "vc_id": vcs[0]["id"],
                "score": 0.85,
                "reasons": ["Sector alignment", "Stage match", "Team strength"]
            }
            
            response = requests.post(f"{BASE_URL}/matches", json=match_data)
            if response.status_code == 201:
                created_match = response.json()
                print(f"{Fore.GREEN}✅ Created match with score: {created_match['score']}{Style.RESET_ALL}")
                print(f"   Startup: {startups[0]['name']}")
                print(f"   VC: {vcs[0]['firm_name']}")
                return True
            else:
                print(f"{Fore.RED}❌ Failed to create match: {response.status_code}{Style.RESET_ALL}")
                print(f"   Response: {response.text}")
        else:
            print(f"{Fore.YELLOW}⚠️  No startups or VCs available for matching{Style.RESET_ALL}")
    else:
        print(f"{Fore.RED}❌ Failed to get startups or VCs for matching{Style.RESET_ALL}")
    
    return False

def main():
    """Run all persistence tests."""
    print(f"\n{Fore.BLUE}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.BLUE}🧪 BiLat API Data Persistence Tests{Style.RESET_ALL}")
    print(f"{Fore.BLUE}{'='*60}{Style.RESET_ALL}")
    
    # Check if API is running
    try:
        response = requests.get("http://localhost:8000/health")
        if response.status_code != 200:
            print(f"{Fore.RED}❌ API is not running at http://localhost:8000{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}Please start the API first:{Style.RESET_ALL}")
            print(f"  python -m uvicorn test_api_minimal:app --reload")
            return
    except Exception as e:
        print(f"{Fore.RED}❌ Cannot connect to API: {e}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}Please start the API first:{Style.RESET_ALL}")
        print(f"  python -m uvicorn test_api_minimal:app --reload")
        return
    
    # Run tests
    tests_passed = 0
    tests_total = 5
    
    if test_startup_persistence():
        tests_passed += 1
    
    if test_vc_persistence():
        tests_passed += 1
    
    test_list_operations()  # Info only, not counted
    
    if test_update_operations():
        tests_passed += 1
    
    if test_match_operations():
        tests_passed += 1
    
    # Summary
    print(f"\n{Fore.BLUE}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.BLUE}📊 Test Summary{Style.RESET_ALL}")
    print(f"{Fore.BLUE}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.GREEN}✅ Passed: {tests_passed}/{tests_total - 1}{Style.RESET_ALL}")  # -1 because list is info only
    
    if tests_passed == tests_total - 1:
        print(f"\n{Fore.GREEN}🎉 All data persistence tests passed!{Style.RESET_ALL}")
        print(f"{Fore.GREEN}The BiLat API successfully persists data to PostgreSQL.{Style.RESET_ALL}")
    else:
        print(f"\n{Fore.YELLOW}⚠️  Some tests failed. Check the output above.{Style.RESET_ALL}")

if __name__ == "__main__":
    main()