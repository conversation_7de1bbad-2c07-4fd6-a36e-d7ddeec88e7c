"""Base scraper class with common functionality."""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Optional, Dict, Any, List
from datetime import datetime
import logging
import httpx
from bs4 import BeautifulSoup
from tenacity import retry, stop_after_attempt, wait_exponential
import asyncio

logger = logging.getLogger(__name__)


@dataclass
class ScraperResult:
    """Result from a scraping operation."""
    
    success: bool
    url: str
    data: Dict[str, Any]
    error: Optional[str] = None
    scraped_at: datetime = None
    
    def __post_init__(self):
        if self.scraped_at is None:
            self.scraped_at = datetime.utcnow()


class BaseScraper(ABC):
    """Base class for all web scrapers."""
    
    def __init__(self, timeout: int = 30, user_agent: Optional[str] = None):
        """
        Initialize the scraper.
        
        Args:
            timeout: Request timeout in seconds
            user_agent: Custom user agent string
        """
        self.timeout = timeout
        self.user_agent = user_agent or (
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
            "(KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        )
        self.headers = {
            "User-Agent": self.user_agent,
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate",
            "DNT": "1",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1"
        }
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    async def fetch_page(self, url: str) -> str:
        """
        Fetch a web page with retry logic.
        
        Args:
            url: URL to fetch
            
        Returns:
            HTML content
            
        Raises:
            httpx.HTTPError: If request fails
        """
        async with httpx.AsyncClient() as client:
            response = await client.get(
                url,
                headers=self.headers,
                timeout=self.timeout,
                follow_redirects=True
            )
            response.raise_for_status()
            return response.text
    
    def parse_html(self, html: str) -> BeautifulSoup:
        """
        Parse HTML content.
        
        Args:
            html: HTML string
            
        Returns:
            BeautifulSoup object
        """
        return BeautifulSoup(html, 'lxml')
    
    def clean_text(self, text: Optional[str]) -> Optional[str]:
        """
        Clean and normalize text.
        
        Args:
            text: Text to clean
            
        Returns:
            Cleaned text or None
        """
        if not text:
            return None
        
        # Strip whitespace and normalize
        text = ' '.join(text.split())
        text = text.strip()
        
        return text if text else None
    
    def extract_number(self, text: Optional[str]) -> Optional[int]:
        """
        Extract number from text.
        
        Args:
            text: Text containing number
            
        Returns:
            Extracted number or None
        """
        if not text:
            return None
        
        # Remove non-numeric characters
        import re
        numbers = re.findall(r'\d+', text)
        
        if numbers:
            # Join and convert to int
            return int(''.join(numbers))
        
        return None
    
    def extract_email(self, text: str) -> Optional[str]:
        """
        Extract email from text.
        
        Args:
            text: Text containing email
            
        Returns:
            Email or None
        """
        import re
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        matches = re.findall(email_pattern, text)
        return matches[0] if matches else None
    
    def extract_social_links(self, soup: BeautifulSoup) -> Dict[str, str]:
        """
        Extract social media links from page.
        
        Args:
            soup: BeautifulSoup object
            
        Returns:
            Dictionary of social platform to URL
        """
        social_links = {}
        
        # Common social media patterns
        patterns = {
            'linkedin': r'linkedin\.com/company/([^/\s]+)',
            'twitter': r'twitter\.com/([^/\s]+)',
            'facebook': r'facebook\.com/([^/\s]+)',
            'instagram': r'instagram\.com/([^/\s]+)',
            'github': r'github\.com/([^/\s]+)',
            'crunchbase': r'crunchbase\.com/organization/([^/\s]+)'
        }
        
        # Find all links
        for link in soup.find_all('a', href=True):
            href = link['href']
            for platform, pattern in patterns.items():
                import re
                if re.search(pattern, href):
                    social_links[platform] = href
        
        return social_links
    
    async def scrape(self, url: str) -> ScraperResult:
        """
        Main scraping method.
        
        Args:
            url: URL to scrape
            
        Returns:
            ScraperResult with extracted data
        """
        try:
            logger.info(f"Scraping {url}")
            
            # Fetch page
            html = await self.fetch_page(url)
            
            # Parse HTML
            soup = self.parse_html(html)
            
            # Extract data using subclass implementation
            data = await self.extract_data(soup, url)
            
            # Add common data
            data['social_links'] = self.extract_social_links(soup)
            data['url'] = url
            
            logger.info(f"Successfully scraped {url}")
            return ScraperResult(
                success=True,
                url=url,
                data=data
            )
            
        except Exception as e:
            logger.error(f"Error scraping {url}: {str(e)}")
            return ScraperResult(
                success=False,
                url=url,
                data={},
                error=str(e)
            )
    
    @abstractmethod
    async def extract_data(self, soup: BeautifulSoup, url: str) -> Dict[str, Any]:
        """
        Extract data from parsed HTML.
        
        Must be implemented by subclasses.
        
        Args:
            soup: BeautifulSoup object
            url: Original URL
            
        Returns:
            Dictionary of extracted data
        """
        pass
    
    async def scrape_multiple(self, urls: List[str], max_concurrent: int = 5) -> List[ScraperResult]:
        """
        Scrape multiple URLs concurrently.
        
        Args:
            urls: List of URLs to scrape
            max_concurrent: Maximum concurrent requests
            
        Returns:
            List of scraper results
        """
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def scrape_with_semaphore(url: str) -> ScraperResult:
            async with semaphore:
                return await self.scrape(url)
        
        tasks = [scrape_with_semaphore(url) for url in urls]
        return await asyncio.gather(*tasks)