"""Scraper for general company websites."""

from typing import Dict, Any, Optional
from bs4 import BeautifulSoup
import re
import logging

from .base import BaseScraper

logger = logging.getLogger(__name__)


class CompanyScraper(BaseScraper):
    """Scraper for extracting data from company websites."""
    
    async def extract_data(self, soup: BeautifulSoup, url: str) -> Dict[str, Any]:
        """
        Extract company data from website.
        
        Args:
            soup: BeautifulSoup object
            url: Original URL
            
        Returns:
            Dictionary of extracted data
        """
        data = {
            'name': self._extract_company_name(soup),
            'description': self._extract_description(soup),
            'industry': self._extract_industry(soup),
            'team_size': self._extract_team_size(soup),
            'location': self._extract_location(soup),
            'founded_year': self._extract_founded_year(soup),
            'contact_email': self._extract_contact_email(soup),
            'technologies': self._extract_technologies(soup),
        }
        
        # Remove None values
        return {k: v for k, v in data.items() if v is not None}
    
    def _extract_company_name(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract company name from various sources."""
        # Try meta property first
        meta_name = soup.find('meta', property='og:site_name')
        if meta_name and meta_name.get('content'):
            return self.clean_text(meta_name['content'])
        
        # Try title tag
        title = soup.find('title')
        if title:
            # Clean common patterns
            title_text = title.get_text()
            # Remove common suffixes
            for suffix in [' | Home', ' - Home', ' | Official Website', ' - Official Site']:
                title_text = title_text.replace(suffix, '')
            return self.clean_text(title_text)
        
        # Try h1 tag
        h1 = soup.find('h1')
        if h1:
            return self.clean_text(h1.get_text())
        
        return None
    
    def _extract_description(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract company description."""
        # Try meta description
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        if meta_desc and meta_desc.get('content'):
            return self.clean_text(meta_desc['content'])
        
        # Try og:description
        og_desc = soup.find('meta', property='og:description')
        if og_desc and og_desc.get('content'):
            return self.clean_text(og_desc['content'])
        
        # Look for about section
        about_section = None
        for tag in ['section', 'div']:
            for attr in ['id', 'class']:
                for value in ['about', 'about-us', 'company', 'mission']:
                    element = soup.find(tag, attrs={attr: re.compile(value, re.I)})
                    if element:
                        about_section = element
                        break
        
        if about_section:
            # Get first paragraph
            p = about_section.find('p')
            if p:
                return self.clean_text(p.get_text())
        
        return None
    
    def _extract_industry(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract industry/sector information."""
        # Look for industry keywords
        industry_keywords = [
            'industry', 'sector', 'vertical', 'market',
            'fintech', 'healthtech', 'edtech', 'saas', 'b2b', 'b2c',
            'technology', 'software', 'hardware', 'ai', 'ml', 'blockchain'
        ]
        
        # Check meta keywords
        meta_keywords = soup.find('meta', attrs={'name': 'keywords'})
        if meta_keywords and meta_keywords.get('content'):
            keywords = meta_keywords['content'].lower()
            for keyword in industry_keywords:
                if keyword in keywords:
                    return keyword.title()
        
        # Search in text
        text = soup.get_text().lower()
        for keyword in industry_keywords[4:]:  # Skip generic terms
            if keyword in text:
                return keyword.upper() if len(keyword) <= 3 else keyword.title()
        
        return None
    
    def _extract_team_size(self, soup: BeautifulSoup) -> Optional[int]:
        """Extract team size information."""
        # Common patterns
        patterns = [
            r'(\d+)\+?\s*employees?',
            r'(\d+)\+?\s*team members?',
            r'team of\s*(\d+)',
            r'(\d+)\+?\s*people',
            r'(\d+)\+?\s*staff'
        ]
        
        text = soup.get_text()
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return int(match.group(1))
        
        # Look for team/about page
        team_section = soup.find(['section', 'div'], attrs={'id': re.compile('team|people|about', re.I)})
        if team_section:
            # Count team member cards/profiles
            team_members = team_section.find_all(['div', 'article'], class_=re.compile('member|person|profile', re.I))
            if len(team_members) > 0:
                return len(team_members)
        
        return None
    
    def _extract_location(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract company location."""
        # Try schema.org markup
        schema = soup.find('script', type='application/ld+json')
        if schema:
            try:
                import json
                data = json.loads(schema.string)
                if isinstance(data, dict) and 'address' in data:
                    addr = data['address']
                    if isinstance(addr, dict):
                        parts = []
                        for key in ['addressLocality', 'addressRegion', 'addressCountry']:
                            if key in addr:
                                parts.append(addr[key])
                        if parts:
                            return ', '.join(parts)
            except:
                pass
        
        # Look for address patterns
        patterns = [
            r'([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*),?\s*([A-Z]{2})',  # City, State
            r'([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*),?\s*([A-Za-z\s]+)',  # City, Country
        ]
        
        text = soup.get_text()
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                return f"{match.group(1)}, {match.group(2)}"
        
        return None
    
    def _extract_founded_year(self, soup: BeautifulSoup) -> Optional[int]:
        """Extract founding year."""
        patterns = [
            r'founded\s+(?:in\s+)?(\d{4})',
            r'established\s+(?:in\s+)?(\d{4})',
            r'since\s+(\d{4})',
            r'©?\s*(\d{4})\s*-\s*\d{4}',  # Copyright range
        ]
        
        text = soup.get_text()
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                year = int(match.group(1))
                # Sanity check
                if 1990 <= year <= 2024:
                    return year
        
        return None
    
    def _extract_contact_email(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract contact email."""
        # Look for mailto links
        mailto = soup.find('a', href=re.compile(r'^mailto:'))
        if mailto:
            email = mailto['href'].replace('mailto:', '')
            return self.extract_email(email) or email
        
        # Look in contact section
        contact_section = soup.find(['section', 'div'], attrs={'id': re.compile('contact', re.I)})
        if contact_section:
            text = contact_section.get_text()
            email = self.extract_email(text)
            if email:
                return email
        
        # Search entire page
        return self.extract_email(soup.get_text())
    
    def _extract_technologies(self, soup: BeautifulSoup) -> list:
        """Extract technology stack."""
        tech_keywords = [
            # Languages
            'python', 'javascript', 'typescript', 'java', 'go', 'rust', 'ruby',
            # Frameworks
            'react', 'angular', 'vue', 'django', 'flask', 'fastapi', 'spring',
            # Databases
            'postgresql', 'mysql', 'mongodb', 'redis', 'elasticsearch',
            # Cloud
            'aws', 'google cloud', 'gcp', 'azure', 'kubernetes', 'docker',
            # AI/ML
            'tensorflow', 'pytorch', 'scikit-learn', 'langchain', 'openai',
        ]
        
        found_tech = []
        text = soup.get_text().lower()
        
        for tech in tech_keywords:
            if tech in text:
                # Proper casing
                if tech in ['aws', 'gcp', 'ai', 'ml']:
                    found_tech.append(tech.upper())
                elif tech in ['postgresql', 'mysql', 'mongodb', 'elasticsearch']:
                    found_tech.append(tech.capitalize())
                else:
                    found_tech.append(tech.title())
        
        return list(set(found_tech))  # Remove duplicates