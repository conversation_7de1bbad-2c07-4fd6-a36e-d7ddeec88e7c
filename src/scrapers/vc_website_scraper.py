"""VC website scraper to extract investment thesis and preferences."""

import asyncio
import aiohttp
from bs4 import BeautifulSoup
from typing import Dict, Any, List, Optional
import logging
import re
from urllib.parse import urljoin, urlparse

from src.scrapers.base import BaseScraper, ScraperResult

logger = logging.getLogger(__name__)


class VCWebsiteScraper(BaseScraper):
    """Scraper for extracting VC investment thesis from websites."""
    
    # Common paths where investment thesis might be found
    THESIS_PATHS = [
        "/about",
        "/investment-thesis",
        "/thesis",
        "/philosophy",
        "/approach",
        "/what-we-invest-in",
        "/portfolio",
        "/team",
        "/focus",
        "/sectors"
    ]
    
    # Keywords that indicate investment preferences
    INVESTMENT_KEYWORDS = {
        "stages": [
            "pre-seed", "preseed", "seed", "series a", "series b", "series c",
            "early stage", "early-stage", "growth", "late stage", "late-stage"
        ],
        "sectors": [
            "ai", "artificial intelligence", "machine learning", "ml",
            "saas", "b2b", "enterprise", "fintech", "financial",
            "healthcare", "health", "biotech", "medtech",
            "crypto", "blockchain", "web3", "defi",
            "climate", "cleantech", "sustainability",
            "consumer", "marketplace", "ecommerce", "e-commerce",
            "deeptech", "deep tech", "hardware", "robotics"
        ],
        "check_sizes": [
            r"\$[\d,]+k", r"\$[\d,]+m", r"\$[\d.]+ million", r"\$[\d.]+ mil"
        ],
        "geography": [
            "bay area", "silicon valley", "new york", "nyc", "london",
            "europe", "asia", "global", "us", "usa", "america"
        ]
    }
    
    async def extract_data(self, soup: BeautifulSoup, url: str) -> Dict[str, Any]:
        """Extract VC thesis data from the website."""
        base_url = f"{urlparse(url).scheme}://{urlparse(url).netloc}"
        
        # Extract basic info
        data = {
            "website": url,
            "title": self._extract_title(soup),
            "description": self._extract_description(soup),
            "thesis_pages": [],
            "extracted_text": {},
            "investment_focus": {
                "stages": [],
                "sectors": [],
                "check_sizes": [],
                "geography": []
            },
            "team_members": [],
            "portfolio_companies": []
        }
        
        # Find and scrape thesis-related pages
        thesis_urls = await self._find_thesis_pages(soup, base_url)
        
        for thesis_url in thesis_urls[:5]:  # Limit to 5 pages to avoid over-scraping
            try:
                page_content = await self._scrape_thesis_page(thesis_url)
                if page_content:
                    data["thesis_pages"].append({
                        "url": thesis_url,
                        "content": page_content
                    })
                    
                    # Extract investment preferences from content
                    self._extract_investment_preferences(page_content, data["investment_focus"])
                    
            except Exception as e:
                logger.warning(f"Failed to scrape {thesis_url}: {str(e)}")
        
        # Extract team information
        data["team_members"] = self._extract_team_info(soup)
        
        # Extract portfolio companies if on main page
        data["portfolio_companies"] = self._extract_portfolio_companies(soup)
        
        return data
    
    def _extract_title(self, soup: BeautifulSoup) -> str:
        """Extract the VC firm's name/title."""
        # Try various selectors
        title = soup.find('title')
        if title:
            return self.clean_text(title.text)
        
        h1 = soup.find('h1')
        if h1:
            return self.clean_text(h1.text)
        
        return ""
    
    def _extract_description(self, soup: BeautifulSoup) -> str:
        """Extract meta description or first meaningful paragraph."""
        # Try meta description
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        if meta_desc and meta_desc.get('content'):
            return self.clean_text(meta_desc['content'])
        
        # Try og:description
        og_desc = soup.find('meta', attrs={'property': 'og:description'})
        if og_desc and og_desc.get('content'):
            return self.clean_text(og_desc['content'])
        
        # Find first substantial paragraph
        for p in soup.find_all('p'):
            text = self.clean_text(p.text)
            if text and len(text) > 50:
                return text
        
        return ""
    
    async def _find_thesis_pages(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """Find URLs that likely contain investment thesis information."""
        thesis_urls = []
        
        # Look for links with thesis-related keywords
        for link in soup.find_all('a', href=True):
            href = link['href']
            link_text = self.clean_text(link.text).lower()
            
            # Check if URL or link text contains thesis keywords
            for path in self.THESIS_PATHS:
                if path in href.lower() or path.strip('/') in link_text:
                    full_url = urljoin(base_url, href)
                    if full_url not in thesis_urls and base_url in full_url:
                        thesis_urls.append(full_url)
        
        return thesis_urls
    
    async def _scrape_thesis_page(self, url: str) -> str:
        """Scrape a specific thesis page and extract text content."""
        try:
            html = await self.fetch_page(url)
            soup = self.parse_html(html)
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Extract text from main content areas
            content_areas = soup.find_all(['main', 'article', 'section', 'div'], 
                                        class_=re.compile('content|main|article|thesis|about'))
            
            if content_areas:
                text = ' '.join([area.get_text() for area in content_areas])
            else:
                text = soup.get_text()
            
            # Clean up text
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            return text[:5000]  # Limit text length
            
        except Exception as e:
            logger.error(f"Error scraping {url}: {str(e)}")
            return ""
    
    def _extract_investment_preferences(self, text: str, focus: Dict[str, List[str]]):
        """Extract investment preferences from text content."""
        text_lower = text.lower()
        
        # Extract stages
        for stage in self.INVESTMENT_KEYWORDS["stages"]:
            if stage in text_lower and stage not in focus["stages"]:
                focus["stages"].append(stage)
        
        # Extract sectors
        for sector in self.INVESTMENT_KEYWORDS["sectors"]:
            if sector in text_lower and sector not in focus["sectors"]:
                # Map common terms to standard sectors
                if sector in ["ai", "artificial intelligence", "machine learning", "ml"]:
                    if "AI/ML" not in focus["sectors"]:
                        focus["sectors"].append("AI/ML")
                elif sector in ["saas", "b2b", "enterprise"]:
                    if "B2B SaaS" not in focus["sectors"]:
                        focus["sectors"].append("B2B SaaS")
                elif sector in ["fintech", "financial"]:
                    if "Fintech" not in focus["sectors"]:
                        focus["sectors"].append("Fintech")
                elif sector in ["healthcare", "health", "biotech", "medtech"]:
                    if "Healthcare" not in focus["sectors"]:
                        focus["sectors"].append("Healthcare")
                elif sector in ["crypto", "blockchain", "web3", "defi"]:
                    if "Crypto" not in focus["sectors"]:
                        focus["sectors"].append("Crypto")
                elif sector in ["climate", "cleantech", "sustainability"]:
                    if "Climate Tech" not in focus["sectors"]:
                        focus["sectors"].append("Climate Tech")
                elif sector in ["deeptech", "deep tech", "hardware", "robotics"]:
                    if "Deep Tech" not in focus["sectors"]:
                        focus["sectors"].append("Deep Tech")
                else:
                    focus["sectors"].append(sector)
        
        # Extract check sizes
        for pattern in self.INVESTMENT_KEYWORDS["check_sizes"]:
            matches = re.findall(pattern, text_lower)
            focus["check_sizes"].extend(matches)
        
        # Extract geography
        for geo in self.INVESTMENT_KEYWORDS["geography"]:
            if geo in text_lower and geo not in focus["geography"]:
                focus["geography"].append(geo)
    
    def _extract_team_info(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """Extract team member information."""
        team_members = []
        
        # Look for team sections
        team_sections = soup.find_all(['section', 'div'], 
                                    class_=re.compile('team|people|partners|leadership'))
        
        for section in team_sections:
            # Find individual team member cards/divs
            member_cards = section.find_all(['div', 'article'], 
                                          class_=re.compile('member|person|partner|profile'))
            
            for card in member_cards[:10]:  # Limit to 10 members
                member = {}
                
                # Extract name
                name_elem = card.find(['h2', 'h3', 'h4', 'h5'], 
                                    class_=re.compile('name|title'))
                if name_elem:
                    member['name'] = self.clean_text(name_elem.text)
                
                # Extract role/title
                role_elem = card.find(['p', 'span', 'div'], 
                                    class_=re.compile('role|title|position'))
                if role_elem:
                    member['role'] = self.clean_text(role_elem.text)
                
                # Extract bio
                bio_elem = card.find(['p', 'div'], 
                                   class_=re.compile('bio|description|about'))
                if bio_elem:
                    member['bio'] = self.clean_text(bio_elem.text)[:500]
                
                # Extract social links
                member['social_links'] = self.extract_social_links(card)
                
                if member.get('name'):
                    team_members.append(member)
        
        return team_members
    
    def _extract_portfolio_companies(self, soup: BeautifulSoup) -> List[str]:
        """Extract portfolio company names."""
        companies = []
        
        # Look for portfolio sections
        portfolio_sections = soup.find_all(['section', 'div'], 
                                         class_=re.compile('portfolio|investments|companies'))
        
        for section in portfolio_sections:
            # Find company names in various formats
            company_elems = section.find_all(['h3', 'h4', 'h5', 'a', 'span'], 
                                           class_=re.compile('company|portfolio|name'))
            
            for elem in company_elems[:20]:  # Limit to 20 companies
                company_name = self.clean_text(elem.text)
                if company_name and len(company_name) > 2 and company_name not in companies:
                    companies.append(company_name)
        
        return companies


async def analyze_vc_website(url: str) -> Dict[str, Any]:
    """Main function to analyze a VC website and extract thesis."""
    scraper = VCWebsiteScraper()
    result = await scraper.scrape(url)
    
    if result.success:
        return {
            "success": True,
            "data": result.data,
            "scraped_at": result.scraped_at.isoformat()
        }
    else:
        return {
            "success": False,
            "error": result.error,
            "url": url
        }