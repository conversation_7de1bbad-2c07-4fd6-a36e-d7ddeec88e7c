"""Extract investment thesis from VC websites using AI."""

import asyncio
import aiohttp
from typing import Dict, Any, Optional
import logging
from datetime import datetime
from bs4 import BeautifulSoup
import re

logger = logging.getLogger(__name__)


class VCThesisExtractor:
    """Extract investment thesis and preferences from VC websites."""
    
    async def extract_thesis_from_url(self, vc_name: str, website_url: str) -> Dict[str, Any]:
        """
        Extract investment thesis from a VC website.
        
        In production, this would:
        1. Use Firecrawl or similar to get clean website content
        2. Feed to GPT-4 to extract structured thesis info
        """
        # For MVP, return simulated extraction based on VC
        mock_extractions = {
            "https://www.sequoiacap.com": {
                "thesis": "We partner with daring founders from idea to IPO and beyond. We focus on companies that can define categories and create lasting value.",
                "sectors": ["Enterprise", "Consumer", "AI/ML", "Fintech", "Healthcare"],
                "stages": ["Seed", "Series A", "Series B", "Growth"],
                "check_sizes": {"min": 1000000, "max": 100000000},
                "key_criteria": [
                    "Exceptional founding teams",
                    "Large addressable markets",
                    "Product-market fit signals",
                    "Defensible technology or business model"
                ],
                "portfolio_themes": [
                    "AI infrastructure",
                    "Developer tools",
                    "Financial services innovation",
                    "Healthcare transformation"
                ]
            },
            "https://a16z.com": {
                "thesis": "We invest in bold entrepreneurs building the future through technology across AI, bio, crypto, and American dynamism.",
                "sectors": ["AI/ML", "Crypto", "Biotech", "Enterprise", "Consumer"],
                "stages": ["Seed", "Series A", "Series B", "Series C"],
                "check_sizes": {"min": 500000, "max": 150000000},
                "key_criteria": [
                    "Technical founders",
                    "Disruptive technology",
                    "Network effects potential",
                    "Category-defining vision"
                ],
                "portfolio_themes": [
                    "AI-first applications",
                    "Web3 infrastructure",
                    "Computational biology",
                    "American industrial base"
                ]
            }
        }
        
        # Simulate extraction delay
        await asyncio.sleep(0.5)
        
        # Return mock data or generic extraction
        if website_url in mock_extractions:
            extraction = mock_extractions[website_url]
        else:
            # Generic extraction for unknown VCs
            extraction = await self._extract_generic_thesis(vc_name, website_url)
        
        return {
            "vc_name": vc_name,
            "website": website_url,
            "extracted_thesis": extraction,
            "extraction_date": datetime.utcnow().isoformat(),
            "extraction_method": "mock_data"  # In production: "gpt4_analysis"
        }
    
    async def _extract_generic_thesis(self, vc_name: str, website_url: str) -> Dict[str, Any]:
        """Extract generic thesis for unknown VCs."""
        # In production, this would actually scrape and analyze
        return {
            "thesis": f"{vc_name} invests in innovative technology companies with strong growth potential.",
            "sectors": ["Technology", "B2B SaaS", "AI/ML"],
            "stages": ["Seed", "Series A"],
            "check_sizes": {"min": 1000000, "max": 25000000},
            "key_criteria": [
                "Strong founding team",
                "Scalable business model",
                "Clear market opportunity"
            ],
            "portfolio_themes": [
                "Digital transformation",
                "AI and automation",
                "Enterprise software"
            ]
        }
    
    def parse_thesis_into_preferences(self, extracted_thesis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse extracted thesis into structured preferences for matching.
        """
        thesis_data = extracted_thesis.get("extracted_thesis", {})
        
        return {
            "investment_thesis": thesis_data.get("thesis", ""),
            "preferred_sectors": thesis_data.get("sectors", []),
            "preferred_stages": thesis_data.get("stages", []),
            "check_size_min": thesis_data.get("check_sizes", {}).get("min", 0),
            "check_size_max": thesis_data.get("check_sizes", {}).get("max", 0),
            "key_investment_criteria": thesis_data.get("key_criteria", []),
            "portfolio_themes": thesis_data.get("portfolio_themes", []),
            "extracted_at": extracted_thesis.get("extraction_date", "")
        }
    
    async def extract_from_about_page(self, website_url: str) -> Optional[str]:
        """
        Extract thesis specifically from about/investment pages.
        
        In production, would navigate to:
        - /about
        - /investment-philosophy
        - /thesis
        - /portfolio
        """
        # Construct potential thesis page URLs
        thesis_urls = [
            f"{website_url}/about",
            f"{website_url}/investment-philosophy",
            f"{website_url}/thesis",
            f"{website_url}/approach"
        ]
        
        # In production, try each URL and extract content
        # For now, return None to use default extraction
        return None


async def extract_vc_thesis(vc_name: str, website: str) -> Dict[str, Any]:
    """Main function to extract VC thesis."""
    extractor = VCThesisExtractor()
    
    # Extract thesis
    extraction = await extractor.extract_thesis_from_url(vc_name, website)
    
    # Parse into preferences
    preferences = extractor.parse_thesis_into_preferences(extraction)
    
    return {
        "extraction": extraction,
        "preferences": preferences,
        "success": True
    }


# Example VCs to extract thesis from
KNOWN_VCS = [
    {
        "name": "Bessemer Venture Partners",
        "website": "https://www.bvp.com",
        "description": "Early and growth stage venture capital"
    },
    {
        "name": "Greylock Partners",
        "website": "https://greylock.com", 
        "description": "Early stage venture capital"
    },
    {
        "name": "Accel",
        "website": "https://www.accel.com",
        "description": "Early and growth stage investments"
    }
]


if __name__ == "__main__":
    # Test the extractor
    async def test():
        for vc in KNOWN_VCS[:2]:
            result = await extract_vc_thesis(vc["name"], vc["website"])
            print(f"\n{vc['name']}:")
            print(f"  Thesis: {result['preferences']['investment_thesis'][:100]}...")
            print(f"  Sectors: {', '.join(result['preferences']['preferred_sectors'])}")
            print(f"  Stages: {', '.join(result['preferences']['preferred_stages'])}")
    
    asyncio.run(test())