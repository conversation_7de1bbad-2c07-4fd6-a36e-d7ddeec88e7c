"""Sample VC data for testing and initial population."""

SAMPLE_VCS = [
    {
        "name": "<PERSON><PERSON><PERSON>",
        "website": "https://a16z.com",
        "description": "Venture capital firm that backs bold entrepreneurs building the future through technology",
        "known_sectors": ["AI/ML", "Crypto", "B2B SaaS", "Biotech"],
        "known_stages": ["Seed", "Series A", "Series B", "Growth"]
    },
    {
        "name": "Sequoia Capital",
        "website": "https://www.sequoiacap.com",
        "description": "Helps daring founders build legendary companies from idea to IPO and beyond",
        "known_sectors": ["AI/ML", "B2B SaaS", "Fintech", "Healthcare"],
        "known_stages": ["Seed", "Series A", "Series B", "Series C"]
    },
    {
        "name": "Y Combinator",
        "website": "https://www.ycombinator.com",
        "description": "Startup accelerator that has funded over 4,000 companies",
        "known_sectors": ["All sectors"],
        "known_stages": ["Pre-seed", "Seed"]
    },
    {
        "name": "Initialized Capital",
        "website": "https://initialized.com",
        "description": "Early-stage venture capital firm founded by former Y Combinator partners",
        "known_sectors": ["B2B SaaS", "Developer Tools", "AI/ML"],
        "known_stages": ["Pre-seed", "Seed"]
    },
    {
        "name": "First Round Capital",
        "website": "https://firstround.com",
        "description": "Seed-stage venture firm focused on building a vibrant community of entrepreneurs",
        "known_sectors": ["B2B SaaS", "Marketplace", "Fintech"],
        "known_stages": ["Pre-seed", "Seed"]
    },
    {
        "name": "Bessemer Venture Partners",
        "website": "https://www.bvp.com",
        "description": "The world's oldest venture capital firm investing from seed to growth",
        "known_sectors": ["Cloud", "AI/ML", "Healthcare", "Fintech"],
        "known_stages": ["Seed", "Series A", "Series B", "Growth"]
    },
    {
        "name": "Accel",
        "website": "https://www.accel.com",
        "description": "Global venture capital firm backing entrepreneurs who use technology to disrupt industries",
        "known_sectors": ["B2B SaaS", "Marketplace", "Fintech", "Security"],
        "known_stages": ["Seed", "Series A", "Series B"]
    },
    {
        "name": "Lightspeed Venture Partners",
        "website": "https://lsvp.com",
        "description": "Global multi-stage venture capital firm focused on enterprise, consumer, and health",
        "known_sectors": ["Enterprise", "Consumer", "Healthcare", "Crypto"],
        "known_stages": ["Seed", "Series A", "Series B", "Growth"]
    },
    {
        "name": "Greylock Partners",
        "website": "https://greylock.com",
        "description": "Committed to helping entrepreneurs build market-transforming companies",
        "known_sectors": ["AI/ML", "B2B SaaS", "Consumer", "Fintech"],
        "known_stages": ["Seed", "Series A", "Series B"]
    },
    {
        "name": "Kleiner Perkins",
        "website": "https://www.kleinerperkins.com",
        "description": "American venture capital firm investing in incubation, seed, and growth companies",
        "known_sectors": ["Healthcare", "Climate Tech", "AI/ML", "B2B SaaS"],
        "known_stages": ["Seed", "Series A", "Series B", "Growth"]
    }
]


def get_sample_vcs(limit: int = None):
    """Get sample VC data."""
    if limit:
        return SAMPLE_VCS[:limit]
    return SAMPLE_VCS