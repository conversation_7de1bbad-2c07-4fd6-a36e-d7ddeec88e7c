"""YC Company scraper to discover new startups."""

import asyncio
import aiohttp
from typing import List, Dict, Any, Optional
import logging
from datetime import datetime
import json

logger = logging.getLogger(__name__)


class YCCompanyScraper:
    """Scrape Y Combinator companies using the unofficial API."""
    
    # Using the unofficial YC API that aggregates public data
    API_BASE_URL = "https://yc-oss.github.io/api"
    META_URL = f"{API_BASE_URL}/meta.json"
    
    async def scrape_companies(self, limit: int = 50, batch_filter: Optional[str] = None) -> List[Dict[str, Any]]:
        """Scrape YC companies from the unofficial API."""
        companies = []
        
        try:
            async with aiohttp.ClientSession() as session:
                # First, get the meta information to find available batches
                async with session.get(self.META_URL) as response:
                    if response.status != 200:
                        logger.error(f"Failed to fetch meta data: {response.status}")
                        return self._get_fallback_data(limit)
                    
                    meta_data = await response.json()
                
                # Determine which endpoint to use
                if batch_filter:
                    # Try to get specific batch data
                    batch_slug = batch_filter.lower().replace(" ", "-")
                    batch_url = f"{self.API_BASE_URL}/batches/{batch_slug}.json"
                else:
                    # Get all companies
                    batch_url = f"{self.API_BASE_URL}/companies/all.json"
                
                # Fetch company data
                async with session.get(batch_url) as response:
                    if response.status != 200:
                        logger.error(f"Failed to fetch company data from {batch_url}: {response.status}")
                        return self._get_fallback_data(limit)
                    
                    all_companies = await response.json()
                
                # Process and transform company data
                for company_data in all_companies[:limit]:
                    transformed = self._transform_company_data(company_data)
                    if transformed:
                        companies.append(transformed)
                
                logger.info(f"Successfully fetched {len(companies)} YC companies from API")
                return companies
                
        except Exception as e:
            logger.error(f"Error fetching YC companies: {str(e)}")
            return self._get_fallback_data(limit)
    
    def _transform_company_data(self, api_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Transform YC API data to our internal format."""
        try:
            # Extract stage from various indicators
            stage = self._determine_stage(api_data)
            
            # Map industry/subindustry to our sector format
            sector = self._map_to_sector(
                api_data.get("industry", ""),
                api_data.get("subindustry", ""),
                api_data.get("tags", [])
            )
            
            return {
                "name": api_data.get("name", ""),
                "description": api_data.get("long_description", "") or api_data.get("one_liner", ""),
                "website": api_data.get("website", ""),
                "batch": api_data.get("batch", ""),
                "sector": sector,
                "stage": stage,
                "team_size": api_data.get("team_size", 0),
                "founded": self._extract_founded_year(api_data),
                "source": "yc_api",
                "discovered_at": datetime.utcnow().isoformat(),
                "yc_profile_url": api_data.get("url", ""),
                "yc_id": api_data.get("id"),
                "all_locations": api_data.get("all_locations", ""),
                "tags": api_data.get("tags", []),
                "is_hiring": api_data.get("isHiring", False),
                "status": api_data.get("status", "Active"),
                "top_company": api_data.get("top_company", False)
            }
        except Exception as e:
            logger.warning(f"Failed to transform company data: {str(e)}")
            return None
    
    def _determine_stage(self, api_data: Dict[str, Any]) -> str:
        """Determine company stage from various indicators."""
        # Check explicit stage field
        stage = api_data.get("stage", "").lower()
        if "early" in stage:
            return "Seed"
        elif "growth" in stage:
            return "Series A"
        
        # Check status
        status = api_data.get("status", "").lower()
        if "acquired" in status:
            return "Acquired"
        elif "public" in status:
            return "Public"
        
        # Estimate from batch year
        batch = api_data.get("batch", "")
        return self.extract_stage_from_batch(batch)
    
    def _map_to_sector(self, industry: str, subindustry: str, tags: List[str]) -> str:
        """Map YC's industry classification to our sectors."""
        # Combine all text for analysis
        text = f"{industry} {subindustry} {' '.join(tags)}".lower()
        
        # Priority mapping based on specific keywords
        if any(term in text for term in ["artificial intelligence", "machine learning", "ai", "ml"]):
            return "AI/ML"
        elif any(term in text for term in ["b2b", "saas", "software"]):
            return "B2B SaaS"
        elif any(term in text for term in ["fintech", "financial", "payments", "banking"]):
            return "Fintech"
        elif any(term in text for term in ["healthcare", "health", "medical", "biotech"]):
            return "Healthcare"
        elif any(term in text for term in ["crypto", "blockchain", "web3", "defi"]):
            return "Crypto"
        elif any(term in text for term in ["climate", "carbon", "sustainability"]):
            return "Climate Tech"
        elif any(term in text for term in ["robotics", "automation", "hardware"]):
            return "Robotics"
        elif any(term in text for term in ["developer tools", "devtools", "api"]):
            return "DevTools"
        elif any(term in text for term in ["consumer", "marketplace", "e-commerce"]):
            return "B2C"
        elif any(term in text for term in ["deep tech", "hard tech", "quantum"]):
            return "Deep Tech"
        
        # Default to the industry if no mapping found
        return industry or "Other"
    
    def _extract_founded_year(self, api_data: Dict[str, Any]) -> str:
        """Extract founded year from launched_at timestamp or batch."""
        # Try to use launched_at timestamp
        launched_at = api_data.get("launched_at")
        if launched_at:
            try:
                return str(datetime.fromtimestamp(launched_at).year)
            except:
                pass
        
        # Fall back to batch year
        batch = api_data.get("batch", "")
        if batch:
            # Extract year from batch (e.g., "Winter 2023" -> "2023")
            parts = batch.split()
            for part in parts:
                if part.isdigit() and len(part) == 4:
                    return part
        
        return str(datetime.now().year)
    
    def _get_fallback_data(self, limit: int) -> List[Dict[str, Any]]:
        """Provide fallback data if API is unavailable."""
        logger.warning("Using fallback YC company data")
        
        # Realistic fallback data based on actual YC companies
        fallback_companies = [
            {
                "name": "Perplexity AI",
                "description": "AI-powered search engine that provides direct answers to questions using large language models and real-time web data.",
                "website": "https://perplexity.ai",
                "batch": "W23",
                "sector": "AI/ML",
                "stage": "Series A",
                "team_size": 45,
                "founded": "2022"
            },
            {
                "name": "Brex",
                "description": "Corporate cards and expense management software designed for startups and growing businesses.",
                "website": "https://brex.com",
                "batch": "W17",
                "sector": "Fintech",
                "stage": "Series D",
                "team_size": 1200,
                "founded": "2017"
            },
            {
                "name": "Retool",
                "description": "Low-code platform for building internal tools quickly with pre-built components.",
                "website": "https://retool.com",
                "batch": "W17",
                "sector": "DevTools",
                "stage": "Series C",
                "team_size": 400,
                "founded": "2017"
            }
        ]
        
        for company in fallback_companies[:limit]:
            company.update({
                "source": "yc_fallback",
                "discovered_at": datetime.utcnow().isoformat(),
                "yc_profile_url": f"https://www.ycombinator.com/companies/{company['name'].lower().replace(' ', '-')}"
            })
        
        return fallback_companies[:limit]
    
    def extract_stage_from_batch(self, batch: str) -> str:
        """Estimate stage based on YC batch."""
        if not batch:
            return "Pre-seed"
        
        # Extract year from batch (e.g., W24 -> 2024)
        try:
            year = int("20" + batch[1:3])
            current_year = datetime.now().year
            years_since = current_year - year
            
            if years_since <= 1:
                return "Pre-seed"
            elif years_since <= 2:
                return "Seed"
            elif years_since <= 3:
                return "Series A"
            else:
                return "Series B"
        except:
            return "Seed"
    
    def normalize_sector(self, yc_category: str) -> str:
        """Map YC categories to our standard sectors."""
        mapping = {
            "artificial intelligence": "AI/ML",
            "saas": "B2B SaaS",
            "fintech": "Fintech",
            "healthcare": "Healthcare",
            "biotech": "Biotech",
            "developer tools": "DevTools",
            "consumer": "B2C",
            "marketplace": "Marketplace",
            "crypto": "Crypto",
            "climate": "Climate Tech",
            "robotics": "Robotics",
            "deep tech": "Deep Tech"
        }
        
        category_lower = yc_category.lower()
        for key, value in mapping.items():
            if key in category_lower:
                return value
        
        return yc_category


async def discover_new_yc_companies():
    """Main function to discover new YC companies."""
    scraper = YCCompanyScraper()
    
    # Get current year for filtering recent batches
    current_year = datetime.now().year
    current_month = datetime.now().month
    
    # Determine recent batches (last 2 years of YC batches)
    recent_batches = []
    for year in [current_year - 1, current_year]:
        recent_batches.extend([
            f"W{str(year)[2:]}",  # Winter batch
            f"S{str(year)[2:]}"   # Summer batch
        ])
    
    # Fetch companies and filter for recent ones
    companies = await scraper.scrape_companies(limit=100)
    
    # Filter for recently added companies based on batch
    recent_companies = []
    for company in companies:
        batch = company.get("batch", "")
        # Check if it's a recent batch
        for recent_batch in recent_batches:
            if recent_batch in batch:
                recent_companies.append(company)
                break
    
    # Also filter by status - only active companies
    recent_companies = [
        c for c in recent_companies 
        if c.get("status", "").lower() == "active"
    ]
    
    # Sort by batch (newest first)
    recent_companies.sort(key=lambda x: x.get("batch", ""), reverse=True)
    
    logger.info(f"Found {len(recent_companies)} recent YC companies")
    return recent_companies


if __name__ == "__main__":
    # Test the scraper
    async def test():
        companies = await discover_new_yc_companies()
        for company in companies:
            print(f"\n{company['name']} ({company['batch']})")
            print(f"  {company['description']}")
            print(f"  Stage: {company['stage']}, Sector: {company['sector']}")
    
    asyncio.run(test())