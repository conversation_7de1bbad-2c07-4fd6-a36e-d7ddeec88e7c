"""LinkedIn company scraper using Playwright for dynamic content."""

from typing import Dict, Any, Optional
from bs4 import BeautifulSoup
import re
import logging
import asyncio

try:
    from playwright.async_api import async_playwright, <PERSON>rowser, Page
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    Browser = None
    Page = None

from .base import BaseScraper, ScraperResult

logger = logging.getLogger(__name__)


class LinkedInScraper(BaseScraper):
    """Scraper for LinkedIn company pages using Playwright."""
    
    def __init__(self, timeout: int = 30, user_agent: Optional[str] = None):
        super().__init__(timeout, user_agent)
        self.browser: Optional[Browser] = None
        self.playwright = None
    
    async def setup_browser(self):
        """Setup Playwright browser instance."""
        if not PLAYWRIGHT_AVAILABLE:
            raise ImportError(
                "Playwright is not installed. Install it with: pip install playwright && playwright install chromium"
            )
        
        if not self.browser:
            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.launch(
                headless=True,
                args=['--no-sandbox', '--disable-setuid-sandbox']
            )
    
    async def cleanup(self):
        """Clean up browser resources."""
        if self.browser:
            await self.browser.close()
        if self.playwright:
            await self.playwright.stop()
    
    async def fetch_page(self, url: str) -> str:
        """
        Fetch LinkedIn page using Playwright.
        
        Args:
            url: LinkedIn company URL
            
        Returns:
            HTML content
        """
        await self.setup_browser()
        
        page = await self.browser.new_page()
        try:
            # Set viewport and user agent
            await page.set_viewport_size({"width": 1920, "height": 1080})
            await page.set_extra_http_headers({"User-Agent": self.user_agent})
            
            # Navigate to URL
            await page.goto(url, wait_until="networkidle", timeout=self.timeout * 1000)
            
            # Wait for content to load
            await page.wait_for_selector('h1', timeout=10000)
            
            # Scroll to load lazy content
            await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            await asyncio.sleep(2)  # Wait for lazy loading
            
            # Get HTML content
            content = await page.content()
            return content
            
        finally:
            await page.close()
    
    async def extract_data(self, soup: BeautifulSoup, url: str) -> Dict[str, Any]:
        """
        Extract company data from LinkedIn page.
        
        Args:
            soup: BeautifulSoup object
            url: Original LinkedIn URL
            
        Returns:
            Dictionary of extracted data
        """
        data = {
            'linkedin_url': url,
            'name': self._extract_company_name(soup),
            'tagline': self._extract_tagline(soup),
            'description': self._extract_description(soup),
            'industry': self._extract_industry(soup),
            'company_size': self._extract_company_size(soup),
            'headquarters': self._extract_headquarters(soup),
            'founded': self._extract_founded_year(soup),
            'website': self._extract_website(soup),
            'specialties': self._extract_specialties(soup),
            'employee_count': self._extract_employee_count(soup),
        }
        
        # Remove None values
        return {k: v for k, v in data.items() if v is not None}
    
    def _extract_company_name(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract company name."""
        # Try main h1 tag
        h1 = soup.find('h1', class_=re.compile('org-top-card-summary__title'))
        if h1:
            return self.clean_text(h1.get_text())
        
        # Try alternative selector
        h1 = soup.find('h1')
        if h1:
            return self.clean_text(h1.get_text())
        
        return None
    
    def _extract_tagline(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract company tagline."""
        tagline = soup.find('p', class_=re.compile('org-top-card-summary__tagline'))
        if tagline:
            return self.clean_text(tagline.get_text())
        
        return None
    
    def _extract_description(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract company description."""
        # Look for about section
        about_section = soup.find('section', class_=re.compile('org-about'))
        if about_section:
            desc = about_section.find('p')
            if desc:
                return self.clean_text(desc.get_text())
        
        # Try overview section
        overview = soup.find('div', class_=re.compile('org-overview'))
        if overview:
            p = overview.find('p')
            if p:
                return self.clean_text(p.get_text())
        
        return None
    
    def _extract_industry(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract industry information."""
        # Look for industry in details
        industry_dt = soup.find('dt', string=re.compile('Industry', re.I))
        if industry_dt:
            industry_dd = industry_dt.find_next_sibling('dd')
            if industry_dd:
                return self.clean_text(industry_dd.get_text())
        
        # Try alternative locations
        industry_div = soup.find('div', class_=re.compile('org-top-card-summary-info-list__info-item'))
        if industry_div:
            return self.clean_text(industry_div.get_text())
        
        return None
    
    def _extract_company_size(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract company size range."""
        # Look for company size in details
        size_dt = soup.find('dt', string=re.compile('Company size', re.I))
        if size_dt:
            size_dd = size_dt.find_next_sibling('dd')
            if size_dd:
                size_text = self.clean_text(size_dd.get_text())
                # Extract just the range part
                match = re.search(r'(\d+(?:,\d+)?(?:\+|\s*-\s*\d+(?:,\d+)?)?)\s*employees', size_text, re.I)
                if match:
                    return match.group(1)
        
        return None
    
    def _extract_employee_count(self, soup: BeautifulSoup) -> Optional[int]:
        """Extract exact employee count if available."""
        # Look for employee count
        emp_link = soup.find('a', href=re.compile('/people/'))
        if emp_link:
            text = emp_link.get_text()
            match = re.search(r'(\d+(?:,\d+)?)', text)
            if match:
                return int(match.group(1).replace(',', ''))
        
        # Try to parse from company size
        size = self._extract_company_size(soup)
        if size:
            # Extract lower bound for ranges
            match = re.match(r'(\d+(?:,\d+)?)', size)
            if match:
                return int(match.group(1).replace(',', ''))
        
        return None
    
    def _extract_headquarters(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract headquarters location."""
        # Look for headquarters in details
        hq_dt = soup.find('dt', string=re.compile('Headquarters', re.I))
        if hq_dt:
            hq_dd = hq_dt.find_next_sibling('dd')
            if hq_dd:
                return self.clean_text(hq_dd.get_text())
        
        return None
    
    def _extract_founded_year(self, soup: BeautifulSoup) -> Optional[int]:
        """Extract founding year."""
        # Look for founded in details
        founded_dt = soup.find('dt', string=re.compile('Founded', re.I))
        if founded_dt:
            founded_dd = founded_dt.find_next_sibling('dd')
            if founded_dd:
                text = founded_dd.get_text()
                match = re.search(r'(\d{4})', text)
                if match:
                    year = int(match.group(1))
                    if 1800 <= year <= 2024:
                        return year
        
        return None
    
    def _extract_website(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract company website."""
        # Look for website in details
        website_dt = soup.find('dt', string=re.compile('Website', re.I))
        if website_dt:
            website_dd = website_dt.find_next_sibling('dd')
            if website_dd:
                link = website_dd.find('a')
                if link and link.get('href'):
                    return link['href']
        
        # Try action buttons
        website_link = soup.find('a', class_=re.compile('org-top-card-primary-actions__action'))
        if website_link and 'website' in website_link.get_text().lower():
            href = website_link.get('href')
            if href:
                # Extract actual URL from LinkedIn redirect
                match = re.search(r'url=([^&]+)', href)
                if match:
                    import urllib.parse
                    return urllib.parse.unquote(match.group(1))
        
        return None
    
    def _extract_specialties(self, soup: BeautifulSoup) -> list:
        """Extract company specialties/skills."""
        specialties = []
        
        # Look for specialties in details
        spec_dt = soup.find('dt', string=re.compile('Specialties', re.I))
        if spec_dt:
            spec_dd = spec_dt.find_next_sibling('dd')
            if spec_dd:
                text = spec_dd.get_text()
                # Split by common delimiters
                items = re.split(r'[,;]|(?:\band\b)', text)
                for item in items:
                    cleaned = self.clean_text(item)
                    if cleaned and len(cleaned) > 2:
                        specialties.append(cleaned)
        
        return specialties
    
    async def scrape(self, url: str) -> ScraperResult:
        """
        Main scraping method with browser cleanup.
        
        Args:
            url: LinkedIn company URL
            
        Returns:
            ScraperResult with extracted data
        """
        try:
            # Validate LinkedIn URL
            if 'linkedin.com/company/' not in url:
                raise ValueError("Invalid LinkedIn company URL")
            
            result = await super().scrape(url)
            return result
            
        finally:
            # Always cleanup browser
            await self.cleanup()
    
    async def scrape_multiple(self, urls: list, max_concurrent: int = 3) -> list:
        """
        Scrape multiple LinkedIn URLs with lower concurrency.
        
        LinkedIn rate limits are strict, so we use lower concurrency.
        
        Args:
            urls: List of LinkedIn company URLs
            max_concurrent: Maximum concurrent requests (default 3)
            
        Returns:
            List of scraper results
        """
        # Use lower concurrency for LinkedIn
        return await super().scrape_multiple(urls, max_concurrent=min(max_concurrent, 3))