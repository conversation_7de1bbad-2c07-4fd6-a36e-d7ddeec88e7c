"""Health check utilities for Celery workers."""

import logging
from typing import Dict, Any
from datetime import datetime, timedelta

from src.workers.celery_app import celery_app
from src.core.config import settings

logger = logging.getLogger(__name__)


def check_celery_status() -> Dict[str, Any]:
    """Check Celery worker and broker status.
    
    Returns:
        Dictionary with Celery health information
    """
    try:
        # Get worker stats
        stats = celery_app.control.inspect().stats()
        active_workers = celery_app.control.inspect().active()
        registered_tasks = celery_app.control.inspect().registered()
        
        # Check if any workers are available
        worker_count = len(stats) if stats else 0
        is_healthy = worker_count > 0
        
        # Get active tasks count
        active_task_count = 0
        if active_workers:
            for worker_tasks in active_workers.values():
                active_task_count += len(worker_tasks)
        
        # Get registered tasks count
        total_registered_tasks = 0
        if registered_tasks:
            for tasks in registered_tasks.values():
                total_registered_tasks += len(tasks)
        
        return {
            "healthy": is_healthy,
            "worker_count": worker_count,
            "active_tasks": active_task_count,
            "registered_tasks": total_registered_tasks,
            "workers": list(stats.keys()) if stats else [],
            "broker_url": settings.celery_broker_url.replace(
                settings.celery_broker_url.split('@')[0].split('://')[1], 
                "***"
            ) if '@' in settings.celery_broker_url else settings.celery_broker_url
        }
        
    except Exception as e:
        logger.error(f"Celery health check failed: {e}")
        return {
            "healthy": False,
            "error": str(e),
            "broker_url": "configured"
        }


def check_celery_beat_status() -> Dict[str, Any]:
    """Check Celery Beat scheduler status.
    
    Returns:
        Dictionary with Celery Beat health information
    """
    try:
        # Check if beat schedule is configured
        beat_schedule = celery_app.conf.beat_schedule
        scheduled_tasks = len(beat_schedule) if beat_schedule else 0
        
        # Note: Actual beat process status would require external monitoring
        # This just checks configuration
        return {
            "healthy": scheduled_tasks > 0,
            "scheduled_tasks": scheduled_tasks,
            "schedule": list(beat_schedule.keys()) if beat_schedule else [],
            "note": "Beat process status requires external monitoring"
        }
        
    except Exception as e:
        logger.error(f"Celery Beat health check failed: {e}")
        return {
            "healthy": False,
            "error": str(e)
        }


async def test_celery_task_execution() -> Dict[str, Any]:
    """Test if Celery can execute a simple task.
    
    Returns:
        Dictionary with task execution test results
    """
    try:
        # Define a simple test task
        @celery_app.task
        def health_check_task():
            return {"status": "success", "timestamp": datetime.utcnow().isoformat()}
        
        # Send task with timeout
        result = health_check_task.apply_async()
        
        # Wait for result (max 5 seconds)
        task_result = result.get(timeout=5)
        
        return {
            "healthy": True,
            "task_id": result.id,
            "result": task_result,
            "execution_time": "< 5 seconds"
        }
        
    except Exception as e:
        logger.error(f"Celery task execution test failed: {e}")
        return {
            "healthy": False,
            "error": str(e),
            "note": "Worker may not be running"
        }


def get_queue_info() -> Dict[str, Any]:
    """Get information about Celery queues.
    
    Returns:
        Dictionary with queue statistics
    """
    try:
        # Get queue info from inspect
        active_queues = celery_app.control.inspect().active_queues()
        
        if not active_queues:
            return {
                "healthy": False,
                "error": "No active queues found",
                "note": "Workers may not be running"
            }
        
        queue_info = {}
        for worker, queues in active_queues.items():
            queue_info[worker] = [q['name'] for q in queues]
        
        # Count total queues
        total_queues = sum(len(queues) for queues in queue_info.values())
        
        return {
            "healthy": total_queues > 0,
            "total_queues": total_queues,
            "queues_by_worker": queue_info,
            "configured_queues": list(celery_app.conf.task_queues) if hasattr(celery_app.conf, 'task_queues') else []
        }
        
    except Exception as e:
        logger.error(f"Queue info retrieval failed: {e}")
        return {
            "healthy": False,
            "error": str(e)
        }