"""Background tasks for generating and updating embeddings."""

import asyncio
import logging
from datetime import datetime
from typing import List, <PERSON><PERSON>

from celery import shared_task
from sqlalchemy import text
from sqlalchemy.orm import Session

from src.core.config import get_settings
from src.core.ai.embeddings import HybridEmbeddingService, EmbeddingBatchProcessor
from src.core.models.startup import Startup as StartupDomain
from src.core.models.vc import VC as VCDomain
from src.database.setup import get_database

logger = logging.getLogger(__name__)
settings = get_settings()


@shared_task(name="generate_missing_embeddings")
def generate_missing_embeddings_task(entity_type: str = "all", batch_size: int = 20):
    """
    Background task to generate embeddings for entities without them.
    
    Args:
        entity_type: 'startup', 'vc', or 'all'
        batch_size: Number of entities to process in each batch
    """
    logger.info(f"Starting embedding generation for {entity_type} entities")
    
    # Run async function in sync context
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        result = loop.run_until_complete(
            _generate_missing_embeddings_async(entity_type, batch_size)
        )
        logger.info(f"Embedding generation completed: {result}")
        return result
    finally:
        loop.close()


async def _generate_missing_embeddings_async(entity_type: str, batch_size: int) -> dict:
    """Async implementation of embedding generation."""
    
    embedding_service = HybridEmbeddingService(settings.OPENAI_API_KEY)
    batch_processor = EmbeddingBatchProcessor(embedding_service, batch_size)
    
    results = {
        "startups_processed": 0,
        "startups_failed": 0,
        "vcs_processed": 0,
        "vcs_failed": 0,
        "start_time": datetime.utcnow().isoformat(),
        "errors": []
    }
    
    with get_database() as db:
        try:
            # Process startups
            if entity_type in ["startup", "all"]:
                logger.info("Processing startups...")
                startup_results = await _process_startup_embeddings(
                    db, batch_processor, batch_size
                )
                results["startups_processed"] = startup_results["processed"]
                results["startups_failed"] = startup_results["failed"]
                results["errors"].extend(startup_results["errors"])
            
            # Process VCs
            if entity_type in ["vc", "all"]:
                logger.info("Processing VCs...")
                vc_results = await _process_vc_embeddings(
                    db, batch_processor, batch_size
                )
                results["vcs_processed"] = vc_results["processed"]
                results["vcs_failed"] = vc_results["failed"]
                results["errors"].extend(vc_results["errors"])
            
            results["end_time"] = datetime.utcnow().isoformat()
            results["success"] = True
            
        except Exception as e:
            logger.error(f"Error in embedding generation: {str(e)}")
            results["success"] = False
            results["error"] = str(e)
    
    return results


async def _process_startup_embeddings(
    db: Session,
    batch_processor: EmbeddingBatchProcessor,
    batch_size: int
) -> dict:
    """Process embeddings for startups."""
    
    processed = 0
    failed = 0
    errors = []
    
    while True:
        # Find startups without embeddings
        query = text("""
            SELECT 
                id,
                name,
                sector,
                stage,
                description,
                team_size,
                monthly_revenue,
                website
            FROM startups 
            WHERE combined_vector IS NULL 
               OR embedding_updated_at IS NULL
               OR embedding_updated_at < updated_at
            ORDER BY monthly_revenue DESC
            LIMIT :batch_size
        """)
        
        result = db.execute(query, {"batch_size": batch_size})
        startups_data = result.fetchall()
        
        if not startups_data:
            logger.info("No more startups to process")
            break
        
        # Convert to domain objects
        startup_batch = []
        for row in startups_data:
            try:
                startup = StartupDomain(
                    name=row.name,
                    sector=row.sector,
                    stage=row.stage,
                    description=row.description or "",
                    team_size=row.team_size or 0,
                    monthly_revenue=row.monthly_revenue or 0,
                    website=row.website or ""
                )
                startup_batch.append((str(row.id), startup))
            except Exception as e:
                logger.error(f"Error creating startup domain object: {e}")
                failed += 1
                errors.append(f"Startup {row.id}: {str(e)}")
                continue
        
        # Process batch
        if startup_batch:
            try:
                batch_successful = await batch_processor.process_startup_batch(
                    db, startup_batch
                )
                processed += batch_successful
                failed += len(startup_batch) - batch_successful
                
                logger.info(f"Processed {batch_successful}/{len(startup_batch)} startups")
                
            except Exception as e:
                logger.error(f"Error processing startup batch: {e}")
                failed += len(startup_batch)
                errors.append(f"Batch error: {str(e)}")
        
        # Small delay between batches
        await asyncio.sleep(1)
    
    return {
        "processed": processed,
        "failed": failed,
        "errors": errors
    }


async def _process_vc_embeddings(
    db: Session,
    batch_processor: EmbeddingBatchProcessor,
    batch_size: int
) -> dict:
    """Process embeddings for VCs."""
    
    processed = 0
    failed = 0
    errors = []
    
    while True:
        # Find VCs without embeddings
        query = text("""
            SELECT 
                id,
                firm_name,
                thesis,
                website,
                sectors,
                stages,
                check_size_min,
                check_size_max
            FROM vcs 
            WHERE combined_vector IS NULL 
               OR embedding_updated_at IS NULL
               OR embedding_updated_at < updated_at
            ORDER BY check_size_max DESC
            LIMIT :batch_size
        """)
        
        result = db.execute(query, {"batch_size": batch_size})
        vcs_data = result.fetchall()
        
        if not vcs_data:
            logger.info("No more VCs to process")
            break
        
        # Convert to domain objects
        vc_batch = []
        for row in vcs_data:
            try:
                vc = VCDomain(
                    firm_name=row.firm_name,
                    thesis=row.thesis or "",
                    website=row.website or "",
                    sectors=list(row.sectors) if row.sectors else [],
                    stages=list(row.stages) if row.stages else [],
                    check_size_min=row.check_size_min or 0,
                    check_size_max=row.check_size_max or 0
                )
                vc_batch.append((str(row.id), vc))
            except Exception as e:
                logger.error(f"Error creating VC domain object: {e}")
                failed += 1
                errors.append(f"VC {row.id}: {str(e)}")
                continue
        
        # Process batch
        if vc_batch:
            try:
                batch_successful = await batch_processor.process_vc_batch(
                    db, vc_batch
                )
                processed += batch_successful
                failed += len(vc_batch) - batch_successful
                
                logger.info(f"Processed {batch_successful}/{len(vc_batch)} VCs")
                
            except Exception as e:
                logger.error(f"Error processing VC batch: {e}")
                failed += len(vc_batch)
                errors.append(f"Batch error: {str(e)}")
        
        # Small delay between batches
        await asyncio.sleep(1)
    
    return {
        "processed": processed,
        "failed": failed,
        "errors": errors
    }


@shared_task(name="update_single_embedding")
def update_single_embedding_task(entity_type: str, entity_id: str):
    """
    Update embedding for a single entity.
    
    Args:
        entity_type: 'startup' or 'vc'
        entity_id: UUID of the entity
    """
    logger.info(f"Updating embedding for {entity_type} {entity_id}")
    
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        result = loop.run_until_complete(
            _update_single_embedding_async(entity_type, entity_id)
        )
        return result
    finally:
        loop.close()


async def _update_single_embedding_async(entity_type: str, entity_id: str) -> dict:
    """Async implementation of single embedding update."""
    
    embedding_service = HybridEmbeddingService(settings.OPENAI_API_KEY)
    
    with get_database() as db:
        try:
            if entity_type == "startup":
                # Get startup data
                query = text("""
                    SELECT * FROM startups WHERE id = :entity_id::uuid
                """)
                result = db.execute(query, {"entity_id": entity_id})
                startup_data = result.fetchone()
                
                if not startup_data:
                    return {"success": False, "error": "Startup not found"}
                
                # Create domain object
                startup = StartupDomain(
                    name=startup_data.name,
                    sector=startup_data.sector,
                    stage=startup_data.stage,
                    description=startup_data.description or "",
                    team_size=startup_data.team_size or 0,
                    monthly_revenue=startup_data.monthly_revenue or 0,
                    website=startup_data.website or ""
                )
                startup.id = entity_id
                
                # Generate embeddings
                embeddings = await embedding_service.generate_startup_embeddings(startup)
                
                # Update database
                await embedding_service.update_startup_embeddings(db, entity_id, embeddings)
                
                return {
                    "success": True,
                    "entity_type": "startup",
                    "entity_id": entity_id,
                    "entity_name": startup.name
                }
                
            elif entity_type == "vc":
                # Get VC data
                query = text("""
                    SELECT * FROM vcs WHERE id = :entity_id::uuid
                """)
                result = db.execute(query, {"entity_id": entity_id})
                vc_data = result.fetchone()
                
                if not vc_data:
                    return {"success": False, "error": "VC not found"}
                
                # Create domain object
                vc = VCDomain(
                    firm_name=vc_data.firm_name,
                    thesis=vc_data.thesis or "",
                    website=vc_data.website or "",
                    sectors=list(vc_data.sectors) if vc_data.sectors else [],
                    stages=list(vc_data.stages) if vc_data.stages else [],
                    check_size_min=vc_data.check_size_min or 0,
                    check_size_max=vc_data.check_size_max or 0
                )
                vc.id = entity_id
                
                # Generate embeddings
                embeddings = await embedding_service.generate_vc_embeddings(vc)
                
                # Update database
                await embedding_service.update_vc_embeddings(db, entity_id, embeddings)
                
                return {
                    "success": True,
                    "entity_type": "vc",
                    "entity_id": entity_id,
                    "entity_name": vc.firm_name
                }
                
            else:
                return {"success": False, "error": f"Invalid entity type: {entity_type}"}
                
        except Exception as e:
            logger.error(f"Error updating embedding: {str(e)}")
            return {"success": False, "error": str(e)}


@shared_task(name="refresh_stale_embeddings")
def refresh_stale_embeddings_task(days_old: int = 30):
    """
    Refresh embeddings that are older than specified days.
    
    Args:
        days_old: Number of days after which embeddings are considered stale
    """
    logger.info(f"Refreshing embeddings older than {days_old} days")
    
    with get_database() as db:
        # Find stale embeddings
        query = text("""
            SELECT 
                'startup' as entity_type,
                id::text as entity_id,
                name as entity_name,
                embedding_updated_at
            FROM startups
            WHERE embedding_updated_at < NOW() - INTERVAL ':days days'
            UNION ALL
            SELECT 
                'vc' as entity_type,
                id::text as entity_id,
                firm_name as entity_name,
                embedding_updated_at
            FROM vcs
            WHERE embedding_updated_at < NOW() - INTERVAL ':days days'
            ORDER BY embedding_updated_at ASC
            LIMIT 100
        """)
        
        result = db.execute(query, {"days": days_old})
        stale_entities = result.fetchall()
        
        # Queue individual updates
        queued = 0
        for entity in stale_entities:
            update_single_embedding_task.delay(
                entity.entity_type,
                entity.entity_id
            )
            queued += 1
        
        logger.info(f"Queued {queued} entities for embedding refresh")
        
        return {
            "success": True,
            "entities_queued": queued,
            "days_threshold": days_old
        }