"""
Weekly digest scheduled task for sending summary emails.
"""
import logging
from typing import Dict, Any
from datetime import datetime, timedelta
from src.workers import celery_app
from src.database.setup import get_db_context
from src.database.repositories.startup_repository import PostgresStartupRepository
from src.database.repositories.vc_repository import PostgresVCRepository
from src.workers.tasks.notification_tasks import send_weekly_digest_task

logger = logging.getLogger(__name__)


@celery_app.task(
    name='src.workers.tasks.weekly_digest.send_all_weekly_digests',
    max_retries=1
)
def send_all_weekly_digests() -> Dict[str, Any]:
    """
    Send weekly digest emails to all active users.
    Scheduled to run every Monday at 9 AM UTC.
    """
    try:
        logger.info("Starting weekly digest distribution")
        
        tasks_queued = {
            'startups': 0,
            'vcs': 0,
            'errors': 0
        }
        
        with get_db_context() as db:
            # Send to all active startups
            startup_repo = PostgresStartupRepository(db)
            startups = startup_repo.list()
            
            for startup in startups:
                # Only send to active startups with email
                if getattr(startup, 'active', True) and getattr(startup, 'email', None):
                    try:
                        send_weekly_digest_task.delay('startup', str(startup.id))
                        tasks_queued['startups'] += 1
                    except Exception as e:
                        logger.error(f"Error queueing digest for startup {startup.id}: {str(e)}")
                        tasks_queued['errors'] += 1
            
            # Send to all active VCs
            vc_repo = PostgresVCRepository(db)
            vcs = vc_repo.list()
            
            for vc in vcs:
                # Only send to active VCs with email
                if getattr(vc, 'active', True) and getattr(vc, 'email', None):
                    try:
                        send_weekly_digest_task.delay('vc', str(vc.id))
                        tasks_queued['vcs'] += 1
                    except Exception as e:
                        logger.error(f"Error queueing digest for VC {vc.id}: {str(e)}")
                        tasks_queued['errors'] += 1
        
        total_queued = tasks_queued['startups'] + tasks_queued['vcs']
        logger.info(f"Queued {total_queued} weekly digest emails")
        
        return {
            'status': 'completed',
            'tasks_queued': tasks_queued,
            'executed_at': datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error in send_all_weekly_digests: {str(e)}")
        raise