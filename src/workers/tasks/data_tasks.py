"""
Data enrichment and collection background tasks.
"""
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from uuid import UUID
import httpx
from bs4 import BeautifulSoup
from celery import Task
from src.workers import celery_app
from src.database.setup import get_db_context
from src.database.repositories.startup_repository import PostgresStartupRepository
from src.database.repositories.vc_repository import PostgresVCRepository
from src.core.models.startup import Startup
from src.core.models.vc import VC
import json

logger = logging.getLogger(__name__)


class DataEnrichmentTask(Task):
    """Base task class for data enrichment tasks."""
    
    def __init__(self):
        self.http_client = None
    
    def __call__(self, *args, **kwargs):
        # Initialize HTTP client if not already done
        if not self.http_client:
            self.http_client = httpx.Client(timeout=30.0)
        return self.run(*args, **kwargs)


@celery_app.task(
    bind=True,
    base=DataEnrichmentTask,
    name='src.workers.tasks.data_tasks.scrape_vc_website_task',
    max_retries=3,
    default_retry_delay=120
)
def scrape_vc_website_task(self, vc_id: str) -> Dict[str, Any]:
    """
    Scrape a VC's website for portfolio companies and investment thesis.
    
    Args:
        vc_id: The ID of the VC to scrape
        
    Returns:
        Dictionary with scraped data
    """
    try:
        logger.info(f"Starting website scraping for VC {vc_id}")
        
        with get_db_context() as db:
            vc_repo = PostgresVCRepository(db)
            vc = vc_repo.get(UUID(vc_id))
            
            if not vc or not vc.website:
                raise ValueError(f"VC {vc_id} not found or has no website")
            
            # Update task progress
            self.update_state(
                state='PROCESSING',
                meta={'current': 'Fetching website content', 'total': 100}
            )
            
            # Fetch website content
            response = self.http_client.get(vc.website)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Extract data (this is a simplified example)
            scraped_data = {
                'portfolio_companies': [],
                'investment_focus': [],
                'team_members': [],
                'recent_news': []
            }
            
            # Look for portfolio section
            portfolio_sections = soup.find_all(['section', 'div'], class_=['portfolio', 'companies', 'investments'])
            for section in portfolio_sections:
                companies = section.find_all(['h3', 'h4', 'p', 'a'])
                for company in companies[:20]:  # Limit to prevent too much data
                    text = company.get_text(strip=True)
                    if text and len(text) > 3:
                        scraped_data['portfolio_companies'].append(text)
            
            # Look for investment thesis/focus
            focus_keywords = ['invest', 'focus', 'thesis', 'stage', 'sector']
            for keyword in focus_keywords:
                elements = soup.find_all(text=lambda t: keyword in t.lower())
                for element in elements[:10]:
                    parent = element.parent
                    if parent and parent.name in ['p', 'div', 'section']:
                        text = parent.get_text(strip=True)[:500]
                        if text:
                            scraped_data['investment_focus'].append(text)
            
            # Update VC with enriched sectors from scraped themes
            if scraped_data['investment_focus']:
                # Extract common investment themes
                themes = self._extract_investment_themes(scraped_data['investment_focus'])
                if themes and not vc.sectors:
                    vc.sectors = themes[:5]  # Top 5 themes as sectors
            
            # Store scraped data as a note in thesis (this is a simplified approach)
            if scraped_data['portfolio_companies'] or scraped_data['investment_focus']:
                enrichment_note = f"[Scraped on {datetime.utcnow().isoformat()}] "
                if scraped_data['portfolio_companies']:
                    enrichment_note += f"Portfolio: {len(scraped_data['portfolio_companies'])} companies. "
                if vc.thesis:
                    vc.thesis = enrichment_note + vc.thesis
                else:
                    vc.thesis = enrichment_note + "Focus areas extracted from website analysis."
            
            vc_repo.update(UUID(vc_id), vc)
            # Note: commit is handled by the repository
            
            logger.info(f"Completed website scraping for VC {vc_id}")
            
            return {
                'vc_id': vc_id,
                'status': 'completed',
                'portfolio_count': len(scraped_data['portfolio_companies']),
                'focus_areas_found': len(scraped_data['investment_focus']),
                'scraped_at': datetime.utcnow().isoformat()
            }
            
    except Exception as e:
        logger.error(f"Error scraping VC website {vc_id}: {str(e)}")
        raise self.retry(exc=e, countdown=120 * (2 ** self.request.retries))
    
    def _extract_investment_themes(self, focus_texts: List[str]) -> List[str]:
        """Extract common investment themes from text."""
        # Common investment themes to look for
        themes = {
            'AI/ML': ['artificial intelligence', 'machine learning', 'ai', 'ml', 'deep learning'],
            'FinTech': ['fintech', 'financial technology', 'payments', 'banking', 'defi'],
            'HealthTech': ['healthtech', 'healthcare', 'medical', 'biotech', 'digital health'],
            'SaaS': ['saas', 'software as a service', 'b2b software', 'enterprise software'],
            'E-commerce': ['ecommerce', 'e-commerce', 'marketplace', 'retail tech'],
            'Climate Tech': ['climate', 'cleantech', 'sustainability', 'renewable', 'green tech'],
            'EdTech': ['edtech', 'education technology', 'learning', 'training'],
            'Cybersecurity': ['cybersecurity', 'security', 'privacy', 'infosec'],
            'Blockchain': ['blockchain', 'crypto', 'web3', 'decentralized'],
            'IoT': ['iot', 'internet of things', 'connected devices', 'smart home']
        }
        
        found_themes = []
        combined_text = ' '.join(focus_texts).lower()
        
        for theme, keywords in themes.items():
            if any(keyword in combined_text for keyword in keywords):
                found_themes.append(theme)
        
        return found_themes


@celery_app.task(
    bind=True,
    base=DataEnrichmentTask,
    name='src.workers.tasks.data_tasks.enrich_startup_data_task',
    max_retries=3
)
def enrich_startup_data_task(self, startup_id: str, sources: List[str] = None) -> Dict[str, Any]:
    """
    Enrich startup data from various sources.
    
    Args:
        startup_id: The ID of the startup to enrich
        sources: List of sources to use (e.g., ['crunchbase', 'linkedin', 'news'])
        
    Returns:
        Dictionary with enrichment results
    """
    try:
        logger.info(f"Starting data enrichment for startup {startup_id}")
        
        if not sources:
            sources = ['news']  # Default to news for now
        
        enriched_data = {}
        
        with get_db_context() as db:
            startup_repo = PostgresStartupRepository(db)
            startup = startup_repo.get(UUID(startup_id))
            
            if not startup:
                raise ValueError(f"Startup {startup_id} not found")
            
            # News enrichment (simplified example)
            if 'news' in sources:
                self.update_state(
                    state='PROCESSING',
                    meta={'current': 'Searching for news', 'total': 100}
                )
                
                # Search for recent news (mock implementation)
                news_data = self._search_startup_news(startup.name)
                enriched_data['news'] = news_data
            
            # LinkedIn enrichment placeholder
            if 'linkedin' in sources:
                self.update_state(
                    state='PROCESSING',
                    meta={'current': 'Fetching LinkedIn data', 'total': 100}
                )
                # This would require LinkedIn API access
                enriched_data['linkedin'] = {
                    'employee_count': 'N/A',
                    'recent_hires': []
                }
            
            # Update startup description with enriched data (simplified approach)
            enrichment_note = f"[Enriched on {datetime.utcnow().isoformat()} from {', '.join(sources)}] "
            if enriched_data.get('news'):
                enrichment_note += f"Latest news: {len(enriched_data['news'])} articles found. "
            
            if startup.description:
                startup.description = enrichment_note + startup.description
            else:
                startup.description = enrichment_note + "Company profile enriched with external data."
            
            startup_repo.update(UUID(startup_id), startup)
            # Note: commit is handled by the repository
            
            logger.info(f"Completed data enrichment for startup {startup_id}")
            
            return {
                'startup_id': startup_id,
                'status': 'completed',
                'sources_processed': sources,
                'enriched_at': datetime.utcnow().isoformat()
            }
            
    except Exception as e:
        logger.error(f"Error enriching startup data {startup_id}: {str(e)}")
        raise self.retry(exc=e, countdown=60 * (2 ** self.request.retries))
    
    def _search_startup_news(self, startup_name: str) -> List[Dict[str, str]]:
        """Search for recent news about the startup (mock implementation)."""
        # In a real implementation, this would use a news API
        return [
            {
                'title': f"Latest updates from {startup_name}",
                'source': 'TechCrunch',
                'date': datetime.utcnow().isoformat(),
                'url': 'https://example.com/article'
            }
        ]


@celery_app.task(
    bind=True,
    name='src.workers.tasks.data_tasks.update_funding_data_task',
    max_retries=2
)
def update_funding_data_task(self, entity_type: str, entity_id: str) -> Dict[str, Any]:
    """
    Update funding data for a startup or VC.
    
    Args:
        entity_type: 'startup' or 'vc'
        entity_id: The ID of the entity to update
        
    Returns:
        Dictionary with update results
    """
    try:
        logger.info(f"Updating funding data for {entity_type} {entity_id}")
        
        with get_db_context() as db:
            if entity_type == 'startup':
                repo = PostgresStartupRepository(db)
                entity = repo.get(UUID(entity_id))
                
                if entity:
                    # Mock funding update - add note to description
                    funding_note = f"[Funding data checked on {datetime.utcnow().isoformat()}] "
                    if entity.description and not "[Funding data checked" in entity.description:
                        entity.description = funding_note + entity.description
                    elif not entity.description:
                        entity.description = funding_note + "Funding information up to date."
                    repo.update(UUID(entity_id), entity)
                    
            elif entity_type == 'vc':
                repo = PostgresVCRepository(db)
                entity = repo.get(UUID(entity_id))
                
                if entity:
                    # Mock portfolio update - add note to thesis
                    portfolio_note = f"[Portfolio data checked on {datetime.utcnow().isoformat()}] "
                    if entity.thesis and not "[Portfolio data checked" in entity.thesis:
                        entity.thesis = portfolio_note + entity.thesis
                    elif not entity.thesis:
                        entity.thesis = portfolio_note + "Portfolio information up to date."
                    repo.update(UUID(entity_id), entity)
            
            # Note: commit is handled by the repository
            
            return {
                'entity_type': entity_type,
                'entity_id': entity_id,
                'status': 'completed',
                'updated_at': datetime.utcnow().isoformat()
            }
            
    except Exception as e:
        logger.error(f"Error updating funding data for {entity_type} {entity_id}: {str(e)}")
        raise self.retry(exc=e, countdown=120)