"""Celery tasks for web scraping."""

from typing import Dict, Any, Optional, List
from datetime import datetime
import logging

from celery import shared_task
from src.scrapers import CompanyScraper, LinkedInScraper, ScraperResult
from src.database.setup import get_session
from src.database.repositories.startup_repository import StartupRepository
from src.database.repositories.vc_repository import VCRepository
from src.database.models import Startup as StartupModel
from src.database.models import VC as VCModel

logger = logging.getLogger(__name__)


@shared_task(name="scrape_company_website")
def scrape_company_website(
    url: str,
    entity_id: str,
    entity_type: str = "startup"
) -> Dict[str, Any]:
    """
    Scrape a company website and update entity data.
    
    Args:
        url: Company website URL
        entity_id: ID of the startup or VC to update
        entity_type: Either "startup" or "vc"
        
    Returns:
        Dictionary with scraping results
    """
    logger.info(f"Scraping {url} for {entity_type} {entity_id}")
    
    try:
        # Run scraper
        import asyncio
        scraper = CompanyScraper()
        result = asyncio.run(scraper.scrape(url))
        
        if not result.success:
            return {
                "success": False,
                "error": result.error,
                "url": url,
                "entity_id": entity_id
            }
        
        # Update entity with scraped data
        with get_session() as db:
            if entity_type == "startup":
                repo = StartupRepository(db)
                startup = repo.get_by_id(entity_id)
                if startup:
                    # Update with scraped data
                    if result.data.get('description') and not startup.description:
                        startup.description = result.data['description']
                    if result.data.get('industry') and not startup.industry:
                        startup.industry = result.data['industry']
                    if result.data.get('team_size'):
                        startup.team_size = result.data['team_size']
                    if result.data.get('technologies'):
                        startup.technologies = result.data['technologies']
                    
                    # Add metadata
                    startup.metadata = startup.metadata or {}
                    startup.metadata['scraped_data'] = {
                        'scraped_at': datetime.utcnow().isoformat(),
                        'source': url,
                        'social_links': result.data.get('social_links', {})
                    }
                    
                    repo.save(startup)
                    
            elif entity_type == "vc":
                repo = VCRepository(db)
                vc = repo.get_by_id(entity_id)
                if vc:
                    # Update with scraped data
                    if result.data.get('description') and not vc.description:
                        vc.description = result.data['description']
                    
                    # Add metadata
                    vc.metadata = vc.metadata or {}
                    vc.metadata['scraped_data'] = {
                        'scraped_at': datetime.utcnow().isoformat(),
                        'source': url,
                        'social_links': result.data.get('social_links', {})
                    }
                    
                    repo.save(vc)
            
            db.commit()
        
        return {
            "success": True,
            "url": url,
            "entity_id": entity_id,
            "entity_type": entity_type,
            "data": result.data
        }
        
    except Exception as e:
        logger.error(f"Error scraping {url}: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "url": url,
            "entity_id": entity_id
        }


@shared_task(name="scrape_linkedin_company")
def scrape_linkedin_company(
    linkedin_url: str,
    entity_id: str,
    entity_type: str = "startup"
) -> Dict[str, Any]:
    """
    Scrape LinkedIn company page and update entity data.
    
    Args:
        linkedin_url: LinkedIn company URL
        entity_id: ID of the startup or VC to update
        entity_type: Either "startup" or "vc"
        
    Returns:
        Dictionary with scraping results
    """
    logger.info(f"Scraping LinkedIn {linkedin_url} for {entity_type} {entity_id}")
    
    try:
        # Check if Playwright is available
        try:
            from playwright.async_api import async_playwright
        except ImportError:
            return {
                "success": False,
                "error": "Playwright not installed. Cannot scrape LinkedIn.",
                "url": linkedin_url,
                "entity_id": entity_id
            }
        
        # Run scraper
        import asyncio
        scraper = LinkedInScraper()
        result = asyncio.run(scraper.scrape(linkedin_url))
        
        if not result.success:
            return {
                "success": False,
                "error": result.error,
                "url": linkedin_url,
                "entity_id": entity_id
            }
        
        # Update entity with LinkedIn data
        with get_session() as db:
            if entity_type == "startup":
                repo = StartupRepository(db)
                startup = repo.get_by_id(entity_id)
                if startup:
                    # Update with LinkedIn data
                    if result.data.get('description') and not startup.description:
                        startup.description = result.data['description']
                    if result.data.get('industry') and not startup.industry:
                        startup.industry = result.data['industry']
                    if result.data.get('employee_count'):
                        startup.team_size = result.data['employee_count']
                    if result.data.get('founded'):
                        startup.founded_date = datetime(result.data['founded'], 1, 1)
                    
                    # Add LinkedIn metadata
                    startup.metadata = startup.metadata or {}
                    startup.metadata['linkedin_data'] = {
                        'scraped_at': datetime.utcnow().isoformat(),
                        'linkedin_url': linkedin_url,
                        'tagline': result.data.get('tagline'),
                        'headquarters': result.data.get('headquarters'),
                        'company_size': result.data.get('company_size'),
                        'specialties': result.data.get('specialties', [])
                    }
                    
                    repo.save(startup)
                    
            elif entity_type == "vc":
                repo = VCRepository(db)
                vc = repo.get_by_id(entity_id)
                if vc:
                    # Update with LinkedIn data
                    if result.data.get('description') and not vc.description:
                        vc.description = result.data['description']
                    
                    # Add LinkedIn metadata
                    vc.metadata = vc.metadata or {}
                    vc.metadata['linkedin_data'] = {
                        'scraped_at': datetime.utcnow().isoformat(),
                        'linkedin_url': linkedin_url,
                        'tagline': result.data.get('tagline'),
                        'headquarters': result.data.get('headquarters'),
                        'company_size': result.data.get('company_size'),
                        'specialties': result.data.get('specialties', [])
                    }
                    
                    repo.save(vc)
            
            db.commit()
        
        return {
            "success": True,
            "url": linkedin_url,
            "entity_id": entity_id,
            "entity_type": entity_type,
            "data": result.data
        }
        
    except Exception as e:
        logger.error(f"Error scraping LinkedIn {linkedin_url}: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "url": linkedin_url,
            "entity_id": entity_id
        }


@shared_task(name="bulk_scrape_websites")
def bulk_scrape_websites(
    urls_with_entities: List[Dict[str, str]],
    max_concurrent: int = 5
) -> Dict[str, Any]:
    """
    Bulk scrape multiple websites.
    
    Args:
        urls_with_entities: List of dicts with 'url', 'entity_id', 'entity_type'
        max_concurrent: Maximum concurrent scraping operations
        
    Returns:
        Dictionary with bulk scraping results
    """
    logger.info(f"Bulk scraping {len(urls_with_entities)} websites")
    
    results = []
    
    # Process in batches to respect rate limits
    import asyncio
    scraper = CompanyScraper()
    
    # Group by URL to scrape
    url_map = {}
    for item in urls_with_entities:
        url = item['url']
        if url not in url_map:
            url_map[url] = []
        url_map[url].append(item)
    
    # Scrape unique URLs
    urls = list(url_map.keys())
    scraped_results = asyncio.run(
        scraper.scrape_multiple(urls, max_concurrent=max_concurrent)
    )
    
    # Map results back to entities
    for result in scraped_results:
        entities = url_map.get(result.url, [])
        for entity in entities:
            if result.success:
                # Trigger individual update task
                task_result = scrape_company_website.apply_async(
                    args=[result.url, entity['entity_id'], entity['entity_type']]
                )
                results.append({
                    "url": result.url,
                    "entity_id": entity['entity_id'],
                    "entity_type": entity['entity_type'],
                    "task_id": task_result.id,
                    "status": "queued"
                })
            else:
                results.append({
                    "url": result.url,
                    "entity_id": entity['entity_id'],
                    "entity_type": entity['entity_type'],
                    "status": "failed",
                    "error": result.error
                })
    
    return {
        "total": len(urls_with_entities),
        "unique_urls": len(urls),
        "results": results
    }


@shared_task(name="enrich_all_startups")
def enrich_all_startups(limit: int = 100) -> Dict[str, Any]:
    """
    Enrich all startups that have websites but no scraped data.
    
    Args:
        limit: Maximum number of startups to process
        
    Returns:
        Dictionary with enrichment results
    """
    logger.info(f"Enriching up to {limit} startups")
    
    with get_session() as db:
        repo = StartupRepository(db)
        
        # Get startups with websites but no scraped data
        startups = db.query(StartupModel).filter(
            StartupModel.website.isnot(None),
            ~StartupModel.metadata.has_key('scraped_data')
        ).limit(limit).all()
        
        urls_with_entities = []
        for startup in startups:
            urls_with_entities.append({
                'url': startup.website,
                'entity_id': str(startup.id),
                'entity_type': 'startup'
            })
    
    if urls_with_entities:
        return bulk_scrape_websites.apply_async(
            args=[urls_with_entities]
        ).get()
    else:
        return {
            "total": 0,
            "message": "No startups found that need enrichment"
        }