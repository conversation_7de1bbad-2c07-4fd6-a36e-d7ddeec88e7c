"""
Match-related background tasks.
"""
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any
from uuid import UUID

from celery import Task
from src.workers import celery_app
from src.database.setup import get_db_context
from src.database.repositories.startup_repository import PostgresStartupRepository
from src.database.repositories.vc_repository import PostgresVCRepository
from src.database.repositories.match_repository import PostgresMatchRepository
from src.core.services.match_service import MatchService
from src.core.services.matching_engine import MatchingEngine

logger = logging.getLogger(__name__)


@celery_app.task(
    name='src.workers.tasks.match_tasks.daily_match_generation',
    bind=True,
    max_retries=3,
    default_retry_delay=300
)
def daily_match_generation(self: Task) -> Dict[str, Any]:
    """
    Generate daily matches between startups and VCs.
    
    This task runs daily to:
    1. Find all active startups
    2. Find all active VCs
    3. Generate matches based on compatibility
    4. Store high-quality matches in the database
    
    Returns:
        Dictionary with task results
    """
    try:
        logger.info("Starting daily match generation task")
        
        with get_db_context() as db:
            # Initialize repositories
            startup_repo = PostgresStartupRepository(db)
            vc_repo = PostgresVCRepository(db)
            match_repo = PostgresMatchRepository(db)
            
            # Get active startups (created in last 30 days)
            cutoff_date = datetime.utcnow() - timedelta(days=30)
            startups = startup_repo.find_by_created_after(cutoff_date)
            
            # Get all active VCs
            vcs = vc_repo.get_all()
            
            logger.info(f"Processing {len(startups)} startups and {len(vcs)} VCs")
            
            # Initialize matching engine
            matching_engine = MatchingEngine()
            
            # Generate matches
            matches_created = 0
            for startup in startups:
                for vc in vcs:
                    # Calculate compatibility
                    score = matching_engine.calculate_match(startup, vc)
                    
                    # Only save high-quality matches (score > 0.7)
                    if score.score > 0.7:
                        # Check if match already exists
                        existing = match_repo.find_by_startup_and_vc(
                            startup_id=startup.id,
                            vc_id=vc.id
                        )
                        
                        if not existing:
                            # Create new match
                            match_data = {
                                'startup_id': startup.id,
                                'vc_id': vc.id,
                                'score': score.score,
                                'reasons': score.reasons,
                                'created_at': datetime.utcnow()
                            }
                            match_repo.create(match_data)
                            matches_created += 1
            
            logger.info(f"Daily match generation completed. Created {matches_created} new matches")
            
            return {
                'status': 'completed',
                'startups_processed': len(startups),
                'vcs_processed': len(vcs),
                'matches_created': matches_created,
                'completed_at': datetime.utcnow().isoformat()
            }
            
    except Exception as e:
        logger.error(f"Error in daily match generation: {str(e)}")
        raise self.retry(exc=e)