"""
Notification and communication background tasks.
"""
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from celery import Task
from src.workers import celery_app
from src.database.setup import get_db_context
from src.database.repositories.match_repository import PostgresMatchRepository
from src.database.repositories.startup_repository import PostgresStartupRepository
from src.database.repositories.vc_repository import PostgresVCRepository
from src.core.schemas.match import MatchStatus
from src.core.services.email_service import get_email_service

logger = logging.getLogger(__name__)


class NotificationTask(Task):
    """Base task class for notification tasks."""
    
    def __init__(self):
        self.email_service = get_email_service()


@celery_app.task(
    bind=True,
    base=NotificationTask,
    name='src.workers.tasks.notification_tasks.send_match_notification_task',
    max_retries=3
)
def send_match_notification_task(
    self,
    match_id: str,
    notify_startup: bool = True,
    notify_vc: bool = True
) -> Dict[str, Any]:
    """
    Send email notifications about a new match.
    
    Args:
        match_id: The ID of the match
        notify_startup: Whether to notify the startup
        notify_vc: Whether to notify the VC
        
    Returns:
        Dictionary with notification results
    """
    try:
        logger.info(f"Sending notifications for match {match_id}")
        
        notifications_sent = []
        
        with get_db_context() as db:
            # Get match details
            match_repo = PostgresMatchRepository(db)
            startup_repo = PostgresStartupRepository(db)
            vc_repo = PostgresVCRepository(db)
            
            match = match_repo.get_by_id(db, match_id)
            if not match:
                raise ValueError(f"Match {match_id} not found")
            
            startup = startup_repo.get_by_id(db, match.startup_id)
            vc = vc_repo.get_by_id(db, match.vc_id)
            
            # Prepare notification data
            match_data = {
                'score': round(match.score * 100),
                'reasons': ', '.join(match.reasons[:3]) if match.reasons else 'Strong alignment'
            }
            
            # Notify startup
            if notify_startup and startup and startup.email:
                self.update_state(
                    state='PROCESSING',
                    meta={'current': 'Notifying startup', 'total': 2}
                )
                
                result = self.email_service.send_email(
                    to_email=startup.email,
                    template_name='match_notification',
                    data={
                        'recipient_name': startup.name,
                        'match_name': vc.name,
                        'match_id': str(match_id),
                        **match_data
                    }
                )
                
                notifications_sent.append({
                    'type': 'startup',
                    'email': startup.email,
                    'status': 'sent' if result['success'] else 'failed'
                })
            
            # Notify VC
            if notify_vc and vc and vc.email:
                self.update_state(
                    state='PROCESSING',
                    meta={'current': 'Notifying VC', 'total': 2}
                )
                
                result = self.email_service.send_email(
                    to_email=vc.email,
                    template_name='match_notification',
                    data={
                        'recipient_name': vc.name,
                        'match_name': startup.name,
                        'match_id': str(match_id),
                        **match_data
                    }
                )
                
                notifications_sent.append({
                    'type': 'vc',
                    'email': vc.email,
                    'status': 'sent' if result['success'] else 'failed'
                })
            
            # Update match with notification status
            match.metadata = match.metadata or {}
            match.metadata['notifications'] = {
                'sent_at': datetime.utcnow().isoformat(),
                'recipients': notifications_sent
            }
            
            match_repo.update(db, match_id, match)
            db.commit()
        
        logger.info(f"Completed notifications for match {match_id}")
        
        return {
            'match_id': match_id,
            'status': 'completed',
            'notifications_sent': len(notifications_sent),
            'details': notifications_sent
        }
        
    except Exception as e:
        logger.error(f"Error sending match notifications: {str(e)}")
        raise self.retry(exc=e, countdown=60 * (2 ** self.request.retries))


@celery_app.task(
    bind=True,
    base=NotificationTask,
    name='src.workers.tasks.notification_tasks.send_weekly_digest_task'
)
def send_weekly_digest_task(self, user_type: str, user_id: str) -> Dict[str, Any]:
    """
    Send weekly digest email to a user.
    
    Args:
        user_type: 'startup' or 'vc'
        user_id: The ID of the user
        
    Returns:
        Dictionary with digest results
    """
    try:
        logger.info(f"Sending weekly digest to {user_type} {user_id}")
        
        with get_db_context() as db:
            # Get user data and recent matches
            match_repo = PostgresMatchRepository(db)
            
            if user_type == 'startup':
                repo = PostgresStartupRepository(db)
                user = repo.get_by_id(user_id)
                matches = match_repo.get_by_startup(user_id, limit=10)
            else:
                repo = PostgresVCRepository(db)
                user = repo.get_by_id(user_id)
                matches = match_repo.get_by_vc(user_id, limit=10)
            
            if not user or not user.email:
                return {
                    'status': 'skipped',
                    'reason': 'No email address'
                }
            
            # Calculate digest data
            week_ago = datetime.utcnow() - timedelta(days=7)
            new_matches = [m for m in matches if m.created_at >= week_ago]
            top_match = max(matches, key=lambda m: m.score) if matches else None
            
            # Send digest
            result = self.email_service.send_email(
                to_email=user.email,
                template_name='weekly_digest',
                data={
                    'recipient_name': user.name,
                    'new_matches': len(new_matches),
                    'updated_profiles': 0,  # Placeholder
                    'top_match': f"{round(top_match.score * 100)}%" if top_match else 'N/A'
                }
            )
            
            return {
                'user_type': user_type,
                'user_id': user_id,
                'status': 'completed',
                'email_status': 'sent' if result['success'] else 'failed',
                'new_matches': len(new_matches)
            }
            
    except Exception as e:
        logger.error(f"Error sending weekly digest: {str(e)}")
        raise self.retry(exc=e, countdown=300)


@celery_app.task(
    bind=True,
    base=NotificationTask,
    name='src.workers.tasks.notification_tasks.send_analysis_complete_task'
)
def send_analysis_complete_task(
    self,
    entity_type: str,
    entity_id: str,
    insights: List[str]
) -> Dict[str, Any]:
    """
    Send notification when AI analysis is complete.
    
    Args:
        entity_type: 'startup' or 'vc'
        entity_id: The ID of the analyzed entity
        insights: List of key insights from the analysis
        
    Returns:
        Dictionary with notification results
    """
    try:
        logger.info(f"Sending analysis complete notification for {entity_type} {entity_id}")
        
        with get_db_context() as db:
            # Get entity
            if entity_type == 'startup':
                repo = PostgresStartupRepository(db)
            else:
                repo = PostgresVCRepository(db)
            
            entity = repo.get_by_id(entity_id)
            
            if not entity or not entity.email:
                return {
                    'status': 'skipped',
                    'reason': 'No email address'
                }
            
            # Send notification
            result = self.email_service.send_email(
                to_email=entity.email,
                template_name='analysis_complete',
                data={
                    'recipient_name': entity.name,
                    'entity_type': entity_type,
                    'entity_id': str(entity_id),
                    'insights': insights[:5]  # Top 5 insights
                }
            )
            
            return {
                'entity_type': entity_type,
                'entity_id': entity_id,
                'status': 'completed',
                'email_status': 'sent' if result['success'] else 'failed'
            }
            
    except Exception as e:
        logger.error(f"Error sending analysis notification: {str(e)}")
        raise self.retry(exc=e, countdown=60)