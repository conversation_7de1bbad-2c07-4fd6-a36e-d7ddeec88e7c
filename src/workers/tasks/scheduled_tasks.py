"""
Scheduled and periodic background tasks.
"""
import logging
from typing import Dict, Any, List
from datetime import datetime, timedelta
from celery import Task
from src.workers import celery_app
from src.database.setup import get_db_context
from src.database.repositories.startup_repository import PostgresStartupRepository
from src.database.repositories.vc_repository import PostgresVCRepository
from src.database.repositories.match_repository_sync import PostgresMatchRepository
from src.workers.tasks.ai_tasks import analyze_startup_task, analyze_vc_task
from src.core.services.cache_service import get_cache_service
from src.core.schemas.match import MatchStatus
import json

logger = logging.getLogger(__name__)


@celery_app.task(
    name='src.workers.tasks.scheduled_tasks.refresh_ai_insights',
    max_retries=1
)
def refresh_ai_insights() -> Dict[str, Any]:
    """
    Refresh AI insights for all entities every 6 hours.
    This implements the "6-hour discovery cycle" mentioned in the roadmap.
    """
    try:
        logger.info("Starting scheduled AI insights refresh")
        
        tasks_queued = {
            'startups': 0,
            'vcs': 0
        }
        
        with get_db_context() as db:
            # Get entities that need refresh (older than 6 hours)
            cutoff_time = datetime.utcnow() - timedelta(hours=6)
            
            # Refresh startups
            startup_repo = PostgresStartupRepository(db)
            startups = startup_repo.list()
            
            for startup in startups:
                # Check if needs refresh
                if startup.metadata and 'ai_analysis' in startup.metadata:
                    analyzed_at = startup.metadata['ai_analysis'].get('analyzed_at')
                    if analyzed_at:
                        analyzed_time = datetime.fromisoformat(analyzed_at)
                        if analyzed_time > cutoff_time:
                            continue  # Skip if recently analyzed
                
                # Queue analysis task
                analyze_startup_task.delay(str(startup.id))
                tasks_queued['startups'] += 1
            
            # Refresh VCs
            vc_repo = PostgresVCRepository(db)
            vcs = vc_repo.list()
            
            for vc in vcs:
                # Check if needs refresh
                if vc.metadata and 'ai_analysis' in vc.metadata:
                    analyzed_at = vc.metadata['ai_analysis'].get('analyzed_at')
                    if analyzed_at:
                        analyzed_time = datetime.fromisoformat(analyzed_at)
                        if analyzed_time > cutoff_time:
                            continue  # Skip if recently analyzed
                
                # Queue analysis task
                analyze_vc_task.delay(str(vc.id))
                tasks_queued['vcs'] += 1
        
        logger.info(f"Queued {sum(tasks_queued.values())} AI refresh tasks")
        
        return {
            'status': 'completed',
            'tasks_queued': tasks_queued,
            'executed_at': datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error in refresh_ai_insights: {str(e)}")
        raise


@celery_app.task(
    name='src.workers.tasks.scheduled_tasks.cleanup_stale_data',
    max_retries=1
)
def cleanup_stale_data() -> Dict[str, Any]:
    """
    Clean up stale data and cache entries daily.
    """
    try:
        logger.info("Starting scheduled data cleanup")
        
        cleanup_stats = {
            'cache_entries_removed': 0,
            'old_matches_archived': 0,
            'orphaned_data_removed': 0
        }
        
        # Clear expired cache entries
        try:
            cache_service = get_cache_service()
            # Redis automatically removes expired keys, but we can clean up patterns
            patterns_to_clear = [
                'startup:*:temp',
                'vc:*:temp',
                'match:*:temp',
                'ai_analysis:*:old'
            ]
            
            cleanup_stats['cache_entries_removed'] = 0
            # Note: In production, would implement cache cleanup logic
            
        except Exception as e:
            logger.error(f"Error cleaning cache: {str(e)}")
        
        with get_db_context() as db:
            # Archive old matches (older than 90 days)
            match_repo = PostgresMatchRepository(db)
            cutoff_date = datetime.utcnow() - timedelta(days=90)
            
            # Get old pending matches
            old_matches = match_repo.list_by_status(MatchStatus.PENDING)
            old_matches = [m for m in old_matches if m.created_at < cutoff_date]
            
            for match in old_matches:
                match.status = MatchStatus.EXPIRED
                match.metadata = match.metadata or {}
                match.metadata['archived_at'] = datetime.utcnow().isoformat()
                cleanup_stats['old_matches_archived'] += 1
            
            db.commit()
        
        logger.info(f"Cleanup completed: {cleanup_stats}")
        
        return {
            'status': 'completed',
            'cleanup_stats': cleanup_stats,
            'executed_at': datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error in cleanup_stale_data: {str(e)}")
        raise


@celery_app.task(
    name='src.workers.tasks.scheduled_tasks.generate_analytics_report',
    max_retries=1
)
def generate_analytics_report() -> Dict[str, Any]:
    """
    Generate daily analytics report.
    """
    try:
        logger.info("Starting analytics report generation")
        
        analytics = {
            'date': datetime.utcnow().date().isoformat(),
            'entities': {},
            'matches': {},
            'ai_usage': {},
            'performance': {}
        }
        
        with get_db_context() as db:
            # Entity statistics
            startup_repo = PostgresStartupRepository(db)
            vc_repo = PostgresVCRepository(db)
            match_repo = PostgresMatchRepository(db)
            
            # Get counts
            all_startups = startup_repo.list()
            all_vcs = vc_repo.list()
            
            analytics['entities'] = {
                'total_startups': len(all_startups),
                'active_startups': len([s for s in all_startups if getattr(s, 'active', True)]),
                'total_vcs': len(all_vcs),
                'active_vcs': len([v for v in all_vcs if getattr(v, 'active', True)])
            }
            
            # Match statistics
            yesterday = datetime.utcnow() - timedelta(days=1)
            
            # Get match statistics
            all_matches = match_repo.list()
            
            analytics['matches'] = {
                'total_matches': len(all_matches),
                'matches_yesterday': len([m for m in all_matches if m.created_at >= yesterday]),
                'high_score_matches': len([m for m in all_matches if m.score >= 0.8]),
                'contacted_matches': len([m for m in all_matches if m.status == MatchStatus.CONTACTED])
            }
            
            # AI usage statistics (from metadata)
            ai_analyzed_startups = len([s for s in all_startups if s.metadata and 'ai_analysis' in s.metadata])
            ai_analyzed_vcs = len([v for v in all_vcs if v.metadata and 'ai_analysis' in v.metadata])
            
            analytics['ai_usage'] = {
                'analyzed_startups': ai_analyzed_startups,
                'analyzed_vcs': ai_analyzed_vcs,
                'ai_enhanced_matches': len([m for m in all_matches if m.metadata and 'ai_insights' in m.metadata])
            }
            
            # Performance metrics
            yesterday_matches = [m for m in all_matches if m.created_at >= yesterday]
            avg_match_score = sum(m.score for m in yesterday_matches) / len(yesterday_matches) if yesterday_matches else 0
            
            analytics['performance'] = {
                'avg_match_score_yesterday': float(avg_match_score) if avg_match_score else 0,
                'cache_hit_rate': 'N/A',  # Would need to track this
                'api_response_time': 'N/A'  # Would need to track this
            }
        
        # Store report
        cache_service = get_cache_service()
        report_key = f"analytics:daily:{analytics['date']}"
        cache_service.set(report_key, analytics, ttl=86400 * 30)  # Keep for 30 days
        
        logger.info(f"Analytics report generated for {analytics['date']}")
        
        return {
            'status': 'completed',
            'report_date': analytics['date'],
            'key_metrics': {
                'total_entities': analytics['entities']['total_startups'] + analytics['entities']['total_vcs'],
                'new_matches': analytics['matches']['matches_yesterday'],
                'ai_coverage': (analytics['ai_usage']['analyzed_startups'] + analytics['ai_usage']['analyzed_vcs']) / 
                              (analytics['entities']['total_startups'] + analytics['entities']['total_vcs'])
                              if (analytics['entities']['total_startups'] + analytics['entities']['total_vcs']) > 0 else 0
            }
        }
        
    except Exception as e:
        logger.error(f"Error generating analytics report: {str(e)}")
        raise


@celery_app.task(
    name='src.workers.tasks.scheduled_tasks.update_match_scores'
)
def update_match_scores() -> Dict[str, Any]:
    """
    Periodically update match scores based on new data and interactions.
    """
    try:
        logger.info("Starting match score update")
        
        updates_made = 0
        
        with get_db_context() as db:
            match_repo = PostgresMatchRepository(db)
            startup_repo = PostgresStartupRepository(db)
            vc_repo = PostgresVCRepository(db)
            
            # Get recent matches that might need score updates
            all_matches = match_repo.list()
            recent_cutoff = datetime.utcnow() - timedelta(days=7)
            recent_matches = [
                m for m in all_matches 
                if m.created_at >= recent_cutoff and 
                m.status in [MatchStatus.PENDING, MatchStatus.INTERESTED]
            ]
            
            for match in recent_matches:
                # Check if either entity has been updated
                startup = startup_repo.get_by_id(match.startup_id)
                vc = vc_repo.get_by_id(match.vc_id)
                
                # Simple heuristic: if metadata has changed, might need score update
                if (startup and startup.metadata and 'ai_analysis' in startup.metadata) or \
                   (vc and vc.metadata and 'ai_analysis' in vc.metadata):
                    
                    # In production, would recalculate score here
                    match.metadata = match.metadata or {}
                    match.metadata['score_updated'] = datetime.utcnow().isoformat()
                    
                    # Update the match
                    match_repo.update(match.id, {'metadata': match.metadata})
                    updates_made += 1
        
        logger.info(f"Updated {updates_made} match scores")
        
        return {
            'status': 'completed',
            'matches_updated': updates_made,
            'executed_at': datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error updating match scores: {str(e)}")
        raise


@celery_app.task(
    name='src.workers.tasks.scheduled_tasks.cleanup_old_tasks',
    bind=True,
    max_retries=3
)
def cleanup_old_tasks(self):
    """Clean up old completed tasks from the result backend."""
    try:
        logger.info("Starting cleanup of old tasks")
        # Implementation would go here
        # For now, just return success
        return {
            'status': 'completed',
            'message': 'Cleanup completed successfully'
        }
    except Exception as e:
        logger.error(f"Error cleaning up tasks: {str(e)}")
        raise self.retry(exc=e)