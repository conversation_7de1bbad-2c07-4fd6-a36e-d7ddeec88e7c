"""
AI-related background tasks for analysis and matching.
"""
import logging
from typing import List, Dict, Any, Optional
from celery import Task
from celery.result import AsyncResult
from src.workers import celery_app
from src.core.ai.analyzer import AIAnalyzerService
from src.core.services.match_service import MatchService
from src.database.repositories.startup_repository import PostgresStartupRepository
from src.database.repositories.vc_repository import PostgresVCRepository
from src.database.repositories.match_repository import PostgresMatchRepository
from src.database.setup import get_db, get_db_context
from src.core.schemas.match import MatchStatus
from datetime import datetime
import asyncio

logger = logging.getLogger(__name__)


class AIAnalysisTask(Task):
    """Base task class with database session management."""
    
    def __init__(self):
        self.ai_service = None
        self.match_service = None
    
    def __call__(self, *args, **kwargs):
        # Initialize services if not already done
        if not self.ai_service:
            self.ai_service = AIAnalyzerService()
        if not self.match_service:
            self.match_service = MatchService(
                MatchRepository(),
                StartupRepository(),
                VCRepository(),
                self.ai_service
            )
        return self.run(*args, **kwargs)


@celery_app.task(
    bind=True,
    base=AIAnalysisTask,
    name='src.workers.tasks.ai_tasks.analyze_startup_task',
    max_retries=3,
    default_retry_delay=60
)
def analyze_startup_task(self, startup_id: str) -> Dict[str, Any]:
    """
    Analyze a startup using AI and store insights.
    
    Args:
        startup_id: The ID of the startup to analyze
        
    Returns:
        Dictionary with analysis results
    """
    try:
        logger.info(f"Starting AI analysis for startup {startup_id}")
        
        with get_db_context() as db:
            # Get startup data
            startup_repo = StartupRepository()
            startup = startup_repo.get_by_id(db, startup_id)
            
            if not startup:
                raise ValueError(f"Startup {startup_id} not found")
            
            # Update task progress
            self.update_state(
                state='PROCESSING',
                meta={'current': 'Analyzing startup with AI', 'total': 100}
            )
            
            # Run AI analysis
            analysis = asyncio.run(
                self.ai_service.analyze_startup(startup)
            )
            
            # Store analysis results in startup metadata
            startup.metadata = startup.metadata or {}
            startup.metadata['ai_analysis'] = {
                'insights': analysis,
                'analyzed_at': datetime.utcnow().isoformat(),
                'version': '1.0'
            }
            
            # Update startup
            startup_repo.update(db, startup_id, startup)
            db.commit()
            
            logger.info(f"Completed AI analysis for startup {startup_id}")
            
            return {
                'startup_id': startup_id,
                'status': 'completed',
                'insights': analysis,
                'analyzed_at': datetime.utcnow().isoformat()
            }
            
    except Exception as e:
        logger.error(f"Error analyzing startup {startup_id}: {str(e)}")
        # Retry with exponential backoff
        raise self.retry(exc=e, countdown=60 * (2 ** self.request.retries))


@celery_app.task(
    bind=True,
    base=AIAnalysisTask,
    name='src.workers.tasks.ai_tasks.analyze_vc_task',
    max_retries=3,
    default_retry_delay=60
)
def analyze_vc_task(self, vc_id: str) -> Dict[str, Any]:
    """
    Analyze a VC's investment thesis using AI.
    
    Args:
        vc_id: The ID of the VC to analyze
        
    Returns:
        Dictionary with analysis results
    """
    try:
        logger.info(f"Starting AI analysis for VC {vc_id}")
        
        with get_db_context() as db:
            # Get VC data
            vc_repo = VCRepository()
            vc = vc_repo.get_by_id(db, vc_id)
            
            if not vc:
                raise ValueError(f"VC {vc_id} not found")
            
            # Update task progress
            self.update_state(
                state='PROCESSING',
                meta={'current': 'Analyzing VC thesis with AI', 'total': 100}
            )
            
            # Run AI analysis
            analysis = asyncio.run(
                self.ai_service.analyze_vc(vc)
            )
            
            # Store analysis results in VC metadata
            vc.metadata = vc.metadata or {}
            vc.metadata['ai_analysis'] = {
                'insights': analysis,
                'analyzed_at': datetime.utcnow().isoformat(),
                'version': '1.0'
            }
            
            # Update VC
            vc_repo.update(db, vc_id, vc)
            db.commit()
            
            logger.info(f"Completed AI analysis for VC {vc_id}")
            
            return {
                'vc_id': vc_id,
                'status': 'completed',
                'insights': analysis,
                'analyzed_at': datetime.utcnow().isoformat()
            }
            
    except Exception as e:
        logger.error(f"Error analyzing VC {vc_id}: {str(e)}")
        raise self.retry(exc=e, countdown=60 * (2 ** self.request.retries))


@celery_app.task(
    bind=True,
    base=AIAnalysisTask,
    name='src.workers.tasks.ai_tasks.batch_match_task',
    max_retries=2,
    time_limit=600,  # 10 minutes
    soft_time_limit=540  # 9 minutes
)
def batch_match_task(
    self,
    startup_ids: List[str],
    vc_ids: Optional[List[str]] = None,
    use_ai: bool = True,
    threshold: float = 0.7
) -> Dict[str, Any]:
    """
    Process batch matching in the background with progress tracking.
    
    Args:
        startup_ids: List of startup IDs to match
        vc_ids: Optional list of VC IDs (if None, matches against all VCs)
        use_ai: Whether to use AI for enhanced matching
        threshold: Minimum score threshold for matches
        
    Returns:
        Dictionary with matching results and statistics
    """
    try:
        logger.info(f"Starting batch match for {len(startup_ids)} startups")
        
        total_matches = 0
        processed = 0
        errors = []
        
        with get_db_context() as db:
            # Get all VCs if not specified
            if not vc_ids:
                vc_repo = VCRepository()
                vcs = vc_repo.get_all(db)
                vc_ids = [vc.id for vc in vcs]
            
            total_combinations = len(startup_ids) * len(vc_ids)
            
            # Process each startup
            for startup_id in startup_ids:
                try:
                    # Update progress
                    self.update_state(
                        state='PROCESSING',
                        meta={
                            'current': processed,
                            'total': total_combinations,
                            'matches_found': total_matches,
                            'current_startup': startup_id
                        }
                    )
                    
                    # Run matching for this startup
                    matches = asyncio.run(
                        self.match_service.batch_match(
                            db,
                            startup_ids=[startup_id],
                            vc_ids=vc_ids,
                            use_ai=use_ai,
                            threshold=threshold
                        )
                    )
                    
                    total_matches += len(matches)
                    processed += len(vc_ids)
                    
                except Exception as e:
                    logger.error(f"Error matching startup {startup_id}: {str(e)}")
                    errors.append({
                        'startup_id': startup_id,
                        'error': str(e)
                    })
                    processed += len(vc_ids)
            
            db.commit()
            
        logger.info(f"Completed batch match: {total_matches} matches found")
        
        return {
            'status': 'completed',
            'total_startups': len(startup_ids),
            'total_vcs': len(vc_ids),
            'total_combinations': total_combinations,
            'matches_found': total_matches,
            'errors': errors,
            'completed_at': datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error in batch match task: {str(e)}")
        raise self.retry(exc=e, countdown=120)


@celery_app.task(
    bind=True,
    base=AIAnalysisTask,
    name='src.workers.tasks.ai_tasks.generate_match_insights_task',
    max_retries=3
)
def generate_match_insights_task(self, match_id: str) -> Dict[str, Any]:
    """
    Generate detailed AI insights for a specific match.
    
    Args:
        match_id: The ID of the match to analyze
        
    Returns:
        Dictionary with detailed match insights
    """
    try:
        logger.info(f"Generating insights for match {match_id}")
        
        with get_db_context() as db:
            # Get match with related data
            match_repo = MatchRepository()
            match = match_repo.get_by_id(db, match_id)
            
            if not match:
                raise ValueError(f"Match {match_id} not found")
            
            # Get startup and VC data
            startup_repo = StartupRepository()
            vc_repo = VCRepository()
            
            startup = startup_repo.get_by_id(db, match.startup_id)
            vc = vc_repo.get_by_id(db, match.vc_id)
            
            # Generate detailed insights
            insights = asyncio.run(
                self.ai_service.generate_match_explanation(
                    startup,
                    vc,
                    match.score,
                    match.reasons
                )
            )
            
            # Update match with insights
            match.metadata = match.metadata or {}
            match.metadata['ai_insights'] = {
                'detailed_analysis': insights,
                'generated_at': datetime.utcnow().isoformat(),
                'version': '1.0'
            }
            
            match_repo.update(db, match_id, match)
            db.commit()
            
            logger.info(f"Generated insights for match {match_id}")
            
            return {
                'match_id': match_id,
                'status': 'completed',
                'insights': insights,
                'generated_at': datetime.utcnow().isoformat()
            }
            
    except Exception as e:
        logger.error(f"Error generating insights for match {match_id}: {str(e)}")
        raise self.retry(exc=e, countdown=60 * (2 ** self.request.retries))


# Helper function to check task status
def get_task_status(task_id: str) -> Dict[str, Any]:
    """
    Get the status of a Celery task.
    
    Args:
        task_id: The Celery task ID
        
    Returns:
        Dictionary with task status and result
    """
    task = AsyncResult(task_id, app=celery_app)
    
    if task.state == 'PENDING':
        return {
            'task_id': task_id,
            'state': task.state,
            'status': 'Task is waiting to be processed'
        }
    elif task.state == 'PROCESSING':
        return {
            'task_id': task_id,
            'state': task.state,
            'current': task.info.get('current', 0),
            'total': task.info.get('total', 1),
            'status': 'Task is being processed'
        }
    elif task.state == 'SUCCESS':
        return {
            'task_id': task_id,
            'state': task.state,
            'result': task.result,
            'status': 'Task completed successfully'
        }
    else:  # FAILURE
        return {
            'task_id': task_id,
            'state': task.state,
            'error': str(task.info),
            'status': 'Task failed'
        }