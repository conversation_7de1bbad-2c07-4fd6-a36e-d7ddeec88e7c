"""
Celery application configuration for VC Matching Platform.
"""
from celery import Celery
from kombu import Exchange, Queue
from src.core.config import settings

# Create Celery app
celery_app = Celery(
    'vc_matching',
    broker=settings.redis_url,
    backend=settings.redis_url,
    include=[
        'src.workers.tasks.ai_tasks',
        'src.workers.tasks.data_tasks',
        'src.workers.tasks.notification_tasks',
        'src.workers.tasks.scheduled_tasks',
        'src.workers.tasks.match_tasks',
        'src.workers.tasks.scraping_tasks',
        'src.workers.tasks.weekly_digest'
    ]
)

# Configure Celery
celery_app.conf.update(
    # Task settings
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    
    # Result backend settings
    result_expires=3600,  # Results expire after 1 hour
    result_persistent=True,
    
    # Task execution settings
    task_acks_late=True,
    task_reject_on_worker_lost=True,
    task_time_limit=300,  # 5 minutes hard limit
    task_soft_time_limit=240,  # 4 minutes soft limit
    
    # Worker settings
    worker_prefetch_multiplier=4,
    worker_max_tasks_per_child=1000,
    worker_disable_rate_limits=False,
    
    # Retry settings
    task_default_retry_delay=60,  # 1 minute
    task_max_retries=3,
    
    # Routing
    task_default_queue='default',
    task_default_exchange='default',
    task_default_exchange_type='direct',
    task_default_routing_key='default',
    
    # Define queues
    task_queues=(
        Queue('default', Exchange('default'), routing_key='default'),
        Queue('ai_analysis', Exchange('ai_analysis'), routing_key='ai_analysis'),
        Queue('data_enrichment', Exchange('data_enrichment'), routing_key='data_enrichment'),
        Queue('notifications', Exchange('notifications'), routing_key='notifications'),
        Queue('scheduled', Exchange('scheduled'), routing_key='scheduled'),
        Queue('scraping', Exchange('scraping'), routing_key='scraping'),
    ),
    
    # Route tasks to specific queues
    task_routes={
        'src.workers.tasks.ai_tasks.*': {'queue': 'ai_analysis'},
        'src.workers.tasks.data_tasks.*': {'queue': 'data_enrichment'},
        'src.workers.tasks.notification_tasks.*': {'queue': 'notifications'},
        'src.workers.tasks.scheduled_tasks.*': {'queue': 'scheduled'},
        'src.workers.tasks.scraping_tasks.*': {'queue': 'scraping'},
    },
    
    # Beat schedule for periodic tasks
    beat_schedule={
        'refresh-ai-insights': {
            'task': 'src.workers.tasks.scheduled_tasks.refresh_ai_insights',
            'schedule': 21600.0,  # Every 6 hours
            'options': {'queue': 'scheduled'}
        },
        'cleanup-stale-data': {
            'task': 'src.workers.tasks.scheduled_tasks.cleanup_stale_data',
            'schedule': 86400.0,  # Daily
            'options': {'queue': 'scheduled'}
        },
        'generate-analytics': {
            'task': 'src.workers.tasks.scheduled_tasks.generate_analytics_report',
            'schedule': 86400.0,  # Daily at midnight
            'options': {'queue': 'scheduled'}
        },
        'daily-match-generation': {
            'task': 'src.workers.tasks.match_tasks.daily_match_generation',
            'schedule': 86400.0,  # Daily
            'options': {'queue': 'scheduled'}
        },
        'cleanup-old-tasks': {
            'task': 'src.workers.tasks.scheduled_tasks.cleanup_old_tasks',
            'schedule': 3600.0,  # Every hour
            'options': {'queue': 'scheduled'}
        },
        'weekly-digest-emails': {
            'task': 'src.workers.tasks.weekly_digest.send_all_weekly_digests',
            'schedule': 604800.0,  # Weekly (every 7 days)
            'options': {'queue': 'notifications'}
        },
    }
)

# Import tasks to register them
from src.workers.tasks import *