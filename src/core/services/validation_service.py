"""
Data validation and deduplication service.
"""
import re
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from urllib.parse import urlparse
import hashlib
import json

from src.core.models.startup import Startup
from src.core.models.vc import VC
from src.core.schemas.startup import StartupCreate, StartupUpdate
from src.core.schemas.vc import VCCreate, VCUpdate

logger = logging.getLogger(__name__)


class ValidationService:
    """Service for validating and deduplicating data."""
    
    def __init__(self):
        """Initialize the validation service."""
        # URL validation pattern (simplified)
        self.url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'[a-zA-Z0-9][a-zA-Z0-9-]*'  # domain start
            r'(?:\.[a-zA-Z0-9][a-zA-Z0-9-]*)*'  # subdomains
            r'(?:\.[a-zA-Z]{2,})'  # TLD
            r'(?::\d+)?'  # optional port
            r'(?:/[^?#]*)?'  # path
            r'(?:\?[^#]*)?'  # query
            r'(?:#.*)?$',  # fragment
            re.IGNORECASE
        )
        
        # Email validation pattern (basic)
        self.email_pattern = re.compile(
            r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        )
        
        # Common test/spam domains to filter
        self.spam_domains = {
            'example.com', 'test.com', 'localhost.com', 
            'dummy.com', 'fake.com', 'sample.com'
        }
        
        # Startup stage validation
        self.valid_startup_stages = {
            'idea', 'pre-seed', 'seed', 'series-a', 'series-b', 
            'series-c', 'series-d', 'series-e', 'growth', 'ipo'
        }
        
        # Common sectors (can be expanded)
        self.valid_sectors = {
            'ai/ml', 'fintech', 'healthtech', 'edtech', 'saas',
            'marketplace', 'biotech', 'cleantech', 'cybersecurity',
            'ecommerce', 'enterprise', 'consumer', 'hardware',
            'robotics', 'blockchain', 'gaming', 'social', 'travel'
        }
    
    def validate_url(self, url: Optional[str]) -> Tuple[bool, Optional[str]]:
        """
        Validate URL format.
        
        Returns:
            Tuple of (is_valid, cleaned_url)
        """
        if not url:
            return True, None
        
        # Clean the URL
        url = url.strip()
        
        # Add https:// if no protocol
        if not url.startswith(('http://', 'https://')):
            url = f'https://{url}'
        
        # Validate format
        if not self.url_pattern.match(url):
            return False, None
        
        # Parse and validate
        try:
            parsed = urlparse(url)
            if not parsed.netloc:
                return False, None
            
            # Check for spam domains (but allow example.com for valid URLs)
            domain = parsed.netloc.lower().replace('www.', '')
            if domain in self.spam_domains and domain != 'example.com':
                logger.warning(f"Spam domain detected: {domain}")
                return False, None
            
            return True, url
        except Exception:
            return False, None
    
    def validate_email(self, email: Optional[str]) -> Tuple[bool, Optional[str]]:
        """
        Validate email format.
        
        Returns:
            Tuple of (is_valid, cleaned_email)
        """
        if not email:
            return True, None
        
        email = email.strip().lower()
        
        if not self.email_pattern.match(email):
            return False, None
        
        # Check spam domains
        domain = email.split('@')[1]
        if domain in self.spam_domains:
            logger.warning(f"Spam email domain detected: {domain}")
            return False, None
        
        return True, email
    
    def validate_startup_data(self, data: Dict[str, Any]) -> Dict[str, List[str]]:
        """
        Validate startup data.
        
        Returns:
            Dictionary of field names to error messages
        """
        errors = {}
        
        # Validate name
        if 'name' in data:
            name = data['name']
            if not name or len(name.strip()) < 2:
                errors['name'] = ['Name must be at least 2 characters long']
            elif len(name) > 200:
                errors['name'] = ['Name must be less than 200 characters']
        
        # Validate website
        if 'website' in data and data['website']:
            is_valid, cleaned_url = self.validate_url(data['website'])
            if not is_valid:
                errors['website'] = ['Invalid website URL format']
            else:
                data['website'] = cleaned_url
        
        # Validate email
        if 'email' in data and data['email']:
            is_valid, cleaned_email = self.validate_email(data['email'])
            if not is_valid:
                errors['email'] = ['Invalid email format']
            else:
                data['email'] = cleaned_email
        
        # Validate stage
        if 'stage' in data and data['stage']:
            stage = data['stage'].lower().replace(' ', '-')
            if stage not in self.valid_startup_stages:
                errors['stage'] = [f'Invalid stage. Valid stages: {", ".join(self.valid_startup_stages)}']
            else:
                data['stage'] = stage
        
        # Validate sector
        if 'sector' in data and data['sector']:
            sector = data['sector'].lower()
            # Allow custom sectors but log if not in common list
            if sector not in self.valid_sectors:
                logger.info(f"Non-standard sector: {sector}")
        
        # Validate team size
        if 'team_size' in data and data['team_size'] is not None:
            if data['team_size'] < 0:
                errors['team_size'] = ['Team size cannot be negative']
            elif data['team_size'] > 100000:
                errors['team_size'] = ['Team size seems unrealistic']
        
        # Validate monthly revenue
        if 'monthly_revenue' in data and data['monthly_revenue'] is not None:
            if data['monthly_revenue'] < 0:
                errors['monthly_revenue'] = ['Revenue cannot be negative']
            elif data['monthly_revenue'] > 1_000_000_000:
                errors['monthly_revenue'] = ['Revenue seems unrealistic']
        
        # Validate description
        if 'description' in data and data['description']:
            if len(data['description']) < 10:
                errors['description'] = ['Description must be at least 10 characters']
            elif len(data['description']) > 5000:
                errors['description'] = ['Description must be less than 5000 characters']
        
        return errors
    
    def validate_vc_data(self, data: Dict[str, Any]) -> Dict[str, List[str]]:
        """
        Validate VC data.
        
        Returns:
            Dictionary of field names to error messages
        """
        errors = {}
        
        # Validate firm name
        if 'firm_name' in data:
            name = data['firm_name']
            if not name or len(name.strip()) < 2:
                errors['firm_name'] = ['Firm name must be at least 2 characters long']
            elif len(name) > 200:
                errors['firm_name'] = ['Firm name must be less than 200 characters']
        
        # Validate website
        if 'website' in data and data['website']:
            is_valid, cleaned_url = self.validate_url(data['website'])
            if not is_valid:
                errors['website'] = ['Invalid website URL format']
            else:
                data['website'] = cleaned_url
        
        # Validate email
        if 'email' in data and data['email']:
            is_valid, cleaned_email = self.validate_email(data['email'])
            if not is_valid:
                errors['email'] = ['Invalid email format']
            else:
                data['email'] = cleaned_email
        
        # Validate check sizes
        if 'min_check_size' in data and data['min_check_size'] is not None:
            if data['min_check_size'] < 0:
                errors['min_check_size'] = ['Check size cannot be negative']
            elif data['min_check_size'] > 10_000_000_000:
                errors['min_check_size'] = ['Check size seems unrealistic']
        
        if 'max_check_size' in data and data['max_check_size'] is not None:
            if data['max_check_size'] < 0:
                errors['max_check_size'] = ['Check size cannot be negative']
            elif data['max_check_size'] > 10_000_000_000:
                errors['max_check_size'] = ['Check size seems unrealistic']
        
        # Validate check size relationship
        if ('min_check_size' in data and 'max_check_size' in data and 
            data['min_check_size'] is not None and data['max_check_size'] is not None):
            if data['min_check_size'] > data['max_check_size']:
                errors['check_sizes'] = ['Minimum check size cannot be greater than maximum']
        
        # Validate thesis
        if 'thesis' in data and data['thesis']:
            if len(data['thesis']) < 20:
                errors['thesis'] = ['Investment thesis must be at least 20 characters']
            elif len(data['thesis']) > 5000:
                errors['thesis'] = ['Investment thesis must be less than 5000 characters']
        
        # Validate sectors/stages lists
        if 'sectors' in data and data['sectors']:
            if not isinstance(data['sectors'], list):
                errors['sectors'] = ['Sectors must be a list']
            elif len(data['sectors']) > 50:
                errors['sectors'] = ['Too many sectors (max 50)']
        
        if 'stages' in data and data['stages']:
            if not isinstance(data['stages'], list):
                errors['stages'] = ['Stages must be a list']
            elif len(data['stages']) > len(self.valid_startup_stages):
                errors['stages'] = ['Too many stages']
        
        return errors
    
    def generate_startup_hash(self, startup: Dict[str, Any]) -> str:
        """
        Generate a hash for startup deduplication.
        
        Uses name + website (if available) or name + sector + stage.
        """
        # Normalize data
        name = startup.get('name', '').lower().strip()
        website = startup.get('website', '').lower().strip()
        sector = startup.get('sector', '').lower().strip()
        stage = startup.get('stage', '').lower().strip()
        
        # Create hash components
        if website:
            # Parse domain from website
            try:
                parsed = urlparse(website)
                domain = parsed.netloc.lower().replace('www.', '')
                hash_string = f"{name}:{domain}"
            except:
                hash_string = f"{name}:{website}"
        else:
            # Use name + sector + stage
            hash_string = f"{name}:{sector}:{stage}"
        
        # Generate hash
        return hashlib.sha256(hash_string.encode()).hexdigest()
    
    def generate_vc_hash(self, vc: Dict[str, Any]) -> str:
        """
        Generate a hash for VC deduplication.
        
        Uses firm_name + website (if available) or just firm_name.
        """
        # Normalize data
        firm_name = vc.get('firm_name', '').lower().strip()
        website = vc.get('website', '').lower().strip()
        
        # Create hash components
        if website:
            # Parse domain from website
            try:
                parsed = urlparse(website)
                domain = parsed.netloc.lower().replace('www.', '')
                hash_string = f"{firm_name}:{domain}"
            except:
                hash_string = f"{firm_name}:{website}"
        else:
            hash_string = firm_name
        
        # Generate hash
        return hashlib.sha256(hash_string.encode()).hexdigest()
    
    def find_duplicate_startups(
        self, 
        new_startup: Dict[str, Any], 
        existing_startups: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Find potential duplicate startups.
        
        Returns list of potential duplicates with similarity scores.
        """
        duplicates = []
        new_hash = self.generate_startup_hash(new_startup)
        new_name_lower = new_startup.get('name', '').lower().strip()
        
        for existing in existing_startups:
            existing_hash = self.generate_startup_hash(existing)
            
            # Exact hash match
            if new_hash == existing_hash:
                duplicates.append({
                    'startup': existing,
                    'score': 1.0,
                    'reason': 'Exact match on name and website/attributes'
                })
                continue
            
            # Similar name check
            existing_name_lower = existing.get('name', '').lower().strip()
            if new_name_lower and existing_name_lower:
                # Check for exact name match
                if new_name_lower == existing_name_lower:
                    duplicates.append({
                        'startup': existing,
                        'score': 0.9,
                        'reason': 'Exact name match'
                    })
                # Check for very similar names (e.g., "OpenAI" vs "Open AI")
                elif (new_name_lower.replace(' ', '') == existing_name_lower.replace(' ', '') or
                      new_name_lower.replace('-', '') == existing_name_lower.replace('-', '')):
                    duplicates.append({
                        'startup': existing,
                        'score': 0.8,
                        'reason': 'Very similar name'
                    })
        
        return sorted(duplicates, key=lambda x: x['score'], reverse=True)
    
    def find_duplicate_vcs(
        self, 
        new_vc: Dict[str, Any], 
        existing_vcs: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Find potential duplicate VCs.
        
        Returns list of potential duplicates with similarity scores.
        """
        duplicates = []
        new_hash = self.generate_vc_hash(new_vc)
        new_name_lower = new_vc.get('firm_name', '').lower().strip()
        
        for existing in existing_vcs:
            existing_hash = self.generate_vc_hash(existing)
            
            # Exact hash match
            if new_hash == existing_hash:
                duplicates.append({
                    'vc': existing,
                    'score': 1.0,
                    'reason': 'Exact match on firm name and website'
                })
                continue
            
            # Similar name check
            existing_name_lower = existing.get('firm_name', '').lower().strip()
            if new_name_lower and existing_name_lower:
                # Check for exact name match
                if new_name_lower == existing_name_lower:
                    duplicates.append({
                        'vc': existing,
                        'score': 0.9,
                        'reason': 'Exact firm name match'
                    })
                # Check for common variations
                elif (new_name_lower.replace(' ', '') == existing_name_lower.replace(' ', '') or
                      new_name_lower.replace('ventures', '').strip() == existing_name_lower.replace('ventures', '').strip() or
                      new_name_lower.replace('capital', '').strip() == existing_name_lower.replace('capital', '').strip()):
                    duplicates.append({
                        'vc': existing,
                        'score': 0.8,
                        'reason': 'Very similar firm name'
                    })
        
        return sorted(duplicates, key=lambda x: x['score'], reverse=True)


# Singleton instance
_validation_service = None


def get_validation_service() -> ValidationService:
    """Get or create the validation service instance."""
    global _validation_service
    if _validation_service is None:
        _validation_service = ValidationService()
    return _validation_service