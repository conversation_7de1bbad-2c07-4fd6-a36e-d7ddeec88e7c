"""Service for monitoring market signals and alerting users."""

from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
from uuid import UUID
import logging
import asyncio

from sqlalchemy.orm import Session
from redis import Redis

logger = logging.getLogger(__name__)


class SignalType(Enum):
    """Types of market signals to monitor."""
    NEW_STARTUP_IN_SECTOR = "new_startup_in_sector"
    NEW_VC_IN_SECTOR = "new_vc_in_sector"
    HOT_STARTUP = "hot_startup"
    FUNDING_ROUND = "funding_round"
    SECTOR_TREND = "sector_trend"
    MATCH_OPPORTUNITY = "match_opportunity"
    COMPETITOR_ACTIVITY = "competitor_activity"
    THESIS_MATCH = "thesis_match"


@dataclass
class Signal:
    """Represents a market signal."""
    id: str
    type: SignalType
    title: str
    description: str
    severity: str  # low, medium, high
    entity_type: str  # startup, vc, sector, market
    entity_id: Optional[str] = None
    metadata: Dict[str, Any] = None
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()
        if self.metadata is None:
            self.metadata = {}


@dataclass
class SignalRule:
    """Rule for generating signals."""
    id: str
    name: str
    type: SignalType
    conditions: Dict[str, Any]
    user_id: Optional[UUID] = None
    enabled: bool = True
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()


class SignalMonitoringService:
    """Service for monitoring and generating market signals."""
    
    def __init__(self, db: Session, redis: Redis):
        """Initialize with database and Redis."""
        self.db = db
        self.redis = redis
        self.signal_queue_key = "signals:queue"
        self.rules_key = "signal:rules"
    
    async def create_signal_rule(
        self,
        user_id: UUID,
        rule_data: Dict[str, Any]
    ) -> SignalRule:
        """Create a new signal monitoring rule."""
        rule = SignalRule(
            id=f"rule_{user_id}_{datetime.utcnow().timestamp()}",
            name=rule_data["name"],
            type=SignalType(rule_data["type"]),
            conditions=rule_data["conditions"],
            user_id=user_id,
            enabled=rule_data.get("enabled", True)
        )
        
        # Store rule in Redis
        rule_key = f"{self.rules_key}:{user_id}:{rule.id}"
        self.redis.hset(
            rule_key,
            mapping={
                "id": rule.id,
                "name": rule.name,
                "type": rule.type.value,
                "conditions": str(rule.conditions),
                "enabled": str(rule.enabled),
                "created_at": rule.created_at.isoformat()
            }
        )
        
        logger.info(f"Created signal rule {rule.id} for user {user_id}")
        return rule
    
    async def get_user_rules(self, user_id: UUID) -> List[SignalRule]:
        """Get all signal rules for a user."""
        pattern = f"{self.rules_key}:{user_id}:*"
        rules = []
        
        for key in self.redis.scan_iter(match=pattern):
            rule_data = self.redis.hgetall(key)
            if rule_data:
                rules.append(SignalRule(
                    id=rule_data[b'id'].decode(),
                    name=rule_data[b'name'].decode(),
                    type=SignalType(rule_data[b'type'].decode()),
                    conditions=eval(rule_data[b'conditions'].decode()),
                    user_id=user_id,
                    enabled=rule_data[b'enabled'].decode() == 'True',
                    created_at=datetime.fromisoformat(rule_data[b'created_at'].decode())
                ))
        
        return rules
    
    async def check_signals(self) -> List[Signal]:
        """Check for new signals based on all active rules."""
        signals = []
        
        # Check various signal types
        signals.extend(await self._check_new_startup_signals())
        signals.extend(await self._check_hot_startup_signals())
        signals.extend(await self._check_sector_trend_signals())
        signals.extend(await self._check_match_opportunity_signals())
        
        # Queue signals for delivery
        for signal in signals:
            await self._queue_signal(signal)
        
        return signals
    
    async def _check_new_startup_signals(self) -> List[Signal]:
        """Check for new startups matching user criteria."""
        signals = []
        
        # Get startups created in last 24 hours
        cutoff = datetime.utcnow() - timedelta(hours=24)
        
        new_startups = self.db.execute(
            """
            SELECT id, name, sector, stage, description
            FROM startups
            WHERE created_at >= :cutoff
            """,
            {"cutoff": cutoff}
        ).fetchall()
        
        for startup in new_startups:
            # Check if any rules match this startup
            all_rules = await self._get_all_sector_rules(startup.sector)
            
            for rule in all_rules:
                if rule.enabled and self._matches_conditions(startup, rule.conditions):
                    signal = Signal(
                        id=f"signal_{startup.id}_{rule.id}_{datetime.utcnow().timestamp()}",
                        type=SignalType.NEW_STARTUP_IN_SECTOR,
                        title=f"New {startup.sector} Startup: {startup.name}",
                        description=f"{startup.name} ({startup.stage}) just joined. {startup.description[:100]}...",
                        severity="medium",
                        entity_type="startup",
                        entity_id=str(startup.id),
                        metadata={
                            "sector": startup.sector,
                            "stage": startup.stage,
                            "rule_id": rule.id,
                            "user_id": str(rule.user_id)
                        }
                    )
                    signals.append(signal)
        
        return signals
    
    async def _check_hot_startup_signals(self) -> List[Signal]:
        """Check for startups getting unusual attention."""
        signals = []
        
        # Find startups with high recent match activity
        hot_startups = self.db.execute(
            """
            SELECT s.id, s.name, s.sector, COUNT(m.id) as recent_matches
            FROM startups s
            JOIN matches m ON s.id = m.startup_id
            WHERE m.created_at >= :cutoff
            GROUP BY s.id, s.name, s.sector
            HAVING COUNT(m.id) >= 5
            ORDER BY recent_matches DESC
            """,
            {"cutoff": datetime.utcnow() - timedelta(days=7)}
        ).fetchall()
        
        for startup in hot_startups:
            signal = Signal(
                id=f"signal_hot_{startup.id}_{datetime.utcnow().timestamp()}",
                type=SignalType.HOT_STARTUP,
                title=f"🔥 {startup.name} is trending",
                description=f"{startup.name} has {startup.recent_matches} VC matches this week",
                severity="high",
                entity_type="startup",
                entity_id=str(startup.id),
                metadata={
                    "sector": startup.sector,
                    "match_count": startup.recent_matches
                }
            )
            signals.append(signal)
        
        return signals
    
    async def _check_sector_trend_signals(self) -> List[Signal]:
        """Check for emerging sector trends."""
        signals = []
        
        # Analyze sector growth
        sector_growth = self.db.execute(
            """
            WITH recent_counts AS (
                SELECT sector, COUNT(*) as recent
                FROM startups
                WHERE created_at >= :recent_cutoff
                GROUP BY sector
            ),
            previous_counts AS (
                SELECT sector, COUNT(*) as previous
                FROM startups
                WHERE created_at >= :prev_cutoff
                AND created_at < :recent_cutoff
                GROUP BY sector
            )
            SELECT 
                r.sector,
                r.recent,
                COALESCE(p.previous, 0) as previous,
                CASE 
                    WHEN COALESCE(p.previous, 0) = 0 THEN 100
                    ELSE ROUND((r.recent - COALESCE(p.previous, 0))::FLOAT / p.previous * 100, 2)
                END as growth_rate
            FROM recent_counts r
            LEFT JOIN previous_counts p ON r.sector = p.sector
            WHERE r.recent >= 5
            ORDER BY growth_rate DESC
            """,
            {
                "recent_cutoff": datetime.utcnow() - timedelta(days=30),
                "prev_cutoff": datetime.utcnow() - timedelta(days=60)
            }
        ).fetchall()
        
        for sector in sector_growth:
            if sector.growth_rate >= 50:  # 50% growth threshold
                signal = Signal(
                    id=f"signal_trend_{sector.sector}_{datetime.utcnow().timestamp()}",
                    type=SignalType.SECTOR_TREND,
                    title=f"📈 {sector.sector} sector surging",
                    description=f"{sector.sector} grew {sector.growth_rate}% with {sector.recent} new startups",
                    severity="high" if sector.growth_rate >= 100 else "medium",
                    entity_type="sector",
                    metadata={
                        "sector": sector.sector,
                        "growth_rate": sector.growth_rate,
                        "new_startups": sector.recent
                    }
                )
                signals.append(signal)
        
        return signals
    
    async def _check_match_opportunity_signals(self) -> List[Signal]:
        """Check for high-quality match opportunities."""
        signals = []
        
        # Find unmatched high-potential pairs
        opportunities = self.db.execute(
            """
            SELECT 
                s.id as startup_id,
                s.name as startup_name,
                v.id as vc_id,
                v.name as vc_name,
                s.sector
            FROM startups s
            CROSS JOIN vcs v
            WHERE s.sector = ANY(v.sectors)
            AND s.stage = ANY(v.stages)
            AND NOT EXISTS (
                SELECT 1 FROM matches m 
                WHERE m.startup_id = s.id AND m.vc_id = v.id
            )
            AND s.created_at >= :cutoff
            LIMIT 10
            """,
            {"cutoff": datetime.utcnow() - timedelta(days=7)}
        ).fetchall()
        
        for opp in opportunities:
            signal = Signal(
                id=f"signal_match_{opp.startup_id}_{opp.vc_id}_{datetime.utcnow().timestamp()}",
                type=SignalType.MATCH_OPPORTUNITY,
                title=f"Match opportunity: {opp.startup_name} × {opp.vc_name}",
                description=f"Strong potential match in {opp.sector} sector",
                severity="medium",
                entity_type="match",
                metadata={
                    "startup_id": str(opp.startup_id),
                    "startup_name": opp.startup_name,
                    "vc_id": str(opp.vc_id),
                    "vc_name": opp.vc_name,
                    "sector": opp.sector
                }
            )
            signals.append(signal)
        
        return signals
    
    async def _queue_signal(self, signal: Signal):
        """Queue a signal for delivery."""
        signal_data = {
            "id": signal.id,
            "type": signal.type.value,
            "title": signal.title,
            "description": signal.description,
            "severity": signal.severity,
            "entity_type": signal.entity_type,
            "entity_id": signal.entity_id,
            "metadata": signal.metadata,
            "created_at": signal.created_at.isoformat()
        }
        
        # Add to Redis queue
        self.redis.lpush(self.signal_queue_key, str(signal_data))
        
        # Also store in user-specific queue if user_id in metadata
        if signal.metadata and signal.metadata.get("user_id"):
            user_queue = f"signals:user:{signal.metadata['user_id']}"
            self.redis.lpush(user_queue, str(signal_data))
            self.redis.expire(user_queue, 86400 * 7)  # 7 days
    
    async def get_user_signals(
        self,
        user_id: UUID,
        limit: int = 50,
        signal_type: Optional[SignalType] = None
    ) -> List[Signal]:
        """Get signals for a specific user."""
        user_queue = f"signals:user:{user_id}"
        signals = []
        
        # Get signals from Redis
        signal_data_list = self.redis.lrange(user_queue, 0, limit - 1)
        
        for signal_data in signal_data_list:
            try:
                data = eval(signal_data.decode())
                signal = Signal(
                    id=data["id"],
                    type=SignalType(data["type"]),
                    title=data["title"],
                    description=data["description"],
                    severity=data["severity"],
                    entity_type=data["entity_type"],
                    entity_id=data.get("entity_id"),
                    metadata=data.get("metadata", {}),
                    created_at=datetime.fromisoformat(data["created_at"])
                )
                
                if not signal_type or signal.type == signal_type:
                    signals.append(signal)
                    
            except Exception as e:
                logger.error(f"Error parsing signal: {e}")
        
        return signals
    
    async def mark_signal_read(self, user_id: UUID, signal_id: str):
        """Mark a signal as read."""
        read_key = f"signals:read:{user_id}"
        self.redis.sadd(read_key, signal_id)
        self.redis.expire(read_key, 86400 * 30)  # 30 days
    
    async def get_signal_stats(self, user_id: UUID) -> Dict[str, Any]:
        """Get signal statistics for a user."""
        signals = await self.get_user_signals(user_id, limit=100)
        read_key = f"signals:read:{user_id}"
        read_signals = self.redis.smembers(read_key)
        read_ids = {s.decode() for s in read_signals}
        
        # Calculate stats
        total = len(signals)
        unread = sum(1 for s in signals if s.id not in read_ids)
        
        by_type = {}
        by_severity = {"low": 0, "medium": 0, "high": 0}
        
        for signal in signals:
            # Count by type
            type_name = signal.type.value
            by_type[type_name] = by_type.get(type_name, 0) + 1
            
            # Count by severity
            by_severity[signal.severity] += 1
        
        return {
            "total_signals": total,
            "unread_signals": unread,
            "read_signals": total - unread,
            "by_type": by_type,
            "by_severity": by_severity,
            "oldest_unread": min(
                (s.created_at for s in signals if s.id not in read_ids),
                default=None
            )
        }
    
    def _matches_conditions(self, entity: Any, conditions: Dict[str, Any]) -> bool:
        """Check if an entity matches rule conditions."""
        for field, expected in conditions.items():
            actual = getattr(entity, field, None)
            
            if isinstance(expected, list):
                if actual not in expected:
                    return False
            elif actual != expected:
                return False
        
        return True
    
    async def _get_all_sector_rules(self, sector: str) -> List[SignalRule]:
        """Get all rules that monitor a specific sector."""
        all_rules = []
        
        # Scan all rule keys
        for key in self.redis.scan_iter(match=f"{self.rules_key}:*"):
            rule_data = self.redis.hgetall(key)
            if rule_data:
                conditions = eval(rule_data[b'conditions'].decode())
                
                # Check if this rule monitors the sector
                if "sector" in conditions:
                    if sector in conditions["sector"] or conditions["sector"] == sector:
                        user_id = key.decode().split(':')[2]
                        
                        all_rules.append(SignalRule(
                            id=rule_data[b'id'].decode(),
                            name=rule_data[b'name'].decode(),
                            type=SignalType(rule_data[b'type'].decode()),
                            conditions=conditions,
                            user_id=UUID(user_id),
                            enabled=rule_data[b'enabled'].decode() == 'True'
                        ))
        
        return all_rules