"""Service for caching discovery results and expensive computations."""

from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta
import json
import hashlib
import logging
from redis import Redis
from pydantic import BaseModel

logger = logging.getLogger(__name__)


class Cache<PERSON>ey:
    """Generate and manage cache keys."""
    
    @staticmethod
    def discovery_search(query: str, filters: Dict[str, Any]) -> str:
        """Generate cache key for discovery search."""
        # Create a stable hash of the query and filters
        key_data = {
            "query": query.lower().strip(),
            "filters": json.dumps(filters, sort_keys=True)
        }
        key_hash = hashlib.md5(json.dumps(key_data).encode()).hexdigest()
        return f"discovery:search:{key_hash}"
    
    @staticmethod
    def vc_discovery(vc_id: str, filters: Dict[str, Any]) -> str:
        """Generate cache key for VC discovery results."""
        filter_hash = hashlib.md5(json.dumps(filters, sort_keys=True).encode()).hexdigest()
        return f"discovery:vc:{vc_id}:{filter_hash}"
    
    @staticmethod
    def startup_discovery(startup_id: str, filters: Dict[str, Any]) -> str:
        """Generate cache key for startup discovery results."""
        filter_hash = hashlib.md5(json.dumps(filters, sort_keys=True).encode()).hexdigest()
        return f"discovery:startup:{startup_id}:{filter_hash}"
    
    @staticmethod
    def ai_analysis(entity_type: str, entity_id: str, analysis_type: str) -> str:
        """Generate cache key for AI analysis results."""
        return f"ai:{entity_type}:{entity_id}:{analysis_type}"
    
    @staticmethod
    def matching_score(startup_id: str, vc_id: str) -> str:
        """Generate cache key for matching scores."""
        return f"match:score:{startup_id}:{vc_id}"
    
    @staticmethod
    def warm_intro_paths(source_id: str, target_id: str, max_depth: int) -> str:
        """Generate cache key for warm intro paths."""
        return f"intro:paths:{source_id}:{target_id}:{max_depth}"


class CachedResult(BaseModel):
    """Wrapper for cached results with metadata."""
    data: Any
    cached_at: datetime
    expires_at: Optional[datetime] = None
    hit_count: int = 0
    
    def is_expired(self) -> bool:
        """Check if cache entry is expired."""
        if not self.expires_at:
            return False
        return datetime.utcnow() > self.expires_at


class CacheService:
    """Service for managing discovery and computation caches."""
    
    # Default TTLs for different cache types (in seconds)
    TTL_DISCOVERY_SEARCH = 3600  # 1 hour
    TTL_AI_ANALYSIS = 86400 * 7  # 7 days
    TTL_MATCHING_SCORE = 3600 * 6  # 6 hours
    TTL_WARM_INTRO = 1800  # 30 minutes
    
    def __init__(self, redis_client: Redis, prefix: str = "vc_platform"):
        """Initialize cache service."""
        self.redis = redis_client
        self.prefix = prefix
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        try:
            full_key = f"{self.prefix}:{key}"
            cached_json = self.redis.get(full_key)
            
            if not cached_json:
                return None
            
            # Parse cached result
            cached_result = CachedResult.parse_raw(cached_json)
            
            # Check expiration
            if cached_result.is_expired():
                await self.delete(key)
                return None
            
            # Increment hit count
            cached_result.hit_count += 1
            self.redis.set(
                full_key,
                cached_result.json(),
                ex=self._get_remaining_ttl(cached_result)
            )
            
            logger.debug(f"Cache hit for key: {key}")
            return cached_result.data
            
        except Exception as e:
            logger.error(f"Cache get error for {key}: {str(e)}")
            return None
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None,
        expires_at: Optional[datetime] = None
    ) -> bool:
        """Set value in cache with optional TTL."""
        try:
            full_key = f"{self.prefix}:{key}"
            
            # Create cached result
            cached_result = CachedResult(
                data=value,
                cached_at=datetime.utcnow(),
                expires_at=expires_at or (
                    datetime.utcnow() + timedelta(seconds=ttl) if ttl else None
                )
            )
            
            # Serialize and store
            self.redis.set(
                full_key,
                cached_result.json(),
                ex=ttl
            )
            
            logger.debug(f"Cached value for key: {key}")
            return True
            
        except Exception as e:
            logger.error(f"Cache set error for {key}: {str(e)}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete value from cache."""
        try:
            full_key = f"{self.prefix}:{key}"
            result = self.redis.delete(full_key)
            logger.debug(f"Deleted cache key: {key}")
            return bool(result)
        except Exception as e:
            logger.error(f"Cache delete error for {key}: {str(e)}")
            return False
    
    async def delete_pattern(self, pattern: str) -> int:
        """Delete all keys matching pattern."""
        try:
            full_pattern = f"{self.prefix}:{pattern}"
            keys = list(self.redis.scan_iter(match=full_pattern))
            
            if keys:
                deleted = self.redis.delete(*keys)
                logger.debug(f"Deleted {deleted} keys matching pattern: {pattern}")
                return deleted
            
            return 0
        except Exception as e:
            logger.error(f"Cache delete pattern error for {pattern}: {str(e)}")
            return 0
    
    # Discovery caching methods
    
    async def cache_discovery_search(
        self,
        query: str,
        filters: Dict[str, Any],
        results: List[Dict[str, Any]]
    ) -> bool:
        """Cache discovery search results."""
        key = CacheKey.discovery_search(query, filters)
        return await self.set(key, results, ttl=self.TTL_DISCOVERY_SEARCH)
    
    async def get_discovery_search(
        self,
        query: str,
        filters: Dict[str, Any]
    ) -> Optional[List[Dict[str, Any]]]:
        """Get cached discovery search results."""
        key = CacheKey.discovery_search(query, filters)
        return await self.get(key)
    
    async def cache_vc_discovery(
        self,
        vc_id: str,
        filters: Dict[str, Any],
        results: List[Dict[str, Any]]
    ) -> bool:
        """Cache VC discovery results."""
        key = CacheKey.vc_discovery(vc_id, filters)
        return await self.set(key, results, ttl=self.TTL_DISCOVERY_SEARCH)
    
    async def get_vc_discovery(
        self,
        vc_id: str,
        filters: Dict[str, Any]
    ) -> Optional[List[Dict[str, Any]]]:
        """Get cached VC discovery results."""
        key = CacheKey.vc_discovery(vc_id, filters)
        return await self.get(key)
    
    # AI analysis caching
    
    async def cache_ai_analysis(
        self,
        entity_type: str,
        entity_id: str,
        analysis_type: str,
        analysis_result: Dict[str, Any]
    ) -> bool:
        """Cache AI analysis results."""
        key = CacheKey.ai_analysis(entity_type, entity_id, analysis_type)
        return await self.set(key, analysis_result, ttl=self.TTL_AI_ANALYSIS)
    
    async def get_ai_analysis(
        self,
        entity_type: str,
        entity_id: str,
        analysis_type: str
    ) -> Optional[Dict[str, Any]]:
        """Get cached AI analysis."""
        key = CacheKey.ai_analysis(entity_type, entity_id, analysis_type)
        return await self.get(key)
    
    # Matching score caching
    
    async def cache_matching_score(
        self,
        startup_id: str,
        vc_id: str,
        score_data: Dict[str, Any]
    ) -> bool:
        """Cache matching score between startup and VC."""
        key = CacheKey.matching_score(startup_id, vc_id)
        return await self.set(key, score_data, ttl=self.TTL_MATCHING_SCORE)
    
    async def get_matching_score(
        self,
        startup_id: str,
        vc_id: str
    ) -> Optional[Dict[str, Any]]:
        """Get cached matching score."""
        key = CacheKey.matching_score(startup_id, vc_id)
        return await self.get(key)
    
    # Warm intro caching
    
    async def cache_warm_intro_paths(
        self,
        source_id: str,
        target_id: str,
        max_depth: int,
        paths: List[Dict[str, Any]]
    ) -> bool:
        """Cache warm intro paths."""
        key = CacheKey.warm_intro_paths(source_id, target_id, max_depth)
        return await self.set(key, paths, ttl=self.TTL_WARM_INTRO)
    
    async def get_warm_intro_paths(
        self,
        source_id: str,
        target_id: str,
        max_depth: int
    ) -> Optional[List[Dict[str, Any]]]:
        """Get cached warm intro paths."""
        key = CacheKey.warm_intro_paths(source_id, target_id, max_depth)
        return await self.get(key)
    
    # Cache invalidation
    
    async def invalidate_entity(self, entity_type: str, entity_id: str) -> int:
        """Invalidate all caches related to an entity."""
        patterns = [
            f"discovery:{entity_type}:{entity_id}:*",
            f"ai:{entity_type}:{entity_id}:*",
            f"match:score:*{entity_id}*",
            f"intro:paths:*{entity_id}*"
        ]
        
        total_deleted = 0
        for pattern in patterns:
            deleted = await self.delete_pattern(pattern)
            total_deleted += deleted
        
        logger.info(f"Invalidated {total_deleted} cache entries for {entity_type} {entity_id}")
        return total_deleted
    
    async def invalidate_discovery_caches(self) -> int:
        """Invalidate all discovery-related caches."""
        return await self.delete_pattern("discovery:*")
    
    async def invalidate_matching_caches(self) -> int:
        """Invalidate all matching-related caches."""
        return await self.delete_pattern("match:*")
    
    # Cache statistics
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        try:
            info = self.redis.info("memory")
            keyspace = self.redis.info("keyspace")
            
            # Count keys by pattern
            patterns = {
                "discovery": "discovery:*",
                "ai_analysis": "ai:*",
                "matching": "match:*",
                "warm_intro": "intro:*"
            }
            
            key_counts = {}
            for name, pattern in patterns.items():
                full_pattern = f"{self.prefix}:{pattern}"
                count = sum(1 for _ in self.redis.scan_iter(match=full_pattern))
                key_counts[name] = count
            
            return {
                "memory_used": info.get("used_memory_human", "0B"),
                "memory_peak": info.get("used_memory_peak_human", "0B"),
                "total_keys": sum(key_counts.values()),
                "keys_by_type": key_counts,
                "evicted_keys": info.get("evicted_keys", 0),
                "hit_rate": self._calculate_hit_rate(info),
                "connected_clients": info.get("connected_clients", 0)
            }
        except Exception as e:
            logger.error(f"Error getting cache stats: {str(e)}")
            return {}
    
    def _get_remaining_ttl(self, cached_result: CachedResult) -> Optional[int]:
        """Calculate remaining TTL for a cached result."""
        if not cached_result.expires_at:
            return None
        
        remaining = (cached_result.expires_at - datetime.utcnow()).total_seconds()
        return max(int(remaining), 1) if remaining > 0 else 1
    
    def _calculate_hit_rate(self, info: Dict[str, Any]) -> float:
        """Calculate cache hit rate from Redis info."""
        hits = info.get("keyspace_hits", 0)
        misses = info.get("keyspace_misses", 0)
        total = hits + misses
        
        if total == 0:
            return 0.0
        
        return round(hits / total * 100, 2)


# Singleton instance
_cache_service = None


def get_cache_service() -> CacheService:
    """Get or create the cache service instance."""
    global _cache_service
    if _cache_service is None:
        _cache_service = CacheService()
    return _cache_service