"""Service for managing web scraping and data enrichment."""

from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import logging

from src.core.models.startup import Startup
from src.core.repositories.startup_repository import StartupRepository
from src.scrapers.yc_scraper import YCCompanyScraper

logger = logging.getLogger(__name__)


class ScraperService:
    """Service for scraping and enriching startup data."""
    
    def __init__(self, startup_repo: StartupRepository):
        """Initialize with repository."""
        self.startup_repo = startup_repo
        self.yc_scraper = YCCompanyScraper()
    
    async def scrape_yc_companies(self, limit: int = 50) -> Dict[str, Any]:
        """Scrape YC companies and store new ones."""
        try:
            # Fetch companies from YC API
            logger.info(f"Starting YC scraping with limit {limit}")
            scraped_companies = await self.yc_scraper.scrape_companies(limit=limit)
            
            # Process results
            results = {
                "total_scraped": len(scraped_companies),
                "new_companies": 0,
                "updated_companies": 0,
                "errors": 0,
                "companies": []
            }
            
            for company_data in scraped_companies:
                try:
                    # Check if company already exists
                    existing = await self._find_existing_company(company_data["name"])
                    
                    if existing:
                        # Update existing company if data has changed
                        if self._should_update_company(existing, company_data):
                            updated = await self._update_company(existing, company_data)
                            results["updated_companies"] += 1
                            results["companies"].append({
                                "name": updated.name,
                                "action": "updated",
                                "id": str(updated.id)
                            })
                    else:
                        # Create new company
                        startup = await self._create_startup_from_scraped(company_data)
                        if startup:
                            results["new_companies"] += 1
                            results["companies"].append({
                                "name": startup.name,
                                "action": "created",
                                "id": str(startup.id)
                            })
                
                except Exception as e:
                    logger.error(f"Error processing company {company_data.get('name')}: {str(e)}")
                    results["errors"] += 1
            
            logger.info(f"YC scraping completed: {results['new_companies']} new, "
                       f"{results['updated_companies']} updated, {results['errors']} errors")
            
            return results
            
        except Exception as e:
            logger.error(f"YC scraping failed: {str(e)}")
            raise
    
    async def scrape_recent_yc_companies(self) -> Dict[str, Any]:
        """Scrape only recent YC companies (last 2 years)."""
        from src.scrapers.yc_scraper import discover_new_yc_companies
        
        try:
            # Get recent companies
            recent_companies = await discover_new_yc_companies()
            
            # Process them
            results = {
                "total_scraped": len(recent_companies),
                "new_companies": 0,
                "updated_companies": 0,
                "errors": 0,
                "recent_batches": list(set(c.get("batch", "") for c in recent_companies)),
                "companies": []
            }
            
            for company_data in recent_companies:
                try:
                    existing = await self._find_existing_company(company_data["name"])
                    
                    if not existing:
                        startup = await self._create_startup_from_scraped(company_data)
                        if startup:
                            results["new_companies"] += 1
                            results["companies"].append({
                                "name": startup.name,
                                "batch": company_data.get("batch"),
                                "sector": startup.sector,
                                "id": str(startup.id)
                            })
                    
                except Exception as e:
                    logger.error(f"Error processing recent company {company_data.get('name')}: {str(e)}")
                    results["errors"] += 1
            
            return results
            
        except Exception as e:
            logger.error(f"Recent YC scraping failed: {str(e)}")
            raise
    
    async def _find_existing_company(self, name: str) -> Optional[Startup]:
        """Find existing company by name."""
        # Simple name matching for now
        # In production, would use fuzzy matching or additional identifiers
        all_startups = await self.startup_repo.find_all()
        for startup in all_startups:
            if startup.name.lower() == name.lower():
                return startup
        return None
    
    async def _create_startup_from_scraped(self, data: Dict[str, Any]) -> Optional[Startup]:
        """Create startup from scraped data."""
        try:
            startup = Startup(
                name=data["name"],
                sector=data["sector"],
                stage=data["stage"],
                description=data.get("description", ""),
                website=data.get("website", ""),
                team_size=data.get("team_size", 0),
                monthly_revenue=0  # Not available from YC data
            )
            
            # Add additional metadata
            startup.metadata = {
                "yc_batch": data.get("batch"),
                "yc_id": data.get("yc_id"),
                "yc_profile_url": data.get("yc_profile_url"),
                "tags": data.get("tags", []),
                "location": data.get("all_locations"),
                "is_hiring": data.get("is_hiring", False),
                "top_company": data.get("top_company", False),
                "founded": data.get("founded"),
                "scraped_at": data.get("discovered_at"),
                "source": data.get("source", "yc_api")
            }
            
            return await self.startup_repo.create(startup)
            
        except Exception as e:
            logger.error(f"Failed to create startup from scraped data: {str(e)}")
            return None
    
    def _should_update_company(self, existing: Startup, new_data: Dict[str, Any]) -> bool:
        """Check if company data should be updated."""
        # Check if key fields have changed
        if existing.description != new_data.get("description", existing.description):
            return True
        if existing.team_size != new_data.get("team_size", existing.team_size):
            return True
        if existing.website != new_data.get("website", existing.website):
            return True
        
        # Check metadata changes
        existing_metadata = getattr(existing, 'metadata', {})
        if existing_metadata.get("is_hiring") != new_data.get("is_hiring"):
            return True
        
        return False
    
    async def _update_company(self, existing: Startup, new_data: Dict[str, Any]) -> Startup:
        """Update existing company with new data."""
        # Update fields
        existing.description = new_data.get("description", existing.description)
        existing.team_size = new_data.get("team_size", existing.team_size)
        existing.website = new_data.get("website", existing.website)
        
        # Update metadata
        if not hasattr(existing, 'metadata'):
            existing.metadata = {}
        
        existing.metadata.update({
            "last_scraped": datetime.utcnow().isoformat(),
            "is_hiring": new_data.get("is_hiring", False),
            "tags": new_data.get("tags", [])
        })
        
        return await self.startup_repo.update(existing)
    
    async def get_scraping_stats(self) -> Dict[str, Any]:
        """Get statistics about scraping activity."""
        all_startups = await self.startup_repo.find_all()
        
        # Count by source
        sources = {}
        yc_batches = {}
        scraped_dates = []
        
        for startup in all_startups:
            metadata = getattr(startup, 'metadata', {})
            
            # Count by source
            source = metadata.get('source', 'unknown')
            sources[source] = sources.get(source, 0) + 1
            
            # Count YC batches
            if batch := metadata.get('yc_batch'):
                yc_batches[batch] = yc_batches.get(batch, 0) + 1
            
            # Collect scrape dates
            if scraped_at := metadata.get('scraped_at'):
                scraped_dates.append(scraped_at)
        
        # Calculate recent activity
        recent_scraped = 0
        if scraped_dates:
            week_ago = datetime.utcnow() - timedelta(days=7)
            for date_str in scraped_dates:
                try:
                    scraped_dt = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                    if scraped_dt > week_ago:
                        recent_scraped += 1
                except:
                    pass
        
        return {
            "total_companies": len(all_startups),
            "by_source": sources,
            "yc_batches": yc_batches,
            "batch_count": len(yc_batches),
            "recent_scraped_7d": recent_scraped,
            "last_scrape": max(scraped_dates) if scraped_dates else None
        }