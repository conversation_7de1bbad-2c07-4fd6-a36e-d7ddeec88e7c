"""Service for importing connections from various sources."""

from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from uuid import UUID
import csv
import json
import logging
from io import StringIO
from dataclasses import dataclass
from enum import Enum

from src.core.models.connection import Connection, ConnectionId, RelationshipType, ConnectionStrength
from src.core.models.user import User
from src.core.services.warm_intro_service import WarmIntroService
from src.core.services.linkedin_enrichment_service import LinkedInEnrichmentService

logger = logging.getLogger(__name__)


class ImportSource(Enum):
    """Supported import sources."""
    CSV = "csv"
    LINKEDIN = "linkedin"
    GOOGLE_CONTACTS = "google_contacts"
    CRM = "crm"
    MANUAL = "manual"


@dataclass
class ImportedConnection:
    """Represents a connection to be imported."""
    name: str
    email: Optional[str] = None
    linkedin_url: Optional[str] = None
    company: Optional[str] = None
    title: Optional[str] = None
    relationship_type: str = "professional"
    notes: Optional[str] = None
    trust_score: float = 0.5
    tags: List[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []


class ConnectionImportService:
    """Service for importing and managing connections."""
    
    def __init__(
        self,
        warm_intro_service: WarmIntroService,
        linkedin_service: LinkedInEnrichmentService,
        user_repo,
        connection_repo
    ):
        """Initialize with required services."""
        self.warm_intro_service = warm_intro_service
        self.linkedin_service = linkedin_service
        self.user_repo = user_repo
        self.connection_repo = connection_repo
    
    async def import_from_csv(
        self,
        user_id: UUID,
        csv_content: str,
        create_missing_users: bool = True
    ) -> Dict[str, Any]:
        """
        Import connections from CSV file.
        
        Expected CSV columns:
        - name (required)
        - email (optional)
        - linkedin_url (optional)
        - company (optional)
        - title (optional)
        - relationship_type (optional)
        - notes (optional)
        - trust_score (optional, 0-1)
        - tags (optional, comma-separated)
        """
        results = {
            "total": 0,
            "imported": 0,
            "updated": 0,
            "failed": 0,
            "errors": []
        }
        
        try:
            # Parse CSV
            csv_file = StringIO(csv_content)
            reader = csv.DictReader(csv_file)
            
            connections = []
            for row_num, row in enumerate(reader, start=2):  # Start at 2 (header is 1)
                try:
                    connection = self._parse_csv_row(row)
                    connections.append(connection)
                    results["total"] += 1
                except Exception as e:
                    results["failed"] += 1
                    results["errors"].append(f"Row {row_num}: {str(e)}")
            
            # Import connections
            import_results = await self._import_connections(
                user_id,
                connections,
                create_missing_users
            )
            
            results["imported"] = import_results["imported"]
            results["updated"] = import_results["updated"]
            results["failed"] += import_results["failed"]
            results["errors"].extend(import_results["errors"])
            
            return results
            
        except Exception as e:
            logger.error(f"CSV import error: {str(e)}")
            results["errors"].append(f"CSV parsing error: {str(e)}")
            return results
    
    async def import_from_linkedin(
        self,
        user_id: UUID,
        linkedin_connections: List[Dict[str, Any]],
        create_missing_users: bool = True
    ) -> Dict[str, Any]:
        """
        Import connections from LinkedIn export.
        
        LinkedIn connections format:
        {
            "First Name": "John",
            "Last Name": "Doe",
            "Email Address": "<EMAIL>",
            "Company": "Example Corp",
            "Position": "CEO",
            "Connected On": "2023-01-15"
        }
        """
        results = {
            "total": len(linkedin_connections),
            "imported": 0,
            "updated": 0,
            "failed": 0,
            "enriched": 0,
            "errors": []
        }
        
        connections = []
        for conn_data in linkedin_connections:
            try:
                # Parse LinkedIn data
                connection = ImportedConnection(
                    name=f"{conn_data.get('First Name', '')} {conn_data.get('Last Name', '')}".strip(),
                    email=conn_data.get("Email Address"),
                    company=conn_data.get("Company"),
                    title=conn_data.get("Position"),
                    relationship_type="professional",
                    notes=f"Connected on LinkedIn: {conn_data.get('Connected On', 'Unknown')}",
                    trust_score=0.6,  # Default trust for LinkedIn connections
                    tags=["linkedin", "imported"]
                )
                
                # Try to enrich with LinkedIn profile URL
                if connection.email:
                    profile = await self.linkedin_service.enrich_profile_from_email(connection.email)
                    if profile:
                        connection.linkedin_url = profile.linkedin_url
                        results["enriched"] += 1
                
                connections.append(connection)
                
            except Exception as e:
                results["failed"] += 1
                results["errors"].append(f"Error parsing connection {conn_data}: {str(e)}")
        
        # Import connections
        import_results = await self._import_connections(
            user_id,
            connections,
            create_missing_users
        )
        
        results["imported"] = import_results["imported"]
        results["updated"] = import_results["updated"]
        results["failed"] += import_results["failed"]
        results["errors"].extend(import_results["errors"])
        
        return results
    
    async def import_single_connection(
        self,
        user_id: UUID,
        connection_data: Dict[str, Any],
        create_if_missing: bool = True
    ) -> Dict[str, Any]:
        """Import a single connection."""
        try:
            # Validate required fields
            if not connection_data.get("name"):
                return {
                    "success": False,
                    "error": "Name is required"
                }
            
            # Create ImportedConnection
            connection = ImportedConnection(
                name=connection_data["name"],
                email=connection_data.get("email"),
                linkedin_url=connection_data.get("linkedin_url"),
                company=connection_data.get("company"),
                title=connection_data.get("title"),
                relationship_type=connection_data.get("relationship_type", "professional"),
                notes=connection_data.get("notes"),
                trust_score=connection_data.get("trust_score", 0.5),
                tags=connection_data.get("tags", [])
            )
            
            # Import the connection
            results = await self._import_connections(
                user_id,
                [connection],
                create_if_missing
            )
            
            if results["imported"] == 1:
                return {
                    "success": True,
                    "connection_id": results["connection_ids"][0],
                    "action": "created"
                }
            elif results["updated"] == 1:
                return {
                    "success": True,
                    "connection_id": results["connection_ids"][0],
                    "action": "updated"
                }
            else:
                return {
                    "success": False,
                    "error": results["errors"][0] if results["errors"] else "Import failed"
                }
                
        except Exception as e:
            logger.error(f"Single connection import error: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def bulk_categorize_connections(
        self,
        user_id: UUID,
        categorizations: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Bulk categorize existing connections.
        
        Format:
        [
            {
                "connection_id": "uuid",
                "relationship_type": "investor|advisor|friend|colleague",
                "trust_score": 0.8,
                "tags": ["tag1", "tag2"]
            }
        ]
        """
        results = {
            "total": len(categorizations),
            "updated": 0,
            "failed": 0,
            "errors": []
        }
        
        for cat in categorizations:
            try:
                connection_id = UUID(cat["connection_id"])
                
                # Get existing connection
                connection = await self.connection_repo.get_connection_by_id(connection_id)
                
                if not connection:
                    results["failed"] += 1
                    results["errors"].append(f"Connection {connection_id} not found")
                    continue
                
                # Verify user owns this connection
                if connection.user_a_id != user_id and connection.user_b_id != user_id:
                    results["failed"] += 1
                    results["errors"].append(f"Not authorized to update connection {connection_id}")
                    continue
                
                # Update connection
                if "relationship_type" in cat:
                    connection.relationship_type = RelationshipType(cat["relationship_type"])
                
                if "trust_score" in cat:
                    connection.metrics.trust_score = cat["trust_score"]
                    connection.strength = connection.metrics.calculate_strength()
                
                if "tags" in cat:
                    if not hasattr(connection, 'tags'):
                        connection.tags = []
                    connection.tags = cat["tags"]
                
                # Save updates
                await self.connection_repo.update_connection(connection)
                results["updated"] += 1
                
            except Exception as e:
                results["failed"] += 1
                results["errors"].append(f"Error updating connection: {str(e)}")
        
        return results
    
    async def export_connections(
        self,
        user_id: UUID,
        format: str = "csv"
    ) -> Tuple[str, str]:
        """
        Export user's connections.
        
        Returns: (content, content_type)
        """
        # Get all user connections
        connections = await self.connection_repo.get_user_connections(user_id)
        
        if format == "csv":
            return self._export_to_csv(connections, user_id)
        elif format == "json":
            return self._export_to_json(connections, user_id)
        else:
            raise ValueError(f"Unsupported export format: {format}")
    
    # Private methods
    
    def _parse_csv_row(self, row: Dict[str, str]) -> ImportedConnection:
        """Parse a CSV row into ImportedConnection."""
        # Required field
        name = row.get("name", "").strip()
        if not name:
            raise ValueError("Name is required")
        
        # Parse trust score
        trust_score = 0.5
        if row.get("trust_score"):
            try:
                trust_score = float(row["trust_score"])
                trust_score = max(0.0, min(1.0, trust_score))
            except ValueError:
                pass
        
        # Parse tags
        tags = []
        if row.get("tags"):
            tags = [tag.strip() for tag in row["tags"].split(",") if tag.strip()]
        
        return ImportedConnection(
            name=name,
            email=row.get("email", "").strip() or None,
            linkedin_url=row.get("linkedin_url", "").strip() or None,
            company=row.get("company", "").strip() or None,
            title=row.get("title", "").strip() or None,
            relationship_type=row.get("relationship_type", "professional").strip(),
            notes=row.get("notes", "").strip() or None,
            trust_score=trust_score,
            tags=tags
        )
    
    async def _import_connections(
        self,
        user_id: UUID,
        connections: List[ImportedConnection],
        create_missing_users: bool
    ) -> Dict[str, Any]:
        """Import a list of connections."""
        results = {
            "imported": 0,
            "updated": 0,
            "failed": 0,
            "errors": [],
            "connection_ids": []
        }
        
        for conn in connections:
            try:
                # Find or create the other user
                other_user = await self._find_or_create_user(
                    conn,
                    create_missing_users
                )
                
                if not other_user:
                    results["failed"] += 1
                    results["errors"].append(f"Could not find/create user for {conn.name}")
                    continue
                
                # Check if connection already exists
                existing = await self.connection_repo.get_connection(user_id, other_user.id)
                
                if existing:
                    # Update existing connection
                    if hasattr(existing.metrics, 'trust_score'):
                        existing.metrics.trust_score = conn.trust_score
                    if conn.notes and not existing.notes:
                        existing.notes = conn.notes
                    
                    await self.connection_repo.update_connection(existing)
                    results["updated"] += 1
                    results["connection_ids"].append(str(existing.id))
                else:
                    # Create new connection
                    relationship_type = self._map_relationship_type(conn.relationship_type)
                    
                    connection = await self.warm_intro_service.create_connection(
                        user_a_id=user_id,
                        user_b_id=other_user.id,
                        relationship_type=relationship_type,
                        notes=conn.notes,
                        trust_score=conn.trust_score
                    )
                    
                    results["imported"] += 1
                    results["connection_ids"].append(str(connection.id))
                
            except Exception as e:
                results["failed"] += 1
                results["errors"].append(f"Error importing {conn.name}: {str(e)}")
        
        return results
    
    async def _find_or_create_user(
        self,
        connection: ImportedConnection,
        create_if_missing: bool
    ) -> Optional[User]:
        """Find existing user or create new one."""
        # Try to find by email first
        if connection.email:
            user = await self.user_repo.get_by_email(connection.email)
            if user:
                return user
        
        # Try to find by name
        users = await self.user_repo.search_by_name(connection.name)
        if users:
            # Return best match (exact name match)
            for user in users:
                if user.full_name == connection.name:
                    return user
            # Return first result if no exact match
            return users[0]
        
        # Create new user if allowed
        if create_if_missing:
            user = User(
                email=connection.email or f"{connection.name.lower().replace(' ', '.')}@imported.com",
                full_name=connection.name,
                username=connection.name.lower().replace(' ', '_'),
                company=connection.company,
                title=connection.title,
                linkedin_url=connection.linkedin_url,
                is_imported=True
            )
            
            return await self.user_repo.create(user)
        
        return None
    
    def _map_relationship_type(self, type_str: str) -> RelationshipType:
        """Map string to RelationshipType enum."""
        mapping = {
            "professional": RelationshipType.PROFESSIONAL,
            "investor": RelationshipType.INVESTOR,
            "advisor": RelationshipType.ADVISOR,
            "friend": RelationshipType.FRIEND,
            "colleague": RelationshipType.COLLEAGUE
        }
        
        return mapping.get(type_str.lower(), RelationshipType.PROFESSIONAL)
    
    def _export_to_csv(self, connections: List[Connection], user_id: UUID) -> Tuple[str, str]:
        """Export connections to CSV format."""
        output = StringIO()
        writer = csv.writer(output)
        
        # Write header
        writer.writerow([
            "name",
            "email",
            "linkedin_url",
            "company",
            "title",
            "relationship_type",
            "trust_score",
            "connection_strength",
            "notes",
            "connected_since"
        ])
        
        # Write connections
        for conn in connections:
            # Get the other user
            other_user_id = conn.get_other_user(user_id)
            other_user = self.user_repo.get(other_user_id)  # This would need to be async in real impl
            
            if other_user:
                writer.writerow([
                    other_user.full_name or other_user.username,
                    other_user.email,
                    getattr(other_user, 'linkedin_url', ''),
                    getattr(other_user, 'company', ''),
                    getattr(other_user, 'title', ''),
                    conn.relationship_type.value,
                    conn.metrics.trust_score,
                    conn.strength.value,
                    conn.notes or '',
                    conn.created_at.isoformat()
                ])
        
        content = output.getvalue()
        return content, "text/csv"
    
    def _export_to_json(self, connections: List[Connection], user_id: UUID) -> Tuple[str, str]:
        """Export connections to JSON format."""
        export_data = {
            "exported_at": datetime.utcnow().isoformat(),
            "user_id": str(user_id),
            "total_connections": len(connections),
            "connections": []
        }
        
        for conn in connections:
            # Get the other user
            other_user_id = conn.get_other_user(user_id)
            other_user = self.user_repo.get(other_user_id)  # This would need to be async in real impl
            
            if other_user:
                export_data["connections"].append({
                    "connection_id": str(conn.id),
                    "user": {
                        "id": str(other_user.id),
                        "name": other_user.full_name or other_user.username,
                        "email": other_user.email,
                        "linkedin_url": getattr(other_user, 'linkedin_url', None),
                        "company": getattr(other_user, 'company', None),
                        "title": getattr(other_user, 'title', None)
                    },
                    "relationship": {
                        "type": conn.relationship_type.value,
                        "strength": conn.strength.value,
                        "trust_score": conn.metrics.trust_score,
                        "notes": conn.notes
                    },
                    "metrics": {
                        "interaction_frequency": conn.metrics.interaction_frequency,
                        "last_interaction_days": conn.metrics.last_interaction_days,
                        "mutual_connections": conn.metrics.mutual_connections_count
                    },
                    "created_at": conn.created_at.isoformat(),
                    "updated_at": conn.updated_at.isoformat()
                })
        
        content = json.dumps(export_data, indent=2)
        return content, "application/json"