"""Service for enriching profiles with LinkedIn data."""

from typing import Dict, Any, Optional, List
from datetime import datetime
import logging
import re
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class LinkedInProfile:
    """Structured LinkedIn profile data."""
    linkedin_url: str
    full_name: str
    headline: Optional[str] = None
    summary: Optional[str] = None
    location: Optional[str] = None
    current_company: Optional[str] = None
    current_title: Optional[str] = None
    experience_years: Optional[int] = None
    education: List[Dict[str, str]] = None
    skills: List[str] = None
    connections: Optional[int] = None
    
    def __post_init__(self):
        if self.education is None:
            self.education = []
        if self.skills is None:
            self.skills = []


class LinkedInEnrichmentService:
    """
    Service for enriching user profiles with LinkedIn data.
    
    Note: This is a mock implementation. Real LinkedIn scraping requires:
    1. LinkedIn API access (limited and requires approval)
    2. Third-party services like Proxycurl, ScrapingBee, or Phantombuster
    3. Compliance with LinkedIn's Terms of Service
    """
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize with optional API key for third-party service.
        
        In production, this would use services like:
        - Proxycurl API
        - People Data Labs
        - Clearbit
        """
        self.api_key = api_key
        logger.info("LinkedIn enrichment service initialized")
    
    async def enrich_profile_from_url(self, linkedin_url: str) -> Optional[LinkedInProfile]:
        """
        Enrich profile data from LinkedIn URL.
        
        In production, this would call a third-party API.
        """
        if not self._validate_linkedin_url(linkedin_url):
            logger.error(f"Invalid LinkedIn URL: {linkedin_url}")
            return None
        
        # Extract username from URL
        username = self._extract_username(linkedin_url)
        
        # In production, make API call here
        # For now, return mock data based on username patterns
        return self._get_mock_profile(username, linkedin_url)
    
    async def enrich_profile_from_email(self, email: str) -> Optional[LinkedInProfile]:
        """
        Find and enrich LinkedIn profile from email.
        
        Services like Clearbit or People Data Labs can do email->LinkedIn lookups.
        """
        # Extract name from email for mock data
        username = email.split('@')[0]
        
        # In production, use email lookup service
        mock_url = f"https://linkedin.com/in/{username}"
        return self._get_mock_profile(username, mock_url)
    
    async def enrich_profile_from_name_company(
        self,
        full_name: str,
        company: str
    ) -> Optional[LinkedInProfile]:
        """
        Find LinkedIn profile from name and company.
        
        This is useful for enriching VC partner or founder profiles.
        """
        # Create search-friendly username
        username = full_name.lower().replace(' ', '-')
        mock_url = f"https://linkedin.com/in/{username}"
        
        profile = self._get_mock_profile(username, mock_url)
        if profile:
            profile.current_company = company
        
        return profile
    
    async def extract_professional_summary(self, profile: LinkedInProfile) -> Dict[str, Any]:
        """
        Extract key professional insights from LinkedIn profile.
        """
        insights = {
            "seniority_level": self._determine_seniority(profile),
            "functional_area": self._determine_functional_area(profile),
            "industry_expertise": self._extract_industries(profile),
            "key_skills": profile.skills[:10] if profile.skills else [],
            "education_level": self._determine_education_level(profile),
            "is_founder": self._is_founder(profile),
            "is_investor": self._is_investor(profile),
            "years_experience": profile.experience_years,
            "profile_strength": self._calculate_profile_strength(profile)
        }
        
        return insights
    
    async def find_mutual_connections(
        self,
        profile1_url: str,
        profile2_url: str
    ) -> List[Dict[str, str]]:
        """
        Find mutual connections between two LinkedIn profiles.
        
        This requires LinkedIn API access or specialized scraping.
        """
        # Mock implementation
        return [
            {
                "name": "John Smith",
                "title": "CEO at TechCorp",
                "linkedin_url": "https://linkedin.com/in/johnsmith"
            },
            {
                "name": "Jane Doe", 
                "title": "Partner at VC Fund",
                "linkedin_url": "https://linkedin.com/in/janedoe"
            }
        ]
    
    def _validate_linkedin_url(self, url: str) -> bool:
        """Validate LinkedIn URL format."""
        pattern = r'^https?://(www\.)?linkedin\.com/in/[\w\-]+/?$'
        return bool(re.match(pattern, url))
    
    def _extract_username(self, url: str) -> str:
        """Extract username from LinkedIn URL."""
        match = re.search(r'/in/([\w\-]+)', url)
        return match.group(1) if match else ""
    
    def _get_mock_profile(self, username: str, url: str) -> LinkedInProfile:
        """
        Generate mock LinkedIn profile data.
        
        In production, this would be replaced with actual API calls.
        """
        # Generate different profiles based on username patterns
        if "founder" in username or "ceo" in username:
            return LinkedInProfile(
                linkedin_url=url,
                full_name=username.replace('-', ' ').title(),
                headline="Founder & CEO | Building the future of AI",
                summary="Serial entrepreneur with 10+ years building tech companies. Previously founded 2 successful startups with exits.",
                location="San Francisco Bay Area",
                current_company="AI Startup Inc",
                current_title="Founder & CEO",
                experience_years=12,
                education=[
                    {"school": "Stanford University", "degree": "BS Computer Science"},
                    {"school": "Stanford GSB", "degree": "MBA"}
                ],
                skills=["Entrepreneurship", "AI/ML", "Product Management", "Fundraising", "Leadership"],
                connections=500
            )
        
        elif "vc" in username or "partner" in username or "investor" in username:
            return LinkedInProfile(
                linkedin_url=url,
                full_name=username.replace('-', ' ').title(),
                headline="Partner at Top Tier Ventures | Early Stage Investor",
                summary="Investing in AI, B2B SaaS, and developer tools. Former operator turned investor.",
                location="Menlo Park, CA",
                current_company="Top Tier Ventures",
                current_title="Partner",
                experience_years=8,
                education=[
                    {"school": "MIT", "degree": "BS Computer Science"},
                    {"school": "Harvard Business School", "degree": "MBA"}
                ],
                skills=["Venture Capital", "Startup Mentoring", "SaaS", "AI/ML", "Due Diligence"],
                connections=500
            )
        
        else:
            # Generic professional profile
            return LinkedInProfile(
                linkedin_url=url,
                full_name=username.replace('-', ' ').title(),
                headline="Senior Software Engineer | Full Stack Developer",
                summary="Passionate about building scalable systems and great user experiences.",
                location="San Francisco, CA",
                current_company="Tech Company",
                current_title="Senior Engineer",
                experience_years=5,
                education=[
                    {"school": "UC Berkeley", "degree": "BS Computer Science"}
                ],
                skills=["Python", "JavaScript", "React", "Node.js", "AWS"],
                connections=300
            )
    
    def _determine_seniority(self, profile: LinkedInProfile) -> str:
        """Determine seniority level from profile."""
        if not profile.current_title:
            return "Unknown"
        
        title_lower = profile.current_title.lower()
        
        if any(word in title_lower for word in ["ceo", "founder", "president", "cto", "cfo"]):
            return "C-Level"
        elif any(word in title_lower for word in ["vp", "vice president", "partner", "director"]):
            return "Executive"
        elif any(word in title_lower for word in ["senior", "lead", "principal", "manager"]):
            return "Senior"
        elif any(word in title_lower for word in ["junior", "associate", "analyst"]):
            return "Junior"
        else:
            return "Mid-Level"
    
    def _determine_functional_area(self, profile: LinkedInProfile) -> str:
        """Determine functional area from profile."""
        if not profile.current_title:
            return "Unknown"
        
        title_lower = profile.current_title.lower()
        headline_lower = (profile.headline or "").lower()
        
        if any(word in title_lower + headline_lower for word in ["engineer", "developer", "architect"]):
            return "Engineering"
        elif any(word in title_lower + headline_lower for word in ["product", "pm"]):
            return "Product"
        elif any(word in title_lower + headline_lower for word in ["sales", "business development"]):
            return "Sales"
        elif any(word in title_lower + headline_lower for word in ["marketing", "growth"]):
            return "Marketing"
        elif any(word in title_lower + headline_lower for word in ["investor", "partner", "vc"]):
            return "Investment"
        elif any(word in title_lower + headline_lower for word in ["founder", "ceo", "entrepreneur"]):
            return "Leadership"
        else:
            return "Operations"
    
    def _extract_industries(self, profile: LinkedInProfile) -> List[str]:
        """Extract industries from profile content."""
        industries = []
        
        # Combine all text for analysis
        text = f"{profile.headline or ''} {profile.summary or ''} {profile.current_company or ''}"
        text_lower = text.lower()
        
        industry_keywords = {
            "AI/ML": ["ai", "artificial intelligence", "machine learning", "ml", "deep learning"],
            "B2B SaaS": ["saas", "b2b", "enterprise software"],
            "Fintech": ["fintech", "financial", "payments", "banking"],
            "Healthcare": ["healthcare", "health", "medical", "biotech"],
            "E-commerce": ["ecommerce", "e-commerce", "retail", "marketplace"],
            "Crypto": ["crypto", "blockchain", "web3", "defi"],
            "EdTech": ["education", "edtech", "learning"],
            "Climate Tech": ["climate", "sustainability", "cleantech"]
        }
        
        for industry, keywords in industry_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                industries.append(industry)
        
        return industries[:3]  # Top 3 industries
    
    def _determine_education_level(self, profile: LinkedInProfile) -> str:
        """Determine highest education level."""
        if not profile.education:
            return "Unknown"
        
        for edu in profile.education:
            degree = edu.get("degree", "").lower()
            if any(term in degree for term in ["phd", "doctorate"]):
                return "PhD"
            elif any(term in degree for term in ["mba", "master", "ms", "ma"]):
                return "Masters"
            elif any(term in degree for term in ["bs", "ba", "bachelor"]):
                return "Bachelors"
        
        return "Unknown"
    
    def _is_founder(self, profile: LinkedInProfile) -> bool:
        """Check if person is/was a founder."""
        indicators = ["founder", "co-founder", "cofounder", "founded"]
        text = f"{profile.current_title or ''} {profile.headline or ''} {profile.summary or ''}"
        return any(ind in text.lower() for ind in indicators)
    
    def _is_investor(self, profile: LinkedInProfile) -> bool:
        """Check if person is an investor."""
        indicators = ["investor", "partner", "vc", "venture", "angel"]
        text = f"{profile.current_title or ''} {profile.headline or ''} {profile.summary or ''}"
        return any(ind in text.lower() for ind in indicators)
    
    def _calculate_profile_strength(self, profile: LinkedInProfile) -> float:
        """Calculate profile completeness/strength score."""
        score = 0.0
        
        # Check completeness of fields
        if profile.full_name:
            score += 0.1
        if profile.headline:
            score += 0.15
        if profile.summary:
            score += 0.15
        if profile.current_company:
            score += 0.1
        if profile.current_title:
            score += 0.1
        if profile.education:
            score += 0.1
        if profile.skills and len(profile.skills) >= 5:
            score += 0.1
        if profile.experience_years and profile.experience_years > 0:
            score += 0.1
        if profile.connections and profile.connections >= 500:
            score += 0.1
        
        return min(score, 1.0)