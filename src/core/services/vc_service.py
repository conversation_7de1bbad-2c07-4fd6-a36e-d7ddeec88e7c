"""Service layer for VC business logic using ports/adapters pattern.

This service uses the AI port interface for all AI operations,
maintaining clean separation between business logic and external systems.
"""
from typing import List, Optional, Dict, Any
from uuid import UUID, uuid4
from dataclasses import replace

from src.core.models.vc import VC
from src.core.repositories.vc_repository import VCRepository
from src.core.ports.ai_port import AIPort, VCInsights
from src.core.services.validation_service import get_validation_service


class VCService:
    """Service for managing VC-related business logic.
    
    This service coordinates between the repository layer and AI port,
    ensuring proper separation of concerns.
    """
    
    def __init__(
        self,
        repository: VCRepository,
        ai_port: AIPort
    ):
        """Initialize the VC service with required dependencies.
        
        Args:
            repository: Repository for VC data persistence
            ai_port: Port for AI operations
        """
        self.repository = repository
        self.ai_port = ai_port
        self.validation_service = get_validation_service()
    
    async def create_vc(self, vc: VC) -> VC:
        """Create a new VC.
        
        Args:
            vc: VC domain object to create
            
        Returns:
            Created VC with ID assigned
            
        Raises:
            ValueError: If VC validation fails
        """
        # Convert to dict for validation
        vc_data = {
            'firm_name': vc.firm_name,
            'website': vc.website,
            'email': getattr(vc, 'email', None),
            'thesis': vc.thesis,
            'sectors': vc.sectors,
            'stages': vc.stages,
            'min_check_size': vc.min_check_size,
            'max_check_size': vc.max_check_size
        }
        
        # Validate data
        errors = self.validation_service.validate_vc_data(vc_data)
        if errors:
            error_msg = '; '.join([f"{field}: {', '.join(msgs)}" for field, msgs in errors.items()])
            raise ValueError(f"Validation failed: {error_msg}")
        
        # Check for duplicates
        existing_vcs = await self.repository.list()
        existing_data = [
            {
                'firm_name': v.firm_name,
                'website': v.website
            }
            for v in existing_vcs
        ]
        
        duplicates = self.validation_service.find_duplicate_vcs(vc_data, existing_data)
        if duplicates and duplicates[0]['score'] >= 0.9:
            raise ValueError(f"Duplicate VC detected: {duplicates[0]['reason']}")
        
        # Apply any cleaned data back to the VC
        if 'website' in vc_data and vc_data['website'] != vc.website:
            vc = replace(vc, website=vc_data['website'])
        if 'email' in vc_data and hasattr(vc, 'email') and vc_data['email'] != getattr(vc, 'email'):
            vc = replace(vc, email=vc_data['email'])
        
        # Assign ID if not provided
        if vc.id is None:
            vc = replace(vc, id=uuid4())
        
        # Save to repository
        saved_vc = await self.repository.save(vc)
        return saved_vc
    
    async def get_vc(self, vc_id: UUID) -> VC:
        """Retrieve a VC by ID.
        
        Args:
            vc_id: UUID of the VC to retrieve
            
        Returns:
            The VC if found
            
        Raises:
            ValueError: If VC not found
        """
        vc = await self.repository.find_by_id(vc_id)
        if vc is None:
            raise ValueError(f"VC with id {vc_id} not found")
        return vc
    
    async def list_vcs(
        self,
        sector: Optional[str] = None,
        stage: Optional[str] = None,
        active_only: bool = True
    ) -> List[VC]:
        """List VCs with optional filters.
        
        Args:
            sector: Filter by investment sector
            stage: Filter by investment stage
            active_only: Whether to include only active VCs
            
        Returns:
            List of VCs matching the filters
        """
        # Get all VCs first
        if sector:
            vcs = await self.repository.find_by_sector(sector)
        elif stage:
            vcs = await self.repository.find_by_stage(stage)
        else:
            vcs = await self.repository.find_all()
        
        # Apply active filter if needed
        if active_only and hasattr(self.repository, 'find_active'):
            vcs = [vc for vc in vcs if getattr(vc, 'is_active', True)]
        
        return vcs
    
    async def extract_vc_thesis(
        self,
        vc_id: UUID,
        website_content: str,
        force_refresh: bool = False
    ) -> VCInsights:
        """Extract VC investment thesis using AI.
        
        This method now returns domain-specific insights using the AI port.
        
        Args:
            vc_id: UUID of the VC to analyze
            website_content: Scraped website content
            force_refresh: Whether to bypass cache
            
        Returns:
            Domain insights about the VC's investment strategy
            
        Raises:
            ValueError: If VC not found
            AIAnalysisError: If AI analysis fails
        """
        # Get the VC
        vc = await self.get_vc(vc_id)
        
        # Use AI port to analyze
        insights = await self.ai_port.analyze_vc(
            vc,
            website_content=website_content,
            use_cache=not force_refresh
        )
        
        # Optionally update VC with extracted insights
        await self._update_vc_from_insights(vc, insights)
        
        return insights
    
    async def update_vc(
        self,
        vc_id: UUID,
        updates: Dict[str, Any]
    ) -> VC:
        """Update an existing VC.
        
        Args:
            vc_id: UUID of the VC to update
            updates: Dictionary of fields to update
            
        Returns:
            Updated VC
            
        Raises:
            ValueError: If VC not found or validation fails
        """
        # Get existing VC
        vc = await self.get_vc(vc_id)
        
        # Validate updates
        if updates:
            errors = self.validation_service.validate_vc_data(updates)
            if errors:
                error_msg = '; '.join([f"{field}: {', '.join(msgs)}" for field, msgs in errors.items()])
                raise ValueError(f"Validation failed: {error_msg}")
            
            # Check for duplicates if firm_name or website is being updated
            if 'firm_name' in updates or 'website' in updates:
                # Merge existing data with updates for duplicate check
                check_data = {
                    'firm_name': updates.get('firm_name', vc.firm_name),
                    'website': updates.get('website', vc.website)
                }
                
                # Get all VCs except the current one
                all_vcs = await self.repository.list()
                existing_data = [
                    {
                        'firm_name': v.firm_name,
                        'website': v.website
                    }
                    for v in all_vcs if v.id != vc_id
                ]
                
                duplicates = self.validation_service.find_duplicate_vcs(check_data, existing_data)
                if duplicates and duplicates[0]['score'] >= 0.9:
                    raise ValueError(f"Update would create duplicate: {duplicates[0]['reason']}")
        
        # Apply updates
        updated_vc = replace(vc, **updates)
        
        # Save updated VC
        saved_vc = await self.repository.save(updated_vc)
        return saved_vc
    
    async def delete_vc(self, vc_id: UUID) -> bool:
        """Delete a VC.
        
        Args:
            vc_id: UUID of the VC to delete
            
        Returns:
            True if deleted, False if not found
        """
        return await self.repository.delete(vc_id)
    
    async def find_vcs_for_startup(
        self,
        sector: str,
        stage: str,
        funding_amount: Optional[int] = None
    ) -> List[VC]:
        """Find VCs that might be interested in a startup.
        
        This is a business logic method that combines multiple filters.
        
        Args:
            sector: Startup's sector
            stage: Startup's funding stage
            funding_amount: Amount being raised
            
        Returns:
            List of potentially matching VCs
        """
        # Start with sector match
        vcs = await self.repository.find_by_sector(sector)
        
        # Filter by stage
        vcs = [vc for vc in vcs if stage in getattr(vc, 'stages', [])]
        
        # Filter by check size if provided
        if funding_amount:
            vcs = [
                vc for vc in vcs
                if (getattr(vc, 'check_size_min', 0) <= funding_amount <= 
                    getattr(vc, 'check_size_max', float('inf')))
            ]
        
        return vcs
    
    async def _update_vc_from_insights(self, vc: VC, insights: VCInsights):
        """Update VC object with AI-extracted insights.
        
        This is an internal method that updates the VC based on AI insights.
        
        Args:
            vc: VC to update
            insights: AI-extracted insights
        """
        updates = {}
        
        # Update thesis if not already set
        if not getattr(vc, 'thesis', None):
            updates['thesis'] = insights.thesis_summary
        
        # Update sectors if AI found more
        if insights.preferred_sectors:
            current_sectors = set(getattr(vc, 'sectors', []))
            new_sectors = set(insights.preferred_sectors)
            if new_sectors - current_sectors:
                updates['sectors'] = list(current_sectors | new_sectors)
        
        # Update stages similarly
        if insights.preferred_stages:
            current_stages = set(getattr(vc, 'stages', []))
            new_stages = set(insights.preferred_stages)
            if new_stages - current_stages:
                updates['stages'] = list(current_stages | new_stages)
        
        # Update check sizes if not set
        if insights.typical_check_size and not getattr(vc, 'check_size_min', None):
            updates['check_size_min'] = insights.typical_check_size.get('min', 0)
            updates['check_size_max'] = insights.typical_check_size.get('max', 0)
        
        # Apply updates if any
        if updates:
            updated_vc = replace(vc, **updates)
            await self.repository.save(updated_vc)