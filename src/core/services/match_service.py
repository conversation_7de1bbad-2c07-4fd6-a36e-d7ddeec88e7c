"""Service layer for Match business logic using ports/adapters pattern.

This service manages matches between startups and VCs, using the AI port
for generating match rationales and insights.
"""
from typing import List, Optional, Dict, Any
from uuid import UUID, uuid4
from datetime import datetime
from dataclasses import replace

from src.core.models.match import Match
from src.core.models.startup import Startup
from src.core.models.vc import VC
from src.core.repositories.startup_repository import StartupRepository
from src.core.repositories.vc_repository import VCRepository
from src.core.repositories.match_repository import MatchRepository
from src.core.ports.ai_port import AIPort, MatchRationale
from src.core.schemas.match import MatchStatus, MatchType
from src.core.services.matching_engine import MatchingEngine


class MatchService:
    """Service for managing matches between startups and VCs.
    
    This service uses the AI port for intelligent matching insights,
    keeping the business logic decoupled from AI implementation details.
    """
    
    def __init__(
        self,
        startup_repository: StartupRepository,
        vc_repository: VCRepository,
        match_repository: MatchRepository,
        ai_port: AIPort
    ):
        """Initialize the match service.
        
        Args:
            startup_repository: Repository for startup data
            vc_repository: Repository for VC data
            match_repository: Repository for match data
            ai_port: Port for AI operations
        """
        self.startup_repo = startup_repository
        self.vc_repo = vc_repository
        self.match_repo = match_repository
        self.ai_port = ai_port
        self.matching_engine = MatchingEngine()
    
    async def create_match(
        self,
        startup_id: UUID,
        vc_id: UUID,
        match_type: MatchType = MatchType.MANUAL,
        notes: Optional[str] = None,
        use_ai_insights: bool = True
    ) -> Match:
        """Create a new match between a startup and VC.
        
        Args:
            startup_id: UUID of the startup
            vc_id: UUID of the VC
            match_type: Type of match (manual, automated, AI-enhanced)
            notes: Optional notes about the match
            use_ai_insights: Whether to generate AI insights
            
        Returns:
            Created match
            
        Raises:
            ValueError: If startup/VC not found or match already exists
        """
        # Check if match already exists
        if await self.match_repo.exists(startup_id, vc_id):
            raise ValueError("Match already exists")
        
        # Get startup and VC
        startup = await self.startup_repo.find_by_id(startup_id)
        if not startup:
            raise ValueError(f"Startup {startup_id} not found")
        
        vc = await self.vc_repo.find_by_id(vc_id)
        if not vc:
            raise ValueError(f"VC {vc_id} not found")
        
        # Generate match rationale if requested
        if use_ai_insights and match_type == MatchType.AI_ENHANCED:
            rationale = await self.ai_port.generate_match_rationale(
                startup, vc
            )
            score = rationale.compatibility_score
            reasons = rationale.key_alignments
        else:
            # Use basic matching engine
            match_result = self.matching_engine.calculate_match(startup, vc)
            score = match_result.score
            reasons = match_result.reasons
        
        # Create match
        match = Match(
            startup=startup,
            vc=vc,
            score=score,
            reasons=reasons,
            id=uuid4(),
            status=MatchStatus.PENDING,
            match_type=match_type,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
            notes=notes
        )
        
        # Store match using repository
        created_match = await self.match_repo.create(match)
        return created_match
    
    async def list_matches(
        self,
        startup_id: Optional[UUID] = None,
        vc_id: Optional[UUID] = None,
        status: Optional[MatchStatus] = None,
        min_score: Optional[float] = None
    ) -> List[Match]:
        """List matches with optional filters.
        
        Args:
            startup_id: Filter by startup
            vc_id: Filter by VC
            status: Filter by status
            min_score: Minimum score filter
            
        Returns:
            List of matches matching filters
        """
        # For now, return empty list - this method needs refactoring
        matches = []
        
        # Apply filters
        if startup_id:
            matches = [m for m in matches if m.startup.id == startup_id]
        if vc_id:
            matches = [m for m in matches if m.vc.id == vc_id]
        if status:
            matches = [m for m in matches if m.status == status]
        if min_score is not None:
            matches = [m for m in matches if m.score >= min_score]
        
        # Sort by score descending
        matches.sort(key=lambda m: m.score, reverse=True)
        
        return matches
    
    async def get_match(self, match_id: UUID) -> Match:
        """Get a specific match by ID.
        
        Args:
            match_id: UUID of the match
            
        Returns:
            The match
            
        Raises:
            ValueError: If match not found
        """
        match = await self.match_repo.get(match_id)
        if not match:
            raise ValueError(f"Match {match_id} not found")
        return match
    
    async def update_match(
        self,
        match_id: UUID,
        updates: Dict[str, Any]
    ) -> Match:
        """Update a match.
        
        Args:
            match_id: UUID of the match to update
            updates: Fields to update
            
        Returns:
            Updated match
            
        Raises:
            ValueError: If match not found
        """
        match = await self.get_match(match_id)
        
        # Apply updates
        for key, value in updates.items():
            if hasattr(match, key):
                setattr(match, key, value)
        
        # Update timestamp
        match.updated_at = datetime.utcnow()
        
        return match
    
    async def delete_match(self, match_id: UUID) -> bool:
        """Delete a match.
        
        Args:
            match_id: UUID of the match to delete
            
        Returns:
            True if deleted, False if not found
        """
        return await self.match_repo.delete(match_id)
    
    async def batch_match(
        self,
        startup_ids: List[UUID],
        vc_ids: List[UUID],
        match_type: MatchType = MatchType.AI_ENHANCED,
        min_score_threshold: float = 0.5
    ) -> List[Match]:
        """Create matches between multiple startups and VCs.
        
        Uses AI to analyze compatibility and only creates high-quality matches.
        
        Args:
            startup_ids: List of startup IDs
            vc_ids: List of VC IDs
            match_type: Type of match to create
            min_score_threshold: Minimum score to create a match
            
        Returns:
            List of created matches
            
        Raises:
            ValueError: If any startup/VC not found
        """
        # Get all startups and VCs
        startups = []
        for sid in startup_ids:
            startup = await self.startup_repo.find_by_id(sid)
            if not startup:
                raise ValueError(f"Startup {sid} not found")
            startups.append(startup)
        
        vcs = []
        for vid in vc_ids:
            vc = await self.vc_repo.find_by_id(vid)
            if not vc:
                raise ValueError(f"VC {vid} not found")
            vcs.append(vc)
        
        # Generate all combinations and evaluate
        created_matches = []
        
        for startup in startups:
            for vc in vcs:
                # Skip if match already exists
                exists = any(
                    m.startup.id == startup.id and m.vc.id == vc.id
                    for m in self._matches.values()
                )
                if exists:
                    continue
                
                # Generate match rationale with AI
                if match_type == MatchType.AI_ENHANCED:
                    try:
                        rationale = await self.ai_port.generate_match_rationale(
                            startup, vc
                        )
                        score = rationale.compatibility_score
                        reasons = rationale.key_alignments
                    except Exception as e:
                        # Fall back to basic matching
                        match_result = self.matching_engine.calculate_match(startup, vc)
                        score = match_result.score
                        reasons = match_result.reasons
                else:
                    match_result = self.matching_engine.calculate_match(startup, vc)
                    score = match_result.score
                    reasons = match_result.reasons
                
                # Only create match if above threshold
                if score >= min_score_threshold:
                    match = Match(
                        startup=startup,
                        vc=vc,
                        score=score,
                        reasons=reasons,
                        id=uuid4(),
                        status=MatchStatus.PENDING,
                        match_type=match_type,
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow()
                    )
                    self._matches[match.id] = match
                    created_matches.append(match)
        
        return created_matches
    
    async def get_ai_insights_for_match(
        self,
        match_id: UUID
    ) -> MatchRationale:
        """Get detailed AI insights for an existing match.
        
        This can be used to get more detailed insights for matches
        that were created without AI, or to refresh insights.
        
        Args:
            match_id: UUID of the match
            
        Returns:
            AI-generated match rationale
            
        Raises:
            ValueError: If match not found
            AIAnalysisError: If AI analysis fails
        """
        match = await self.get_match(match_id)
        
        # Generate fresh AI insights
        rationale = await self.ai_port.generate_match_rationale(
            match.startup,
            match.vc
        )
        
        # Optionally update match with new insights
        match.score = rationale.compatibility_score
        match.reasons = rationale.key_alignments
        match.updated_at = datetime.utcnow()
        
        return rationale