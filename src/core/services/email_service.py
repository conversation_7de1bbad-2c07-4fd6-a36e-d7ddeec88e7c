"""
Email service for sending transactional and notification emails.
"""
import os
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from jinja2 import Template, Environment, FileSystemLoader
import sendgrid
from sendgrid.helpers.mail import Mail, Email, To, Content
from email_validator import validate_email, EmailNotValidError

logger = logging.getLogger(__name__)


class EmailService:
    """Service for sending emails using SendGrid."""
    
    def __init__(self):
        """Initialize the email service."""
        self.sendgrid_api_key = os.getenv('SENDGRID_API_KEY')
        self.from_email = os.getenv('EMAIL_FROM', '<EMAIL>')
        self.from_name = os.getenv('EMAIL_FROM_NAME', 'VC Matching Platform')
        self.environment = os.getenv('ENVIRONMENT', 'development')
        
        # Initialize SendGrid client if API key is available
        if self.sendgrid_api_key and self.sendgrid_api_key != 'your-sendgrid-api-key':
            self.sg_client = sendgrid.SendGridAPIClient(api_key=self.sendgrid_api_key)
            self.use_sendgrid = True
        else:
            self.sg_client = None
            self.use_sendgrid = False
            logger.warning("SendGrid API key not configured - emails will be logged only")
        
        # Email templates
        self.templates = {
            'match_notification': {
                'subject': 'New Match Alert: {{ match_name }}',
                'html': """
                    <h2>New Match Alert!</h2>
                    <p>Hi {{ recipient_name }},</p>
                    <p>We've found a new match for you:</p>
                    <ul>
                        <li><strong>{{ match_name }}</strong></li>
                        <li>Score: {{ score }}%</li>
                        <li>Key Reasons: {{ reasons }}</li>
                    </ul>
                    <p><a href="{{ app_url }}/matches/{{ match_id }}">View Match Details</a></p>
                    <p>Best regards,<br>The VC Matching Team</p>
                """
            },
            'weekly_digest': {
                'subject': 'Your Weekly VC Matching Digest',
                'html': """
                    <h2>Your Weekly VC Matching Digest</h2>
                    <p>Hi {{ recipient_name }},</p>
                    <p>Here's your weekly summary:</p>
                    <ul>
                        <li>New matches: {{ new_matches }}</li>
                        <li>Updated profiles: {{ updated_profiles }}</li>
                        <li>Top match this week: {{ top_match }}</li>
                    </ul>
                    <p><a href="{{ app_url }}/dashboard">View Dashboard</a></p>
                    <p>Best regards,<br>The VC Matching Team</p>
                """
            },
            'analysis_complete': {
                'subject': 'Your {{ entity_type }} Analysis is Ready',
                'html': """
                    <h2>Analysis Complete</h2>
                    <p>Hi {{ recipient_name }},</p>
                    <p>Your {{ entity_type }} analysis has been completed.</p>
                    <p>Key insights:</p>
                    <ul>
                        {% for insight in insights %}
                        <li>{{ insight }}</li>
                        {% endfor %}
                    </ul>
                    <p><a href="{{ app_url }}/{{ entity_type }}s/{{ entity_id }}">View Full Analysis</a></p>
                    <p>Best regards,<br>The VC Matching Team</p>
                """
            },
            'introduction_request': {
                'subject': 'Introduction Request: {{ requester_name }}',
                'html': """
                    <h2>New Introduction Request</h2>
                    <p>Hi {{ recipient_name }},</p>
                    <p>{{ requester_name }} would like to connect with you.</p>
                    <p><strong>Message:</strong></p>
                    <blockquote>{{ message }}</blockquote>
                    <p><strong>About {{ requester_name }}:</strong></p>
                    <p>{{ requester_description }}</p>
                    <p>
                        <a href="{{ app_url }}/introductions/{{ request_id }}/accept">Accept</a> | 
                        <a href="{{ app_url }}/introductions/{{ request_id }}/decline">Decline</a>
                    </p>
                    <p>Best regards,<br>The VC Matching Team</p>
                """
            },
            'introduction_accepted': {
                'subject': 'Your introduction request was accepted!',
                'html': """
                    <h2>Great News!</h2>
                    <p>Hi {{ recipient_name }},</p>
                    <p>{{ acceptor_name }} has accepted your introduction request.</p>
                    <p><strong>Contact Information:</strong></p>
                    <ul>
                        <li>Email: {{ acceptor_email }}</li>
                        {% if acceptor_phone %}<li>Phone: {{ acceptor_phone }}</li>{% endif %}
                    </ul>
                    <p><a href="{{ app_url }}/introductions/{{ request_id }}">View Details</a></p>
                    <p>Best regards,<br>The VC Matching Team</p>
                """
            }
        }
        
        # Load app URL from environment
        self.app_url = os.getenv('APP_URL', 'http://localhost:3000')
    
    def validate_email_address(self, email: str) -> bool:
        """Validate email address format."""
        try:
            # In development, be more lenient with email validation
            if self.environment == 'development' and '@' in email:
                return True
            validate_email(email)
            return True
        except EmailNotValidError:
            return False
    
    def send_email(
        self,
        to_email: str,
        template_name: str,
        data: Dict[str, Any],
        cc_emails: Optional[List[str]] = None,
        attachments: Optional[List[Dict[str, Any]]] = None
    ) -> Dict[str, Any]:
        """
        Send an email using the specified template.
        
        Args:
            to_email: Recipient email address
            template_name: Name of the template to use
            data: Data to render the template with
            cc_emails: Optional list of CC recipients
            attachments: Optional list of attachments
            
        Returns:
            Dictionary with send results
        """
        try:
            # Validate email
            if not self.validate_email_address(to_email):
                return {
                    'success': False,
                    'error': 'Invalid email address',
                    'email': to_email
                }
            
            # Get template
            if template_name not in self.templates:
                return {
                    'success': False,
                    'error': f'Template {template_name} not found',
                    'email': to_email
                }
            
            template = self.templates[template_name]
            
            # Add default data
            data['app_url'] = self.app_url
            data['current_year'] = datetime.utcnow().year
            
            # Render subject and content
            subject_template = Template(template['subject'])
            subject = subject_template.render(**data)
            
            html_template = Template(template['html'])
            html_content = html_template.render(**data)
            
            # Send email
            if self.use_sendgrid:
                return self._send_via_sendgrid(
                    to_email=to_email,
                    subject=subject,
                    html_content=html_content,
                    cc_emails=cc_emails,
                    attachments=attachments
                )
            else:
                return self._log_email(
                    to_email=to_email,
                    subject=subject,
                    html_content=html_content,
                    cc_emails=cc_emails
                )
                
        except Exception as e:
            logger.error(f"Error sending email to {to_email}: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'email': to_email
            }
    
    def _send_via_sendgrid(
        self,
        to_email: str,
        subject: str,
        html_content: str,
        cc_emails: Optional[List[str]] = None,
        attachments: Optional[List[Dict[str, Any]]] = None
    ) -> Dict[str, Any]:
        """Send email via SendGrid."""
        try:
            # Create message
            message = Mail(
                from_email=Email(self.from_email, self.from_name),
                to_emails=To(to_email),
                subject=subject,
                html_content=Content("text/html", html_content)
            )
            
            # Add CC recipients
            if cc_emails:
                for cc_email in cc_emails:
                    if self.validate_email_address(cc_email):
                        message.add_cc(Email(cc_email))
            
            # Add attachments
            if attachments:
                for attachment in attachments:
                    # Attachment should have 'content', 'filename', and 'type'
                    message.add_attachment(attachment)
            
            # Send
            response = self.sg_client.send(message)
            
            return {
                'success': True,
                'email': to_email,
                'message_id': response.headers.get('X-Message-Id'),
                'status_code': response.status_code
            }
            
        except Exception as e:
            logger.error(f"SendGrid error: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'email': to_email
            }
    
    def _log_email(
        self,
        to_email: str,
        subject: str,
        html_content: str,
        cc_emails: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Log email details when SendGrid is not configured."""
        logger.info(f"Email logged (not sent):")
        logger.info(f"  To: {to_email}")
        if cc_emails:
            logger.info(f"  CC: {', '.join(cc_emails)}")
        logger.info(f"  Subject: {subject}")
        logger.info(f"  Content preview: {html_content[:200]}...")
        
        return {
            'success': True,
            'email': to_email,
            'status': 'logged',
            'environment': self.environment
        }
    
    def send_bulk_emails(
        self,
        template_name: str,
        recipients: List[Dict[str, Any]],
        global_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Send bulk emails to multiple recipients.
        
        Args:
            template_name: Name of the template to use
            recipients: List of recipient dictionaries with 'email' and 'data' keys
            global_data: Data to apply to all recipients
            
        Returns:
            Dictionary with bulk send results
        """
        results = {
            'total': len(recipients),
            'sent': 0,
            'failed': 0,
            'results': []
        }
        
        for recipient in recipients:
            if 'email' not in recipient:
                continue
            
            # Merge global and recipient-specific data
            data = global_data.copy() if global_data else {}
            if 'data' in recipient:
                data.update(recipient['data'])
            
            # Send email
            result = self.send_email(
                to_email=recipient['email'],
                template_name=template_name,
                data=data
            )
            
            results['results'].append(result)
            if result['success']:
                results['sent'] += 1
            else:
                results['failed'] += 1
        
        return results


# Singleton instance
_email_service = None


def get_email_service() -> EmailService:
    """Get or create the email service instance."""
    global _email_service
    if _email_service is None:
        _email_service = EmailService()
    return _email_service