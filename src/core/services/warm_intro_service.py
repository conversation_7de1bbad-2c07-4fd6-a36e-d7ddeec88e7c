"""Service layer for warm introduction functionality."""

from typing import List, Optional, Dict, Any, Tuple
from uuid import UUID
from datetime import datetime
import logging

from src.core.models.connection import (
    Connection,
    ConnectionId,
    ConnectionMetrics,
    ConnectionStrength,
    RelationshipType,
    ConnectionPath,
    IntroductionRequest,
    IntroductionStatus
)
from src.core.models.user import User
from src.core.models.startup import Startup
from src.core.models.vc import VC
from src.core.repositories.connection_repository import ConnectionRepository, IntroductionRepository
from src.core.repositories.user_repository import UserRepository
from src.core.repositories.startup_repository import StartupRepository
from src.core.repositories.vc_repository import VCRepository

logger = logging.getLogger(__name__)


class WarmIntroService:
    """Service for managing warm introductions between users."""
    
    def __init__(
        self,
        connection_repo: ConnectionRepository,
        introduction_repo: IntroductionRepository,
        user_repo: UserRepository,
        startup_repo: StartupRepository,
        vc_repo: VCRepository
    ):
        """Initialize with repositories."""
        self.connection_repo = connection_repo
        self.introduction_repo = introduction_repo
        self.user_repo = user_repo
        self.startup_repo = startup_repo
        self.vc_repo = vc_repo
    
    async def create_connection(
        self,
        user_a_id: UUID,
        user_b_id: UUID,
        relationship_type: RelationshipType,
        notes: Optional[str] = None,
        trust_score: float = 0.5
    ) -> Connection:
        """Create a new connection between two users."""
        # Validate users exist
        user_a = await self.user_repo.get(user_a_id)
        user_b = await self.user_repo.get(user_b_id)
        
        if not user_a or not user_b:
            raise ValueError("One or both users not found")
        
        # Check if connection already exists
        existing = await self.connection_repo.get_connection(user_a_id, user_b_id)
        if existing:
            raise ValueError("Connection already exists between these users")
        
        # Create connection with initial metrics
        metrics = ConnectionMetrics(
            interaction_frequency=1,
            last_interaction_days=0,
            mutual_connections_count=0,
            trust_score=trust_score
        )
        
        connection = Connection(
            id=ConnectionId(),
            user_a_id=user_a_id,
            user_b_id=user_b_id,
            relationship_type=relationship_type,
            strength=metrics.calculate_strength(),
            metrics=metrics,
            notes=notes
        )
        
        # Save to repository
        saved_connection = await self.connection_repo.create_connection(connection)
        
        logger.info(f"Created connection between {user_a_id} and {user_b_id}")
        return saved_connection
    
    async def find_intro_paths_for_match(
        self,
        requester_id: UUID,
        startup_id: Optional[UUID] = None,
        vc_id: Optional[UUID] = None,
        max_depth: int = 3
    ) -> List[Dict[str, Any]]:
        """Find introduction paths for a startup-VC match."""
        if not startup_id and not vc_id:
            raise ValueError("Either startup_id or vc_id must be provided")
        
        # Determine target users
        target_users = []
        
        if startup_id:
            startup = await self.startup_repo.get(startup_id)
            if startup:
                # Get founders/team members associated with startup
                startup_users = await self.user_repo.get_users_by_startup(startup_id)
                target_users.extend(startup_users)
        
        if vc_id:
            vc = await self.vc_repo.get(vc_id)
            if vc:
                # Get partners associated with VC
                vc_users = await self.user_repo.get_users_by_vc(vc_id)
                target_users.extend(vc_users)
        
        # Find paths to each target user
        all_paths = []
        for target_user in target_users:
            paths = await self.connection_repo.find_shortest_paths(
                source_user_id=requester_id,
                target_user_id=target_user.id,
                max_depth=max_depth
            )
            
            for path in paths:
                # Enhance path with user information
                enhanced_path = await self._enhance_path_with_user_info(path)
                all_paths.append({
                    "target_user": {
                        "id": str(target_user.id),
                        "name": target_user.full_name or target_user.username,
                        "email": target_user.email,
                        "role": getattr(target_user, 'role', None)
                    },
                    "path": enhanced_path,
                    "strength_score": path.total_strength_score,
                    "hop_count": path.length,
                    "can_request_intro": path.length <= 3  # Max 3 hops for intro requests
                })
        
        # Sort by strength score and hop count
        all_paths.sort(key=lambda x: (-x["strength_score"], x["hop_count"]))
        
        return all_paths
    
    async def request_introduction(
        self,
        requester_id: UUID,
        target_id: UUID,
        connector_id: UUID,
        message: str
    ) -> IntroductionRequest:
        """Request an introduction through a connector."""
        # Validate all users exist
        requester = await self.user_repo.get(requester_id)
        target = await self.user_repo.get(target_id)
        connector = await self.user_repo.get(connector_id)
        
        if not all([requester, target, connector]):
            raise ValueError("One or more users not found")
        
        # Verify connector can make the introduction
        can_introduce = await self._verify_connector_can_introduce(
            connector_id, requester_id, target_id
        )
        
        if not can_introduce:
            raise ValueError("Connector cannot introduce these users - insufficient connections")
        
        # Create introduction request
        intro_request = IntroductionRequest(
            requester_id=requester_id,
            target_id=target_id,
            connector_id=connector_id,
            message=message
        )
        
        # Save to repository
        saved_request = await self.introduction_repo.create_request(intro_request)
        
        # TODO: Send notification to connector
        logger.info(f"Introduction requested: {requester_id} -> {connector_id} -> {target_id}")
        
        return saved_request
    
    async def get_pending_intro_requests(self, connector_id: UUID) -> List[Dict[str, Any]]:
        """Get pending introduction requests for a connector."""
        requests = await self.introduction_repo.get_pending_requests(connector_id)
        
        # Enhance with user information
        enhanced_requests = []
        for request in requests:
            requester = await self.user_repo.get(request.requester_id)
            target = await self.user_repo.get(request.target_id)
            
            # Get connection strengths
            requester_connection = await self.connection_repo.get_connection(
                connector_id, request.requester_id
            )
            target_connection = await self.connection_repo.get_connection(
                connector_id, request.target_id
            )
            
            enhanced_requests.append({
                "request": {
                    "id": str(request.id),
                    "message": request.message,
                    "created_at": request.created_at.isoformat(),
                    "expires_at": request.expires_at.isoformat() if request.expires_at else None
                },
                "requester": {
                    "id": str(requester.id),
                    "name": requester.full_name or requester.username,
                    "email": requester.email
                },
                "target": {
                    "id": str(target.id),
                    "name": target.full_name or target.username,
                    "email": target.email
                },
                "connections": {
                    "requester_strength": requester_connection.strength.value if requester_connection else None,
                    "target_strength": target_connection.strength.value if target_connection else None
                }
            })
        
        return enhanced_requests
    
    async def respond_to_intro_request(
        self,
        request_id: UUID,
        connector_id: UUID,
        accept: bool,
        notes: Optional[str] = None
    ) -> IntroductionRequest:
        """Accept or decline an introduction request."""
        request = await self.introduction_repo.get_request(request_id)
        
        if not request:
            raise ValueError("Introduction request not found")
        
        if request.connector_id != connector_id:
            raise ValueError("User is not the connector for this request")
        
        if request.status != IntroductionStatus.PENDING:
            raise ValueError(f"Request is not pending (status: {request.status.value})")
        
        # Update request status
        if accept:
            request.accept(notes)
            # TODO: Send notifications to requester and target
            logger.info(f"Introduction accepted: {request_id}")
        else:
            request.decline(notes)
            # TODO: Send notification to requester
            logger.info(f"Introduction declined: {request_id}")
        
        # Save updated request
        updated_request = await self.introduction_repo.update_request(request)
        
        return updated_request
    
    async def get_connection_analytics(self, user_id: UUID) -> Dict[str, Any]:
        """Get analytics about a user's connections."""
        connections = await self.connection_repo.get_user_connections(user_id)
        
        # Calculate statistics
        total_connections = len(connections)
        strength_distribution = {
            "strong": sum(1 for c in connections if c.strength == ConnectionStrength.STRONG),
            "medium": sum(1 for c in connections if c.strength == ConnectionStrength.MEDIUM),
            "weak": sum(1 for c in connections if c.strength == ConnectionStrength.WEAK)
        }
        
        relationship_distribution = {}
        for conn in connections:
            rel_type = conn.relationship_type.value
            relationship_distribution[rel_type] = relationship_distribution.get(rel_type, 0) + 1
        
        # Get introduction statistics
        user_requests = await self.introduction_repo.get_user_requests(user_id)
        intro_stats = {
            "requests_sent": sum(1 for r in user_requests if r.requester_id == user_id),
            "requests_received": sum(1 for r in user_requests if r.target_id == user_id),
            "introductions_made": sum(1 for r in user_requests if r.connector_id == user_id),
            "successful_intros": sum(
                1 for r in user_requests 
                if r.connector_id == user_id and r.status == IntroductionStatus.COMPLETED
            )
        }
        
        return {
            "total_connections": total_connections,
            "strength_distribution": strength_distribution,
            "relationship_distribution": relationship_distribution,
            "introduction_stats": intro_stats,
            "network_reach": await self._calculate_network_reach(user_id),
            "key_connectors": await self._identify_key_connectors(user_id)
        }
    
    async def _enhance_path_with_user_info(self, path: ConnectionPath) -> List[Dict[str, Any]]:
        """Enhance connection path with user information."""
        enhanced_path = []
        
        for i, user_id in enumerate(path.path):
            user = await self.user_repo.get(user_id)
            
            user_info = {
                "id": str(user_id),
                "name": (user.full_name or user.username) if user else "Unknown",
                "email": user.email if user else None,
                "position": i
            }
            
            # Add connection info (except for last user)
            if i < len(path.connections):
                conn = path.connections[i]
                user_info["connection_to_next"] = {
                    "strength": conn.strength.value,
                    "relationship": conn.relationship_type.value
                }
            
            enhanced_path.append(user_info)
        
        return enhanced_path
    
    async def _verify_connector_can_introduce(
        self,
        connector_id: UUID,
        requester_id: UUID,
        target_id: UUID
    ) -> bool:
        """Verify if connector has connections to both users."""
        requester_connection = await self.connection_repo.get_connection(
            connector_id, requester_id
        )
        target_connection = await self.connection_repo.get_connection(
            connector_id, target_id
        )
        
        return bool(requester_connection and target_connection)
    
    async def _calculate_network_reach(self, user_id: UUID, max_depth: int = 2) -> Dict[str, int]:
        """Calculate how many users are reachable at different depths."""
        reach = {"depth_1": 0, "depth_2": 0}
        
        # Direct connections
        direct_connections = await self.connection_repo.get_user_connections(user_id)
        reach["depth_1"] = len(direct_connections)
        
        # Second-degree connections
        second_degree_users = set()
        for conn in direct_connections:
            other_user_id = conn.get_other_user(user_id)
            their_connections = await self.connection_repo.get_user_connections(other_user_id)
            
            for their_conn in their_connections:
                second_degree_user = their_conn.get_other_user(other_user_id)
                if second_degree_user != user_id:
                    second_degree_users.add(second_degree_user)
        
        reach["depth_2"] = len(second_degree_users)
        
        return reach
    
    async def _identify_key_connectors(self, user_id: UUID, limit: int = 5) -> List[Dict[str, Any]]:
        """Identify users who could provide valuable connections."""
        connections = await self.connection_repo.get_user_connections(user_id)
        
        connector_scores = {}
        for conn in connections:
            other_user_id = conn.get_other_user(user_id)
            
            # Score based on their network size and connection strength
            their_connections = await self.connection_repo.get_user_connections(other_user_id)
            network_size = len(their_connections)
            
            strength_multiplier = {
                ConnectionStrength.STRONG: 1.0,
                ConnectionStrength.MEDIUM: 0.7,
                ConnectionStrength.WEAK: 0.4
            }[conn.strength]
            
            score = network_size * strength_multiplier
            connector_scores[other_user_id] = score
        
        # Sort by score and get top connectors
        top_connectors = sorted(
            connector_scores.items(),
            key=lambda x: x[1],
            reverse=True
        )[:limit]
        
        # Enhance with user info
        result = []
        for user_id, score in top_connectors:
            user = await self.user_repo.get(user_id)
            if user:
                result.append({
                    "user": {
                        "id": str(user_id),
                        "name": user.name,
                        "email": user.email
                    },
                    "connector_score": score
                })
        
        return result