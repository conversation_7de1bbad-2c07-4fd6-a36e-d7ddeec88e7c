from typing import List
from src.core.models.startup import Startup
from src.core.models.vc import VC
from src.core.models.match import Match
import uuid

class MatchingEngine:
    def calculate_match(self, startup: Startup, vc: VC) -> Match:
        score = 0.0
        reasons = []
        
        # Stage matching (0-40 points)
        if vc.can_invest_in_stage(startup.stage):
            score += 0.4
            reasons.append("stage match")
        else:
            reasons.append("stage mismatch")
            
        # Sector alignment (0-40 points)
        startup_sectors = startup.extract_sectors()
        if vc.sectors:
            matching_sectors = set(startup_sectors) & set(vc.sectors)
            if matching_sectors:
                sector_score = len(matching_sectors) / max(len(vc.sectors), 1)
                score += 0.4 * sector_score
                reasons.append("sector alignment")
            else:
                reasons.append("no sector match")
        else:
            # If VC has no specific sectors, give partial credit
            score += 0.2
            reasons.append("VC invests across sectors")
            
        # Thesis alignment (0-20 points)
        if vc.thesis and startup.description:
            thesis_keywords = vc.thesis.lower().split()
            description_words = startup.description.lower().split()
            matching_keywords = set(thesis_keywords) & set(description_words)
            if matching_keywords:
                score += 0.2
                reasons.append("thesis alignment")
                
        return Match(
            startup=startup,
            vc=vc,
            score=min(score, 1.0),
            reasons=reasons
        )