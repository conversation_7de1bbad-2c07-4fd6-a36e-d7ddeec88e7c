"""Cached discovery service for improved performance."""

from typing import List, Dict, Any, Optional
from uuid import UUID
import logging
from sqlalchemy.orm import Session
from redis import Redis

from src.core.services.cache_service import CacheService
from src.core.models.startup import Startup
from src.core.models.vc import VC
from src.database.repositories.startup_repository import StartupRepository
from src.database.repositories.vc_repository import VCRepository

logger = logging.getLogger(__name__)


class CachedDiscoveryService:
    """Discovery service with Redis caching for improved performance."""
    
    def __init__(
        self,
        db: Session,
        redis_client: Redis,
        cache_prefix: str = "vc_platform"
    ):
        """Initialize with database session and Redis client."""
        self.db = db
        self.cache = CacheService(redis_client, prefix=cache_prefix)
        self.startup_repo = StartupRepository(db)
        self.vc_repo = VCRepository(db)
    
    async def discover_startups_for_vc(
        self,
        vc_id: UUID,
        limit: int = 20,
        min_score: float = 0.7,
        page: int = 1,
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """
        Discover startups for a VC with caching.
        
        Cache key includes all parameters to ensure correct results.
        """
        # Create cache key from parameters
        filters = {
            "limit": limit,
            "min_score": min_score,
            "page": page
        }
        
        # Try to get from cache if enabled
        if use_cache:
            cached_result = await self.cache.get_vc_discovery(str(vc_id), filters)
            if cached_result:
                logger.info(f"Cache hit for VC discovery: {vc_id}")
                return cached_result
        
        # Get VC data
        vc = await self.vc_repo.get(vc_id)
        if not vc:
            raise ValueError(f"VC {vc_id} not found")
        
        # Perform discovery (expensive operation)
        logger.info(f"Performing discovery for VC {vc_id}")
        result = await self._perform_vc_discovery(vc, limit, min_score, page)
        
        # Cache the result
        if use_cache:
            await self.cache.cache_vc_discovery(str(vc_id), filters, result)
        
        return result
    
    async def discover_vcs_for_startup(
        self,
        startup_id: UUID,
        limit: int = 20,
        min_score: float = 0.7,
        page: int = 1,
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """
        Discover VCs for a startup with caching.
        """
        # Create cache key from parameters
        filters = {
            "limit": limit,
            "min_score": min_score,
            "page": page
        }
        
        # Try to get from cache if enabled
        if use_cache:
            cached_result = await self.cache.get_startup_discovery(str(startup_id), filters)
            if cached_result:
                logger.info(f"Cache hit for startup discovery: {startup_id}")
                return cached_result
        
        # Get startup data
        startup = await self.startup_repo.get(startup_id)
        if not startup:
            raise ValueError(f"Startup {startup_id} not found")
        
        # Perform discovery (expensive operation)
        logger.info(f"Performing discovery for startup {startup_id}")
        result = await self._perform_startup_discovery(startup, limit, min_score, page)
        
        # Cache the result
        if use_cache:
            await self.cache.cache_startup_discovery(str(startup_id), filters, result)
        
        return result
    
    async def search_with_cache(
        self,
        query: str,
        entity_type: str = "all",
        sectors: Optional[List[str]] = None,
        stages: Optional[List[str]] = None,
        limit: int = 20,
        use_cache: bool = True
    ) -> List[Dict[str, Any]]:
        """
        Perform search with caching.
        """
        # Create filters dict for cache key
        filters = {
            "entity_type": entity_type,
            "sectors": sectors or [],
            "stages": stages or [],
            "limit": limit
        }
        
        # Try to get from cache
        if use_cache:
            cached_result = await self.cache.get_discovery_search(query, filters)
            if cached_result:
                logger.info(f"Cache hit for search: {query}")
                return cached_result
        
        # Perform search
        logger.info(f"Performing search: {query}")
        result = await self._perform_search(query, entity_type, sectors, stages, limit)
        
        # Cache the result
        if use_cache:
            await self.cache.cache_discovery_search(query, filters, result)
        
        return result
    
    async def get_matching_score_with_cache(
        self,
        startup_id: UUID,
        vc_id: UUID,
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """
        Get matching score between startup and VC with caching.
        """
        # Try to get from cache
        if use_cache:
            cached_score = await self.cache.get_matching_score(str(startup_id), str(vc_id))
            if cached_score:
                logger.info(f"Cache hit for matching score: {startup_id} <-> {vc_id}")
                return cached_score
        
        # Calculate score
        startup = await self.startup_repo.get(startup_id)
        vc = await self.vc_repo.get(vc_id)
        
        if not startup or not vc:
            raise ValueError("Startup or VC not found")
        
        # Perform matching calculation
        score_data = await self._calculate_matching_score(startup, vc)
        
        # Cache the result
        if use_cache:
            await self.cache.cache_matching_score(str(startup_id), str(vc_id), score_data)
        
        return score_data
    
    async def invalidate_vc_cache(self, vc_id: UUID) -> int:
        """Invalidate all caches related to a VC."""
        return await self.cache.invalidate_entity("vc", str(vc_id))
    
    async def invalidate_startup_cache(self, startup_id: UUID) -> int:
        """Invalidate all caches related to a startup."""
        return await self.cache.invalidate_entity("startup", str(startup_id))
    
    async def get_cache_statistics(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return await self.cache.get_cache_stats()
    
    # Private methods for actual discovery logic
    
    async def _perform_vc_discovery(
        self,
        vc: VC,
        limit: int,
        min_score: float,
        page: int
    ) -> Dict[str, Any]:
        """Perform the actual VC discovery logic."""
        # Extract thesis keywords
        thesis_keywords = self._extract_keywords(vc.thesis) if vc.thesis else []
        
        # Calculate pagination offset
        offset = (page - 1) * limit
        
        # Get pre-filtered startups using database indexes
        candidate_startups = await self.startup_repo.find_for_vc_discovery(
            vc_sectors=vc.sectors or [],
            vc_stages=vc.stages or [],
            min_funding=vc.check_size_min or 0,
            max_funding=vc.check_size_max or float('inf'),
            thesis_keywords=thesis_keywords,
            limit=limit * 3,  # Get 3x limit for scoring flexibility
            offset=0
        )
        
        # Score the candidates
        matches = []
        for startup in candidate_startups:
            score, reasons = self._calculate_vc_startup_match(vc, startup)
            
            if score >= min_score:
                matches.append({
                    "startup": {
                        "id": str(startup.id),
                        "name": startup.name,
                        "sector": startup.sector,
                        "stage": startup.stage,
                        "description": startup.description,
                        "website": startup.website,
                        "team_size": startup.team_size,
                        "monthly_revenue": startup.monthly_revenue
                    },
                    "match_score": round(score, 2),
                    "match_reasons": reasons,
                    "investment_thesis_alignment": self._analyze_thesis_alignment(vc.thesis, startup)
                })
        
        # Sort by score and paginate
        matches.sort(key=lambda x: x["match_score"], reverse=True)
        paginated_matches = matches[offset:offset + limit]
        
        # Get total count
        total_count = await self.startup_repo.count_for_vc_discovery(
            vc_sectors=vc.sectors or [],
            vc_stages=vc.stages or [],
            min_funding=vc.check_size_min or 0,
            max_funding=vc.check_size_max or float('inf')
        )
        
        return {
            "vc": {
                "id": str(vc.id),
                "name": vc.name,
                "thesis": vc.thesis,
                "sectors": vc.sectors,
                "stages": vc.stages
            },
            "matches": paginated_matches,
            "pagination": {
                "page": page,
                "limit": limit,
                "total": len(matches),
                "filtered_total": total_count,
                "pages": (len(matches) + limit - 1) // limit
            },
            "filters_applied": {
                "sectors": vc.sectors,
                "stages": vc.stages,
                "min_score": min_score
            }
        }
    
    async def _perform_startup_discovery(
        self,
        startup: Startup,
        limit: int,
        min_score: float,
        page: int
    ) -> Dict[str, Any]:
        """Perform the actual startup discovery logic."""
        # Similar to VC discovery but reversed
        offset = (page - 1) * limit
        
        # Get candidate VCs
        candidate_vcs = await self.vc_repo.find_for_startup_discovery(
            startup_sector=startup.sector,
            startup_stage=startup.stage,
            funding_amount=startup.monthly_revenue * 12,  # Annualized
            limit=limit * 3,
            offset=0
        )
        
        # Score the candidates
        matches = []
        for vc in candidate_vcs:
            score, reasons = self._calculate_startup_vc_match(startup, vc)
            
            if score >= min_score:
                matches.append({
                    "vc": {
                        "id": str(vc.id),
                        "name": vc.name,
                        "thesis": vc.thesis,
                        "sectors": vc.sectors,
                        "stages": vc.stages,
                        "portfolio_size": getattr(vc, 'portfolio_companies', [])
                    },
                    "match_score": round(score, 2),
                    "match_reasons": reasons
                })
        
        # Sort and paginate
        matches.sort(key=lambda x: x["match_score"], reverse=True)
        paginated_matches = matches[offset:offset + limit]
        
        return {
            "startup": {
                "id": str(startup.id),
                "name": startup.name,
                "sector": startup.sector,
                "stage": startup.stage,
                "description": startup.description
            },
            "matches": paginated_matches,
            "pagination": {
                "page": page,
                "limit": limit,
                "total": len(matches),
                "pages": (len(matches) + limit - 1) // limit
            }
        }
    
    async def _perform_search(
        self,
        query: str,
        entity_type: str,
        sectors: Optional[List[str]],
        stages: Optional[List[str]],
        limit: int
    ) -> List[Dict[str, Any]]:
        """Perform search across entities."""
        results = []
        
        if entity_type in ["all", "startup"]:
            startups = await self.startup_repo.search(
                query=query,
                sectors=sectors,
                stages=stages,
                limit=limit if entity_type == "startup" else limit // 2
            )
            
            for startup in startups:
                results.append({
                    "type": "startup",
                    "id": str(startup.id),
                    "name": startup.name,
                    "description": startup.description,
                    "sector": startup.sector,
                    "stage": startup.stage
                })
        
        if entity_type in ["all", "vc"]:
            vcs = await self.vc_repo.search(
                query=query,
                sectors=sectors,
                stages=stages,
                limit=limit if entity_type == "vc" else limit // 2
            )
            
            for vc in vcs:
                results.append({
                    "type": "vc",
                    "id": str(vc.id),
                    "name": vc.name,
                    "thesis": vc.thesis,
                    "sectors": vc.sectors,
                    "stages": vc.stages
                })
        
        return results
    
    async def _calculate_matching_score(
        self,
        startup: Startup,
        vc: VC
    ) -> Dict[str, Any]:
        """Calculate detailed matching score."""
        score, reasons = self._calculate_vc_startup_match(vc, startup)
        
        return {
            "score": round(score, 2),
            "reasons": reasons,
            "breakdown": {
                "sector_match": self._calculate_sector_match(startup.sector, vc.sectors),
                "stage_match": self._calculate_stage_match(startup.stage, vc.stages),
                "thesis_alignment": self._calculate_thesis_alignment(startup, vc.thesis)
            },
            "recommendation": self._get_match_recommendation(score)
        }
    
    # Utility methods
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract keywords from text."""
        if not text:
            return []
        
        # Simple keyword extraction - in production use NLP
        keywords = []
        important_words = text.lower().split()
        
        # Filter common words
        stopwords = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for'}
        
        for word in important_words:
            if len(word) > 3 and word not in stopwords:
                keywords.append(word)
        
        return keywords[:10]  # Limit to top 10
    
    def _calculate_vc_startup_match(self, vc: VC, startup: Startup) -> tuple[float, List[str]]:
        """Calculate match score between VC and startup."""
        score = 0.0
        reasons = []
        
        # Sector match (40% weight)
        sector_score = self._calculate_sector_match(startup.sector, vc.sectors)
        score += sector_score * 0.4
        if sector_score > 0:
            reasons.append(f"Sector match: {startup.sector}")
        
        # Stage match (30% weight)
        stage_score = self._calculate_stage_match(startup.stage, vc.stages)
        score += stage_score * 0.3
        if stage_score > 0:
            reasons.append(f"Stage match: {startup.stage}")
        
        # Funding range match (20% weight)
        if vc.check_size_min and vc.check_size_max:
            funding_score = self._calculate_funding_match(
                startup.monthly_revenue * 12,  # Annualized
                vc.check_size_min,
                vc.check_size_max
            )
            score += funding_score * 0.2
            if funding_score > 0:
                reasons.append("Funding range match")
        
        # Thesis alignment (10% weight)
        if vc.thesis:
            thesis_score = self._calculate_thesis_alignment(startup, vc.thesis)
            score += thesis_score * 0.1
            if thesis_score > 0.5:
                reasons.append("Strong thesis alignment")
        
        return score, reasons
    
    def _calculate_startup_vc_match(self, startup: Startup, vc: VC) -> tuple[float, List[str]]:
        """Calculate match score from startup perspective."""
        # Reverse of VC-startup match with slightly different weights
        return self._calculate_vc_startup_match(vc, startup)
    
    def _calculate_sector_match(self, startup_sector: str, vc_sectors: List[str]) -> float:
        """Calculate sector match score."""
        if not vc_sectors:
            return 0.5  # No preference means neutral match
        
        if startup_sector in vc_sectors:
            return 1.0
        
        # Check for related sectors
        sector_relations = {
            "AI/ML": ["B2B SaaS", "Deep Tech"],
            "Fintech": ["B2B SaaS", "Crypto"],
            "Healthcare": ["Biotech"],
            "B2B SaaS": ["AI/ML", "Fintech", "DevTools"]
        }
        
        for vc_sector in vc_sectors:
            if startup_sector in sector_relations.get(vc_sector, []):
                return 0.7
        
        return 0.0
    
    def _calculate_stage_match(self, startup_stage: str, vc_stages: List[str]) -> float:
        """Calculate stage match score."""
        if not vc_stages:
            return 0.5
        
        if startup_stage in vc_stages:
            return 1.0
        
        # Adjacent stages get partial credit
        stage_order = ["Pre-seed", "Seed", "Series A", "Series B", "Series C", "Growth"]
        
        try:
            startup_idx = stage_order.index(startup_stage)
            for vc_stage in vc_stages:
                vc_idx = stage_order.index(vc_stage)
                if abs(startup_idx - vc_idx) == 1:
                    return 0.6
        except ValueError:
            pass
        
        return 0.0
    
    def _calculate_funding_match(self, amount: float, min_check: float, max_check: float) -> float:
        """Calculate funding range match."""
        if amount >= min_check and amount <= max_check:
            return 1.0
        elif amount < min_check:
            # Too small
            ratio = amount / min_check
            return max(0, ratio)
        else:
            # Too large
            ratio = max_check / amount
            return max(0, ratio)
    
    def _calculate_thesis_alignment(self, startup: Startup, thesis: str) -> float:
        """Calculate thesis alignment score."""
        if not thesis:
            return 0.5
        
        # Simple keyword matching - in production use embeddings
        thesis_lower = thesis.lower()
        description_lower = startup.description.lower()
        
        matches = 0
        keywords = self._extract_keywords(thesis)
        
        for keyword in keywords:
            if keyword in description_lower:
                matches += 1
        
        if keywords:
            return min(1.0, matches / len(keywords))
        
        return 0.5
    
    def _analyze_thesis_alignment(self, thesis: str, startup: Startup) -> str:
        """Analyze and explain thesis alignment."""
        if not thesis:
            return "No thesis provided"
        
        score = self._calculate_thesis_alignment(startup, thesis)
        
        if score > 0.7:
            return "Strong alignment with investment thesis"
        elif score > 0.4:
            return "Moderate alignment with investment thesis"
        else:
            return "Limited alignment with investment thesis"
    
    def _get_match_recommendation(self, score: float) -> str:
        """Get recommendation based on match score."""
        if score >= 0.9:
            return "Excellent match - highly recommended"
        elif score >= 0.7:
            return "Good match - worth exploring"
        elif score >= 0.5:
            return "Potential match - review carefully"
        else:
            return "Weak match - likely not a fit"