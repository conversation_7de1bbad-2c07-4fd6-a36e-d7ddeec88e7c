"""Service for extracting and analyzing VC investment thesis using AI."""

from typing import Dict, Any, List, Optional
from datetime import datetime
import logging
import json

from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import PydanticOutputParser
from pydantic import BaseModel, Field

from src.scrapers.vc_website_scraper import analyze_vc_website
from src.core.models.vc import VC

logger = logging.getLogger(__name__)


class ExtractedThesis(BaseModel):
    """Structured VC thesis extracted from website content."""
    
    firm_name: str = Field(description="Name of the VC firm")
    firm_description: str = Field(description="Brief description of the VC firm")
    
    # Investment preferences
    investment_stages: List[str] = Field(
        description="Preferred investment stages (Pre-seed, Seed, Series A, etc.)"
    )
    sectors: List[str] = Field(
        description="Sectors they invest in (AI/ML, B2B SaaS, Fintech, etc.)"
    )
    check_size_min: Optional[int] = Field(
        description="Minimum check size in USD",
        default=None
    )
    check_size_max: Optional[int] = Field(
        description="Maximum check size in USD",
        default=None
    )
    sweet_spot_check_size: Optional[int] = Field(
        description="Typical/sweet spot check size in USD",
        default=None
    )
    
    # Investment criteria
    geography: List[str] = Field(
        description="Geographic focus areas",
        default_factory=list
    )
    
    # Specific preferences
    thesis_summary: str = Field(
        description="Summary of their investment thesis in 2-3 sentences"
    )
    
    # What they look for
    key_criteria: List[str] = Field(
        description="Key things they look for in investments",
        default_factory=list
    )
    
    # What they avoid
    avoid_criteria: List[str] = Field(
        description="Things they explicitly avoid or don't invest in",
        default_factory=list
    )
    
    # Notable portfolio companies
    portfolio_examples: List[str] = Field(
        description="Examples of portfolio companies",
        default_factory=list
    )
    
    # Partner information
    key_partners: List[Dict[str, str]] = Field(
        description="Key partners with their focus areas",
        default_factory=list
    )
    
    confidence_score: float = Field(
        description="Confidence score of extraction (0-1)",
        ge=0,
        le=1
    )


class VCThesisExtractor:
    """Extract and analyze VC investment thesis from websites."""
    
    def __init__(self, openai_api_key: str):
        """Initialize with OpenAI API key."""
        self.llm = ChatOpenAI(
            api_key=openai_api_key,
            model_name="gpt-4-turbo-preview",
            temperature=0.1  # Low temperature for consistent extraction
        )
        
        # Create output parser
        self.parser = PydanticOutputParser(pydantic_object=ExtractedThesis)
        
        # Create extraction prompt
        self.extraction_prompt = ChatPromptTemplate.from_messages([
            ("system", """You are an expert at analyzing VC firm websites and extracting their investment thesis.
            
            Analyze the provided website content and extract structured information about the VC's investment preferences.
            Be precise and only include information that is explicitly stated or strongly implied in the content.
            
            For sectors, use these standardized categories when applicable:
            - AI/ML
            - B2B SaaS
            - Fintech
            - Healthcare
            - Biotech
            - Crypto
            - Climate Tech
            - Deep Tech
            - DevTools
            - B2C
            - Marketplace
            - Robotics
            
            For stages, use these standardized categories:
            - Pre-seed
            - Seed
            - Series A
            - Series B
            - Series C
            - Growth
            
            {format_instructions}"""),
            ("user", """Based on the following content scraped from a VC website, extract their investment thesis:

            Website: {website_url}
            
            Main Page Content:
            {main_content}
            
            Thesis Pages Content:
            {thesis_content}
            
            Team Information:
            {team_info}
            
            Portfolio Companies:
            {portfolio_companies}
            
            Investment Focus Keywords Found:
            {investment_focus}
            
            Please extract the investment thesis in the requested format.""")
        ])
    
    async def extract_thesis_from_website(self, website_url: str) -> Dict[str, Any]:
        """Extract investment thesis from a VC website."""
        try:
            # Scrape the website
            logger.info(f"Scraping VC website: {website_url}")
            scrape_result = await analyze_vc_website(website_url)
            
            if not scrape_result["success"]:
                return {
                    "success": False,
                    "error": f"Failed to scrape website: {scrape_result.get('error', 'Unknown error')}",
                    "website": website_url
                }
            
            # Extract data from scrape result
            data = scrape_result["data"]
            
            # Prepare content for AI analysis
            main_content = f"{data.get('title', '')} - {data.get('description', '')}"
            
            thesis_content = ""
            for page in data.get("thesis_pages", []):
                thesis_content += f"\n\nPage: {page['url']}\n{page['content'][:2000]}"
            
            team_info = json.dumps(data.get("team_members", []), indent=2)
            portfolio_companies = ", ".join(data.get("portfolio_companies", []))
            investment_focus = json.dumps(data.get("investment_focus", {}), indent=2)
            
            # Run AI extraction
            prompt = self.extraction_prompt.format_prompt(
                format_instructions=self.parser.get_format_instructions(),
                website_url=website_url,
                main_content=main_content,
                thesis_content=thesis_content,
                team_info=team_info,
                portfolio_companies=portfolio_companies,
                investment_focus=investment_focus
            )
            
            response = await self.llm.apredict(prompt.to_string())
            
            # Parse the response
            extracted_thesis = self.parser.parse(response)
            
            return {
                "success": True,
                "thesis": extracted_thesis.dict(),
                "raw_data": data,
                "extracted_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error extracting thesis from {website_url}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "website": website_url
            }
    
    async def update_vc_with_thesis(self, vc: VC, thesis_data: Dict[str, Any]) -> VC:
        """Update a VC object with extracted thesis data."""
        thesis = thesis_data["thesis"]
        
        # Update VC fields
        vc.sectors = thesis.get("sectors", [])
        vc.stages = thesis.get("investment_stages", [])
        vc.check_size_min = thesis.get("check_size_min")
        vc.check_size_max = thesis.get("check_size_max")
        vc.sweet_spot_check_size = thesis.get("sweet_spot_check_size")
        
        # Store additional data in metadata
        if not hasattr(vc, 'metadata'):
            vc.metadata = {}
        
        vc.metadata.update({
            "thesis_summary": thesis.get("thesis_summary"),
            "geography": thesis.get("geography", []),
            "key_criteria": thesis.get("key_criteria", []),
            "avoid_criteria": thesis.get("avoid_criteria", []),
            "portfolio_examples": thesis.get("portfolio_examples", []),
            "key_partners": thesis.get("key_partners", []),
            "thesis_extracted_at": thesis_data.get("extracted_at"),
            "extraction_confidence": thesis.get("confidence_score", 0.0)
        })
        
        return vc
    
    async def extract_thesis_for_multiple_vcs(
        self, 
        vc_websites: List[Dict[str, str]]
    ) -> List[Dict[str, Any]]:
        """Extract thesis for multiple VCs in batch."""
        results = []
        
        for vc_info in vc_websites:
            website = vc_info.get("website")
            vc_name = vc_info.get("name", "Unknown")
            
            if not website:
                results.append({
                    "vc_name": vc_name,
                    "success": False,
                    "error": "No website provided"
                })
                continue
            
            logger.info(f"Extracting thesis for {vc_name} from {website}")
            
            thesis_result = await self.extract_thesis_from_website(website)
            thesis_result["vc_name"] = vc_name
            results.append(thesis_result)
            
            # Add a small delay to avoid rate limiting
            import asyncio
            await asyncio.sleep(1)
        
        return results
    
    def generate_thesis_summary(self, thesis_data: Dict[str, Any]) -> str:
        """Generate a concise summary of the investment thesis."""
        thesis = thesis_data.get("thesis", {})
        
        summary_parts = []
        
        # Firm name and description
        summary_parts.append(f"{thesis.get('firm_name', 'VC Firm')}: {thesis.get('firm_description', '')}")
        
        # Investment focus
        if thesis.get("sectors"):
            summary_parts.append(f"Sectors: {', '.join(thesis['sectors'])}")
        
        if thesis.get("investment_stages"):
            summary_parts.append(f"Stages: {', '.join(thesis['investment_stages'])}")
        
        # Check sizes
        if thesis.get("check_size_min") and thesis.get("check_size_max"):
            summary_parts.append(
                f"Check sizes: ${thesis['check_size_min']:,} - ${thesis['check_size_max']:,}"
            )
        elif thesis.get("sweet_spot_check_size"):
            summary_parts.append(f"Typical check: ${thesis['sweet_spot_check_size']:,}")
        
        # Geography
        if thesis.get("geography"):
            summary_parts.append(f"Geography: {', '.join(thesis['geography'])}")
        
        # Thesis summary
        if thesis.get("thesis_summary"):
            summary_parts.append(f"\nThesis: {thesis['thesis_summary']}")
        
        return "\n".join(summary_parts)