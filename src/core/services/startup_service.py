"""Service layer for Startup business logic using ports/adapters pattern.

This service uses the AI port interface instead of directly coupling
to the AI implementation, following hexagonal architecture principles.
"""
from typing import List, Optional, Dict, Any
from uuid import UUID, uuid4
from dataclasses import replace

from src.core.models.startup import Startup
from src.core.repositories.startup_repository import StartupRepository
from src.core.ports.ai_port import AIPort, StartupInsights
from src.core.services.validation_service import get_validation_service


class StartupService:
    """Service for managing startup-related business logic.
    
    This service coordinates between the repository layer and AI port,
    ensuring proper separation of concerns and encapsulation of business rules.
    """
    
    def __init__(
        self,
        repository: StartupRepository,
        ai_port: AIPort
    ):
        """Initialize the startup service with required dependencies.
        
        Args:
            repository: Repository for startup data persistence
            ai_port: Port for AI operations (decoupled from implementation)
        """
        self.repository = repository
        self.ai_port = ai_port
        self.validation_service = get_validation_service()
    
    async def create_startup(self, startup: Startup) -> Startup:
        """Create a new startup.
        
        Assigns an ID if not provided and saves to repository.
        
        Args:
            startup: Startup domain object to create
            
        Returns:
            Created startup with ID assigned
            
        Raises:
            ValueError: If startup validation fails
        """
        # Convert to dict for validation
        startup_data = {
            'name': startup.name,
            'sector': startup.sector,
            'stage': startup.stage,
            'description': startup.description,
            'website': startup.website,
            'email': getattr(startup, 'email', None),
            'team_size': startup.team_size,
            'monthly_revenue': startup.monthly_revenue
        }
        
        # Validate data
        errors = self.validation_service.validate_startup_data(startup_data)
        if errors:
            error_msg = '; '.join([f"{field}: {', '.join(msgs)}" for field, msgs in errors.items()])
            raise ValueError(f"Validation failed: {error_msg}")
        
        # Check for duplicates
        existing_startups = await self.repository.list()
        existing_data = [
            {
                'name': s.name,
                'website': s.website,
                'sector': s.sector,
                'stage': s.stage
            }
            for s in existing_startups
        ]
        
        duplicates = self.validation_service.find_duplicate_startups(startup_data, existing_data)
        if duplicates and duplicates[0]['score'] >= 0.9:
            raise ValueError(f"Duplicate startup detected: {duplicates[0]['reason']}")
        
        # Apply any cleaned data back to the startup
        if 'website' in startup_data and startup_data['website'] != startup.website:
            startup = replace(startup, website=startup_data['website'])
        if 'email' in startup_data and hasattr(startup, 'email') and startup_data['email'] != getattr(startup, 'email'):
            startup = replace(startup, email=startup_data['email'])
        # This will raise ValueError if name is empty
        _ = startup.name  # Trigger validation
        
        # Assign ID if not provided
        if startup.id is None:
            startup = replace(startup, id=uuid4())
        
        # Save to repository
        saved_startup = await self.repository.save(startup)
        return saved_startup
    
    async def get_startup(self, startup_id: UUID) -> Startup:
        """Retrieve a startup by ID.
        
        Args:
            startup_id: UUID of the startup to retrieve
            
        Returns:
            The startup if found
            
        Raises:
            ValueError: If startup not found
        """
        startup = await self.repository.find_by_id(startup_id)
        if startup is None:
            raise ValueError(f"Startup with id {startup_id} not found")
        return startup
    
    async def list_startups(
        self,
        sector: Optional[str] = None,
        stage: Optional[str] = None
    ) -> List[Startup]:
        """List startups with optional filters.
        
        Args:
            sector: Filter by sector if provided
            stage: Filter by funding stage if provided
            
        Returns:
            List of startups matching the filters
        """
        if sector:
            return await self.repository.find_by_sector(sector)
        elif stage:
            return await self.repository.find_by_stage(stage)
        else:
            return await self.repository.find_all()
    
    async def analyze_startup(
        self,
        startup_id: UUID,
        force_refresh: bool = False
    ) -> StartupInsights:
        """Analyze a startup using AI to extract key insights.
        
        This method now returns domain-specific insights instead of
        implementation-specific analysis objects.
        
        Args:
            startup_id: UUID of the startup to analyze
            force_refresh: Whether to bypass cache
            
        Returns:
            Domain insights about the startup
            
        Raises:
            ValueError: If startup not found
            AIAnalysisError: If AI analysis fails
        """
        # First, get the startup
        startup = await self.get_startup(startup_id)
        
        # Use AI port to analyze (implementation agnostic)
        insights = await self.ai_port.analyze_startup(
            startup,
            use_cache=not force_refresh
        )
        
        return insights
    
    async def update_startup(
        self,
        startup_id: UUID,
        updates: Dict[str, Any]
    ) -> Startup:
        """Update an existing startup.
        
        Args:
            startup_id: UUID of the startup to update
            updates: Dictionary of fields to update
            
        Returns:
            Updated startup
            
        Raises:
            ValueError: If startup not found or validation fails
        """
        # Get existing startup
        startup = await self.get_startup(startup_id)
        
        # Validate updates
        if updates:
            errors = self.validation_service.validate_startup_data(updates)
            if errors:
                error_msg = '; '.join([f"{field}: {', '.join(msgs)}" for field, msgs in errors.items()])
                raise ValueError(f"Validation failed: {error_msg}")
            
            # Check for duplicates if name or website is being updated
            if 'name' in updates or 'website' in updates:
                # Merge existing data with updates for duplicate check
                check_data = {
                    'name': updates.get('name', startup.name),
                    'website': updates.get('website', startup.website),
                    'sector': updates.get('sector', startup.sector),
                    'stage': updates.get('stage', startup.stage)
                }
                
                # Get all startups except the current one
                all_startups = await self.repository.list()
                existing_data = [
                    {
                        'name': s.name,
                        'website': s.website,
                        'sector': s.sector,
                        'stage': s.stage
                    }
                    for s in all_startups if s.id != startup_id
                ]
                
                duplicates = self.validation_service.find_duplicate_startups(check_data, existing_data)
                if duplicates and duplicates[0]['score'] >= 0.9:
                    raise ValueError(f"Update would create duplicate: {duplicates[0]['reason']}")
        
        # Apply updates
        updated_startup = replace(startup, **updates)
        
        # Save updated startup
        saved_startup = await self.repository.save(updated_startup)
        return saved_startup
    
    async def delete_startup(self, startup_id: UUID) -> bool:
        """Delete a startup.
        
        Args:
            startup_id: UUID of the startup to delete
            
        Returns:
            True if deleted, False if not found
        """
        return await self.repository.delete(startup_id)
    
    async def analyze_startup_batch(
        self,
        startup_ids: List[UUID],
        max_concurrent: int = 5
    ) -> List[StartupInsights]:
        """Analyze multiple startups in batch.
        
        This is a new method that leverages the AI port's batch capability.
        
        Args:
            startup_ids: List of startup IDs to analyze
            max_concurrent: Maximum concurrent analyses
            
        Returns:
            List of insights for each startup
            
        Raises:
            ValueError: If any startup not found
            AIAnalysisError: If AI analysis fails
        """
        # Get all startups
        startups = []
        for startup_id in startup_ids:
            startup = await self.get_startup(startup_id)
            startups.append(startup)
        
        # Use AI port batch analysis
        insights = await self.ai_port.batch_analyze_startups(
            startups,
            max_concurrent=max_concurrent
        )
        
        return insights
    
    def get_ai_usage_stats(self) -> Dict[str, Any]:
        """Get AI usage statistics.
        
        This delegates to the AI port, keeping the service
        decoupled from the specific AI implementation.
        
        Returns:
            Dictionary with usage statistics
        """
        return self.ai_port.get_usage_stats()