"""AI Port Interface for the hexagonal architecture.

This port defines the contract for AI operations that the core domain needs,
without being coupled to any specific AI provider or implementation.
"""
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, List
from dataclasses import dataclass

from src.core.models.startup import Startup
from src.core.models.vc import VC


@dataclass
class StartupInsights:
    """Domain object for startup AI insights."""
    
    key_technologies: List[str]
    market_opportunity: str
    competitive_advantages: List[str]
    team_strengths: List[str]
    risk_factors: List[str]
    growth_potential_score: float  # 0-1 scale
    innovation_score: float  # 0-1 scale
    market_fit_score: float  # 0-1 scale
    
    def overall_score(self) -> float:
        """Calculate overall startup score."""
        return (
            self.growth_potential_score + 
            self.innovation_score + 
            self.market_fit_score
        ) / 3


@dataclass
class VCInsights:
    """Domain object for VC AI insights."""
    
    thesis_summary: str
    preferred_sectors: List[str]
    preferred_stages: List[str]
    typical_check_size: Dict[str, int]  # {"min": amount, "max": amount}
    portfolio_focus: List[str]
    investment_criteria: List[str]
    exclusion_criteria: List[str]
    

@dataclass
class MatchRationale:
    """Domain object for AI-generated match rationale."""
    
    compatibility_score: float  # 0-1 scale
    key_alignments: List[str]
    potential_concerns: List[str]
    suggested_talking_points: List[str]
    confidence_level: float  # 0-1 scale


class AIPort(ABC):
    """Port interface for AI operations.
    
    This interface defines all AI operations needed by the domain,
    allowing different AI providers to be plugged in as adapters.
    """
    
    @abstractmethod
    async def analyze_startup(
        self,
        startup: Startup,
        use_cache: bool = True
    ) -> StartupInsights:
        """Analyze a startup to extract key insights.
        
        Args:
            startup: The startup to analyze
            use_cache: Whether to use cached results if available
            
        Returns:
            Insights about the startup
            
        Raises:
            AIAnalysisError: If analysis fails
        """
        pass
    
    @abstractmethod
    async def analyze_vc(
        self,
        vc: VC,
        website_content: Optional[str] = None,
        use_cache: bool = True
    ) -> VCInsights:
        """Analyze a VC to extract investment thesis and preferences.
        
        Args:
            vc: The VC to analyze
            website_content: Optional pre-scraped website content
            use_cache: Whether to use cached results if available
            
        Returns:
            Insights about the VC's investment strategy
            
        Raises:
            AIAnalysisError: If analysis fails
        """
        pass
    
    @abstractmethod
    async def generate_match_rationale(
        self,
        startup: Startup,
        vc: VC,
        startup_insights: Optional[StartupInsights] = None,
        vc_insights: Optional[VCInsights] = None
    ) -> MatchRationale:
        """Generate AI rationale for why a startup and VC match.
        
        Args:
            startup: The startup in the match
            vc: The VC in the match
            startup_insights: Pre-computed startup insights (optional)
            vc_insights: Pre-computed VC insights (optional)
            
        Returns:
            Detailed rationale for the match
            
        Raises:
            AIAnalysisError: If analysis fails
        """
        pass
    
    @abstractmethod
    async def batch_analyze_startups(
        self,
        startups: List[Startup],
        max_concurrent: int = 5
    ) -> List[StartupInsights]:
        """Analyze multiple startups concurrently.
        
        Args:
            startups: List of startups to analyze
            max_concurrent: Maximum concurrent analyses
            
        Returns:
            List of insights for each startup
            
        Raises:
            AIAnalysisError: If analysis fails
        """
        pass
    
    @abstractmethod
    def get_usage_stats(self) -> Dict[str, Any]:
        """Get AI usage statistics (tokens, costs, etc).
        
        Returns:
            Dictionary with usage statistics
        """
        pass
    
    @abstractmethod
    def reset_usage_stats(self) -> None:
        """Reset usage statistics."""
        pass


class AIAnalysisError(Exception):
    """Exception raised when AI analysis fails."""
    
    def __init__(self, message: str, original_error: Optional[Exception] = None):
        super().__init__(message)
        self.original_error = original_error