from dataclasses import dataclass, field
from typing import List, Optional
from uuid import UUID

@dataclass
class VC:
    firm_name: str
    website: str = ""
    thesis: str = ""
    check_size_min: float = 0
    check_size_max: float = 0
    sectors: List[str] = field(default_factory=list)
    stages: List[str] = field(default_factory=list)
    portfolio_companies: List[str] = field(default_factory=list)
    partners: List[str] = field(default_factory=list)
    id: Optional[UUID] = None
    
    def can_invest_in_stage(self, stage: str) -> bool:
        if not self.stages:
            return True
        return stage in self.stages
    
    def matches_funding_amount(self, amount: float) -> bool:
        if self.check_size_min == 0 and self.check_size_max == 0:
            return True
        if self.check_size_min > 0 and amount < self.check_size_min:
            return False
        if self.check_size_max > 0 and amount > self.check_size_max:
            return False
        return True