from dataclasses import dataclass, field
from typing import List, Optional
from datetime import datetime
from uuid import UUID

from src.core.models.enums import MatchStatus, MatchType

@dataclass
class Match:
    startup: 'Startup'  # Forward reference
    vc: 'VC'  # Forward reference
    score: float
    reasons: List[str]
    id: Optional[UUID] = None
    status: Optional[MatchStatus] = None
    match_type: Optional[MatchType] = None
    notes: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    # Legacy fields for backward compatibility
    startup_id: Optional[str] = None
    vc_id: Optional[str] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()
        if self.updated_at is None:
            self.updated_at = datetime.utcnow()
        # Set legacy fields if not provided
        if self.startup_id is None and hasattr(self, 'startup') and self.startup:
            self.startup_id = str(self.startup.id) if self.startup.id else None
        if self.vc_id is None and hasattr(self, 'vc') and self.vc:
            self.vc_id = str(self.vc.id) if self.vc.id else None