"""Domain enums for the VC matching platform."""

from enum import Enum


class MatchStatus(str, Enum):
    """Status of a match between startup and VC."""
    PENDING = "pending"
    ACCEPTED = "accepted"
    REJECTED = "rejected"
    EXPIRED = "expired"
    IN_DISCUSSION = "in_discussion"


class MatchType(str, Enum):
    """Type of match between startup and VC."""
    AUTOMATED = "automated"
    MANUAL = "manual"
    RECOMMENDED = "recommended"
    REQUESTED = "requested"
    AI_ENHANCED = "ai_enhanced"


class StartupStage(str, Enum):
    """Startup development stages."""
    IDEA = "idea"
    MVP = "mvp"
    SEED = "seed"
    SERIES_A = "series_a"
    SERIES_B = "series_b"
    SERIES_C = "series_c"
    GROWTH = "growth"
    LATE_STAGE = "late_stage"


class Industry(str, Enum):
    """Industry sectors."""
    FINTECH = "fintech"
    HEALTHTECH = "healthtech"
    EDTECH = "edtech"
    SAAS = "saas"
    MARKETPLACE = "marketplace"
    AI_ML = "ai_ml"
    BLOCKCHAIN = "blockchain"
    ECOMMERCE = "ecommerce"
    GAMING = "gaming"
    SOCIAL = "social"
    ENTERPRISE = "enterprise"
    CONSUMER = "consumer"
    HARDWARE = "hardware"
    BIOTECH = "biotech"
    CLEANTECH = "cleantech"
    OTHER = "other"


class UserRole(str, Enum):
    """User roles in the system."""
    ADMIN = "admin"
    USER = "user"
    INVESTOR = "investor"
    STARTUP_OWNER = "startup_owner"
    VC_PARTNER = "vc_partner"