from dataclasses import dataclass
from typing import List, Optional
from uuid import UUID

@dataclass
class Startup:
    name: str
    sector: str
    stage: str
    description: str = ""
    website: str = ""
    team_size: int = 0
    monthly_revenue: float = 0
    id: Optional[UUID] = None
    
    def __post_init__(self):
        if not self.name:
            raise ValueError("Startup name is required")
            
    def is_fundable(self) -> bool:
        fundable_stages = ["Pre-seed", "Seed", "Series A", "Series B"]
        return self.stage in fundable_stages
        
    def extract_sectors(self) -> List[str]:
        sectors = []
        keywords = {
            "AI/ML": ["ai", "machine learning", "ml", "artificial intelligence"],
            "B2B SaaS": ["b2b", "saas", "software as a service"],
            "Fintech": ["fintech", "financial", "payments", "banking"]
        }
        
        text = f"{self.description} {self.sector}".lower()
        for sector, terms in keywords.items():
            if any(term in text for term in terms):
                sectors.append(sector)
                
        return sectors or [self.sector]