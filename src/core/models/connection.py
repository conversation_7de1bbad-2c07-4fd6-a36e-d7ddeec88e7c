"""Connection domain model for warm intro finder."""

from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import List, Optional
from uuid import UUID, uuid4


class ConnectionStrength(Enum):
    """Connection strength levels based on interaction frequency and quality."""
    STRONG = "strong"      # Direct collaboration, frequent interaction
    MEDIUM = "medium"      # Some interaction, mutual connections
    WEAK = "weak"          # Distant connection, minimal interaction


class RelationshipType(Enum):
    """Type of relationship between connected users."""
    COLLEAGUE = "colleague"           # Worked together
    BUSINESS_PARTNER = "business_partner"  # Business relationship
    MENTOR_MENTEE = "mentor_mentee"   # Mentoring relationship
    INVESTOR_FOUNDER = "investor_founder"  # Investment relationship
    INDUSTRY_PEER = "industry_peer"   # Same industry, different companies
    REFERRAL = "referral"            # Connected through introduction
    SOCIAL = "social"                # Social/personal connection


class IntroductionStatus(Enum):
    """Status of introduction requests."""
    PENDING = "pending"
    ACCEPTED = "accepted" 
    DECLINED = "declined"
    COMPLETED = "completed"
    EXPIRED = "expired"


@dataclass(frozen=True)
class ConnectionId:
    """Value object for connection identification."""
    value: UUID = field(default_factory=uuid4)

    def __str__(self) -> str:
        return str(self.value)


@dataclass(frozen=True)
class ConnectionMetrics:
    """Value object for connection quality metrics."""
    interaction_frequency: int = 0  # Interactions per month
    last_interaction_days: Optional[int] = None  # Days since last interaction
    mutual_connections_count: int = 0  # Number of mutual connections
    trust_score: float = 0.0  # 0.0 to 1.0 trust score

    def calculate_strength(self) -> ConnectionStrength:
        """Calculate connection strength based on metrics."""
        if self.interaction_frequency >= 4 or self.trust_score >= 0.8:
            return ConnectionStrength.STRONG
        elif self.interaction_frequency >= 1 or self.trust_score >= 0.5:
            return ConnectionStrength.MEDIUM
        else:
            return ConnectionStrength.WEAK


@dataclass
class Connection:
    """Core connection entity representing bidirectional relationships."""
    
    id: ConnectionId
    user_a_id: UUID  # Always the smaller UUID for consistency
    user_b_id: UUID  # Always the larger UUID for consistency
    relationship_type: RelationshipType
    strength: ConnectionStrength
    metrics: ConnectionMetrics
    notes: Optional[str] = None
    tags: List[str] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    is_active: bool = True

    def __post_init__(self):
        """Ensure consistent ordering of user IDs."""
        if self.user_a_id > self.user_b_id:
            self.user_a_id, self.user_b_id = self.user_b_id, self.user_a_id
        self._validate()

    def _validate(self):
        """Validate connection invariants."""
        if self.user_a_id == self.user_b_id:
            raise ValueError("Cannot create connection to self")
        
        if self.metrics.trust_score < 0.0 or self.metrics.trust_score > 1.0:
            raise ValueError("Trust score must be between 0.0 and 1.0")

    def involves_user(self, user_id: UUID) -> bool:
        """Check if connection involves a specific user."""
        return user_id in (self.user_a_id, self.user_b_id)

    def get_other_user(self, user_id: UUID) -> UUID:
        """Get the other user in the connection."""
        if user_id == self.user_a_id:
            return self.user_b_id
        elif user_id == self.user_b_id:
            return self.user_a_id
        else:
            raise ValueError("User not part of this connection")

    def update_metrics(self, new_metrics: ConnectionMetrics) -> None:
        """Update connection metrics and derived strength."""
        self.metrics = new_metrics
        self.strength = new_metrics.calculate_strength()
        self.updated_at = datetime.utcnow()

    def add_tag(self, tag: str) -> None:
        """Add a tag to the connection."""
        if tag not in self.tags:
            self.tags.append(tag)
            self.updated_at = datetime.utcnow()

    def remove_tag(self, tag: str) -> None:
        """Remove a tag from the connection."""
        if tag in self.tags:
            self.tags.remove(tag)
            self.updated_at = datetime.utcnow()


@dataclass
class IntroductionRequest:
    """Entity for managing introduction requests."""
    
    id: UUID = field(default_factory=uuid4)
    requester_id: UUID = None  # Who is requesting the intro
    target_id: UUID = None     # Who they want to meet
    connector_id: UUID = None  # Who can make the connection
    status: IntroductionStatus = IntroductionStatus.PENDING
    message: str = ""          # Requester's message
    connector_notes: Optional[str] = None  # Connector's private notes
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    expires_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None

    def __post_init__(self):
        """Set default expiration if not provided."""
        if self.expires_at is None:
            # Default 30-day expiration
            self.expires_at = self.created_at + timedelta(days=30)

    def accept(self, connector_notes: Optional[str] = None) -> None:
        """Accept the introduction request."""
        self.status = IntroductionStatus.ACCEPTED
        self.connector_notes = connector_notes
        self.updated_at = datetime.utcnow()

    def decline(self, connector_notes: Optional[str] = None) -> None:
        """Decline the introduction request."""
        self.status = IntroductionStatus.DECLINED
        self.connector_notes = connector_notes
        self.updated_at = datetime.utcnow()

    def complete(self) -> None:
        """Mark introduction as completed."""
        self.status = IntroductionStatus.COMPLETED
        self.completed_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()

    def is_expired(self) -> bool:
        """Check if request has expired."""
        if self.expires_at is None:
            return False
        return datetime.utcnow() > self.expires_at


@dataclass
class ConnectionPath:
    """Value object representing a path between two users."""
    
    source_user_id: UUID
    target_user_id: UUID
    path: List[UUID]  # List of user IDs in the path
    connections: List[Connection]  # Connections that form the path
    total_strength_score: float = 0.0

    def __post_init__(self):
        """Calculate path strength score."""
        if self.connections:
            # Use harmonic mean for path strength (weakest link principle)
            strength_values = {
                ConnectionStrength.STRONG: 1.0,
                ConnectionStrength.MEDIUM: 0.6,
                ConnectionStrength.WEAK: 0.3
            }
            scores = [strength_values[conn.strength] for conn in self.connections]
            if scores:
                self.total_strength_score = len(scores) / sum(1/score for score in scores)

    @property
    def length(self) -> int:
        """Get path length (number of hops)."""
        return len(self.path) - 1

    @property
    def intermediary_count(self) -> int:
        """Get number of intermediaries."""
        return max(0, len(self.path) - 2)

    def get_next_connector(self) -> Optional[UUID]:
        """Get the first intermediary in the path."""
        if len(self.path) >= 3:
            return self.path[1]
        return None