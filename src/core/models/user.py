"""User domain model."""

from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional
from uuid import UUID


@dataclass
class User:
    """User domain model for authentication and authorization."""
    
    id: UUID
    email: str
    username: str
    full_name: Optional[str] = None
    is_active: bool = True
    is_superuser: bool = False
    roles: List[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    last_login: Optional[datetime] = None
    
    def __post_init__(self):
        """Initialize default values."""
        if self.roles is None:
            self.roles = []
        if self.created_at is None:
            self.created_at = datetime.utcnow()
        if self.updated_at is None:
            self.updated_at = datetime.utcnow()
    
    def has_role(self, role: str) -> bool:
        """Check if user has a specific role."""
        return role in self.roles
    
    def has_any_role(self, roles: List[str]) -> bool:
        """Check if user has any of the specified roles."""
        return any(role in self.roles for role in roles)
    
    def add_role(self, role: str) -> None:
        """Add a role to the user."""
        if role not in self.roles:
            self.roles.append(role)
            self.updated_at = datetime.utcnow()
    
    def remove_role(self, role: str) -> None:
        """Remove a role from the user."""
        if role in self.roles:
            self.roles.remove(role)
            self.updated_at = datetime.utcnow()
    
    def is_admin(self) -> bool:
        """Check if user is an admin."""
        return self.is_superuser or self.has_role("admin")
    
    def can_access_resource(self, resource_type: str) -> bool:
        """Check if user can access a specific resource type."""
        # Admins can access everything
        if self.is_admin():
            return True
        
        # Define resource access rules
        resource_roles = {
            "startup": ["user", "investor", "startup_owner"],
            "vc": ["investor", "vc_partner"],
            "match": ["user", "investor", "startup_owner", "vc_partner"],
            "admin": ["admin"]
        }
        
        allowed_roles = resource_roles.get(resource_type, [])
        return self.has_any_role(allowed_roles)
    
    def update_last_login(self) -> None:
        """Update the last login timestamp."""
        self.last_login = datetime.utcnow()
        self.updated_at = datetime.utcnow()