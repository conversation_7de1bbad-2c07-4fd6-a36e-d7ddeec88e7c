"""Pydantic models for structured AI outputs."""

from typing import List, Optional, Dict
from pydantic import BaseModel, Field
from enum import Enum

class BusinessModelType(str, Enum):
    B2B = "B2B"
    B2C = "B2C"
    B2B2C = "B2B2C"
    MARKETPLACE = "Marketplace"
    SAAS = "SaaS"
    PLATFORM = "Platform"
    SUBSCRIPTION = "Subscription"
    TRANSACTIONAL = "Transactional"
    FREEMIUM = "Freemium"

class InvestmentStage(str, Enum):
    PRE_SEED = "Pre-seed"
    SEED = "Seed"
    SERIES_A = "Series A"
    SERIES_B = "Series B"
    SERIES_C = "Series C"
    GROWTH = "Growth"
    LATE_STAGE = "Late Stage"

class TechnologyStack(BaseModel):
    """Extracted technology stack information."""
    languages: List[str] = Field(default_factory=list, description="Programming languages used")
    frameworks: List[str] = Field(default_factory=list, description="Frameworks and libraries")
    infrastructure: List[str] = Field(default_factory=list, description="Infrastructure technologies")
    ai_ml_tools: List[str] = Field(default_factory=list, description="AI/ML specific tools")

class BusinessModel(BaseModel):
    """Extracted business model information."""
    type: BusinessModelType = Field(description="Primary business model type")
    revenue_streams: List[str] = Field(default_factory=list, description="Revenue generation methods")
    target_market: str = Field(description="Primary target market")
    pricing_model: Optional[str] = Field(None, description="Pricing strategy")

class StartupAnalysis(BaseModel):
    """Complete analysis of a startup from description."""
    company_summary: str = Field(
        default="",
        description="Brief summary of the company"
    )
    sectors: List[str] = Field(
        default_factory=list,
        description="Industry sectors the startup operates in"
    )
    key_technologies: List[str] = Field(
        default_factory=list,
        description="Key technologies used by the startup"
    )
    technologies: TechnologyStack = Field(
        default_factory=TechnologyStack,
        description="Technology stack analysis"
    )
    business_model: BusinessModel = Field(
        description="Business model analysis"
    )
    market_opportunity: str = Field(
        default="",
        description="Market opportunity description"
    )
    competitive_advantages: List[str] = Field(
        default_factory=list,
        description="Key competitive advantages"
    )
    team_strengths: List[str] = Field(
        default_factory=list,
        description="Team strengths and expertise"
    )
    keywords: List[str] = Field(
        default_factory=list,
        description="Key terms and concepts"
    )
    risk_factors: List[str] = Field(
        default_factory=list,
        description="Key risk factors"
    )
    value_proposition: str = Field(
        description="Core value proposition"
    )
    target_customers: List[str] = Field(
        default_factory=list,
        description="Target customer segments"
    )
    competitive_advantages: List[str] = Field(
        default_factory=list,
        description="Key competitive advantages"
    )
    confidence_score: float = Field(
        ge=0.0, le=1.0,
        description="Confidence in the analysis"
    )

class InvestmentFocus(BaseModel):
    """VC investment focus areas."""
    sectors: List[str] = Field(default_factory=list, description="Target sectors")
    stages: List[InvestmentStage] = Field(default_factory=list, description="Investment stages")
    technologies: List[str] = Field(default_factory=list, description="Technologies of interest")
    geographical_focus: List[str] = Field(default_factory=list, description="Geographic regions")
    
class VCThesisAnalysis(BaseModel):
    """Extracted VC investment thesis from website content."""
    thesis_summary: str = Field(description="Summary of investment thesis")
    investment_focus: InvestmentFocus = Field(description="Investment focus areas")
    check_size_range: Dict[str, float] = Field(
        default_factory=dict,
        description="Check size range (min/max in USD)"
    )
    portfolio_themes: List[str] = Field(
        default_factory=list,
        description="Common themes in portfolio"
    )
    avoided_sectors: List[str] = Field(
        default_factory=list,
        description="Sectors they avoid investing in"
    )
    key_criteria: List[str] = Field(
        default_factory=list,
        description="Key investment criteria"
    )
    notable_partners: List[str] = Field(
        default_factory=list,
        description="Notable partners mentioned"
    )
    confidence_score: float = Field(
        ge=0.0, le=1.0,
        description="Confidence in the analysis"
    )