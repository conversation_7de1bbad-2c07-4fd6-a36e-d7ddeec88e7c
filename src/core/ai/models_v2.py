"""Updated Pydantic models for structured AI outputs.

These models are designed to work with the ports/adapters pattern
and provide the fields expected by the LangChain adapter.
"""
from typing import List, Optional, Dict
from pydantic import BaseModel, Field
from enum import Enum


class Stage(str, Enum):
    """Investment stages."""
    PRE_SEED = "pre_seed"
    SEED = "seed"
    SERIES_A = "series_a"
    SERIES_B = "series_b"
    SERIES_C = "series_c"
    SERIES_D_PLUS = "series_d_plus"
    
    @property
    def display_name(self) -> str:
        """Get human-readable display name."""
        return self.value.replace('_', ' ').title()


class StartupAnalysis(BaseModel):
    """Analysis of a startup with all required fields."""
    
    # Core analysis fields
    company_summary: str = Field(description="Brief summary of the company")
    key_technologies: List[str] = Field(default_factory=list, description="Key technologies used")
    market_opportunity: str = Field(description="Market opportunity description")
    competitive_advantages: List[str] = Field(default_factory=list, description="Competitive advantages")
    team_strengths: List[str] = Field(default_factory=list, description="Team strengths")
    risk_factors: List[str] = Field(default_factory=list, description="Risk factors")
    
    # Scoring fields (0-10 scale)
    growth_potential: float = Field(ge=0, le=10, description="Growth potential score")
    innovation_score: float = Field(ge=0, le=10, description="Innovation score")
    market_fit_score: float = Field(ge=0, le=10, description="Market fit score")
    
    # Additional fields
    sectors: List[str] = Field(default_factory=list, description="Industry sectors")
    target_customers: List[str] = Field(default_factory=list, description="Target customer segments")
    revenue_model: str = Field(description="Revenue model")


class InvestmentFocus(BaseModel):
    """VC investment focus areas."""
    sectors: List[str] = Field(default_factory=list, description="Target sectors")
    stages: List[Stage] = Field(default_factory=list, description="Investment stages")
    themes: List[str] = Field(default_factory=list, description="Investment themes")


class VCThesisAnalysis(BaseModel):
    """Extracted VC investment thesis from website content."""
    thesis_summary: str = Field(description="Summary of investment thesis")
    investment_focus: InvestmentFocus = Field(description="Investment focus areas")
    check_size_range: Optional[Dict[str, int]] = Field(None, description="Check size range (min/max)")
    portfolio_themes: List[str] = Field(default_factory=list, description="Portfolio themes")
    investment_criteria: List[str] = Field(default_factory=list, description="Investment criteria")
    notable_investments: List[str] = Field(default_factory=list, description="Notable investments")
    investment_pace: str = Field(default="", description="Investment pace")