"""Rate limiting for AI API calls."""

import asyncio
import time
from typing import Optional, Dict, Any
from collections import deque
import logging

from .exceptions import AIRateLimitError

logger = logging.getLogger(__name__)

class RateLimiter:
    """Token bucket rate limiter for API calls."""
    
    def __init__(
        self,
        requests_per_minute: int = 20,
        burst_size: Optional[int] = None
    ):
        self.requests_per_minute = requests_per_minute
        self.burst_size = burst_size or requests_per_minute
        self.tokens = self.burst_size
        self.last_update = time.time()
        self.lock = asyncio.Lock()
        
        # Track request history for monitoring
        self.request_history = deque(maxlen=100)
        
    async def acquire(self, timeout: float = 30.0) -> bool:
        """
        Acquire a token for making a request.
        
        Args:
            timeout: Maximum time to wait for a token
            
        Returns:
            True if token acquired
            
        Raises:
            AIRateLimitError if timeout exceeded
        """
        start_time = time.time()
        
        while True:
            async with self.lock:
                # Update tokens based on time passed
                now = time.time()
                elapsed = now - self.last_update
                tokens_to_add = elapsed * (self.requests_per_minute / 60.0)
                
                self.tokens = min(
                    self.burst_size,
                    self.tokens + tokens_to_add
                )
                self.last_update = now
                
                # Check if we have a token available
                if self.tokens >= 1:
                    self.tokens -= 1
                    self.request_history.append(now)
                    return True
            
            # Check timeout
            if time.time() - start_time > timeout:
                raise AIRateLimitError(
                    f"Rate limit timeout after {timeout}s. "
                    f"Current rate: {self.requests_per_minute} req/min"
                )
            
            # Wait before trying again
            await asyncio.sleep(0.1)
    
    def get_current_rate(self) -> float:
        """Get current request rate (requests per minute)."""
        now = time.time()
        recent_requests = [
            t for t in self.request_history
            if now - t < 60
        ]
        return len(recent_requests)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get rate limiter statistics."""
        return {
            "current_tokens": self.tokens,
            "max_tokens": self.burst_size,
            "requests_per_minute": self.requests_per_minute,
            "current_rate": self.get_current_rate(),
            "total_requests": len(self.request_history)
        }

class AdaptiveRateLimiter(RateLimiter):
    """
    Adaptive rate limiter that adjusts based on error rates.
    """
    
    def __init__(
        self,
        initial_rpm: int = 20,
        min_rpm: int = 5,
        max_rpm: int = 60,
        error_threshold: float = 0.1
    ):
        super().__init__(requests_per_minute=initial_rpm)
        self.min_rpm = min_rpm
        self.max_rpm = max_rpm
        self.error_threshold = error_threshold
        
        # Track successes and errors
        self.success_count = 0
        self.error_count = 0
        self.last_adjustment = time.time()
        
    async def record_success(self):
        """Record a successful API call."""
        async with self.lock:
            self.success_count += 1
            await self._maybe_adjust_rate()
    
    async def record_error(self):
        """Record a failed API call."""
        async with self.lock:
            self.error_count += 1
            await self._maybe_adjust_rate()
    
    async def _maybe_adjust_rate(self):
        """Adjust rate based on error rate."""
        # Only adjust every 60 seconds
        now = time.time()
        if now - self.last_adjustment < 60:
            return
        
        total_requests = self.success_count + self.error_count
        if total_requests < 10:  # Need minimum samples
            return
        
        error_rate = self.error_count / total_requests
        
        if error_rate > self.error_threshold:
            # Decrease rate
            new_rpm = max(
                self.min_rpm,
                int(self.requests_per_minute * 0.8)
            )
            if new_rpm != self.requests_per_minute:
                logger.warning(
                    f"Decreasing rate limit from {self.requests_per_minute} "
                    f"to {new_rpm} RPM (error rate: {error_rate:.2%})"
                )
                self.requests_per_minute = new_rpm
                
        elif error_rate < self.error_threshold / 2:
            # Increase rate
            new_rpm = min(
                self.max_rpm,
                int(self.requests_per_minute * 1.2)
            )
            if new_rpm != self.requests_per_minute:
                logger.info(
                    f"Increasing rate limit from {self.requests_per_minute} "
                    f"to {new_rpm} RPM (error rate: {error_rate:.2%})"
                )
                self.requests_per_minute = new_rpm
        
        # Reset counters
        self.success_count = 0
        self.error_count = 0
        self.last_adjustment = now