"""Configuration for AI analyzer service."""

import os
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings

class AIConfig(BaseSettings):
    """AI service configuration."""
    
    # OpenAI settings
    openai_api_key: str = Field(..., env="OPENAI_API_KEY")
    openai_model: str = Field("gpt-4", env="OPENAI_MODEL")
    temperature: float = Field(0.3, env="AI_TEMPERATURE")
    
    # Cache settings
    redis_host: str = Field("localhost", env="REDIS_HOST")
    redis_port: int = Field(6379, env="REDIS_PORT")
    redis_db: int = Field(0, env="REDIS_DB")
    cache_ttl: int = Field(86400, env="AI_CACHE_TTL")  # 24 hours
    
    # Retry settings
    max_retries: int = Field(3, env="AI_MAX_RETRIES")
    retry_min_wait: int = Field(4, env="AI_RETRY_MIN_WAIT")
    retry_max_wait: int = Field(10, env="AI_RETRY_MAX_WAIT")
    
    # Rate limiting
    max_concurrent_requests: int = Field(5, env="AI_MAX_CONCURRENT")
    requests_per_minute: int = Field(20, env="AI_REQUESTS_PER_MINUTE")
    
    # Feature flags
    enable_streaming: bool = Field(False, env="AI_ENABLE_STREAMING")
    enable_cache: bool = Field(True, env="AI_ENABLE_CACHE")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"  # Ignore extra fields from .env file

# Singleton configuration instance
ai_config = AIConfig()