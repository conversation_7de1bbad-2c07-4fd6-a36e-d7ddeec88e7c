"""Custom exceptions for AI analyzer service."""

class AIAnalyzerError(Exception):
    """Base exception for AI analyzer errors."""
    pass

class AIModelError(AIAnalyzerError):
    """Error in AI model processing."""
    pass

class AIRateLimitError(AIAnalyzerError):
    """Rate limit exceeded error."""
    pass

class AICacheError(AIAnalyzerError):
    """Cache operation error."""
    pass

class AIValidationError(AIAnalyzerError):
    """Validation error for AI inputs/outputs."""
    pass

class AITimeoutError(AIAnalyzerError):
    """Timeout error for AI operations."""
    pass