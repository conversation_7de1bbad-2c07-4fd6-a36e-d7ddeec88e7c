"""Caching implementation for AI operations."""

import json
import hashlib
import asyncio
from typing import Optional, Any, Dict
from datetime import datetime, timedelta
import redis
from redis.exceptions import RedisError
import logging

from .models import StartupAnalysis, VCThesisAnalysis

logger = logging.getLogger(__name__)

class AICache:
    """Redis-based cache for AI analysis results."""
    
    def __init__(
        self,
        redis_client: Optional[redis.Redis] = None,
        default_ttl: int = 86400,  # 24 hours
        key_prefix: str = "ai_analysis"
    ):
        self.redis_client = redis_client or redis.Redis(
            host='localhost',
            port=6379,
            decode_responses=True
        )
        self.default_ttl = default_ttl
        self.key_prefix = key_prefix
        
    def _generate_key(self, operation: str, content: str, **kwargs) -> str:
        """Generate a cache key based on operation and content."""
        # Create a unique key from operation type and content hash
        content_dict = {
            "operation": operation,
            "content": content,
            **kwargs
        }
        content_str = json.dumps(content_dict, sort_keys=True)
        content_hash = hashlib.sha256(content_str.encode()).hexdigest()
        
        return f"{self.key_prefix}:{operation}:{content_hash}"
    
    def _get_startup_cache_key(self, description: str, name: str, sector: str, stage: str) -> str:
        """Generate cache key for startup analysis."""
        return self._generate_key(
            "startup",
            description,
            name=name,
            sector=sector,
            stage=stage
        )
    
    def _get_vc_cache_key(self, website_content: str, firm_name: str) -> str:
        """Generate cache key for VC thesis."""
        return self._generate_key(
            "vc",
            website_content,
            firm_name=firm_name
        )
    
    async def get_startup_analysis(
        self,
        description: str,
        name: str,
        sector: str,
        stage: str
    ) -> Optional[StartupAnalysis]:
        """Get cached startup analysis if available."""
        try:
            key = self._generate_key(
                "startup_analysis",
                description,
                name=name,
                sector=sector,
                stage=stage
            )
            
            cached_data = await self.redis_client.get(key) if asyncio.iscoroutinefunction(self.redis_client.get) else self.redis_client.get(key)
            if cached_data:
                data = json.loads(cached_data)
                return StartupAnalysis(**data)
            return None
            
        except (RedisError, json.JSONDecodeError) as e:
            logger.warning(f"Cache retrieval error: {str(e)}")
            return None
    
    async def set_startup_analysis(
        self,
        description: str,
        name: str,
        sector: str,
        stage: str,
        analysis: StartupAnalysis,
        ttl: Optional[int] = None
    ) -> bool:
        """Cache startup analysis result."""
        try:
            key = self._generate_key(
                "startup_analysis",
                description,
                name=name,
                sector=sector,
                stage=stage
            )
            
            ttl = ttl or self.default_ttl
            data = analysis.model_dump_json()
            
            if asyncio.iscoroutinefunction(self.redis_client.setex):
                await self.redis_client.setex(key, ttl, data)
            else:
                self.redis_client.setex(key, ttl, data)
            return True
            
        except RedisError as e:
            logger.warning(f"Cache storage error: {str(e)}")
            return False
    
    async def get_vc_thesis(
        self,
        website_content: str,
        firm_name: str
    ) -> Optional[VCThesisAnalysis]:
        """Get cached VC thesis analysis if available."""
        try:
            key = self._generate_key(
                "vc_thesis",
                website_content,
                firm_name=firm_name
            )
            
            cached_data = await self.redis_client.get(key) if asyncio.iscoroutinefunction(self.redis_client.get) else self.redis_client.get(key)
            if cached_data:
                data = json.loads(cached_data)
                return VCThesisAnalysis(**data)
            return None
            
        except (RedisError, json.JSONDecodeError) as e:
            logger.warning(f"Cache retrieval error: {str(e)}")
            return None
    
    async def set_vc_thesis(
        self,
        website_content: str,
        firm_name: str,
        analysis: VCThesisAnalysis,
        ttl: Optional[int] = None
    ) -> bool:
        """Cache VC thesis analysis result."""
        try:
            key = self._generate_key(
                "vc_thesis",
                website_content,
                firm_name=firm_name
            )
            
            ttl = ttl or self.default_ttl
            data = analysis.model_dump_json()
            
            if asyncio.iscoroutinefunction(self.redis_client.setex):
                await self.redis_client.setex(key, ttl, data)
            else:
                self.redis_client.setex(key, ttl, data)
            return True
            
        except RedisError as e:
            logger.warning(f"Cache storage error: {str(e)}")
            return False
    
    async def invalidate_startup_cache(
        self,
        description: str,
        name: str,
        sector: str,
        stage: str
    ) -> bool:
        """Invalidate specific startup cache entry."""
        try:
            key = self._get_startup_cache_key(
                description=description,
                name=name,
                sector=sector,
                stage=stage
            )
            if asyncio.iscoroutinefunction(self.redis_client.delete):
                result = await self.redis_client.delete(key)
            else:
                result = self.redis_client.delete(key)
            return result > 0
        except RedisError as e:
            logger.warning(f"Cache invalidation error: {str(e)}")
            return False
    
    async def invalidate_vc_cache(
        self,
        website_content: str,
        firm_name: str
    ) -> bool:
        """Invalidate specific VC cache entry."""
        try:
            key = self._get_vc_cache_key(
                website_content=website_content,
                firm_name=firm_name
            )
            if asyncio.iscoroutinefunction(self.redis_client.delete):
                result = await self.redis_client.delete(key)
            else:
                result = self.redis_client.delete(key)
            return result > 0
        except RedisError as e:
            logger.warning(f"Cache invalidation error: {str(e)}")
            return False
    
    def clear_cache(self, pattern: Optional[str] = None) -> int:
        """Clear cache entries matching pattern."""
        try:
            if pattern:
                full_pattern = f"{self.key_prefix}:{pattern}:*"
            else:
                full_pattern = f"{self.key_prefix}:*"
            
            keys = self.redis_client.keys(full_pattern)
            if keys:
                return self.redis_client.delete(*keys)
            return 0
            
        except RedisError as e:
            logger.error(f"Cache clear error: {str(e)}")
            return 0
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        try:
            info = self.redis_client.info("stats")
            keys = self.redis_client.keys(f"{self.key_prefix}:*")
            
            return {
                "total_keys": len(keys),
                "hits": info.get("keyspace_hits", 0),
                "misses": info.get("keyspace_misses", 0),
                "hit_rate": (
                    info.get("keyspace_hits", 0) / 
                    (info.get("keyspace_hits", 0) + info.get("keyspace_misses", 1))
                ) if info.get("keyspace_hits", 0) > 0 else 0
            }
            
        except RedisError as e:
            logger.error(f"Cache stats error: {str(e)}")
            return {
                "total_keys": 0,
                "hits": 0,
                "misses": 0,
                "hit_rate": 0
            }