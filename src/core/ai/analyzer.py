"""Main AI analyzer service with error handling and retries."""

import asyncio
from typing import Optional, Dict, Any, List
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type
)
from langchain_openai import ChatOpenAI
from langchain_core.callbacks import AsyncCallbackHandler
import logging
import time
from datetime import datetime

from ..models.startup import Startup
from ..models.vc import VC
from .models import StartupAnalysis, VCThesisAnalysis
from .chains import StartupAnalyzer<PERSON>hain, VCThesisExtractorChain
from .cache import AICache

logger = logging.getLogger(__name__)

class TokenUsageCallback(AsyncCallbackHandler):
    """Callback to track token usage."""
    
    def __init__(self):
        self.total_tokens = 0
        self.prompt_tokens = 0
        self.completion_tokens = 0
        self.total_cost = 0.0
        
    async def on_llm_end(self, response, **kwargs):
        """Calculate tokens and cost when LLM finishes."""
        if hasattr(response, 'llm_output') and response.llm_output:
            usage = response.llm_output.get('token_usage', {})
            self.total_tokens += usage.get('total_tokens', 0)
            self.prompt_tokens += usage.get('prompt_tokens', 0)
            self.completion_tokens += usage.get('completion_tokens', 0)
            
            # Rough cost calculation (GPT-4 pricing)
            self.total_cost += (
                self.prompt_tokens * 0.03 / 1000 +
                self.completion_tokens * 0.06 / 1000
            )


class AIAnalyzerService:
    """Main service for AI-powered analysis of startups and VCs."""
    
    def __init__(
        self,
        openai_api_key: Optional[str] = None,
        model_name: str = "gpt-4",
        temperature: float = 0.3,
        cache: Optional[AICache] = None,
        max_retries: int = 3,
        enable_streaming: bool = False
    ):
        self.llm = ChatOpenAI(
            openai_api_key=openai_api_key,
            model_name=model_name,
            temperature=temperature,
            streaming=enable_streaming
        )
        
        self.startup_chain = StartupAnalyzerChain(llm=self.llm)
        self.vc_chain = VCThesisExtractorChain(llm=self.llm)
        self.cache = cache or AICache()
        self.max_retries = max_retries
        self.token_tracker = TokenUsageCallback()
        
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((Exception,))
    )
    async def analyze_startup(
        self,
        startup: Startup,
        use_cache: bool = True,
        cache_ttl: Optional[int] = None
    ) -> StartupAnalysis:
        """
        Analyze a startup to extract key information.
        
        Args:
            startup: Startup object to analyze
            use_cache: Whether to use cached results
            cache_ttl: Cache TTL in seconds
            
        Returns:
            StartupAnalysis with extracted information
        """
        start_time = time.time()
        
        # Check cache first
        if use_cache:
            try:
                cached_result = await self.cache.get_startup_analysis(
                    description=startup.description,
                    name=startup.name,
                    sector=startup.sector,
                    stage=startup.stage
                )
                if cached_result:
                    logger.info(f"Cache hit for startup: {startup.name}")
                    return cached_result
            except Exception as e:
                logger.warning(f"Cache error, proceeding without cache: {str(e)}")
        
        try:
            # Perform analysis with token tracking
            callbacks = [self.token_tracker]
            
            analysis = await self.startup_chain.analyze(
                description=startup.description,
                name=startup.name,
                sector=startup.sector,
                stage=startup.stage,
                website=startup.website,
                callbacks=callbacks
            )
            
            # Cache the result
            if use_cache:
                try:
                    await self.cache.set_startup_analysis(
                        description=startup.description,
                        name=startup.name,
                        sector=startup.sector,
                        stage=startup.stage,
                        analysis=analysis,
                        ttl=cache_ttl
                    )
                except Exception as e:
                    logger.warning(f"Failed to cache analysis: {str(e)}")
            
            # Log performance metrics
            elapsed_time = time.time() - start_time
            logger.info(
                f"Analyzed startup {startup.name} in {elapsed_time:.2f}s "
                f"(tokens: {self.token_tracker.total_tokens}, "
                f"cost: ${self.token_tracker.total_cost:.4f})"
            )
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing startup {startup.name}: {str(e)}")
            raise
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((Exception,))
    )
    async def extract_vc_thesis(
        self,
        vc: VC,
        website_content: str,
        use_cache: bool = True,
        cache_ttl: Optional[int] = None
    ) -> VCThesisAnalysis:
        """
        Extract VC investment thesis from website content.
        
        Args:
            vc: VC object
            website_content: Scraped website content
            use_cache: Whether to use cached results
            cache_ttl: Cache TTL in seconds
            
        Returns:
            VCThesisAnalysis with extracted information
        """
        start_time = time.time()
        
        # Check cache first
        if use_cache:
            cached_result = await self.cache.get_vc_thesis(
                website_content=website_content,
                firm_name=vc.firm_name
            )
            if cached_result:
                logger.info(f"Cache hit for VC: {vc.firm_name}")
                return cached_result
        
        try:
            # Perform analysis with token tracking
            callbacks = [self.token_tracker]
            
            analysis = await self.vc_chain.extract(
                website_content=website_content,
                firm_name=vc.firm_name,
                callbacks=callbacks
            )
            
            # Cache the result
            if use_cache:
                await self.cache.set_vc_thesis(
                    website_content=website_content,
                    firm_name=vc.firm_name,
                    analysis=analysis,
                    ttl=cache_ttl
                )
            
            # Update VC object with extracted data
            vc.thesis = analysis.thesis_summary
            vc.sectors = analysis.investment_focus.sectors
            vc.stages = [stage.value for stage in analysis.investment_focus.stages]
            
            if analysis.check_size_range:
                vc.check_size_min = analysis.check_size_range.get('min', 0)
                vc.check_size_max = analysis.check_size_range.get('max', 0)
            
            # Log performance metrics
            elapsed_time = time.time() - start_time
            logger.info(
                f"Extracted thesis for {vc.firm_name} in {elapsed_time:.2f}s "
                f"(tokens: {self.token_tracker.total_tokens}, "
                f"cost: ${self.token_tracker.total_cost:.4f})"
            )
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error extracting thesis for {vc.firm_name}: {str(e)}")
            raise
    
    async def batch_analyze_startups(
        self,
        startups: List[Startup],
        use_cache: bool = True,
        max_concurrent: int = 5
    ) -> List[StartupAnalysis]:
        """
        Analyze multiple startups concurrently with rate limiting.
        
        Args:
            startups: List of startups to analyze
            use_cache: Whether to use cached results
            max_concurrent: Maximum concurrent analyses
            
        Returns:
            List of StartupAnalysis results
        """
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def analyze_with_semaphore(startup):
            async with semaphore:
                try:
                    return await self.analyze_startup(startup, use_cache=use_cache)
                except Exception as e:
                    logger.error(f"Failed to analyze {startup.name}: {str(e)}")
                    return None
        
        tasks = [analyze_with_semaphore(startup) for startup in startups]
        results = await asyncio.gather(*tasks)
        
        # Filter out failed analyses
        return [r for r in results if r is not None]
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """Get token usage and cost statistics."""
        return {
            "total_tokens": self.token_tracker.total_tokens,
            "prompt_tokens": self.token_tracker.prompt_tokens,
            "completion_tokens": self.token_tracker.completion_tokens,
            "total_cost": self.token_tracker.total_cost,
            "cache_stats": self.cache.get_cache_stats()
        }
    
    def reset_usage_stats(self):
        """Reset token usage statistics."""
        self.token_tracker = TokenUsageCallback()