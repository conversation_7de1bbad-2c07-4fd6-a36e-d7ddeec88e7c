"""Embeddings service for hybrid search functionality."""

from typing import List, Dict, Any, Optional, Tuple
import asyncio
import logging
from datetime import datetime
import numpy as np
from tenacity import retry, stop_after_attempt, wait_exponential

from langchain_openai import OpenAIEmbeddings
from sqlalchemy.orm import Session
from sqlalchemy import text

from src.core.models.startup import Startup as StartupDomain
from src.core.models.vc import VC as VCDomain
from src.core.config import Settings

logger = logging.getLogger(__name__)


class HybridEmbeddingService:
    """Service for generating and managing embeddings for hybrid search."""
    
    def __init__(self, openai_api_key: str):
        """Initialize with OpenAI API key."""
        self.embeddings = OpenAIEmbeddings(
            openai_api_key=openai_api_key,
            model="text-embedding-3-small",  # 1536 dimensions, cost-effective
            chunk_size=1000,
            max_retries=3
        )
        self._cache = {}  # Simple in-memory cache
        
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def generate_startup_embeddings(self, startup: StartupDomain) -> Dict[str, List[float]]:
        """Generate embeddings for startup description and combined text."""
        
        # Description embedding
        description_text = startup.description or ""
        
        # Combined embedding (richer context for better matching)
        combined_text = self._create_startup_combined_text(startup)
        
        try:
            # Check cache first
            cache_key = f"startup_{startup.id}"
            if cache_key in self._cache:
                logger.info(f"Using cached embeddings for startup {startup.name}")
                return self._cache[cache_key]
            
            # Generate embeddings in parallel
            embeddings = await asyncio.gather(
                self.embeddings.aembed_query(description_text),
                self.embeddings.aembed_query(combined_text)
            )
            
            result = {
                "description_vector": embeddings[0],
                "combined_vector": embeddings[1]
            }
            
            # Cache the result
            self._cache[cache_key] = result
            
            logger.info(f"Generated embeddings for startup {startup.name}")
            return result
            
        except Exception as e:
            logger.error(f"Error generating embeddings for startup {startup.name}: {e}")
            raise
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def generate_vc_embeddings(self, vc: VCDomain) -> Dict[str, List[float]]:
        """Generate embeddings for VC thesis and combined text."""
        
        # Thesis embedding
        thesis_text = vc.thesis or ""
        
        # Combined embedding
        combined_text = self._create_vc_combined_text(vc)
        
        try:
            # Check cache first
            cache_key = f"vc_{vc.id}"
            if cache_key in self._cache:
                logger.info(f"Using cached embeddings for VC {vc.firm_name}")
                return self._cache[cache_key]
            
            # Generate embeddings in parallel
            embeddings = await asyncio.gather(
                self.embeddings.aembed_query(thesis_text),
                self.embeddings.aembed_query(combined_text)
            )
            
            result = {
                "thesis_vector": embeddings[0],
                "combined_vector": embeddings[1]
            }
            
            # Cache the result
            self._cache[cache_key] = result
            
            logger.info(f"Generated embeddings for VC {vc.firm_name}")
            return result
            
        except Exception as e:
            logger.error(f"Error generating embeddings for VC {vc.firm_name}: {e}")
            raise

    async def generate_query_embedding(self, query_text: str) -> List[float]:
        """Generate embedding for search query."""
        try:
            # Check cache
            cache_key = f"query_{hash(query_text)}"
            if cache_key in self._cache:
                return self._cache[cache_key]
            
            embedding = await self.embeddings.aembed_query(query_text)
            
            # Cache with limited size
            if len(self._cache) < 1000:  # Simple cache size limit
                self._cache[cache_key] = embedding
            
            return embedding
            
        except Exception as e:
            logger.error(f"Error generating query embedding: {e}")
            raise
    
    def _create_startup_combined_text(self, startup: StartupDomain) -> str:
        """Create combined text representation of startup for embedding."""
        # Extract sectors if available
        sectors = startup.extract_sectors() if hasattr(startup, 'extract_sectors') else [startup.sector]
        
        combined_text = f"""
        Company: {startup.name}
        Sector: {', '.join(sectors)}
        Stage: {startup.stage}
        Description: {startup.description or 'No description available'}
        Team Size: {startup.team_size} employees
        Monthly Revenue: ${startup.monthly_revenue:,.0f}
        """
        
        # Add additional context if available
        if hasattr(startup, 'website') and startup.website:
            combined_text += f"\nWebsite: {startup.website}"
            
        return combined_text.strip()
    
    def _create_vc_combined_text(self, vc: VCDomain) -> str:
        """Create combined text representation of VC for embedding."""
        sectors_text = ", ".join(vc.sectors) if vc.sectors else "All sectors"
        stages_text = ", ".join(vc.stages) if vc.stages else "All stages"
        
        combined_text = f"""
        VC Firm: {vc.firm_name}
        Investment Thesis: {vc.thesis or 'General technology investments'}
        Focus Sectors: {sectors_text}
        Investment Stages: {stages_text}
        Check Size: ${vc.check_size_min:,.0f} - ${vc.check_size_max:,.0f}
        """
        
        # Add portfolio context if available
        if hasattr(vc, 'portfolio_companies') and vc.portfolio_companies:
            portfolio_sample = vc.portfolio_companies[:5]  # First 5 for context
            combined_text += f"\nPortfolio includes: {', '.join(portfolio_sample)}"
            
        return combined_text.strip()
    
    @staticmethod
    def vector_to_string(vector: List[float]) -> str:
        """Convert vector to PostgreSQL-compatible string format."""
        return f"[{','.join(map(str, vector))}]"
    
    @staticmethod
    def string_to_vector(vector_str: str) -> List[float]:
        """Convert PostgreSQL vector string back to list of floats."""
        if not vector_str or vector_str == 'null':
            return []
        # Remove brackets and split
        vector_str = vector_str.strip('[]')
        return [float(x) for x in vector_str.split(',')]
    
    async def update_startup_embeddings(
        self, 
        db: Session, 
        startup_id: str,
        embeddings: Dict[str, List[float]]
    ) -> None:
        """Update startup embeddings in database."""
        try:
            update_query = text("""
                UPDATE startups 
                SET 
                    description_vector = :desc_vector,
                    combined_vector = :combined_vector,
                    embedding_updated_at = :updated_at
                WHERE id = :startup_id
            """)
            
            db.execute(update_query, {
                "desc_vector": self.vector_to_string(embeddings["description_vector"]),
                "combined_vector": self.vector_to_string(embeddings["combined_vector"]),
                "updated_at": datetime.utcnow(),
                "startup_id": startup_id
            })
            db.commit()
            
            logger.info(f"Updated embeddings for startup {startup_id}")
            
        except Exception as e:
            logger.error(f"Failed to update embeddings for startup {startup_id}: {e}")
            db.rollback()
            raise
    
    async def update_vc_embeddings(
        self,
        db: Session,
        vc_id: str,
        embeddings: Dict[str, List[float]]
    ) -> None:
        """Update VC embeddings in database."""
        try:
            update_query = text("""
                UPDATE vcs 
                SET 
                    thesis_vector = :thesis_vector,
                    combined_vector = :combined_vector,
                    embedding_updated_at = :updated_at
                WHERE id = :vc_id
            """)
            
            db.execute(update_query, {
                "thesis_vector": self.vector_to_string(embeddings["thesis_vector"]),
                "combined_vector": self.vector_to_string(embeddings["combined_vector"]),
                "updated_at": datetime.utcnow(),
                "vc_id": vc_id
            })
            db.commit()
            
            logger.info(f"Updated embeddings for VC {vc_id}")
            
        except Exception as e:
            logger.error(f"Failed to update embeddings for VC {vc_id}: {e}")
            db.rollback()
            raise
    
    def calculate_similarity(self, vector1: List[float], vector2: List[float]) -> float:
        """Calculate cosine similarity between two vectors."""
        if not vector1 or not vector2:
            return 0.0
            
        # Convert to numpy arrays
        v1 = np.array(vector1)
        v2 = np.array(vector2)
        
        # Calculate cosine similarity
        dot_product = np.dot(v1, v2)
        norm_product = np.linalg.norm(v1) * np.linalg.norm(v2)
        
        if norm_product == 0:
            return 0.0
            
        return float(dot_product / norm_product)


class EmbeddingBatchProcessor:
    """Process embeddings in batches for better performance."""
    
    def __init__(self, embedding_service: HybridEmbeddingService, batch_size: int = 20):
        self.embedding_service = embedding_service
        self.batch_size = batch_size
        
    async def process_startup_batch(
        self, 
        db: Session,
        startups: List[Tuple[str, StartupDomain]]
    ) -> int:
        """Process a batch of startups to generate embeddings."""
        successful = 0
        
        for startup_id, startup in startups:
            try:
                # Set the ID for the domain object
                startup.id = startup_id
                
                # Generate embeddings
                embeddings = await self.embedding_service.generate_startup_embeddings(startup)
                
                # Update in database
                await self.embedding_service.update_startup_embeddings(
                    db, startup_id, embeddings
                )
                
                successful += 1
                
                # Small delay to avoid rate limits
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Failed to process startup {startup_id}: {e}")
                continue
                
        return successful
    
    async def process_vc_batch(
        self,
        db: Session,
        vcs: List[Tuple[str, VCDomain]]
    ) -> int:
        """Process a batch of VCs to generate embeddings."""
        successful = 0
        
        for vc_id, vc in vcs:
            try:
                # Set the ID for the domain object
                vc.id = vc_id
                
                # Generate embeddings
                embeddings = await self.embedding_service.generate_vc_embeddings(vc)
                
                # Update in database
                await self.embedding_service.update_vc_embeddings(
                    db, vc_id, embeddings
                )
                
                successful += 1
                
                # Small delay to avoid rate limits
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Failed to process VC {vc_id}: {e}")
                continue
                
        return successful