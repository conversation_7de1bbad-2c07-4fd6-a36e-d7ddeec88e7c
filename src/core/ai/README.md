# AI Analyzer Service

The AI Analyzer Service provides intelligent analysis of startup descriptions and VC investment theses using LangChain and OpenAI GPT-4. It enhances the VC-startup matching platform with natural language understanding and structured data extraction.

## Features

### Core Capabilities
- **Startup Analysis**: Extract sectors, technologies, business models, and value propositions from descriptions
- **VC Thesis Extraction**: Analyze VC websites to understand investment focus and criteria  
- **Structured Outputs**: Use Pydantic models for type-safe, validated results
- **Caching**: Redis-based caching for expensive AI operations
- **Rate Limiting**: Adaptive rate limiting to handle API quotas
- **Streaming**: Real-time streaming responses for better UX
- **Error Handling**: Comprehensive retry logic with exponential backoff

### Integration Features
- **Enhanced Matching**: AI-powered matching that goes beyond keyword matching
- **Batch Processing**: Efficient batch analysis with concurrency control
- **Token Tracking**: Monitor usage and costs
- **Confidence Scoring**: Each analysis includes confidence metrics

## Architecture

```
src/core/ai/
├── __init__.py          # Package exports
├── models.py            # Pydantic models for structured outputs
├── chains.py            # LangChain chains for analysis
├── analyzer.py          # Main AI analyzer service
├── cache.py             # Redis caching implementation
├── rate_limiter.py      # Adaptive rate limiting
├── streaming.py         # Streaming response handlers
├── integration.py       # Integration with existing services
├── exceptions.py        # Custom exceptions
├── config.py            # Configuration management
└── examples.py          # Usage examples
```

## Installation

1. Install required dependencies:
```bash
pip install -r requirements.txt
```

2. Set up environment variables:
```bash
export OPENAI_API_KEY="your-openai-api-key"
export REDIS_HOST="localhost"
export REDIS_PORT="6379"
```

## Usage

### Basic Startup Analysis

```python
from src.core.models.startup import Startup
from src.core.ai.analyzer import AIAnalyzerService

# Initialize the service
ai_analyzer = AIAnalyzerService(
    openai_api_key="your-key",
    model_name="gpt-4",
    temperature=0.3
)

# Analyze a startup
startup = Startup(
    name="TechCo",
    sector="AI/ML",
    stage="Seed",
    description="Building AI-powered analytics..."
)

analysis = await ai_analyzer.analyze_startup(startup)
print(f"Sectors: {analysis.sectors}")
print(f"Business Model: {analysis.business_model.type}")
```

### VC Thesis Extraction

```python
from src.core.models.vc import VC

vc = VC(firm_name="Innovation Capital")
website_content = "We invest in B2B SaaS startups..."

thesis = await ai_analyzer.extract_vc_thesis(
    vc, 
    website_content
)
print(f"Investment Focus: {thesis.investment_focus.sectors}")
```

### AI-Enhanced Matching

```python
from src.core.ai.integration import AIEnhancedMatchingEngine

# Create enhanced matching engine
ai_matching = AIEnhancedMatchingEngine(
    matching_engine,
    ai_analyzer
)

# Find AI-enhanced matches
matches = await ai_matching.find_matches_with_ai(
    startup,
    vcs,
    min_score=0.7
)
```

### Streaming Analysis

```python
from src.core.ai.streaming import StreamingAnalyzer

streaming = StreamingAnalyzer(ai_analyzer)

async for chunk in streaming.stream_startup_analysis(startup):
    print(chunk, end="", flush=True)
```

## Configuration

The service can be configured via environment variables or the `AIConfig` class:

| Variable | Description | Default |
|----------|-------------|---------|
| `OPENAI_API_KEY` | OpenAI API key | Required |
| `OPENAI_MODEL` | Model to use | gpt-4 |
| `AI_TEMPERATURE` | Model temperature | 0.3 |
| `REDIS_HOST` | Redis host | localhost |
| `REDIS_PORT` | Redis port | 6379 |
| `AI_CACHE_TTL` | Cache TTL in seconds | 86400 |
| `AI_MAX_RETRIES` | Max retry attempts | 3 |
| `AI_MAX_CONCURRENT` | Max concurrent requests | 5 |

## Error Handling

The service includes comprehensive error handling:

- **Retry Logic**: Automatic retries with exponential backoff
- **Rate Limiting**: Prevents API quota exhaustion
- **Fallbacks**: Gracefully degrades when AI is unavailable
- **Validation**: Input/output validation with Pydantic

## Performance Considerations

1. **Caching**: Results are cached for 24 hours by default
2. **Batch Processing**: Use `batch_analyze_startups()` for multiple items
3. **Token Usage**: Monitor costs with `get_usage_stats()`
4. **Concurrency**: Control with `max_concurrent` parameter

## Best Practices

1. **Use Caching**: Always enable caching for production
2. **Handle Failures**: AI-enhanced features should gracefully degrade
3. **Monitor Usage**: Track token usage and costs
4. **Rate Limiting**: Use adaptive rate limiting for resilience
5. **Structured Outputs**: Always use Pydantic models for type safety

## Testing

Run tests with:
```bash
pytest tests/unit/test_ai_analyzer.py -v
```

## Contributing

When adding new AI features:
1. Define Pydantic models for outputs
2. Create focused prompt templates
3. Add proper error handling
4. Include caching support
5. Write comprehensive tests