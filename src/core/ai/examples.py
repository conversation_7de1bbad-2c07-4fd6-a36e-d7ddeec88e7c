"""Examples of using the AI analyzer service."""

import asyncio
import os
from typing import List

from ..models.startup import Startup
from ..models.vc import VC
from ..services.matching_engine import MatchingEngine
from .analyzer import AIAnalyzerService
from .integration import AIEnhancedMatchingEngine
from .streaming import StreamingAnalyzer
from .rate_limiter import AdaptiveRateLimiter
from .cache import AICache

async def basic_startup_analysis_example():
    """Example of analyzing a startup description."""
    
    # Initialize the AI analyzer
    ai_analyzer = AIAnalyzerService(
        openai_api_key=os.getenv("OPENAI_API_KEY"),
        model_name="gpt-4",
        temperature=0.3
    )
    
    # Create a sample startup
    startup = Startup(
        name="TechVision AI",
        sector="AI/ML",
        stage="Seed",
        description="""
        TechVision AI is building an advanced computer vision platform for 
        manufacturing quality control. Our solution uses deep learning models 
        trained on millions of product images to detect defects 10x faster 
        than human inspectors. We serve B2B enterprise customers in automotive 
        and electronics manufacturing, with a SaaS subscription model starting 
        at $10k/month. Our proprietary edge computing technology allows 
        real-time inference on the factory floor.
        """,
        website="https://techvision.ai",
        team_size=12,
        monthly_revenue=50000
    )
    
    # Analyze the startup
    analysis = await ai_analyzer.analyze_startup(startup)
    
    print(f"Startup Analysis for {startup.name}:")
    print(f"Sectors: {', '.join(analysis.sectors)}")
    print(f"Technologies: {', '.join(analysis.technologies.ai_ml_tools)}")
    print(f"Business Model: {analysis.business_model.type.value}")
    print(f"Value Proposition: {analysis.value_proposition}")
    print(f"Target Customers: {', '.join(analysis.target_customers)}")
    print(f"Confidence Score: {analysis.confidence_score:.2f}")
    
    # Get usage statistics
    stats = ai_analyzer.get_usage_stats()
    print(f"\nToken Usage: {stats['total_tokens']} tokens")
    print(f"Cost: ${stats['total_cost']:.4f}")
    
    return analysis

async def vc_thesis_extraction_example():
    """Example of extracting VC investment thesis."""
    
    # Initialize the AI analyzer
    ai_analyzer = AIAnalyzerService(
        openai_api_key=os.getenv("OPENAI_API_KEY"),
        model_name="gpt-4",
        temperature=0.3
    )
    
    # Create a sample VC
    vc = VC(
        firm_name="Innovation Ventures",
        website="https://innovation.vc"
    )
    
    # Sample website content (in real use, this would come from a scraper)
    website_content = """
    Innovation Ventures is a seed-stage venture capital firm focused on 
    B2B SaaS and AI/ML startups. We lead rounds from $500K to $2M and 
    look for technical founders solving real business problems.
    
    Our Portfolio:
    - DataSync (B2B data integration platform) - Series A
    - MLOps Pro (Machine learning operations) - Seed
    - CloudGuard (Cloud security SaaS) - Series B
    
    Investment Criteria:
    - Strong technical founding team
    - Clear path to $10M ARR
    - Defensible technology moat
    - B2B focus with enterprise potential
    
    We avoid: consumer apps, hardware, biotech, and crypto.
    
    Partners: John Smith (ex-Google), Sarah Johnson (ex-Salesforce)
    """
    
    # Extract thesis
    analysis = await ai_analyzer.extract_vc_thesis(vc, website_content)
    
    print(f"\nVC Thesis Analysis for {vc.firm_name}:")
    print(f"Thesis Summary: {analysis.thesis_summary}")
    print(f"Target Sectors: {', '.join(analysis.investment_focus.sectors)}")
    print(f"Stages: {', '.join([s.value for s in analysis.investment_focus.stages])}")
    print(f"Check Size: ${analysis.check_size_range.get('min', 0):,} - ${analysis.check_size_range.get('max', 0):,}")
    print(f"Avoided Sectors: {', '.join(analysis.avoided_sectors)}")
    print(f"Key Criteria: {', '.join(analysis.key_criteria[:3])}")
    
    return analysis

async def streaming_analysis_example():
    """Example of streaming AI analysis for real-time feedback."""
    
    # Initialize services
    ai_analyzer = AIAnalyzerService(
        openai_api_key=os.getenv("OPENAI_API_KEY"),
        enable_streaming=True
    )
    streaming_analyzer = StreamingAnalyzer(ai_analyzer)
    
    # Create a sample startup
    startup = Startup(
        name="StreamTech",
        sector="SaaS",
        stage="Pre-seed",
        description="Building real-time data streaming infrastructure..."
    )
    
    print("Streaming analysis in progress...\n")
    
    # Stream the analysis
    async for chunk in streaming_analyzer.stream_startup_analysis(startup):
        if "---FINAL_ANALYSIS---" in chunk:
            print("\n\nFinal structured analysis received!")
        else:
            print(chunk, end="", flush=True)

async def ai_enhanced_matching_example():
    """Example of AI-enhanced startup-VC matching."""
    
    # Initialize services
    cache = AICache()
    ai_analyzer = AIAnalyzerService(
        openai_api_key=os.getenv("OPENAI_API_KEY"),
        cache=cache
    )
    
    matching_engine = MatchingEngine()
    ai_matching = AIEnhancedMatchingEngine(matching_engine, ai_analyzer)
    
    # Create sample data
    startup = Startup(
        name="AIHealthTech",
        sector="Healthcare",
        stage="Seed",
        description="""
        AIHealthTech uses machine learning to predict patient readmission 
        risk and optimize hospital resource allocation. Our B2B SaaS platform 
        integrates with existing EMR systems and has shown 30% reduction in 
        readmissions for our pilot hospitals.
        """
    )
    
    vcs = [
        VC(
            firm_name="HealthTech Ventures",
            thesis="Focus on AI/ML applications in healthcare",
            sectors=["Healthcare", "AI/ML"],
            stages=["Seed", "Series A"],
            check_size_min=500000,
            check_size_max=3000000
        ),
        VC(
            firm_name="General Partners",
            thesis="Generalist fund investing in B2B SaaS",
            sectors=["B2B SaaS", "Enterprise"],
            stages=["Seed", "Series A", "Series B"],
            check_size_min=1000000,
            check_size_max=5000000
        ),
        VC(
            firm_name="AI First Capital",
            thesis="Deep tech AI companies with enterprise applications",
            sectors=["AI/ML", "Deep Tech", "Enterprise"],
            stages=["Pre-seed", "Seed"],
            check_size_min=250000,
            check_size_max=1000000
        )
    ]
    
    # Find matches with AI enhancement
    matches = await ai_matching.find_matches_with_ai(
        startup,
        vcs,
        min_score=0.6,
        use_ai_enhancement=True
    )
    
    print(f"\nAI-Enhanced Matches for {startup.name}:")
    for i, match in enumerate(matches, 1):
        print(f"\n{i}. {match.vc_name} (Score: {match.score:.2f})")
        print("   Reasons:")
        for reason in match.reasons:
            print(f"   - {reason}")
        if match.metadata.get("sectors_matched"):
            print(f"   Matched Sectors: {', '.join(match.metadata['sectors_matched'])}")

async def batch_analysis_with_rate_limiting():
    """Example of batch analysis with rate limiting."""
    
    # Initialize services with rate limiting
    rate_limiter = AdaptiveRateLimiter(
        initial_rpm=20,
        min_rpm=5,
        max_rpm=60
    )
    
    ai_analyzer = AIAnalyzerService(
        openai_api_key=os.getenv("OPENAI_API_KEY"),
        max_retries=3
    )
    
    # Create multiple startups
    startups = [
        Startup(
            name=f"Startup{i}",
            sector="Tech",
            stage="Seed",
            description=f"Building innovative solution #{i}..."
        )
        for i in range(10)
    ]
    
    print("Analyzing batch of startups with rate limiting...\n")
    
    # Analyze with rate limiting
    results = []
    for startup in startups:
        # Acquire rate limit token
        await rate_limiter.acquire()
        
        try:
            analysis = await ai_analyzer.analyze_startup(startup)
            await rate_limiter.record_success()
            results.append(analysis)
            print(f"✓ Analyzed {startup.name}")
        except Exception as e:
            await rate_limiter.record_error()
            print(f"✗ Failed to analyze {startup.name}: {str(e)}")
    
    # Show rate limiter stats
    stats = rate_limiter.get_stats()
    print(f"\nRate Limiter Stats:")
    print(f"Current Rate: {stats['current_rate']} requests/min")
    print(f"Total Requests: {stats['total_requests']}")

async def main():
    """Run all examples."""
    print("=== AI Analyzer Service Examples ===\n")
    
    # Example 1: Basic startup analysis
    print("1. Basic Startup Analysis")
    print("-" * 50)
    await basic_startup_analysis_example()
    
    # Example 2: VC thesis extraction
    print("\n\n2. VC Thesis Extraction")
    print("-" * 50)
    await vc_thesis_extraction_example()
    
    # Example 3: AI-enhanced matching
    print("\n\n3. AI-Enhanced Matching")
    print("-" * 50)
    await ai_enhanced_matching_example()
    
    # Example 4: Streaming analysis
    print("\n\n4. Streaming Analysis")
    print("-" * 50)
    await streaming_analysis_example()
    
    # Example 5: Batch analysis with rate limiting
    print("\n\n5. Batch Analysis with Rate Limiting")
    print("-" * 50)
    await batch_analysis_with_rate_limiting()

if __name__ == "__main__":
    # Make sure to set OPENAI_API_KEY environment variable
    asyncio.run(main())