"""Streaming response handler for real-time AI feedback."""

import async<PERSON>
from typing import <PERSON>ync<PERSON><PERSON><PERSON>, Optional, Callable, Any
from langchain_core.callbacks import <PERSON>yncCallbackHandler
from langchain_core.outputs import LLMResult
import json
import logging

logger = logging.getLogger(__name__)

class StreamingCallback(AsyncCallbackHandler):
    """Callback handler for streaming LLM responses."""
    
    def __init__(self, on_token: Optional[Callable[[str], None]] = None):
        self.on_token = on_token
        self.tokens = []
        
    async def on_llm_new_token(self, token: str, **kwargs) -> None:
        """Handle new token from LLM."""
        self.tokens.append(token)
        if self.on_token:
            await self.on_token(token)
    
    async def on_llm_end(self, response: LLMResult, **kwargs) -> None:
        """Handle LLM completion."""
        full_response = "".join(self.tokens)
        logger.debug(f"Streaming complete. Total tokens: {len(self.tokens)}")
    
    def get_full_response(self) -> str:
        """Get the complete response."""
        return "".join(self.tokens)

class StreamingAnalyzer:
    """Wrapper for streaming AI analysis responses."""
    
    def __init__(self, analyzer_service):
        self.analyzer = analyzer_service
        
    async def stream_startup_analysis(
        self,
        startup,
        chunk_size: int = 10
    ) -> AsyncIterator[str]:
        """
        Stream startup analysis in chunks.
        
        Args:
            startup: Startup to analyze
            chunk_size: Number of tokens per chunk
            
        Yields:
            String chunks of the analysis
        """
        buffer = []
        
        async def handle_token(token: str):
            buffer.append(token)
            if len(buffer) >= chunk_size:
                chunk = "".join(buffer)
                buffer.clear()
                return chunk
            return None
        
        streaming_callback = StreamingCallback(on_token=handle_token)
        
        # Enable streaming on the LLM temporarily
        original_streaming = self.analyzer.llm.streaming
        self.analyzer.llm.streaming = True
        
        try:
            # Run analysis with streaming callback
            analysis = await self.analyzer.analyze_startup(
                startup,
                callbacks=[streaming_callback]
            )
            
            # Yield any remaining tokens
            if buffer:
                yield "".join(buffer)
            
            # Yield the final structured result
            yield f"\n\n---FINAL_ANALYSIS---\n{analysis.model_dump_json(indent=2)}"
            
        finally:
            # Restore original streaming setting
            self.analyzer.llm.streaming = original_streaming
    
    async def stream_vc_thesis(
        self,
        vc,
        website_content: str,
        chunk_size: int = 10
    ) -> AsyncIterator[str]:
        """
        Stream VC thesis extraction in chunks.
        
        Args:
            vc: VC firm to analyze
            website_content: Website content to analyze
            chunk_size: Number of tokens per chunk
            
        Yields:
            String chunks of the analysis
        """
        buffer = []
        
        async def handle_token(token: str):
            buffer.append(token)
            if len(buffer) >= chunk_size:
                chunk = "".join(buffer)
                buffer.clear()
                return chunk
            return None
        
        streaming_callback = StreamingCallback(on_token=handle_token)
        
        # Enable streaming on the LLM temporarily
        original_streaming = self.analyzer.llm.streaming
        self.analyzer.llm.streaming = True
        
        try:
            # Run analysis with streaming callback
            analysis = await self.analyzer.extract_vc_thesis(
                vc,
                website_content,
                callbacks=[streaming_callback]
            )
            
            # Yield any remaining tokens
            if buffer:
                yield "".join(buffer)
            
            # Yield the final structured result
            yield f"\n\n---FINAL_ANALYSIS---\n{analysis.model_dump_json(indent=2)}"
            
        finally:
            # Restore original streaming setting
            self.analyzer.llm.streaming = original_streaming