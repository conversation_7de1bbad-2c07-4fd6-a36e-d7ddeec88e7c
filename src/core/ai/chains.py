"""LangChain chains for analyzing startups and VCs."""

from typing import Optional, Dict, Any
from langchain_openai import Chat<PERSON>penAI
from langchain_core.prompts import PromptTemplate
from langchain.output_parsers import PydanticOutputParser
from langchain_core.runnables import RunnablePassthrough
from langchain_core.callbacks import AsyncCallbackHandler
import logging

from .models import StartupAnalysis, VCThesisAnalysis

logger = logging.getLogger(__name__)

class StartupAnalyzerChain:
    """Chain for analyzing startup descriptions."""
    
    def __init__(self, llm: Optional[ChatOpenAI] = None, temperature: float = 0.3):
        self.llm = llm or ChatOpenAI(temperature=temperature, model="gpt-4")
        self.parser = PydanticOutputParser(pydantic_object=StartupAnalysis)
        
        self.prompt = PromptTemplate(
            template="""You are an expert startup analyst. Analyze the following startup description and extract structured information.

Startup Description:
{description}

Additional Context:
- Company Name: {name}
- Stated Sector: {sector}
- Stage: {stage}
- Website: {website}

Instructions:
1. Identify all relevant industry sectors (be specific, e.g., "B2B SaaS", "Fintech", "Healthcare AI")
2. Extract technology stack mentions (programming languages, frameworks, infrastructure)
3. Determine the business model type and revenue streams
4. Identify key value propositions and competitive advantages
5. Extract target customer segments
6. Rate your confidence in the analysis (0-1)

{format_instructions}

Provide a comprehensive analysis based on the information available.""",
            input_variables=["description", "name", "sector", "stage", "website"],
            partial_variables={"format_instructions": self.parser.get_format_instructions()}
        )
        
        self.chain = (
            RunnablePassthrough() |
            self.prompt |
            self.llm |
            self.parser
        )
    
    async def analyze(
        self,
        description: str,
        name: str,
        sector: str,
        stage: str,
        website: str = "",
        callbacks: Optional[list] = None
    ) -> StartupAnalysis:
        """Analyze a startup description and extract structured information."""
        try:
            result = await self.chain.ainvoke(
                {
                    "description": description,
                    "name": name,
                    "sector": sector,
                    "stage": stage,
                    "website": website
                },
                config={"callbacks": callbacks} if callbacks else None
            )
            return result
        except Exception as e:
            logger.error(f"Error analyzing startup {name}: {str(e)}")
            raise


class VCThesisExtractorChain:
    """Chain for extracting VC investment thesis from website content."""
    
    def __init__(self, llm: Optional[ChatOpenAI] = None, temperature: float = 0.3):
        self.llm = llm or ChatOpenAI(temperature=temperature, model="gpt-4")
        self.parser = PydanticOutputParser(pydantic_object=VCThesisAnalysis)
        
        self.prompt = PromptTemplate(
            template="""You are an expert at analyzing VC firm investment strategies. Extract the investment thesis from the following website content.

VC Firm: {firm_name}
Website Content:
{website_content}

Instructions:
1. Summarize their investment thesis in 2-3 sentences
2. Identify target sectors, stages, and technologies
3. Extract check size ranges if mentioned (convert to USD)
4. Identify portfolio themes and patterns
5. Note any sectors or types of companies they explicitly avoid
6. Extract key investment criteria they look for
7. Identify notable partners or team members mentioned
8. Rate your confidence in the analysis (0-1)

If information is not explicitly stated, make reasonable inferences based on portfolio companies or team backgrounds mentioned.

{format_instructions}

Provide a thorough analysis of their investment strategy.""",
            input_variables=["firm_name", "website_content"],
            partial_variables={"format_instructions": self.parser.get_format_instructions()}
        )
        
        self.chain = (
            RunnablePassthrough() |
            self.prompt |
            self.llm |
            self.parser
        )
    
    async def extract(
        self,
        website_content: str,
        firm_name: str,
        callbacks: Optional[list] = None
    ) -> VCThesisAnalysis:
        """Extract VC investment thesis from website content."""
        try:
            result = await self.chain.ainvoke(
                {
                    "website_content": website_content,
                    "firm_name": firm_name
                },
                config={"callbacks": callbacks} if callbacks else None
            )
            return result
        except Exception as e:
            logger.error(f"Error extracting thesis for {firm_name}: {str(e)}")
            raise