"""Integration with existing platform services."""

from typing import Optional, Dict, Any, List
import logging
from datetime import datetime

from ..models.startup import Startup
from ..models.vc import VC
from ..models.match import Match
from ..services.matching_engine import MatchingEngine
from .analyzer import AIAnalyzerService
from .models import StartupAnalysis, VCThesisAnalysis

logger = logging.getLogger(__name__)

class AIEnhancedMatchingEngine:
    """
    Enhanced matching engine that uses AI analysis for better matches.
    """
    
    def __init__(
        self,
        matching_engine: MatchingEngine,
        ai_analyzer: AIAnalyzerService
    ):
        self.matching_engine = matching_engine
        self.ai_analyzer = ai_analyzer
        
    async def find_matches_with_ai(
        self,
        startup: Startup,
        vcs: List[VC],
        min_score: float = 0.7,
        use_ai_enhancement: bool = True
    ) -> List[Match]:
        """
        Find matches using both traditional matching and AI analysis.
        
        Args:
            startup: Startup to match
            vcs: List of VCs to match against
            min_score: Minimum match score
            use_ai_enhancement: Whether to use AI for enhanced matching
            
        Returns:
            List of matches sorted by score
        """
        # Get traditional matches first
        traditional_matches = self.matching_engine.find_matches(startup, vcs)
        
        if not use_ai_enhancement:
            return traditional_matches
        
        try:
            # Analyze startup with AI
            startup_analysis = await self.ai_analyzer.analyze_startup(startup)
            
            # Enhanced matching with AI insights
            enhanced_matches = []
            
            for match in traditional_matches:
                vc = next((v for v in vcs if v.firm_name == match.vc_name), None)
                if not vc:
                    continue
                
                # Calculate enhanced score
                enhanced_score = await self._calculate_enhanced_score(
                    startup,
                    vc,
                    startup_analysis,
                    match.score
                )
                
                # Create enhanced match with AI-generated reasons
                reasons = await self._generate_match_reasons(
                    startup,
                    vc,
                    startup_analysis,
                    enhanced_score
                )
                
                enhanced_match = Match(
                    startup_name=startup.name,
                    vc_name=vc.firm_name,
                    score=enhanced_score,
                    reasons=reasons,
                    metadata={
                        "ai_enhanced": True,
                        "original_score": match.score,
                        "confidence": startup_analysis.confidence_score,
                        "sectors_matched": list(
                            set(startup_analysis.sectors) & set(vc.sectors)
                        ),
                        "timestamp": datetime.utcnow().isoformat()
                    }
                )
                
                if enhanced_score >= min_score:
                    enhanced_matches.append(enhanced_match)
            
            # Sort by enhanced score
            enhanced_matches.sort(key=lambda m: m.score, reverse=True)
            
            logger.info(
                f"Enhanced {len(enhanced_matches)} matches for {startup.name} "
                f"using AI analysis (confidence: {startup_analysis.confidence_score:.2f})"
            )
            
            return enhanced_matches
            
        except Exception as e:
            logger.error(f"AI enhancement failed, falling back to traditional: {str(e)}")
            return traditional_matches
    
    async def _calculate_enhanced_score(
        self,
        startup: Startup,
        vc: VC,
        analysis: StartupAnalysis,
        base_score: float
    ) -> float:
        """Calculate enhanced match score using AI insights."""
        score_components = {
            "base": base_score * 0.4,  # 40% weight to traditional score
            "sector_match": 0.0,
            "technology_match": 0.0,
            "business_model_match": 0.0,
            "stage_match": 0.0
        }
        
        # Sector matching (20% weight)
        common_sectors = set(analysis.sectors) & set(vc.sectors)
        if common_sectors:
            score_components["sector_match"] = 0.2 * (
                len(common_sectors) / max(len(analysis.sectors), len(vc.sectors))
            )
        
        # Technology matching (15% weight)
        vc_tech_keywords = self._extract_tech_keywords(vc.thesis)
        startup_techs = (
            analysis.technologies.languages +
            analysis.technologies.frameworks +
            analysis.technologies.ai_ml_tools
        )
        
        tech_matches = sum(
            1 for tech in startup_techs
            if any(keyword in tech.lower() for keyword in vc_tech_keywords)
        )
        
        if tech_matches:
            score_components["technology_match"] = 0.15 * min(1.0, tech_matches / 3)
        
        # Business model matching (15% weight)
        if self._matches_business_model(analysis.business_model, vc.thesis):
            score_components["business_model_match"] = 0.15
        
        # Stage matching (10% weight)
        if vc.can_invest_in_stage(startup.stage):
            score_components["stage_match"] = 0.1
        
        # Calculate total enhanced score
        enhanced_score = sum(score_components.values())
        
        # Apply confidence adjustment
        enhanced_score *= (0.8 + 0.2 * analysis.confidence_score)
        
        return min(1.0, enhanced_score)
    
    def _extract_tech_keywords(self, thesis: str) -> List[str]:
        """Extract technology keywords from VC thesis."""
        tech_keywords = [
            "ai", "ml", "machine learning", "deep learning",
            "blockchain", "crypto", "web3",
            "saas", "cloud", "api",
            "mobile", "ios", "android",
            "data", "analytics", "big data",
            "iot", "hardware", "robotics"
        ]
        
        thesis_lower = thesis.lower()
        return [kw for kw in tech_keywords if kw in thesis_lower]
    
    def _matches_business_model(self, business_model, thesis: str) -> bool:
        """Check if business model matches VC preferences."""
        thesis_lower = thesis.lower()
        model_keywords = {
            "B2B": ["b2b", "enterprise", "business"],
            "B2C": ["b2c", "consumer", "retail"],
            "SaaS": ["saas", "subscription", "recurring"],
            "Marketplace": ["marketplace", "platform", "two-sided"],
        }
        
        model_type = business_model.type.value
        if model_type in model_keywords:
            return any(kw in thesis_lower for kw in model_keywords[model_type])
        
        return False
    
    async def _generate_match_reasons(
        self,
        startup: Startup,
        vc: VC,
        analysis: StartupAnalysis,
        score: float
    ) -> List[str]:
        """Generate detailed reasons for the match using AI insights."""
        reasons = []
        
        # Sector alignment
        common_sectors = set(analysis.sectors) & set(vc.sectors)
        if common_sectors:
            reasons.append(
                f"Strong sector alignment in: {', '.join(common_sectors)}"
            )
        
        # Value proposition fit
        if analysis.value_proposition:
            reasons.append(
                f"Value proposition aligns with portfolio: {analysis.value_proposition[:100]}..."
            )
        
        # Technology fit
        if analysis.technologies.ai_ml_tools and "ai" in vc.thesis.lower():
            reasons.append(
                f"AI/ML technology stack matches VC focus: "
                f"{', '.join(analysis.technologies.ai_ml_tools[:3])}"
            )
        
        # Business model fit
        reasons.append(
            f"{analysis.business_model.type.value} model targeting "
            f"{analysis.business_model.target_market}"
        )
        
        # Competitive advantages
        if analysis.competitive_advantages:
            reasons.append(
                f"Key differentiators: {', '.join(analysis.competitive_advantages[:2])}"
            )
        
        # Stage and funding fit
        if vc.matches_funding_amount(startup.monthly_revenue * 12 * 10):
            reasons.append(
                f"Funding requirements align with {vc.firm_name}'s "
                f"check size range"
            )
        
        return reasons

class AIAnalysisRepository:
    """Repository for storing and retrieving AI analysis results."""
    
    def __init__(self, cache: Optional[Any] = None):
        self.cache = cache
        self._storage: Dict[str, Any] = {}
        
    async def save_startup_analysis(
        self,
        startup_id: str,
        analysis: StartupAnalysis
    ) -> None:
        """Save startup analysis results."""
        key = f"startup_analysis:{startup_id}"
        self._storage[key] = {
            "analysis": analysis.model_dump(),
            "timestamp": datetime.utcnow().isoformat()
        }
        
        if self.cache:
            await self.cache.set(key, analysis.model_dump_json(), ttl=86400)
    
    async def get_startup_analysis(
        self,
        startup_id: str
    ) -> Optional[StartupAnalysis]:
        """Retrieve startup analysis results."""
        key = f"startup_analysis:{startup_id}"
        
        # Try cache first
        if self.cache:
            cached = await self.cache.get(key)
            if cached:
                return StartupAnalysis.model_validate_json(cached)
        
        # Fall back to storage
        if key in self._storage:
            return StartupAnalysis(**self._storage[key]["analysis"])
        
        return None
    
    async def save_vc_analysis(
        self,
        vc_id: str,
        analysis: VCThesisAnalysis
    ) -> None:
        """Save VC thesis analysis results."""
        key = f"vc_analysis:{vc_id}"
        self._storage[key] = {
            "analysis": analysis.model_dump(),
            "timestamp": datetime.utcnow().isoformat()
        }
        
        if self.cache:
            await self.cache.set(key, analysis.model_dump_json(), ttl=86400)
    
    async def get_vc_analysis(
        self,
        vc_id: str
    ) -> Optional[VCThesisAnalysis]:
        """Retrieve VC thesis analysis results."""
        key = f"vc_analysis:{vc_id}"
        
        # Try cache first
        if self.cache:
            cached = await self.cache.get(key)
            if cached:
                return VCThesisAnalysis.model_validate_json(cached)
        
        # Fall back to storage
        if key in self._storage:
            return VCThesisAnalysis(**self._storage[key]["analysis"])
        
        return None