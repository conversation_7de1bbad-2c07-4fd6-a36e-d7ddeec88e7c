"""Enhanced AI caching implementation using clean architecture."""

import json
import hashlib
import asyncio
from typing import Optional, Any, Dict
from datetime import datetime, timedelta
import logging

from .models import StartupAnalysis, VCThesisAnalysis
from src.infrastructure.redis.port import CachePort
from src.infrastructure.redis.adapter import RedisAdapter

logger = logging.getLogger(__name__)


class EnhancedAICache:
    """Enhanced AI cache using clean architecture principles.
    
    This cache implementation uses the CachePort interface,
    making it decoupled from specific cache technologies.
    """
    
    def __init__(
        self,
        cache_adapter: Optional[CachePort] = None,
        default_ttl: int = 86400,  # 24 hours
        key_prefix: str = "ai_analysis"
    ):
        """Initialize enhanced AI cache.
        
        Args:
            cache_adapter: Cache adapter implementing CachePort
            default_ttl: Default time-to-live in seconds
            key_prefix: Prefix for all cache keys
        """
        self.cache = cache_adapter or RedisAdapter(
            default_ttl=default_ttl,
            key_prefix=key_prefix
        )
        self.default_ttl = default_ttl
        self.key_prefix = key_prefix
        
    def _generate_key(self, operation: str, content: str, **kwargs) -> str:
        """Generate a cache key based on operation and content."""
        # Create a unique key from operation type and content hash
        content_dict = {
            "operation": operation,
            "content": content,
            **kwargs
        }
        content_str = json.dumps(content_dict, sort_keys=True)
        content_hash = hashlib.sha256(content_str.encode()).hexdigest()
        
        return f"{operation}:{content_hash}"
    
    async def get_startup_analysis(
        self,
        description: str,
        name: str,
        sector: str,
        stage: str
    ) -> Optional[StartupAnalysis]:
        """Get cached startup analysis if available."""
        try:
            key = self._generate_key(
                "startup_analysis",
                description,
                name=name,
                sector=sector,
                stage=stage
            )
            
            cached_data = await self.cache.get_json(key)
            if cached_data:
                return StartupAnalysis(**cached_data)
            return None
            
        except Exception as e:
            logger.warning(f"Cache retrieval error: {str(e)}")
            return None
    
    async def set_startup_analysis(
        self,
        description: str,
        name: str,
        sector: str,
        stage: str,
        analysis: StartupAnalysis,
        ttl: Optional[int] = None
    ) -> bool:
        """Cache startup analysis result."""
        try:
            key = self._generate_key(
                "startup_analysis",
                description,
                name=name,
                sector=sector,
                stage=stage
            )
            
            ttl_value = ttl or self.default_ttl
            data = analysis.model_dump()
            
            return await self.cache.set_json(key, data, ttl_value)
            
        except Exception as e:
            logger.warning(f"Cache storage error: {str(e)}")
            return False
    
    async def get_vc_thesis(
        self,
        website_content: str,
        firm_name: str
    ) -> Optional[VCThesisAnalysis]:
        """Get cached VC thesis analysis if available."""
        try:
            key = self._generate_key(
                "vc_thesis",
                website_content,
                firm_name=firm_name
            )
            
            cached_data = await self.cache.get_json(key)
            if cached_data:
                return VCThesisAnalysis(**cached_data)
            return None
            
        except Exception as e:
            logger.warning(f"Cache retrieval error: {str(e)}")
            return None
    
    async def set_vc_thesis(
        self,
        website_content: str,
        firm_name: str,
        analysis: VCThesisAnalysis,
        ttl: Optional[int] = None
    ) -> bool:
        """Cache VC thesis analysis result."""
        try:
            key = self._generate_key(
                "vc_thesis",
                website_content,
                firm_name=firm_name
            )
            
            ttl_value = ttl or self.default_ttl
            data = analysis.model_dump()
            
            return await self.cache.set_json(key, data, ttl_value)
            
        except Exception as e:
            logger.warning(f"Cache storage error: {str(e)}")
            return False
    
    async def invalidate_startup_cache(
        self,
        description: str,
        name: str,
        sector: str,
        stage: str
    ) -> bool:
        """Invalidate specific startup cache entry."""
        try:
            key = self._generate_key(
                "startup_analysis",
                description,
                name=name,
                sector=sector,
                stage=stage
            )
            return await self.cache.delete(key)
        except Exception as e:
            logger.warning(f"Cache invalidation error: {str(e)}")
            return False
    
    async def invalidate_vc_cache(
        self,
        website_content: str,
        firm_name: str
    ) -> bool:
        """Invalidate specific VC cache entry."""
        try:
            key = self._generate_key(
                "vc_thesis",
                website_content,
                firm_name=firm_name
            )
            return await self.cache.delete(key)
        except Exception as e:
            logger.warning(f"Cache invalidation error: {str(e)}")
            return False
    
    async def clear_cache(self, pattern: Optional[str] = None) -> int:
        """Clear cache entries matching pattern."""
        try:
            if pattern:
                full_pattern = f"{pattern}:*"
            else:
                full_pattern = "*"
            
            keys = await self.cache.keys(full_pattern)
            if keys:
                return await self.cache.delete_many(keys)
            return 0
            
        except Exception as e:
            logger.error(f"Cache clear error: {str(e)}")
            return 0
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        try:
            return self.cache.get_stats()
        except Exception as e:
            logger.error(f"Cache stats error: {str(e)}")
            return {
                "total_keys": 0,
                "hits": 0,
                "misses": 0,
                "hit_rate": 0,
                "error": str(e)
            }
    
    async def health_check(self) -> bool:
        """Check if cache service is healthy."""
        try:
            return await self.cache.health_check()
        except Exception as e:
            logger.error(f"Cache health check failed: {e}")
            return False
    
    # Additional cache operations
    
    async def cache_analysis_batch(
        self,
        analyses: Dict[str, Any],
        ttl: Optional[int] = None
    ) -> Dict[str, bool]:
        """Cache multiple analyses in batch.
        
        Args:
            analyses: Dict mapping cache keys to analysis objects
            ttl: Time to live for all entries
            
        Returns:
            Dict mapping keys to success status
        """
        results = {}
        ttl_value = ttl or self.default_ttl
        
        for key, analysis in analyses.items():
            try:
                if hasattr(analysis, 'model_dump'):
                    data = analysis.model_dump()
                else:
                    data = analysis
                    
                success = await self.cache.set_json(key, data, ttl_value)
                results[key] = success
                
            except Exception as e:
                logger.error(f"Batch cache error for key {key}: {e}")
                results[key] = False
                
        return results
    
    async def get_or_compute(
        self,
        key: str,
        compute_func,
        ttl: Optional[int] = None,
        **kwargs
    ) -> Any:
        """Get value from cache or compute if not found.
        
        Args:
            key: Cache key
            compute_func: Function to compute value if not cached
            ttl: Time to live for computed value
            **kwargs: Arguments to pass to compute_func
            
        Returns:
            Cached or computed value
        """
        try:
            # Try to get from cache first
            cached_value = await self.cache.get_json(key)
            if cached_value is not None:
                return cached_value
            
            # Compute the value
            if asyncio.iscoroutinefunction(compute_func):
                computed_value = await compute_func(**kwargs)
            else:
                computed_value = compute_func(**kwargs)
            
            # Cache the computed value
            if computed_value is not None:
                ttl_value = ttl or self.default_ttl
                await self.cache.set_json(key, computed_value, ttl_value)
            
            return computed_value
            
        except Exception as e:
            logger.error(f"Get-or-compute error for key {key}: {e}")
            # If caching fails, still try to compute
            try:
                if asyncio.iscoroutinefunction(compute_func):
                    return await compute_func(**kwargs)
                else:
                    return compute_func(**kwargs)
            except Exception as compute_error:
                logger.error(f"Compute function failed: {compute_error}")
                return None