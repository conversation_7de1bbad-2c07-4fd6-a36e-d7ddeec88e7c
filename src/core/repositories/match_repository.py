"""Repository interface for Match entities."""

from abc import ABC, abstractmethod
from typing import List, Optional
from uuid import UUID

from src.core.models.match import Match


class MatchRepository(ABC):
    """Abstract interface for match persistence."""
    
    @abstractmethod
    async def create(self, match: Match) -> Match:
        """Create a new match."""
        pass
    
    @abstractmethod
    async def get(self, match_id: UUID) -> Optional[Match]:
        """Get a match by ID."""
        pass
    
    @abstractmethod
    async def list_by_startup(self, startup_id: UUID) -> List[Match]:
        """List all matches for a startup."""
        pass
    
    @abstractmethod
    async def list_by_vc(self, vc_id: UUID) -> List[Match]:
        """List all matches for a VC."""
        pass
    
    @abstractmethod
    async def delete(self, match_id: UUID) -> bool:
        """Delete a match."""
        pass
    
    @abstractmethod
    async def exists(self, startup_id: UUID, vc_id: UUID) -> bool:
        """Check if a match exists between startup and VC."""
        pass
    
    @abstractmethod
    async def update_score(self, match_id: UUID, score: float) -> Optional[Match]:
        """Update the score of an existing match."""
        pass