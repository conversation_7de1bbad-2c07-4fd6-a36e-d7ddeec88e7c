"""Repository interfaces for data access abstraction.

Following DDD principles, these interfaces define contracts for data access
without any implementation details or infrastructure dependencies.
"""
from .startup_repository import StartupRepository, InMemoryStartupRepository
from .vc_repository import VCRepository, InMemoryVCRepository
from .match_repository import MatchRepository

__all__ = [
    'StartupRepository', 
    'VCRepository',
    'MatchRepository',
    'InMemoryStartupRepository',
    'InMemoryVCRepository'
]