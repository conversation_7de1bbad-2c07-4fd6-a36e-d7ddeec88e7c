"""Repository interface for Startup entity.

This interface follows DDD principles and the Repository pattern.
It provides an abstraction for data access without exposing implementation details.
"""
from typing import Dict, List, Optional, Protocol
from uuid import UUID, uuid4
from dataclasses import replace

from src.core.models.startup import Startup


class StartupRepository(Protocol):
    """Repository interface for Startup aggregate operations.
    
    This interface defines the contract for data access operations
    on Startup entities. Implementations can use any storage mechanism
    (in-memory, database, file system, etc.).
    """
    
    async def save(self, startup: Startup) -> Startup:
        """Save a startup entity.
        
        If the startup doesn't have an ID, one will be assigned.
        If the startup already has an ID, it will be updated.
        
        Args:
            startup: The startup entity to save
            
        Returns:
            The saved startup with ID assigned
        """
        ...
    
    async def find_by_id(self, startup_id: UUID) -> Optional[Startup]:
        """Find a startup by its unique identifier.
        
        Args:
            startup_id: The UUID of the startup
            
        Returns:
            The startup if found, None otherwise
        """
        ...
    
    async def find_by_sector(self, sector: str) -> List[Startup]:
        """Find all startups in a specific sector.
        
        Args:
            sector: The sector to filter by
            
        Returns:
            List of startups in the specified sector
        """
        ...
    
    async def find_by_stage(self, stage: str) -> List[Startup]:
        """Find all startups at a specific funding stage.
        
        Args:
            stage: The funding stage to filter by
            
        Returns:
            List of startups at the specified stage
        """
        ...
    
    async def find_all(self) -> List[Startup]:
        """Retrieve all startups.
        
        Returns:
            List of all startups in the repository
        """
        ...
    
    async def delete(self, startup_id: UUID) -> bool:
        """Delete a startup by its ID.
        
        Args:
            startup_id: The UUID of the startup to delete
            
        Returns:
            True if the startup was deleted, False if not found
        """
        ...


class InMemoryStartupRepository:
    """In-memory implementation of StartupRepository for testing.
    
    This implementation stores startups in a dictionary in memory.
    It's suitable for testing and development but not for production use.
    """
    
    def __init__(self):
        self._storage: Dict[UUID, Startup] = {}
    
    async def save(self, startup: Startup) -> Startup:
        """Save a startup, assigning an ID if needed."""
        if startup.id is None:
            # Create a new startup with an ID
            startup_with_id = replace(startup, id=uuid4())
        else:
            startup_with_id = startup
        
        self._storage[startup_with_id.id] = startup_with_id
        return startup_with_id
    
    async def find_by_id(self, startup_id: UUID) -> Optional[Startup]:
        """Find a startup by ID."""
        return self._storage.get(startup_id)
    
    async def find_by_sector(self, sector: str) -> List[Startup]:
        """Find startups by sector."""
        return [
            startup for startup in self._storage.values()
            if startup.sector == sector
        ]
    
    async def find_by_stage(self, stage: str) -> List[Startup]:
        """Find startups by funding stage."""
        return [
            startup for startup in self._storage.values()
            if startup.stage == stage
        ]
    
    async def find_all(self) -> List[Startup]:
        """Get all startups."""
        return list(self._storage.values())
    
    async def delete(self, startup_id: UUID) -> bool:
        """Delete a startup by ID."""
        if startup_id in self._storage:
            del self._storage[startup_id]
            return True
        return False