"""Repository interface for connection management."""

from abc import ABC, abstractmethod
from typing import List, Optional
from uuid import UUID

from src.core.models.connection import Connection, ConnectionPath, IntroductionRequest


class ConnectionRepository(ABC):
    """Repository interface for connection management."""

    @abstractmethod
    async def create_connection(self, connection: Connection) -> Connection:
        """Create a new connection."""
        pass

    @abstractmethod
    async def get_connection(self, user_a_id: UUID, user_b_id: UUID) -> Optional[Connection]:
        """Get connection between two users."""
        pass

    @abstractmethod
    async def get_user_connections(self, user_id: UUID) -> List[Connection]:
        """Get all connections for a user."""
        pass

    @abstractmethod
    async def update_connection(self, connection: Connection) -> Connection:
        """Update an existing connection."""
        pass

    @abstractmethod
    async def delete_connection(self, connection_id: UUID) -> bool:
        """Soft delete a connection."""
        pass

    @abstractmethod
    async def find_shortest_paths(
        self, 
        source_user_id: UUID, 
        target_user_id: UUID, 
        max_depth: int = 3
    ) -> List[ConnectionPath]:
        """Find shortest paths between two users."""
        pass

    @abstractmethod
    async def get_mutual_connections(self, user_a_id: UUID, user_b_id: UUID) -> List[Connection]:
        """Get mutual connections between two users."""
        pass

    @abstractmethod
    async def search_connections(
        self,
        user_id: UUID,
        filters: dict = None
    ) -> List[Connection]:
        """Search connections with filters."""
        pass


class IntroductionRepository(ABC):
    """Repository interface for introduction requests."""

    @abstractmethod
    async def create_request(self, request: IntroductionRequest) -> IntroductionRequest:
        """Create a new introduction request."""
        pass

    @abstractmethod
    async def get_request(self, request_id: UUID) -> Optional[IntroductionRequest]:
        """Get introduction request by ID."""
        pass

    @abstractmethod
    async def get_pending_requests(self, connector_id: UUID) -> List[IntroductionRequest]:
        """Get pending requests for a connector."""
        pass

    @abstractmethod
    async def update_request(self, request: IntroductionRequest) -> IntroductionRequest:
        """Update introduction request."""
        pass

    @abstractmethod
    async def get_user_requests(self, user_id: UUID) -> List[IntroductionRequest]:
        """Get all requests involving a user."""
        pass

    @abstractmethod
    async def cleanup_expired_requests(self) -> int:
        """Remove expired requests and return count."""
        pass