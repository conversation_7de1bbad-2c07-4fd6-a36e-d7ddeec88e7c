"""Repository interface for VC entity.

This interface follows DDD principles and the Repository pattern.
It provides an abstraction for data access without exposing implementation details.
"""
from typing import Dict, List, Optional, Protocol
from uuid import UUID, uuid4
from dataclasses import replace

from src.core.models.vc import VC


class VCRepository(Protocol):
    """Repository interface for VC aggregate operations.
    
    This interface defines the contract for data access operations
    on VC entities. Implementations can use any storage mechanism
    (in-memory, database, file system, etc.).
    """
    
    async def save(self, vc: VC) -> VC:
        """Save a VC entity.
        
        If the VC doesn't have an ID, one will be assigned.
        If the VC already has an ID, it will be updated.
        
        Args:
            vc: The VC entity to save
            
        Returns:
            The saved VC with ID assigned
        """
        ...
    
    async def find_by_id(self, vc_id: UUID) -> Optional[VC]:
        """Find a VC by its unique identifier.
        
        Args:
            vc_id: The UUID of the VC
            
        Returns:
            The VC if found, None otherwise
        """
        ...
    
    async def find_by_sector(self, sector: str) -> List[VC]:
        """Find all VCs that invest in a specific sector.
        
        Args:
            sector: The sector to filter by
            
        Returns:
            List of VCs that invest in the specified sector
        """
        ...
    
    async def find_by_stage(self, stage: str) -> List[VC]:
        """Find all VCs that invest at a specific funding stage.
        
        Args:
            stage: The funding stage to filter by
            
        Returns:
            List of VCs that invest at the specified stage
        """
        ...
    
    async def find_by_check_size_range(self, amount: float) -> List[VC]:
        """Find all VCs whose check size range includes the given amount.
        
        Args:
            amount: The funding amount to check
            
        Returns:
            List of VCs that can invest the specified amount
        """
        ...
    
    async def find_all(self) -> List[VC]:
        """Retrieve all VCs.
        
        Returns:
            List of all VCs in the repository
        """
        ...
    
    async def delete(self, vc_id: UUID) -> bool:
        """Delete a VC by its ID.
        
        Args:
            vc_id: The UUID of the VC to delete
            
        Returns:
            True if the VC was deleted, False if not found
        """
        ...


class InMemoryVCRepository:
    """In-memory implementation of VCRepository for testing.
    
    This implementation stores VCs in a dictionary in memory.
    It's suitable for testing and development but not for production use.
    """
    
    def __init__(self):
        self._storage: Dict[UUID, VC] = {}
    
    async def save(self, vc: VC) -> VC:
        """Save a VC, assigning an ID if needed."""
        if vc.id is None:
            # Create a new VC with an ID
            vc_with_id = replace(vc, id=uuid4())
        else:
            vc_with_id = vc
        
        self._storage[vc_with_id.id] = vc_with_id
        return vc_with_id
    
    async def find_by_id(self, vc_id: UUID) -> Optional[VC]:
        """Find a VC by ID."""
        return self._storage.get(vc_id)
    
    async def find_by_sector(self, sector: str) -> List[VC]:
        """Find VCs that invest in the specified sector."""
        return [
            vc for vc in self._storage.values()
            if sector in vc.sectors
        ]
    
    async def find_by_stage(self, stage: str) -> List[VC]:
        """Find VCs that invest at the specified stage."""
        return [
            vc for vc in self._storage.values()
            if stage in vc.stages
        ]
    
    async def find_by_check_size_range(self, amount: float) -> List[VC]:
        """Find VCs whose check size range includes the amount."""
        return [
            vc for vc in self._storage.values()
            if vc.matches_funding_amount(amount)
        ]
    
    async def find_all(self) -> List[VC]:
        """Get all VCs."""
        return list(self._storage.values())
    
    async def delete(self, vc_id: UUID) -> bool:
        """Delete a VC by ID."""
        if vc_id in self._storage:
            del self._storage[vc_id]
            return True
        return False