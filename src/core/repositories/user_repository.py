"""Repository interface for user management."""

from abc import ABC, abstractmethod
from typing import List, Optional
from uuid import UUID

from src.core.models.user import User


class UserRepository(ABC):
    """Repository interface for user management."""

    @abstractmethod
    async def create(self, user: User) -> User:
        """Create a new user."""
        pass

    @abstractmethod
    async def get(self, user_id: UUID) -> Optional[User]:
        """Get user by ID."""
        pass

    @abstractmethod
    async def get_by_email(self, email: str) -> Optional[User]:
        """Get user by email."""
        pass

    @abstractmethod
    async def update(self, user: User) -> User:
        """Update user."""
        pass

    @abstractmethod
    async def delete(self, user_id: UUID) -> bool:
        """Delete user."""
        pass

    @abstractmethod
    async def list(self, skip: int = 0, limit: int = 100) -> List[User]:
        """List users with pagination."""
        pass

    @abstractmethod
    async def get_users_by_startup(self, startup_id: UUID) -> List[User]:
        """Get all users associated with a startup."""
        pass

    @abstractmethod
    async def get_users_by_vc(self, vc_id: UUID) -> List[User]:
        """Get all users associated with a VC firm."""
        pass