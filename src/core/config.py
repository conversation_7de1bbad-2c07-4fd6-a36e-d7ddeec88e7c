"""Application configuration using Pydantic Settings."""

from typing import Optional, List
from pydantic_settings import BaseSettings
from pydantic import Field, validator
import os


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # API Configuration
    app_name: str = "VC Matching Platform API"
    app_version: str = "1.0.0"
    api_v1_prefix: str = "/api/v1"
    debug: bool = Field(default=False, env="DEBUG")
    
    # Server Configuration
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    workers: int = Field(default=1, env="WORKERS")
    
    # CORS Configuration
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8080"],
        env="CORS_ORIGINS"
    )
    cors_allow_credentials: bool = True
    cors_allow_methods: List[str] = ["*"]
    cors_allow_headers: List[str] = ["*"]
    
    # Security Configuration
    secret_key: str = Field(
        default=...,  # Required - must be set via environment variable
        env="SECRET_KEY"
    )
    algorithm: str = "HS256"
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    # Database Configuration
    database_url: str = Field(
        default="postgresql://user:password@localhost/vc_matching",
        env="DATABASE_URL"
    )
    database_pool_size: int = Field(default=5, env="DATABASE_POOL_SIZE")
    database_max_overflow: int = Field(default=10, env="DATABASE_MAX_OVERFLOW")
    database_echo: bool = Field(default=False, env="DATABASE_ECHO")
    
    @property
    def async_database_url(self) -> str:
        """Convert sync database URL to async URL."""
        if self.database_url.startswith("postgresql://"):
            return self.database_url.replace("postgresql://", "postgresql+asyncpg://")
        elif self.database_url.startswith("sqlite://"):
            return self.database_url.replace("sqlite://", "sqlite+aiosqlite://")
        return self.database_url
    
    # Redis Configuration
    redis_url: str = Field(
        default="redis://localhost:6379/0",
        env="REDIS_URL"
    )
    redis_ttl: int = Field(default=3600, env="REDIS_TTL")  # 1 hour default
    
    # Celery configuration
    @property
    def celery_broker_url(self) -> str:
        """Celery broker URL (same as Redis URL)."""
        return self.redis_url
    
    @property
    def celery_result_backend(self) -> str:
        """Celery result backend URL (same as Redis URL)."""
        return self.redis_url
    
    # OpenAI Configuration
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    openai_model: str = Field(default="gpt-4", env="OPENAI_MODEL")
    openai_temperature: float = Field(default=0.3, env="OPENAI_TEMPERATURE")
    
    # AI Service Configuration
    ai_max_retries: int = Field(default=3, env="AI_MAX_RETRIES")
    ai_cache_enabled: bool = Field(default=True, env="AI_CACHE_ENABLED")
    ai_cache_ttl: int = Field(default=86400, env="AI_CACHE_TTL")  # 24 hours
    
    # Rate Limiting
    rate_limit_enabled: bool = Field(default=True, env="RATE_LIMIT_ENABLED")
    rate_limit_per_minute: int = Field(default=60, env="RATE_LIMIT_PER_MINUTE")
    rate_limit_per_hour: int = Field(default=1000, env="RATE_LIMIT_PER_HOUR")
    
    # Logging Configuration
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        env="LOG_FORMAT"
    )
    
    # Testing Configuration
    testing: bool = Field(default=False, env="TESTING")
    
    # Async Migration Feature Flag
    use_async_db: bool = Field(default=False, env="USE_ASYNC_DB")
    
    @validator("cors_origins", pre=True)
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("openai_temperature")
    def validate_temperature(cls, v):
        if not 0 <= v <= 2:
            raise ValueError("Temperature must be between 0 and 2")
        return v
    
    @validator("redis_url")
    def validate_redis_url(cls, v):
        """Validate Redis URL format."""
        if not v:
            raise ValueError("Redis URL cannot be empty")
        
        # Basic URL format validation
        if not (v.startswith('redis://') or v.startswith('rediss://')):
            raise ValueError("Redis URL must start with 'redis://' or 'rediss://'")
        
        return v
    
    @validator("redis_ttl")
    def validate_redis_ttl(cls, v):
        """Validate Redis TTL is positive."""
        if v <= 0:
            raise ValueError("Redis TTL must be positive")
        return v
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        extra = "ignore"


settings = Settings()