"""Pydantic schemas for Match entities."""

from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any
from datetime import datetime

# Import enums from domain layer
from src.core.models.enums import MatchStatus, MatchType


class MatchRequest(BaseModel):
    """Request schema for creating a match."""
    
    startup_id: str = Field(..., description="Startup identifier")
    vc_id: str = Field(..., description="VC identifier")
    use_ai_analysis: bool = Field(default=True, description="Use AI for enhanced matching")
    match_type: Optional[MatchType] = Field(default=MatchType.MANUAL, description="Type of match to create")
    notes: Optional[str] = Field(None, max_length=2000, description="Optional notes")


class BatchMatchRequest(BaseModel):
    """Request schema for batch matching."""
    
    startup_ids: List[str] = Field(..., min_items=1, description="List of startup IDs")
    vc_ids: Optional[List[str]] = Field(None, description="Optional list of VC IDs to match against")
    use_ai_analysis: bool = Field(default=True, description="Use AI for enhanced matching")
    min_score_threshold: float = Field(default=0.5, ge=0, le=1, description="Minimum match score")
    max_results_per_startup: int = Field(default=10, ge=1, le=50, description="Max matches per startup")
    match_type: Optional[MatchType] = Field(default=MatchType.AI_ENHANCED, description="Type of match to create")


class MatchBase(BaseModel):
    """Base schema for Match."""
    
    startup_id: str = Field(..., description="Startup identifier")
    vc_id: str = Field(..., description="VC identifier")
    score: float = Field(..., ge=0, le=1, description="Match score (0-1)")
    reasons: List[str] = Field(default_factory=list, description="Matching reasons")
    status: MatchStatus = Field(default=MatchStatus.PENDING, description="Match status")
    match_type: MatchType = Field(default=MatchType.AUTOMATED, description="How the match was created")
    
    @validator("score")
    def validate_score(cls, v):
        return round(v, 3)  # Round to 3 decimal places


class MatchCreate(MatchBase):
    """Schema for creating a new Match."""
    
    ai_insights: Optional[Dict[str, Any]] = Field(None, description="AI-generated insights")


class MatchUpdate(BaseModel):
    """Schema for updating a Match."""
    
    status: Optional[MatchStatus] = None
    notes: Optional[str] = Field(None, max_length=2000)
    next_steps: Optional[str] = Field(None, max_length=1000)


class MatchInDB(MatchBase):
    """Schema for Match stored in database."""
    
    id: str = Field(..., description="Unique identifier")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    notes: Optional[str] = Field(None, description="Internal notes")
    next_steps: Optional[str] = Field(None, description="Next action steps")
    ai_insights: Optional[Dict[str, Any]] = Field(None, description="AI-generated insights")
    
    class Config:
        from_attributes = True


class MatchResponse(MatchInDB):
    """Schema for Match API response with expanded information."""
    
    startup_name: Optional[str] = Field(None, description="Startup name")
    vc_firm_name: Optional[str] = Field(None, description="VC firm name")
    compatibility_breakdown: Dict[str, float] = Field(
        default_factory=dict,
        description="Detailed compatibility scores"
    )
    
    @classmethod
    def from_domain(cls, match):
        """Create response from domain model."""
        # Calculate compatibility breakdown from reasons
        compatibility = {
            "stage_match": 0.0,
            "sector_alignment": 0.0,
            "thesis_fit": 0.0,
            "check_size_fit": 0.0
        }
        
        # Simple heuristic based on reasons (case-insensitive)
        reasons_lower = [r.lower() for r in match.reasons]
        if any("stage match" in r for r in reasons_lower):
            compatibility["stage_match"] = 1.0
        if any("sector alignment" in r for r in reasons_lower):
            compatibility["sector_alignment"] = 0.8
        if any("thesis" in r for r in reasons_lower):
            compatibility["thesis_fit"] = 0.9
        if any("funding amount" in r for r in reasons_lower):
            compatibility["check_size_fit"] = 1.0
        
        # Extract startup and VC IDs
        startup_id = str(match.startup.id) if hasattr(match, 'startup') and match.startup else match.startup_id
        vc_id = str(match.vc.id) if hasattr(match, 'vc') and match.vc else match.vc_id
        
        # Extract names if available
        startup_name = match.startup.name if hasattr(match, 'startup') and hasattr(match.startup, 'name') else None
        vc_firm_name = match.vc.firm_name if hasattr(match, 'vc') and hasattr(match.vc, 'firm_name') else None
            
        return cls(
            id=str(match.id) if match.id else f"{startup_id}_{vc_id}",
            startup_id=startup_id,
            vc_id=vc_id,
            score=match.score,
            reasons=match.reasons,
            status=match.status if match.status is not None else MatchStatus.PENDING,
            match_type=match.match_type if match.match_type is not None else MatchType.AUTOMATED,
            created_at=getattr(match, "created_at", datetime.utcnow()),
            updated_at=getattr(match, "updated_at", datetime.utcnow()),
            notes=getattr(match, "notes", None),
            next_steps=getattr(match, "next_steps", None),
            ai_insights=getattr(match, "ai_insights", None),
            startup_name=startup_name,
            vc_firm_name=vc_firm_name,
            compatibility_breakdown=compatibility
        )


class MatchListResponse(BaseModel):
    """Schema for paginated match list response."""
    
    items: List[MatchResponse] = Field(..., description="List of matches")
    total: int = Field(..., description="Total number of items")
    page: int = Field(..., description="Current page number")
    size: int = Field(..., description="Page size")
    pages: int = Field(..., description="Total number of pages")
    average_score: float = Field(..., description="Average match score")


class MatchSearchParams(BaseModel):
    """Schema for match search parameters."""
    
    startup_id: Optional[str] = Field(None, description="Filter by startup")
    vc_id: Optional[str] = Field(None, description="Filter by VC")
    status: Optional[MatchStatus] = Field(None, description="Filter by status")
    match_type: Optional[MatchType] = Field(None, description="Filter by match type")
    min_score: Optional[float] = Field(None, ge=0, le=1, description="Minimum score")
    max_score: Optional[float] = Field(None, ge=0, le=1, description="Maximum score")
    created_after: Optional[datetime] = Field(None, description="Filter by creation date")
    created_before: Optional[datetime] = Field(None, description="Filter by creation date")