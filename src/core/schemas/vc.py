"""Pydantic schemas for VC entities."""

from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any
from datetime import datetime


class VCBase(BaseModel):
    """Base schema for VC."""
    
    firm_name: str = Field(..., min_length=1, max_length=200, description="VC firm name")
    website: str = Field(default="", max_length=500, description="Firm website URL")
    thesis: str = Field(default="", description="Investment thesis")
    sectors: List[str] = Field(default_factory=list, description="Investment sectors")
    stages: List[str] = Field(default_factory=list, description="Investment stages")
    check_size_min: float = Field(default=0.0, ge=0, description="Minimum check size in USD")
    check_size_max: float = Field(default=0.0, ge=0, description="Maximum check size in USD")
    portfolio_companies: List[str] = Field(default_factory=list, description="Portfolio company names")
    partners: List[str] = Field(default_factory=list, description="Partner names")
    
    @validator("website")
    def validate_website(cls, v):
        if v and not (v.startswith("http://") or v.startswith("https://")):
            v = f"https://{v}"
        return v
    
    @validator("check_size_max")
    def validate_check_sizes(cls, v, values):
        if v > 0 and "check_size_min" in values and values["check_size_min"] > v:
            raise ValueError("Maximum check size must be greater than minimum check size")
        return v
    
    @validator("stages")
    def validate_stages(cls, v):
        valid_stages = ["Pre-seed", "Seed", "Series A", "Series B", "Series C", "Series D+", "Growth", "Pre-IPO"]
        for stage in v:
            if stage not in valid_stages:
                raise ValueError(f"Invalid stage: {stage}. Must be one of: {', '.join(valid_stages)}")
        return v


class VCCreate(VCBase):
    """Schema for creating a new VC."""
    pass


class VCUpdate(BaseModel):
    """Schema for updating a VC."""
    
    firm_name: Optional[str] = Field(None, min_length=1, max_length=200)
    website: Optional[str] = Field(None, max_length=500)
    thesis: Optional[str] = None
    sectors: Optional[List[str]] = None
    stages: Optional[List[str]] = None
    check_size_min: Optional[float] = Field(None, ge=0)
    check_size_max: Optional[float] = Field(None, ge=0)
    portfolio_companies: Optional[List[str]] = None
    partners: Optional[List[str]] = None


class VCInDB(VCBase):
    """Schema for VC stored in database."""
    
    id: str = Field(..., description="Unique identifier")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    
    class Config:
        from_attributes = True


class VCResponse(VCInDB):
    """Schema for VC API response."""
    
    @classmethod
    def from_domain(cls, vc):
        """Create response from domain model."""
        return cls(
            id=str(vc.id) if vc.id else "",
            firm_name=vc.firm_name,
            website=getattr(vc, "website", ""),
            thesis=getattr(vc, "thesis", ""),
            sectors=getattr(vc, "sectors", []),
            stages=getattr(vc, "stages", []),
            check_size_min=getattr(vc, "check_size_min", 0),
            check_size_max=getattr(vc, "check_size_max", 0),
            portfolio_companies=getattr(vc, "portfolio_companies", []),
            partners=getattr(vc, "partners", []),
            created_at=getattr(vc, "created_at", datetime.utcnow()),
            updated_at=getattr(vc, "updated_at", datetime.utcnow())
        )


class VCListResponse(BaseModel):
    """Schema for paginated VC list response."""
    
    items: List[VCResponse] = Field(..., description="List of VCs")
    total: int = Field(..., description="Total number of items")
    page: int = Field(..., description="Current page number")
    size: int = Field(..., description="Page size")
    pages: int = Field(..., description="Total number of pages")


class VCSearchParams(BaseModel):
    """Schema for VC search parameters."""
    
    query: Optional[str] = Field(None, description="Search query")
    sectors: Optional[List[str]] = Field(None, description="Filter by sectors")
    stages: Optional[List[str]] = Field(None, description="Filter by stages")
    min_check_size: Optional[float] = Field(None, ge=0, description="Minimum check size")
    max_check_size: Optional[float] = Field(None, ge=0, description="Maximum check size")


class VCThesisExtractionRequest(BaseModel):
    """Request schema for VC thesis extraction."""
    
    website_content: str = Field(..., description="Pre-scraped website content")
    force_refresh: bool = Field(default=False, description="Force refresh from cache")


class ThesisExtractionResponse(BaseModel):
    """Response schema for VC thesis extraction."""
    
    vc_id: str = Field(..., description="VC identifier")
    thesis: Dict[str, Any] = Field(..., description="Extracted thesis information")
    extracted_at: datetime = Field(..., description="Extraction timestamp")


# Alias for backward compatibility
ThesisExtractionRequest = VCThesisExtractionRequest