"""Pydantic schemas for Startup entities."""

from pydantic import BaseModel, Field, validator
from typing import List, Optional
from datetime import datetime


class StartupBase(BaseModel):
    """Base schema for Startup."""
    
    name: str = Field(..., min_length=1, max_length=200, description="Company name")
    sector: str = Field(..., min_length=1, max_length=100, description="Primary sector")
    stage: str = Field(..., description="Funding stage (e.g., Pre-seed, Seed, Series A)")
    description: Optional[str] = Field(default="", max_length=5000, description="Company description")
    website: Optional[str] = Field(default="", max_length=500, description="Company website URL")
    team_size: Optional[int] = Field(default=0, ge=0, description="Current team size")
    monthly_revenue: Optional[float] = Field(default=0.0, ge=0, description="Monthly revenue in USD")
    
    @validator("stage")
    def validate_stage(cls, v):
        valid_stages = ["Pre-seed", "Seed", "Series A", "Series B", "Series C", "Series D+", "Growth", "Pre-IPO"]
        if v not in valid_stages:
            raise ValueError(f"Stage must be one of: {', '.join(valid_stages)}")
        return v
    
    @validator("website")
    def validate_website(cls, v):
        if v is None:
            return v
        if v and not (v.startswith("http://") or v.startswith("https://")):
            v = f"https://{v}"
        return v
    
    @validator("team_size", "monthly_revenue", pre=True)
    def validate_numbers(cls, v):
        if v is None:
            return v
        return v


class StartupCreate(StartupBase):
    """Schema for creating a new Startup."""
    pass


class StartupUpdate(BaseModel):
    """Schema for updating a Startup."""
    
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    sector: Optional[str] = Field(None, min_length=1, max_length=100)
    stage: Optional[str] = None
    description: Optional[str] = Field(None, max_length=5000)
    website: Optional[str] = Field(None, max_length=500)
    team_size: Optional[int] = Field(None, ge=0)
    monthly_revenue: Optional[float] = Field(None, ge=0)
    
    @validator("stage")
    def validate_stage(cls, v):
        if v is not None:
            valid_stages = ["Pre-seed", "Seed", "Series A", "Series B", "Series C", "Series D+", "Growth", "Pre-IPO"]
            if v not in valid_stages:
                raise ValueError(f"Stage must be one of: {', '.join(valid_stages)}")
        return v


class StartupInDB(StartupBase):
    """Schema for Startup stored in database."""
    
    id: str = Field(..., description="Unique identifier")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    
    class Config:
        from_attributes = True


class StartupResponse(StartupInDB):
    """Schema for Startup API response."""
    
    is_fundable: bool = Field(..., description="Whether the startup is in a fundable stage")
    extracted_sectors: List[str] = Field(default_factory=list, description="AI-extracted sectors")
    
    @classmethod
    def from_domain(cls, startup, extracted_sectors: Optional[List[str]] = None):
        """Create response from domain model."""
        return cls(
            id=str(startup.id) if startup.id else "",
            name=startup.name,
            sector=startup.sector,
            stage=startup.stage,
            description=startup.description,
            website=startup.website,
            team_size=startup.team_size,
            monthly_revenue=startup.monthly_revenue,
            created_at=getattr(startup, "created_at", datetime.utcnow()),
            updated_at=getattr(startup, "updated_at", datetime.utcnow()),
            is_fundable=startup.is_fundable(),
            extracted_sectors=extracted_sectors or startup.extract_sectors()
        )


class StartupListResponse(BaseModel):
    """Schema for paginated startup list response."""
    
    items: List[StartupResponse] = Field(..., description="List of startups")
    total: int = Field(..., description="Total number of items")
    page: int = Field(..., description="Current page number")
    size: int = Field(..., description="Page size")
    pages: int = Field(..., description="Total number of pages")


class StartupSearchParams(BaseModel):
    """Schema for startup search parameters."""
    
    query: Optional[str] = Field(None, description="Search query")
    sector: Optional[str] = Field(None, description="Filter by sector")
    stage: Optional[str] = Field(None, description="Filter by stage")
    min_team_size: Optional[int] = Field(None, ge=0, description="Minimum team size")
    max_team_size: Optional[int] = Field(None, ge=0, description="Maximum team size")
    min_revenue: Optional[float] = Field(None, ge=0, description="Minimum monthly revenue")
    max_revenue: Optional[float] = Field(None, ge=0, description="Maximum monthly revenue")
    is_fundable: Optional[bool] = Field(None, description="Filter by fundable status")