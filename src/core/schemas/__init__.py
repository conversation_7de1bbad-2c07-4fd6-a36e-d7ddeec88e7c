"""Pydantic schemas for API validation."""

from .startup import (
    StartupBase,
    StartupCreate,
    StartupUpdate,
    StartupResponse,
    StartupListResponse,
    StartupSearchParams
)
from .vc import (
    VCBase,
    VCCreate,
    VCUpdate,
    VCResponse,
    VCListResponse,
    VCSearchParams,
    VCThesisExtractionRequest,
    ThesisExtractionRequest,
    ThesisExtractionResponse
)
from .match import (
    MatchRequest,
    BatchMatchRequest,
    MatchCreate,
    MatchUpdate,
    MatchResponse,
    MatchListResponse,
    MatchSearchParams,
    MatchStatus,
    MatchType
)

__all__ = [
    # Startup schemas
    "StartupBase",
    "StartupCreate",
    "StartupUpdate",
    "StartupResponse",
    "StartupListResponse",
    "StartupSearchParams",
    # VC schemas
    "VCBase",
    "VCCreate",
    "VCUpdate",
    "VCResponse",
    "VCListResponse",
    "VCSearchParams",
    "VCThesisExtractionRequest",
    "ThesisExtractionRequest",
    "ThesisExtractionResponse",
    # Match schemas
    "MatchRequest",
    "BatchMatchRequest",
    "MatchCreate",
    "MatchUpdate",
    "MatchResponse",
    "MatchListResponse",
    "MatchSearchParams",
    "MatchStatus",
    "MatchType"
]