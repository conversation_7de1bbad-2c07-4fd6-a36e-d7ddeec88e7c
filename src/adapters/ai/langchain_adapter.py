"""LangChain adapter implementation of the AI port.

This adapter implements the AIPort interface using LangChain and OpenAI,
providing concrete AI functionality while keeping the domain decoupled.
"""
import asyncio
from typing import Optional, Dict, Any, List
import logging
from tenacity import retry, stop_after_attempt, wait_exponential

from src.core.ports.ai_port import (
    AIPort, StartupInsights, VCInsights, MatchRationale, AIAnalysisError
)
from src.core.models.startup import Startup
from src.core.models.vc import VC
from src.core.ai.analyzer import AIAnalyzerService
from src.core.ai.models_v2 import StartupAnalysis, VCThesisAnalysis

logger = logging.getLogger(__name__)


class LangChainAIAdapter(AIPort):
    """LangChain implementation of the AI port.
    
    This adapter wraps the existing AIAnalyzerService and translates
    between the port interface and the LangChain implementation.
    """
    
    def __init__(
        self,
        openai_api_key: Optional[str] = None,
        model_name: str = "gpt-4",
        temperature: float = 0.3,
        enable_caching: bool = True
    ):
        """Initialize the LangChain adapter.
        
        Args:
            openai_api_key: OpenAI API key
            model_name: Model to use (default: gpt-4)
            temperature: Temperature for generation
            enable_caching: Whether to enable result caching
        """
        self.analyzer = AIAnalyzerService(
            openai_api_key=openai_api_key,
            model_name=model_name,
            temperature=temperature,
            enable_streaming=False
        )
        self.enable_caching = enable_caching
    
    async def analyze_startup(
        self,
        startup: Startup,
        use_cache: bool = True
    ) -> StartupInsights:
        """Analyze a startup using LangChain/OpenAI.
        
        Translates the LangChain analysis result to our domain object.
        """
        try:
            # Use existing analyzer
            analysis: StartupAnalysis = await self.analyzer.analyze_startup(
                startup,
                use_cache=use_cache and self.enable_caching
            )
            
            # Translate to domain insights
            return StartupInsights(
                key_technologies=analysis.key_technologies,
                market_opportunity=analysis.market_opportunity,
                competitive_advantages=analysis.competitive_advantages,
                team_strengths=analysis.team_strengths,
                risk_factors=analysis.risk_factors,
                growth_potential_score=analysis.growth_potential / 10.0,  # Convert to 0-1
                innovation_score=analysis.innovation_score / 10.0,
                market_fit_score=analysis.market_fit_score / 10.0
            )
            
        except Exception as e:
            logger.error(f"Failed to analyze startup {startup.name}: {str(e)}")
            raise AIAnalysisError(
                f"Failed to analyze startup: {str(e)}",
                original_error=e
            )
    
    async def analyze_vc(
        self,
        vc: VC,
        website_content: Optional[str] = None,
        use_cache: bool = True
    ) -> VCInsights:
        """Analyze a VC using LangChain/OpenAI.
        
        If website content is not provided, this will need to be scraped
        or provided through another mechanism.
        """
        try:
            # For now, require website content
            if not website_content:
                raise ValueError("Website content is required for VC analysis")
            
            # Use existing analyzer
            analysis: VCThesisAnalysis = await self.analyzer.extract_vc_thesis(
                vc,
                website_content,
                use_cache=use_cache and self.enable_caching
            )
            
            # Translate to domain insights
            return VCInsights(
                thesis_summary=analysis.thesis_summary,
                preferred_sectors=analysis.investment_focus.sectors,
                preferred_stages=[stage.value for stage in analysis.investment_focus.stages],
                typical_check_size=analysis.check_size_range or {"min": 0, "max": 0},
                portfolio_focus=analysis.portfolio_themes,
                investment_criteria=analysis.investment_criteria,
                exclusion_criteria=[]  # Not in current model, could be extracted
            )
            
        except Exception as e:
            logger.error(f"Failed to analyze VC {vc.firm_name}: {str(e)}")
            raise AIAnalysisError(
                f"Failed to analyze VC: {str(e)}",
                original_error=e
            )
    
    async def generate_match_rationale(
        self,
        startup: Startup,
        vc: VC,
        startup_insights: Optional[StartupInsights] = None,
        vc_insights: Optional[VCInsights] = None
    ) -> MatchRationale:
        """Generate match rationale using AI.
        
        This is a new capability that combines startup and VC insights
        to generate a detailed match rationale.
        """
        try:
            # Get insights if not provided
            if not startup_insights:
                startup_insights = await self.analyze_startup(startup)
            if not vc_insights:
                vc_insights = await self.analyze_vc(vc)
            
            # Calculate compatibility based on insights alignment
            compatibility_score = self._calculate_compatibility(
                startup, vc, startup_insights, vc_insights
            )
            
            # Generate rationale components
            key_alignments = self._find_alignments(startup_insights, vc_insights)
            potential_concerns = self._identify_concerns(startup_insights, vc_insights)
            talking_points = self._generate_talking_points(
                startup, vc, startup_insights, vc_insights
            )
            
            return MatchRationale(
                compatibility_score=compatibility_score,
                key_alignments=key_alignments,
                potential_concerns=potential_concerns,
                suggested_talking_points=talking_points,
                confidence_level=0.85  # High confidence in structured analysis
            )
            
        except Exception as e:
            logger.error(f"Failed to generate match rationale: {str(e)}")
            raise AIAnalysisError(
                f"Failed to generate match rationale: {str(e)}",
                original_error=e
            )
    
    async def batch_analyze_startups(
        self,
        startups: List[Startup],
        max_concurrent: int = 5
    ) -> List[StartupInsights]:
        """Analyze multiple startups concurrently."""
        try:
            # Use existing batch analyzer
            analyses = await self.analyzer.batch_analyze_startups(
                startups,
                use_cache=self.enable_caching,
                max_concurrent=max_concurrent
            )
            
            # Convert all analyses to insights
            insights = []
            for analysis in analyses:
                insights.append(StartupInsights(
                    key_technologies=analysis.key_technologies,
                    market_opportunity=analysis.market_opportunity,
                    competitive_advantages=analysis.competitive_advantages,
                    team_strengths=analysis.team_strengths,
                    risk_factors=analysis.risk_factors,
                    growth_potential_score=analysis.growth_potential / 10.0,
                    innovation_score=analysis.innovation_score / 10.0,
                    market_fit_score=analysis.market_fit_score / 10.0
                ))
            
            return insights
            
        except Exception as e:
            logger.error(f"Failed to batch analyze startups: {str(e)}")
            raise AIAnalysisError(
                f"Failed to batch analyze startups: {str(e)}",
                original_error=e
            )
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """Get usage statistics from the underlying analyzer."""
        return self.analyzer.get_usage_stats()
    
    def reset_usage_stats(self) -> None:
        """Reset usage statistics."""
        self.analyzer.reset_usage_stats()
    
    # Private helper methods
    
    def _calculate_compatibility(
        self,
        startup: Startup,
        vc: VC,
        startup_insights: StartupInsights,
        vc_insights: VCInsights
    ) -> float:
        """Calculate compatibility score between startup and VC."""
        score = 0.0
        weights = 0.0
        
        # Sector alignment (40% weight)
        if startup.sector in vc_insights.preferred_sectors:
            score += 0.4
        weights += 0.4
        
        # Stage alignment (30% weight)
        if startup.stage in vc_insights.preferred_stages:
            score += 0.3
        weights += 0.3
        
        # Check size alignment (20% weight)
        if hasattr(startup, 'funding_amount') and vc_insights.typical_check_size:
            funding = getattr(startup, 'funding_amount', 0)
            if (vc_insights.typical_check_size['min'] <= funding <= 
                vc_insights.typical_check_size['max']):
                score += 0.2
        weights += 0.2
        
        # Innovation alignment (10% weight)
        if startup_insights.innovation_score > 0.7:
            score += 0.1
        weights += 0.1
        
        return score / weights if weights > 0 else 0.0
    
    def _find_alignments(
        self,
        startup_insights: StartupInsights,
        vc_insights: VCInsights
    ) -> List[str]:
        """Find key alignments between startup and VC."""
        alignments = []
        
        # Check technology alignment
        tech_overlap = set(startup_insights.key_technologies) & set(vc_insights.portfolio_focus)
        if tech_overlap:
            alignments.append(f"Technology alignment in: {', '.join(tech_overlap)}")
        
        # Check market opportunity
        if startup_insights.market_opportunity:
            alignments.append(f"Strong market opportunity: {startup_insights.market_opportunity}")
        
        # Check growth potential
        if startup_insights.growth_potential_score > 0.7:
            alignments.append("High growth potential aligns with VC thesis")
        
        return alignments
    
    def _identify_concerns(
        self,
        startup_insights: StartupInsights,
        vc_insights: VCInsights
    ) -> List[str]:
        """Identify potential concerns in the match."""
        concerns = []
        
        # Check risk factors
        if len(startup_insights.risk_factors) > 3:
            concerns.append(f"Multiple risk factors: {', '.join(startup_insights.risk_factors[:3])}")
        
        # Check market fit
        if startup_insights.market_fit_score < 0.5:
            concerns.append("Market fit needs further validation")
        
        return concerns
    
    def _generate_talking_points(
        self,
        startup: Startup,
        vc: VC,
        startup_insights: StartupInsights,
        vc_insights: VCInsights
    ) -> List[str]:
        """Generate suggested talking points for the match."""
        points = []
        
        # Technology discussion
        if startup_insights.key_technologies:
            points.append(
                f"Discuss {startup.name}'s technology stack and roadmap: "
                f"{', '.join(startup_insights.key_technologies[:3])}"
            )
        
        # Market opportunity
        points.append(
            f"Explore market opportunity and go-to-market strategy for "
            f"{startup_insights.market_opportunity}"
        )
        
        # Team strengths
        if startup_insights.team_strengths:
            points.append(
                f"Highlight team strengths: {', '.join(startup_insights.team_strengths[:2])}"
            )
        
        # VC thesis alignment
        points.append(
            f"Discuss how {startup.name} aligns with {vc.firm_name}'s "
            f"investment thesis in {', '.join(vc_insights.preferred_sectors[:2])}"
        )
        
        return points