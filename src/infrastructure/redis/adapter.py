"""Redis adapter implementing the cache port interface."""

from typing import Optional, Any, Dict, List
import json
import logging
from redis.exceptions import RedisError
import redis.asyncio as redis_async

from .port import CachePort
from .connection import RedisConnectionFactory

logger = logging.getLogger(__name__)


class RedisAdapter(CachePort):
    """Redis implementation of the cache port.
    
    This adapter provides a clean interface to Redis operations
    while handling connection management, error handling, and
    serialization concerns.
    """
    
    def __init__(
        self, 
        redis_url: Optional[str] = None,
        default_ttl: int = 3600,
        key_prefix: str = ""
    ):
        """Initialize Redis adapter.
        
        Args:
            redis_url: Redis connection URL (uses settings if not provided)
            default_ttl: Default time-to-live in seconds
            key_prefix: Prefix for all cache keys
        """
        self.redis_url = redis_url
        self.default_ttl = default_ttl
        self.key_prefix = key_prefix
        self._client: Optional[redis_async.Redis] = None
        
    async def _get_client(self) -> redis_async.Redis:
        """Get Redis client, creating connection if needed."""
        if self._client is None:
            self._client = await RedisConnectionFactory.get_async_client(self.redis_url)
        return self._client
    
    def _make_key(self, key: str) -> str:
        """Create full cache key with prefix."""
        if self.key_prefix:
            return f"{self.key_prefix}:{key}"
        return key
    
    async def get(self, key: str) -> Optional[str]:
        """Get a value from cache by key.
        
        Args:
            key: Cache key
            
        Returns:
            Cached value or None if not found
        """
        try:
            client = await self._get_client()
            full_key = self._make_key(key)
            value = await client.get(full_key)
            return value
            
        except RedisError as e:
            logger.warning(f"Redis get failed for key '{key}': {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error in Redis get: {e}")
            return None
    
    async def set(self, key: str, value: str, ttl: Optional[int] = None) -> bool:
        """Set a value in cache with optional TTL.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds (uses default if not provided)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            client = await self._get_client()
            full_key = self._make_key(key)
            ttl_value = ttl if ttl is not None else self.default_ttl
            
            result = await client.setex(full_key, ttl_value, value)
            return result is True
            
        except RedisError as e:
            logger.warning(f"Redis set failed for key '{key}': {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error in Redis set: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete a key from cache.
        
        Args:
            key: Cache key to delete
            
        Returns:
            True if key was deleted, False if key didn't exist
        """
        try:
            client = await self._get_client()
            full_key = self._make_key(key)
            result = await client.delete(full_key)
            return result > 0
            
        except RedisError as e:
            logger.warning(f"Redis delete failed for key '{key}': {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error in Redis delete: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if a key exists in cache.
        
        Args:
            key: Cache key to check
            
        Returns:
            True if key exists, False otherwise
        """
        try:
            client = await self._get_client()
            full_key = self._make_key(key)
            result = await client.exists(full_key)
            return result > 0
            
        except RedisError as e:
            logger.warning(f"Redis exists check failed for key '{key}': {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error in Redis exists: {e}")
            return False
    
    async def increment(self, key: str, amount: int = 1) -> int:
        """Increment a numeric value in cache.
        
        Args:
            key: Cache key
            amount: Amount to increment by
            
        Returns:
            New value after increment
        """
        try:
            client = await self._get_client()
            full_key = self._make_key(key)
            
            if amount == 1:
                result = await client.incr(full_key)
            else:
                result = await client.incrby(full_key, amount)
                
            return result
            
        except RedisError as e:
            logger.warning(f"Redis increment failed for key '{key}': {e}")
            return 0
        except Exception as e:
            logger.error(f"Unexpected error in Redis increment: {e}")
            return 0
    
    async def expire(self, key: str, ttl: int) -> bool:
        """Set expiration time for a key.
        
        Args:
            key: Cache key
            ttl: Time to live in seconds
            
        Returns:
            True if expiration was set, False otherwise
        """
        try:
            client = await self._get_client()
            full_key = self._make_key(key)
            result = await client.expire(full_key, ttl)
            return result is True
            
        except RedisError as e:
            logger.warning(f"Redis expire failed for key '{key}': {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error in Redis expire: {e}")
            return False
    
    async def keys(self, pattern: str) -> List[str]:
        """Get all keys matching a pattern.
        
        Args:
            pattern: Pattern to match (e.g., "prefix:*")
            
        Returns:
            List of matching keys (with prefix removed)
        """
        try:
            client = await self._get_client()
            full_pattern = self._make_key(pattern)
            keys = await client.keys(full_pattern)
            
            # Remove prefix from returned keys
            if self.key_prefix:
                prefix_len = len(self.key_prefix) + 1  # +1 for the colon
                return [key[prefix_len:] for key in keys if key.startswith(f"{self.key_prefix}:")]
            else:
                return keys
                
        except RedisError as e:
            logger.warning(f"Redis keys search failed for pattern '{pattern}': {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error in Redis keys: {e}")
            return []
    
    async def delete_many(self, keys: List[str]) -> int:
        """Delete multiple keys from cache.
        
        Args:
            keys: List of keys to delete
            
        Returns:
            Number of keys actually deleted
        """
        if not keys:
            return 0
            
        try:
            client = await self._get_client()
            full_keys = [self._make_key(key) for key in keys]
            result = await client.delete(*full_keys)
            return result
            
        except RedisError as e:
            logger.warning(f"Redis delete_many failed: {e}")
            return 0
        except Exception as e:
            logger.error(f"Unexpected error in Redis delete_many: {e}")
            return 0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics.
        
        Returns:
            Dictionary with cache statistics
        """
        try:
            # Note: This needs to be sync for the interface
            # In practice, you might want to cache these stats
            from .connection import RedisConnectionFactory
            sync_client = RedisConnectionFactory.get_sync_client(self.redis_url)
            
            info = sync_client.info()
            
            # Calculate key count for our prefix
            if self.key_prefix:
                keys = sync_client.keys(f"{self.key_prefix}:*")
                key_count = len(keys)
            else:
                key_count = info.get('db0', {}).get('keys', 0)
            
            return {
                'total_keys': key_count,
                'hits': info.get('keyspace_hits', 0),
                'misses': info.get('keyspace_misses', 0),
                'hit_rate': self._calculate_hit_rate(
                    info.get('keyspace_hits', 0),
                    info.get('keyspace_misses', 0)
                ),
                'memory_usage': info.get('used_memory_human', 'unknown'),
                'connected_clients': info.get('connected_clients', 0),
                'commands_processed': info.get('total_commands_processed', 0)
            }
            
        except Exception as e:
            logger.error(f"Failed to get Redis stats: {e}")
            return {
                'total_keys': 0,
                'hits': 0,
                'misses': 0,
                'hit_rate': 0.0,
                'memory_usage': 'unknown',
                'connected_clients': 0,
                'commands_processed': 0,
                'error': str(e)
            }
    
    def _calculate_hit_rate(self, hits: int, misses: int) -> float:
        """Calculate cache hit rate."""
        total = hits + misses
        if total == 0:
            return 0.0
        return round(hits / total, 4)
    
    async def health_check(self) -> bool:
        """Check if cache service is healthy.
        
        Returns:
            True if healthy, False otherwise
        """
        try:
            client = await self._get_client()
            pong = await client.ping()
            return pong is True
            
        except Exception as e:
            logger.error(f"Redis health check failed: {e}")
            return False
    
    # Additional Redis-specific methods
    
    async def set_json(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set a JSON-serializable value in cache.
        
        Args:
            key: Cache key
            value: JSON-serializable value
            ttl: Time to live in seconds
            
        Returns:
            True if successful, False otherwise
        """
        try:
            json_value = json.dumps(value)
            return await self.set(key, json_value, ttl)
        except (TypeError, ValueError) as e:
            logger.error(f"JSON serialization failed for key '{key}': {e}")
            return False
    
    async def get_json(self, key: str) -> Optional[Any]:
        """Get a JSON value from cache.
        
        Args:
            key: Cache key
            
        Returns:
            Deserialized JSON value or None if not found/invalid
        """
        try:
            value = await self.get(key)
            if value is None:
                return None
            return json.loads(value)
        except (json.JSONDecodeError, TypeError) as e:
            logger.error(f"JSON deserialization failed for key '{key}': {e}")
            return None
    
    async def clear_prefix(self, prefix: str) -> int:
        """Clear all keys with a specific prefix.
        
        Args:
            prefix: Key prefix to clear
            
        Returns:
            Number of keys deleted
        """
        keys = await self.keys(f"{prefix}:*")
        if keys:
            return await self.delete_many(keys)
        return 0