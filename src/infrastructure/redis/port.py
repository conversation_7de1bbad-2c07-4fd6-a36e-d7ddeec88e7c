"""Cache port interface for clean architecture."""

from abc import ABC, abstractmethod
from typing import Optional, Any, Dict, List
import asyncio


class CachePort(ABC):
    """Abstract interface for cache operations.
    
    This port defines the contract for cache implementations,
    allowing the domain layer to remain decoupled from specific
    cache technologies like Redis.
    """
    
    @abstractmethod
    async def get(self, key: str) -> Optional[str]:
        """Get a value from cache by key.
        
        Args:
            key: Cache key
            
        Returns:
            Cached value or None if not found
        """
        pass
    
    @abstractmethod
    async def set(self, key: str, value: str, ttl: Optional[int] = None) -> bool:
        """Set a value in cache with optional TTL.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds
            
        Returns:
            True if successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> bool:
        """Delete a key from cache.
        
        Args:
            key: Cache key to delete
            
        Returns:
            True if key was deleted, False if key didn't exist
        """
        pass
    
    @abstractmethod
    async def exists(self, key: str) -> bool:
        """Check if a key exists in cache.
        
        Args:
            key: Cache key to check
            
        Returns:
            True if key exists, False otherwise
        """
        pass
    
    @abstractmethod
    async def increment(self, key: str, amount: int = 1) -> int:
        """Increment a numeric value in cache.
        
        Args:
            key: Cache key
            amount: Amount to increment by
            
        Returns:
            New value after increment
        """
        pass
    
    @abstractmethod
    async def expire(self, key: str, ttl: int) -> bool:
        """Set expiration time for a key.
        
        Args:
            key: Cache key
            ttl: Time to live in seconds
            
        Returns:
            True if expiration was set, False otherwise
        """
        pass
    
    @abstractmethod
    async def keys(self, pattern: str) -> List[str]:
        """Get all keys matching a pattern.
        
        Args:
            pattern: Pattern to match (e.g., "prefix:*")
            
        Returns:
            List of matching keys
        """
        pass
    
    @abstractmethod
    async def delete_many(self, keys: List[str]) -> int:
        """Delete multiple keys from cache.
        
        Args:
            keys: List of keys to delete
            
        Returns:
            Number of keys actually deleted
        """
        pass
    
    @abstractmethod
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics.
        
        Returns:
            Dictionary with cache statistics
        """
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """Check if cache service is healthy.
        
        Returns:
            True if healthy, False otherwise
        """
        pass