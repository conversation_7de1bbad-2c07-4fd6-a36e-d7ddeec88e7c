"""Redis connection factory with health checks and connection pooling."""

from typing import Optional, Dict, Any
import redis
import redis.asyncio as redis_async
from redis.exceptions import RedisError, ConnectionError
import asyncio
import logging
from urllib.parse import urlparse

from src.core.config import settings

logger = logging.getLogger(__name__)


class RedisConnectionFactory:
    """Factory for creating and managing Redis connections.
    
    Provides both sync and async Redis connections with proper
    connection pooling, health checks, and error handling.
    """
    
    _sync_client: Optional[redis.Redis] = None
    _async_client: Optional[redis_async.Redis] = None
    _connection_params: Optional[Dict[str, Any]] = None
    
    @classmethod
    def _parse_redis_url(cls, redis_url: str) -> Dict[str, Any]:
        """Parse Redis URL into connection parameters.
        
        Args:
            redis_url: Redis connection URL (e.g., redis://localhost:6379/0)
            
        Returns:
            Dictionary with connection parameters
        """
        if cls._connection_params is not None:
            return cls._connection_params
            
        try:
            parsed = urlparse(redis_url)
            
            # Handle different Redis URL formats
            if parsed.scheme in ('redis', 'rediss'):
                params = {
                    'host': parsed.hostname or 'localhost',
                    'port': parsed.port or 6379,
                    'db': int(parsed.path.lstrip('/')) if parsed.path else 0,
                    'decode_responses': True,
                    'health_check_interval': 30,  # Health check every 30 seconds
                    'socket_connect_timeout': 5,
                    'socket_timeout': 5,
                    'retry_on_timeout': True,
                    'max_connections': 20,  # Connection pool size
                }
                
                # Add authentication if provided
                if parsed.password:
                    params['password'] = parsed.password
                if parsed.username:
                    params['username'] = parsed.username
                    
                # SSL for rediss://
                if parsed.scheme == 'rediss':
                    params['ssl'] = True
                    params['ssl_cert_reqs'] = None  # Disable cert verification for dev
                    
            else:
                raise ValueError(f"Unsupported Redis URL scheme: {parsed.scheme}")
                
            cls._connection_params = params
            return params
            
        except Exception as e:
            logger.error(f"Failed to parse Redis URL: {e}")
            # Fallback to default local Redis
            return {
                'host': 'localhost',
                'port': 6379,
                'db': 0,
                'decode_responses': True,
                'health_check_interval': 30,
                'socket_connect_timeout': 5,
                'socket_timeout': 5,
                'retry_on_timeout': True,
                'max_connections': 20,
            }
    
    @classmethod
    def get_sync_client(cls, redis_url: Optional[str] = None) -> redis.Redis:
        """Get a synchronous Redis client with connection pooling.
        
        Args:
            redis_url: Redis connection URL (uses settings if not provided)
            
        Returns:
            Redis client instance
            
        Raises:
            ConnectionError: If unable to connect to Redis
        """
        if cls._sync_client is not None:
            return cls._sync_client
            
        try:
            url = redis_url or settings.redis_url
            params = cls._parse_redis_url(url)
            
            # Create connection pool
            pool = redis.ConnectionPool(**params)
            client = redis.Redis(connection_pool=pool)
            
            # Test connection
            client.ping()
            
            cls._sync_client = client
            logger.info(f"Redis sync client connected to {params['host']}:{params['port']}")
            return client
            
        except Exception as e:
            logger.error(f"Failed to create Redis sync client: {e}")
            raise ConnectionError(f"Could not connect to Redis: {e}")
    
    @classmethod
    async def get_async_client(cls, redis_url: Optional[str] = None) -> redis_async.Redis:
        """Get an asynchronous Redis client with connection pooling.
        
        Args:
            redis_url: Redis connection URL (uses settings if not provided)
            
        Returns:
            Async Redis client instance
            
        Raises:
            ConnectionError: If unable to connect to Redis
        """
        if cls._async_client is not None:
            return cls._async_client
            
        try:
            url = redis_url or settings.redis_url
            params = cls._parse_redis_url(url)
            
            # Create async connection pool
            pool = redis_async.ConnectionPool(**params)
            client = redis_async.Redis(connection_pool=pool)
            
            # Test connection
            await client.ping()
            
            cls._async_client = client
            logger.info(f"Redis async client connected to {params['host']}:{params['port']}")
            return client
            
        except Exception as e:
            logger.error(f"Failed to create Redis async client: {e}")
            raise ConnectionError(f"Could not connect to Redis: {e}")
    
    @classmethod
    async def health_check(cls, redis_url: Optional[str] = None) -> Dict[str, Any]:
        """Perform comprehensive health check on Redis connection.
        
        Args:
            redis_url: Redis connection URL (uses settings if not provided)
            
        Returns:
            Dictionary with health check results
        """
        health_info = {
            'healthy': False,
            'sync_connection': False,
            'async_connection': False,
            'latency_ms': None,
            'memory_usage': None,
            'connected_clients': None,
            'error': None
        }
        
        try:
            # Test sync connection
            import time
            start_time = time.time()
            
            sync_client = cls.get_sync_client(redis_url)
            pong = sync_client.ping()
            
            end_time = time.time()
            latency_ms = (end_time - start_time) * 1000
            
            if pong:
                health_info['sync_connection'] = True
                health_info['latency_ms'] = round(latency_ms, 2)
                
                # Get Redis info
                info = sync_client.info()
                health_info['memory_usage'] = info.get('used_memory_human')
                health_info['connected_clients'] = info.get('connected_clients')
            
            # Test async connection
            async_client = await cls.get_async_client(redis_url)
            async_pong = await async_client.ping()
            
            if async_pong:
                health_info['async_connection'] = True
                
            # Overall health
            health_info['healthy'] = (
                health_info['sync_connection'] and 
                health_info['async_connection']
            )
            
        except Exception as e:
            health_info['error'] = str(e)
            logger.error(f"Redis health check failed: {e}")
            
        return health_info
    
    @classmethod
    def reset_connections(cls):
        """Reset all connection instances (useful for testing)."""
        if cls._sync_client:
            try:
                cls._sync_client.close()
            except:
                pass
                
        if cls._async_client:
            try:
                asyncio.create_task(cls._async_client.close())
            except:
                pass
                
        cls._sync_client = None
        cls._async_client = None
        cls._connection_params = None
        
    @classmethod
    def get_connection_info(cls) -> Dict[str, Any]:
        """Get current connection information.
        
        Returns:
            Dictionary with connection details
        """
        return {
            'sync_client_connected': cls._sync_client is not None,
            'async_client_connected': cls._async_client is not None,
            'connection_params': cls._connection_params,
            'settings_redis_url': settings.redis_url
        }