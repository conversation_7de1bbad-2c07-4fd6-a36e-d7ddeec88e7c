"""OpenTelemetry configuration for performance monitoring."""

import logging
from typing import Optional, Dict, Any
from opentelemetry import trace, metrics
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.exporter.otlp.proto.grpc.metric_exporter import OTLPMetricExporter
from opentelemetry.exporter.prometheus import PrometheusMetricReader
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.instrumentation.sqlalchemy import SQLAlchemyInstrumentor
from opentelemetry.instrumentation.redis import RedisInstrumentor
from opentelemetry.instrumentation.httpx import HTTPXClientInstrumentor
from opentelemetry.instrumentation.logging import LoggingInstrumentor
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor, ConsoleSpanExporter
from opentelemetry.sdk.metrics import MeterProvider
from opentelemetry.sdk.metrics.export import PeriodicExportingMetricReader
from opentelemetry.sdk.resources import Resource
from prometheus_client import start_http_server

logger = logging.getLogger(__name__)


class TelemetryService:
    """Service for managing OpenTelemetry instrumentation."""
    
    def __init__(self, service_name: str = "vc-matching-platform", environment: str = "development"):
        """Initialize telemetry service."""
        self.service_name = service_name
        self.environment = environment
        self.resource = Resource.create({
            "service.name": service_name,
            "service.environment": environment,
            "service.version": "1.0.0"
        })
        
        self.tracer_provider = None
        self.meter_provider = None
        self.tracer = None
        self.meter = None
        
        # Custom metrics
        self.request_counter = None
        self.request_duration = None
        self.discovery_operations = None
        self.cache_operations = None
        self.ai_operations = None
    
    def initialize(
        self,
        otlp_endpoint: Optional[str] = None,
        prometheus_port: int = 9090,
        enable_console_export: bool = False
    ):
        """Initialize OpenTelemetry with exporters."""
        # Initialize tracing
        self._setup_tracing(otlp_endpoint, enable_console_export)
        
        # Initialize metrics
        self._setup_metrics(otlp_endpoint, prometheus_port)
        
        # Create custom metrics
        self._create_custom_metrics()
        
        logger.info(f"OpenTelemetry initialized for {self.service_name}")
    
    def _setup_tracing(self, otlp_endpoint: Optional[str], enable_console: bool):
        """Set up tracing with exporters."""
        self.tracer_provider = TracerProvider(resource=self.resource)
        
        # Add OTLP exporter if endpoint provided
        if otlp_endpoint:
            otlp_exporter = OTLPSpanExporter(
                endpoint=otlp_endpoint,
                insecure=True  # Use secure=False for local development
            )
            self.tracer_provider.add_span_processor(
                BatchSpanProcessor(otlp_exporter)
            )
        
        # Add console exporter for development
        if enable_console:
            console_exporter = ConsoleSpanExporter()
            self.tracer_provider.add_span_processor(
                BatchSpanProcessor(console_exporter)
            )
        
        # Set global tracer provider
        trace.set_tracer_provider(self.tracer_provider)
        self.tracer = trace.get_tracer(self.service_name)
    
    def _setup_metrics(self, otlp_endpoint: Optional[str], prometheus_port: int):
        """Set up metrics with exporters."""
        metric_readers = []
        
        # Add Prometheus exporter
        prometheus_reader = PrometheusMetricReader()
        metric_readers.append(prometheus_reader)
        
        # Start Prometheus metrics server
        start_http_server(port=prometheus_port)
        logger.info(f"Prometheus metrics available at http://localhost:{prometheus_port}/metrics")
        
        # Add OTLP exporter if endpoint provided
        if otlp_endpoint:
            otlp_exporter = OTLPMetricExporter(
                endpoint=otlp_endpoint,
                insecure=True
            )
            periodic_reader = PeriodicExportingMetricReader(
                exporter=otlp_exporter,
                export_interval_millis=10000  # 10 seconds
            )
            metric_readers.append(periodic_reader)
        
        # Create meter provider
        self.meter_provider = MeterProvider(
            resource=self.resource,
            metric_readers=metric_readers
        )
        
        # Set global meter provider
        metrics.set_meter_provider(self.meter_provider)
        self.meter = metrics.get_meter(self.service_name)
    
    def _create_custom_metrics(self):
        """Create custom application metrics."""
        # Request metrics
        self.request_counter = self.meter.create_counter(
            name="http_requests_total",
            description="Total number of HTTP requests",
            unit="requests"
        )
        
        self.request_duration = self.meter.create_histogram(
            name="http_request_duration_seconds",
            description="HTTP request duration in seconds",
            unit="seconds"
        )
        
        # Discovery operation metrics
        self.discovery_operations = self.meter.create_counter(
            name="discovery_operations_total",
            description="Total number of discovery operations",
            unit="operations"
        )
        
        # Cache operation metrics
        self.cache_operations = self.meter.create_counter(
            name="cache_operations_total",
            description="Total number of cache operations",
            unit="operations"
        )
        
        # AI operation metrics
        self.ai_operations = self.meter.create_counter(
            name="ai_operations_total",
            description="Total number of AI operations",
            unit="operations"
        )
        
        self.ai_operation_duration = self.meter.create_histogram(
            name="ai_operation_duration_seconds",
            description="AI operation duration in seconds",
            unit="seconds"
        )
    
    def instrument_app(self, app):
        """Instrument FastAPI application."""
        FastAPIInstrumentor.instrument_app(app)
        logger.info("FastAPI instrumented")
    
    def instrument_sqlalchemy(self, engine):
        """Instrument SQLAlchemy engine."""
        SQLAlchemyInstrumentor().instrument(
            engine=engine,
            service=self.service_name
        )
        logger.info("SQLAlchemy instrumented")
    
    def instrument_redis(self):
        """Instrument Redis client."""
        RedisInstrumentor().instrument()
        logger.info("Redis instrumented")
    
    def instrument_httpx(self):
        """Instrument HTTPX client."""
        HTTPXClientInstrumentor().instrument()
        logger.info("HTTPX instrumented")
    
    def instrument_logging(self):
        """Instrument logging to include trace context."""
        LoggingInstrumentor().instrument()
        logger.info("Logging instrumented")
    
    # Custom instrumentation decorators
    
    def trace_operation(self, operation_name: str):
        """Decorator to trace a function execution."""
        def decorator(func):
            def wrapper(*args, **kwargs):
                with self.tracer.start_as_current_span(operation_name) as span:
                    try:
                        # Add attributes
                        span.set_attribute("operation.type", operation_name)
                        
                        # Execute function
                        result = func(*args, **kwargs)
                        
                        # Mark as successful
                        span.set_attribute("operation.success", True)
                        return result
                        
                    except Exception as e:
                        # Record exception
                        span.set_attribute("operation.success", False)
                        span.record_exception(e)
                        raise
            
            return wrapper
        return decorator
    
    def record_discovery_operation(self, operation_type: str, entity_type: str, success: bool):
        """Record a discovery operation metric."""
        if self.discovery_operations:
            self.discovery_operations.add(
                1,
                attributes={
                    "operation_type": operation_type,
                    "entity_type": entity_type,
                    "success": str(success)
                }
            )
    
    def record_cache_operation(self, operation: str, cache_type: str, hit: bool):
        """Record a cache operation metric."""
        if self.cache_operations:
            self.cache_operations.add(
                1,
                attributes={
                    "operation": operation,
                    "cache_type": cache_type,
                    "hit": str(hit)
                }
            )
    
    def record_ai_operation(self, operation_type: str, model: str, duration: float, success: bool):
        """Record an AI operation metric."""
        if self.ai_operations:
            self.ai_operations.add(
                1,
                attributes={
                    "operation_type": operation_type,
                    "model": model,
                    "success": str(success)
                }
            )
        
        if self.ai_operation_duration:
            self.ai_operation_duration.record(
                duration,
                attributes={
                    "operation_type": operation_type,
                    "model": model
                }
            )
    
    def get_current_span(self) -> Optional[trace.Span]:
        """Get the current active span."""
        return trace.get_current_span()
    
    def create_span(self, name: str, attributes: Optional[Dict[str, Any]] = None) -> trace.Span:
        """Create a new span with optional attributes."""
        span = self.tracer.start_span(name)
        if attributes:
            for key, value in attributes.items():
                span.set_attribute(key, value)
        return span


# Global telemetry instance
telemetry = TelemetryService()


def init_telemetry(
    service_name: str = "vc-matching-platform",
    environment: str = "development",
    otlp_endpoint: Optional[str] = None,
    prometheus_port: int = 9090,
    enable_console: bool = False
):
    """Initialize global telemetry instance."""
    telemetry.service_name = service_name
    telemetry.environment = environment
    telemetry.initialize(
        otlp_endpoint=otlp_endpoint,
        prometheus_port=prometheus_port,
        enable_console_export=enable_console
    )
    
    # Instrument libraries
    telemetry.instrument_redis()
    telemetry.instrument_httpx()
    telemetry.instrument_logging()
    
    return telemetry