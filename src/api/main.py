"""Main FastAPI application."""

from fastapi import <PERSON><PERSON><PERSON>, Request, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
from sqlalchemy.orm import Session
import logging
import time
import async<PERSON>
from typing import Callable

from src.core.config import settings
from src.api.errors import (
    APIException,
    api_exception_handler,
    http_exception_handler,
    validation_exception_handler,
    generic_exception_handler
)
from src.api.v1.endpoints import (
    startups, vcs, matches, auth, health, scraping, discovery, vc_thesis, 
    connections, hybrid_discovery, vc_enrichment, cached_discovery,
    introduction_workflow, market_intelligence, signals
)
from src.api.endpoints import tasks
from src.api.v1.deps import get_database as get_db
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.log_level),
    format=settings.log_format
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info(f"Starting {settings.app_name} v{settings.app_version}")
    logger.info(f"Debug mode: {settings.debug}")
    logger.info(f"API prefix: {settings.api_v1_prefix}")
    
    # Initialize any connections or resources here
    # TODO: Initialize database connection pool
    # TODO: Initialize Redis connection
    # TODO: Warm up AI models if needed
    
    yield
    
    # Shutdown
    logger.info("Shutting down application")
    # TODO: Close database connections
    # TODO: Close Redis connections
    # TODO: Cleanup any resources


# Create FastAPI app
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    openapi_url=f"{settings.api_v1_prefix}/openapi.json",
    docs_url=f"{settings.api_v1_prefix}/docs",
    redoc_url=f"{settings.api_v1_prefix}/redoc",
    lifespan=lifespan
)

# Custom UUID validation handler
async def uuid_validation_exception_handler(
    request: Request,
    exc: RequestValidationError
) -> JSONResponse:
    """Handle UUID validation errors specially to return 404."""
    # Check if this is a UUID validation error in the path
    for error in exc.errors():
        # Check for both old and new UUID error types
        if (("uuid" in error["type"] or "UUID" in str(error["msg"])) 
            and "path" in str(error["loc"])):
            return JSONResponse(
                status_code=404,
                content={
                    "detail": "Not Found"  # Match expected format
                }
            )
    # If not a UUID path error, use default validation handler
    return await validation_exception_handler(request, exc)

# Add exception handlers
app.add_exception_handler(APIException, api_exception_handler)
app.add_exception_handler(StarletteHTTPException, http_exception_handler)
app.add_exception_handler(RequestValidationError, uuid_validation_exception_handler)
app.add_exception_handler(Exception, generic_exception_handler)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=settings.cors_allow_credentials,
    allow_methods=settings.cors_allow_methods,
    allow_headers=settings.cors_allow_headers,
)

# Add trusted host middleware for security
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"]  # Configure based on your deployment
)


# Timeout middleware
@app.middleware("http")
async def timeout_middleware(request: Request, call_next: Callable):
    """Handle request timeouts."""
    try:
        # Set a default timeout for all requests (30 seconds)
        response = await asyncio.wait_for(
            call_next(request), 
            timeout=30.0
        )
        return response
    except asyncio.TimeoutError:
        logger.error(f"Request timeout: {request.method} {request.url.path}")
        return JSONResponse(
            status_code=500,
            content={
                "detail": "Request timeout"  # Match expected format
            }
        )

# Database error handling middleware
@app.middleware("http")
async def database_error_middleware(request: Request, call_next: Callable):
    """Handle database connection errors gracefully."""
    try:
        response = await call_next(request)
        return response
    except Exception as e:
        # Check if it's a database error
        error_message = str(e).lower()
        if any(db_error in error_message for db_error in ["database", "connection", "postgresql", "sqlalchemy"]):
            logger.error(f"Database error: {str(e)} | Path: {request.url.path}")
            return JSONResponse(
                status_code=500,
                content={
                    "detail": "Internal server error"  # Match expected format
                }
            )
        # Re-raise if not a database error
        raise

# Request timing middleware
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """Add X-Process-Time header to track request processing time."""
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    
    # Log slow requests
    if process_time > 1.0:  # Log requests taking more than 1 second
        logger.warning(
            f"Slow request: {request.method} {request.url.path} "
            f"took {process_time:.2f}s"
        )
    
    return response


# Request ID middleware
@app.middleware("http")
async def add_request_id_header(request: Request, call_next):
    """Add X-Request-ID header for request tracing."""
    import uuid
    request_id = str(uuid.uuid4())
    
    # Store request ID in request state for logging
    request.state.request_id = request_id
    
    response = await call_next(request)
    response.headers["X-Request-ID"] = request_id
    
    return response

# Content-Type validation middleware
@app.middleware("http")
async def content_type_validation_middleware(request: Request, call_next: Callable):
    """Validate content-type for requests with body."""
    # Only check content-type for requests with body
    if request.method in ["POST", "PUT", "PATCH"]:
        content_type = request.headers.get("content-type", "")
        
        # Skip validation for OAuth2 login endpoint (requires form-encoded data)
        if request.url.path.endswith("/auth/login"):
            response = await call_next(request)
            return response
        
        # Check if content-type is present and valid
        if content_type and not content_type.startswith("application/json"):
            return JSONResponse(
                status_code=415,
                content={
                    "error": {
                        "status_code": 415,
                        "detail": "Unsupported Media Type. Use application/json"
                    }
                }
            )
    
    response = await call_next(request)
    return response


# Health check endpoint
@app.get("/health", tags=["health"])
async def health_check(db: Session = Depends(get_db)):
    """Health check endpoint."""
    status = "healthy"
    checks = {}
    
    # Check database
    try:
        # Simple query to check DB connection
        from sqlalchemy import text
        db.execute(text("SELECT 1"))
        checks["database"] = "healthy"
    except Exception as e:
        checks["database"] = f"unhealthy: {str(e)}"
        status = "unhealthy"
    
    # Check Redis
    try:
        from src.infrastructure.redis import RedisConnectionFactory
        redis_health = await RedisConnectionFactory.health_check()
        checks["redis"] = "healthy" if redis_health["healthy"] else "unhealthy"
    except Exception as e:
        checks["redis"] = f"unhealthy: {str(e)}"
        status = "unhealthy"
    
    return {
        "status": status,
        "app": settings.app_name,
        "version": settings.app_version,
        "debug": settings.debug,
        "checks": checks
    }


# Ready check endpoint (for k8s readiness probe)
@app.get("/ready", tags=["health"])
async def ready_check(db: Session = Depends(get_db)):
    """
    Readiness check endpoint.
    
    Checks if all required services are available.
    """
    checks = {
        "database": False,
        "redis": False,
        "ai_service": True  # AI service is optional
    }
    
    # Check database
    try:
        from sqlalchemy import text
        db.execute(text("SELECT 1"))
        checks["database"] = True
    except Exception:
        checks["database"] = False
    
    # Check Redis
    try:
        from src.infrastructure.redis import RedisConnectionFactory
        redis_health = await RedisConnectionFactory.health_check()
        checks["redis"] = redis_health["healthy"]
    except Exception:
        checks["redis"] = False
    
    all_ready = all(checks.values())
    
    return {
        "ready": all_ready,
        "checks": checks
    }


# Root endpoint
@app.get("/", tags=["root"])
async def root():
    """Root endpoint with API information."""
    return {
        "message": f"Welcome to {settings.app_name}",
        "version": settings.app_version,
        "docs": f"{settings.api_v1_prefix}/docs",
        "health": "/health",
        "ready": "/ready"
    }


# Include v1 routers
app.include_router(
    auth.router,
    prefix=f"{settings.api_v1_prefix}",
    tags=["authentication"]
)

app.include_router(
    startups.router,
    prefix=f"{settings.api_v1_prefix}",
    tags=["startups"]
)

app.include_router(
    vcs.router,
    prefix=f"{settings.api_v1_prefix}",
    tags=["vcs"]
)

app.include_router(
    matches.router,
    prefix=f"{settings.api_v1_prefix}",
    tags=["matches"]
)

app.include_router(
    health.router,
    prefix=f"{settings.api_v1_prefix}",
    tags=["health"]
)

app.include_router(
    tasks.router,
    prefix=f"{settings.api_v1_prefix}",
    tags=["tasks"]
)

app.include_router(
    scraping.router,
    prefix=f"{settings.api_v1_prefix}/scraping",
    tags=["scraping"]
)

app.include_router(
    discovery.router,
    prefix=f"{settings.api_v1_prefix}",
    tags=["discovery"]
)

app.include_router(
    vc_thesis.router,
    prefix=f"{settings.api_v1_prefix}",
    tags=["vc-thesis"]
)

app.include_router(
    connections.router,
    prefix=f"{settings.api_v1_prefix}",
    tags=["connections"]
)

app.include_router(
    hybrid_discovery.router,
    prefix=f"{settings.api_v1_prefix}",
    tags=["hybrid_discovery"]
)

app.include_router(
    vc_enrichment.router,
    prefix=f"{settings.api_v1_prefix}",
    tags=["vc-enrichment"]
)

app.include_router(
    cached_discovery.router,
    prefix=f"{settings.api_v1_prefix}",
    tags=["cached-discovery"]
)

app.include_router(
    introduction_workflow.router,
    prefix=f"{settings.api_v1_prefix}",
    tags=["introductions"]
)

app.include_router(
    market_intelligence.router,
    prefix=f"{settings.api_v1_prefix}",
    tags=["market-intelligence"]
)

app.include_router(
    signals.router,
    prefix=f"{settings.api_v1_prefix}",
    tags=["signals"]
)


# API info endpoint
@app.get(f"{settings.api_v1_prefix}/info", tags=["info"])
async def api_info():
    """Get API version and capability information."""
    return {
        "version": "v1",
        "capabilities": {
            "startups": {
                "crud": True,
                "ai_analysis": True,
                "batch_operations": False
            },
            "vcs": {
                "crud": True,
                "thesis_extraction": True,
                "portfolio_tracking": True
            },
            "matching": {
                "single_match": True,
                "batch_match": True,
                "ai_enhanced": True,
                "recommendations": True
            },
            "warm_intros": {
                "connection_management": True,
                "path_finding": True,
                "introduction_requests": True,
                "network_analytics": True,
                "max_path_depth": 3
            }
        },
        "authentication": {
            "jwt": True,
            "api_key": True,
            "required_for": ["ai_analysis", "write_operations"]
        },
        "rate_limits": {
            "enabled": settings.rate_limit_enabled,
            "per_minute": settings.rate_limit_per_minute,
            "per_hour": settings.rate_limit_per_hour
        }
    }


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "src.api.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        workers=settings.workers if not settings.debug else 1,
        log_level=settings.log_level.lower()
    )