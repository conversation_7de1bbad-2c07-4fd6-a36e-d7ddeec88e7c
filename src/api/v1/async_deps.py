"""Async dependencies for gradual migration to async architecture."""

from typing import AsyncGenerator, Union
from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from src.database.async_setup import get_async_db
from src.database.repositories.async_startup_repository import AsyncStartupRepository
from src.database.repositories.async_vc_repository import AsyncVCRepository
from src.database.repositories.async_match_repository import AsyncMatchRepository
from src.database.repositories.async_user_repository import AsyncUserRepository
from src.core.config import settings
import logging

logger = logging.getLogger(__name__)


# Feature flag for async database usage
USE_ASYNC_DB = settings.use_async_db


async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Get async database session.
    
    This is the primary dependency for async database operations.
    """
    async for session in get_async_db():
        yield session


# Import sync deps for feature flag switching
from src.api.v1.deps import get_database
from src.database.repositories.startup_repository import StartupRepository
from src.database.repositories.vc_repository import VCRepository
from src.database.repositories.match_repository import MatchRepository
from src.database.repositories.user_repository import UserRepository


def get_startup_repository(
    async_session: AsyncSession = Depends(get_async_session),
    sync_session: Session = Depends(get_database)
) -> Union[AsyncStartupRepository, StartupRepository]:
    """
    Smart repository getter for startups.
    
    Returns async or sync repository based on feature flag.
    """
    if USE_ASYNC_DB:
        logger.debug("Using async startup repository")
        return AsyncStartupRepository(async_session)
    else:
        logger.debug("Using sync startup repository")
        return StartupRepository(sync_session)


def get_vc_repository(
    async_session: AsyncSession = Depends(get_async_session),
    sync_session: Session = Depends(get_database)
) -> Union[AsyncVCRepository, VCRepository]:
    """
    Smart repository getter for VCs.
    
    Returns async or sync repository based on feature flag.
    """
    if USE_ASYNC_DB:
        logger.debug("Using async VC repository")
        return AsyncVCRepository(async_session)
    else:
        logger.debug("Using sync VC repository")
        return VCRepository(sync_session)


def get_match_repository(
    async_session: AsyncSession = Depends(get_async_session),
    sync_session: Session = Depends(get_database)
) -> Union[AsyncMatchRepository, MatchRepository]:
    """
    Smart repository getter for matches.
    
    Returns async or sync repository based on feature flag.
    """
    if USE_ASYNC_DB:
        logger.debug("Using async match repository")
        return AsyncMatchRepository(async_session)
    else:
        logger.debug("Using sync match repository")
        return MatchRepository(sync_session)


def get_user_repository(
    async_session: AsyncSession = Depends(get_async_session),
    sync_session: Session = Depends(get_database)
) -> Union[AsyncUserRepository, UserRepository]:
    """
    Smart repository getter for users.
    
    Returns async or sync repository based on feature flag.
    """
    if USE_ASYNC_DB:
        logger.debug("Using async user repository")
        return AsyncUserRepository(async_session)
    else:
        logger.debug("Using sync user repository")
        return UserRepository(sync_session)