"""Centralized error handling for API endpoints."""

from functools import wraps
from typing import Callable
from fastapi import HTTPException, status
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
import logging

from src.api.errors import BadRequestError, NotFoundError

logger = logging.getLogger(__name__)


def handle_api_errors(func: Callable) -> Callable:
    """
    Decorator for consistent error handling across API endpoints.
    
    Handles:
    - ValueError -> BadRequestError (400)
    - NotFoundError -> stays as is (404)
    - IntegrityError -> BadRequestError (400) with specific message
    - SQLAlchemyError -> InternalServerError (500)
    - Generic Exception -> InternalServerError (500)
    """
    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except (BadRequestError, NotFoundError, HTTPException):
            # These are already proper HTTP exceptions, re-raise them
            raise
        except ValueError as e:
            logger.warning(f"Bad request in {func.__name__}: {str(e)}")
            raise BadRequestError(str(e))
        except IntegrityError as e:
            logger.warning(f"Integrity error in {func.__name__}: {str(e)}")
            # Extract meaningful message from database error
            if "duplicate key" in str(e).lower():
                raise BadRequestError("A resource with these details already exists")
            elif "foreign key" in str(e).lower():
                raise BadRequestError("Referenced resource does not exist")
            else:
                raise BadRequestError("Database constraint violation")
        except SQLAlchemyError as e:
            logger.error(f"Database error in {func.__name__}: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Database operation failed"
            )
        except Exception as e:
            logger.error(f"Unexpected error in {func.__name__}: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An unexpected error occurred"
            )
    
    return wrapper