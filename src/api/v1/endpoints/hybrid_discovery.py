"""Hybrid discovery endpoints combining keyword and semantic search."""

from typing import Dict, Any, List, Optional
from uuid import UUID
import logging

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import text

from src.api.v1.deps import get_current_user, get_database
from src.core.config import get_settings
from src.core.ai.embeddings import HybridEmbeddingService
from src.database.repositories.hybrid_search_repository import HybridSearchRepository
from src.database.repositories.vc_repository import PostgresVCRepository
from src.database.repositories.startup_repository import PostgresStartupRepository

logger = logging.getLogger(__name__)
settings = get_settings()

router = APIRouter(prefix="/hybrid", tags=["hybrid_discovery"])


class HybridScoringStrategy:
    """Dynamic scoring strategies for different use cases."""
    
    @staticmethod
    def get_weights_for_strategy(strategy: str) -> Dict[str, float]:
        """Return optimal weights for different search strategies."""
        
        strategies = {
            # Precise: Favor exact keyword matches
            "precise": {
                "keyword_weight": 0.7,
                "semantic_weight": 0.3,
                "min_score": 0.4
            },
            
            # Balanced: Equal weight to both approaches
            "balanced": {
                "keyword_weight": 0.5,
                "semantic_weight": 0.5,
                "min_score": 0.3
            },
            
            # Exploratory: Favor semantic understanding
            "exploratory": {
                "keyword_weight": 0.3,
                "semantic_weight": 0.7,
                "min_score": 0.2
            },
            
            # Semantic-only: Pure semantic search
            "semantic": {
                "keyword_weight": 0.0,
                "semantic_weight": 1.0,
                "min_score": 0.3
            }
        }
        
        return strategies.get(strategy, strategies["balanced"])
    
    @staticmethod
    def apply_business_logic_boosts(
        base_score: float,
        entity: Dict[str, Any],
        entity_type: str,
        criteria: Dict[str, Any]
    ) -> tuple[float, List[str]]:
        """Apply business logic boosts to base hybrid score."""
        
        boosted_score = base_score
        boost_reasons = []
        
        if entity_type == "startup":
            # Revenue traction boost
            monthly_revenue = entity.get('monthly_revenue', 0)
            if monthly_revenue > 500000:
                boosted_score += 0.15
                boost_reasons.append("Exceptional revenue traction")
            elif monthly_revenue > 100000:
                boosted_score += 0.1
                boost_reasons.append("Strong revenue traction")
            elif monthly_revenue > 50000:
                boosted_score += 0.05
                boost_reasons.append("Good revenue traction")
            
            # Perfect sector match boost
            if entity.get('sector') in criteria.get('sectors', []):
                boosted_score += 0.15
                boost_reasons.append("Perfect sector alignment")
            
            # Stage match boost
            if entity.get('stage') in criteria.get('stages', []):
                boosted_score += 0.1
                boost_reasons.append("Target stage match")
            
            # Hot sector boost
            hot_sectors = ['AI/ML', 'Climate Tech', 'Fintech', 'Healthcare', 'B2B SaaS']
            if entity.get('sector') in hot_sectors:
                boosted_score += 0.05
                boost_reasons.append(f"Hot sector: {entity.get('sector')}")
        
        elif entity_type == "vc":
            # Check size match boost
            funding_needed = criteria.get('funding_amount', 0)
            if funding_needed > 0:
                check_min = entity.get('check_size_min', 0)
                check_max = entity.get('check_size_max', 0)
                if check_min <= funding_needed <= check_max:
                    boosted_score += 0.15
                    boost_reasons.append("Perfect check size match")
            
            # Multi-sector VC boost (flexibility)
            sectors = entity.get('sectors', [])
            if len(sectors) > 3:
                boosted_score += 0.05
                boost_reasons.append("Multi-sector investor")
        
        return min(boosted_score, 1.0), boost_reasons


@router.post("/discover-startups")
async def hybrid_discover_startups(
    search_query: str = Query(default="", description="Natural language search query"),
    search_strategy: str = Query(
        default="balanced", 
        description="Search strategy",
        regex="^(precise|balanced|exploratory|semantic)$"
    ),
    sectors: Optional[List[str]] = Query(default=None, description="Filter by sectors"),
    stages: Optional[List[str]] = Query(default=None, description="Filter by stages"),
    min_revenue: float = Query(default=0, description="Minimum monthly revenue"),
    max_revenue: Optional[float] = Query(default=None, description="Maximum monthly revenue"),
    limit: int = Query(default=20, le=100, description="Maximum results"),
    explain_matches: bool = Query(default=False, description="Include match explanations"),
    current_user_id: UUID = Depends(get_current_user),
    db: Session = Depends(get_database)
) -> Dict[str, Any]:
    """
    Discover startups using hybrid keyword + semantic search.
    
    This endpoint combines traditional keyword matching with AI-powered semantic understanding
    to find the most relevant startups based on your search query.
    """
    
    try:
        # Initialize services
        embedding_service = HybridEmbeddingService(settings.OPENAI_API_KEY)
        hybrid_repo = HybridSearchRepository(db)
        
        # Get scoring weights for strategy
        weights = HybridScoringStrategy.get_weights_for_strategy(search_strategy)
        
        # Generate query embedding if semantic search is enabled
        query_embedding = []
        if weights["semantic_weight"] > 0 and search_query:
            query_embedding = await embedding_service.generate_query_embedding(search_query)
        
        # Perform hybrid search
        results = hybrid_repo.hybrid_startup_search(
            query_embedding=query_embedding,
            keyword_query=search_query,
            vc_sectors=sectors,
            vc_stages=stages,
            min_funding=min_revenue,
            max_funding=max_revenue or float('inf'),
            limit=limit * 2,  # Get extra for post-filtering
            keyword_weight=weights["keyword_weight"],
            semantic_weight=weights["semantic_weight"],
            min_score=weights["min_score"]
        )
        
        # Apply business logic boosts
        enhanced_results = []
        for result in results:
            boosted_score, boost_reasons = HybridScoringStrategy.apply_business_logic_boosts(
                result["hybrid_score"],
                result,
                "startup",
                {"sectors": sectors, "stages": stages}
            )
            
            result["final_score"] = boosted_score
            result["boost_reasons"] = boost_reasons
            
            # Add match explanation if requested
            if explain_matches and result.get('id'):
                explanation = hybrid_repo.explain_hybrid_match(
                    entity_type="startup",
                    entity_id=result["id"],
                    query_embedding=query_embedding,
                    keyword_query=search_query
                )
                result["match_explanation"] = explanation
            
            enhanced_results.append(result)
        
        # Sort by final score and apply limit
        enhanced_results.sort(key=lambda x: x["final_score"], reverse=True)
        final_results = enhanced_results[:limit]
        
        return {
            "search_query": search_query,
            "search_strategy": search_strategy,
            "filters": {
                "sectors": sectors,
                "stages": stages,
                "min_revenue": min_revenue,
                "max_revenue": max_revenue
            },
            "discovered_startups": final_results,
            "search_metadata": {
                "total_candidates": len(results),
                "after_boost_filtering": len(enhanced_results),
                "final_results": len(final_results),
                "weights_used": weights,
                "hybrid_search_enabled": True,
                "semantic_enabled": weights["semantic_weight"] > 0,
                "keyword_enabled": weights["keyword_weight"] > 0
            }
        }
        
    except Exception as e:
        logger.error(f"Error in hybrid startup discovery: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Search error: {str(e)}")


@router.post("/vcs/{vc_id}/discover-startups")
async def hybrid_vc_discover_startups(
    vc_id: UUID,
    search_query: str = Query(default="", description="Additional search criteria"),
    search_strategy: str = Query(
        default="balanced",
        description="Search strategy", 
        regex="^(precise|balanced|exploratory|semantic)$"
    ),
    limit: int = Query(default=20, le=100),
    min_score: Optional[float] = Query(default=None, ge=0.0, le=1.0),
    explain_matches: bool = Query(default=False),
    current_user_id: UUID = Depends(get_current_user),
    db: Session = Depends(get_database)
) -> Dict[str, Any]:
    """
    Discover startups for a specific VC using their thesis and preferences.
    
    This combines the VC's investment thesis with semantic search to find
    the most relevant startups that match their investment criteria.
    """
    
    try:
        # Get VC details
        vc_repo = PostgresVCRepository(db)
        vc = vc_repo.get(vc_id)
        
        if not vc:
            raise HTTPException(status_code=404, detail="VC not found")
        
        # Initialize services
        embedding_service = HybridEmbeddingService(settings.OPENAI_API_KEY)
        hybrid_repo = HybridSearchRepository(db)
        
        # Create combined query from VC thesis + user query
        combined_query = f"{vc.thesis or ''} {search_query}".strip()
        
        # Get scoring weights
        weights = HybridScoringStrategy.get_weights_for_strategy(search_strategy)
        
        # Use custom min_score if provided
        if min_score is not None:
            weights["min_score"] = min_score
        
        # Generate query embedding
        query_embedding = []
        if weights["semantic_weight"] > 0 and combined_query:
            query_embedding = await embedding_service.generate_query_embedding(combined_query)
        
        # Perform hybrid search
        results = hybrid_repo.hybrid_startup_search(
            query_embedding=query_embedding,
            keyword_query=search_query,  # Use only additional query for keyword matching
            vc_sectors=vc.sectors,
            vc_stages=vc.stages,
            min_funding=vc.check_size_min or 0,
            max_funding=vc.check_size_max or float('inf'),
            limit=limit * 2,
            keyword_weight=weights["keyword_weight"],
            semantic_weight=weights["semantic_weight"],
            min_score=weights["min_score"]
        )
        
        # Apply business logic boosts
        enhanced_results = []
        for result in results:
            boosted_score, boost_reasons = HybridScoringStrategy.apply_business_logic_boosts(
                result["hybrid_score"],
                result,
                "startup",
                {"sectors": vc.sectors, "stages": vc.stages}
            )
            
            result["final_score"] = boosted_score
            result["boost_reasons"] = boost_reasons
            
            # Add match explanation if requested
            if explain_matches and result.get('id'):
                explanation = hybrid_repo.explain_hybrid_match(
                    entity_type="startup",
                    entity_id=result["id"],
                    query_embedding=query_embedding,
                    keyword_query=search_query
                )
                result["match_explanation"] = explanation
                result["thesis_alignment"] = explanation.get("semantic_similarity", 0)
            
            enhanced_results.append(result)
        
        # Sort and limit
        enhanced_results.sort(key=lambda x: x["final_score"], reverse=True)
        final_results = enhanced_results[:limit]
        
        return {
            "vc": {
                "id": str(vc.id),
                "firm_name": vc.firm_name,
                "thesis": vc.thesis,
                "sectors": vc.sectors,
                "stages": vc.stages
            },
            "search_query": search_query,
            "combined_query": combined_query,
            "search_strategy": search_strategy,
            "discovered_startups": final_results,
            "search_metadata": {
                "total_candidates": len(results),
                "after_filtering": len(enhanced_results),
                "final_results": len(final_results),
                "weights_used": weights,
                "thesis_based_search": True,
                "hybrid_enabled": True
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in VC hybrid discovery: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Search error: {str(e)}")


@router.post("/discover-vcs")
async def hybrid_discover_vcs(
    search_query: str = Query(default="", description="Natural language search query"),
    search_strategy: str = Query(
        default="balanced",
        regex="^(precise|balanced|exploratory|semantic)$"
    ),
    sector: Optional[str] = Query(default=None, description="Startup sector"),
    stage: Optional[str] = Query(default=None, description="Startup stage"),
    funding_amount: Optional[float] = Query(default=None, description="Desired funding amount"),
    limit: int = Query(default=20, le=100),
    explain_matches: bool = Query(default=False),
    current_user_id: UUID = Depends(get_current_user),
    db: Session = Depends(get_database)
) -> Dict[str, Any]:
    """
    Discover VCs using hybrid search based on their investment thesis.
    
    Find investors whose thesis aligns with your search criteria using
    both keyword matching and semantic understanding.
    """
    
    try:
        # Initialize services
        embedding_service = HybridEmbeddingService(settings.OPENAI_API_KEY)
        hybrid_repo = HybridSearchRepository(db)
        
        # Get scoring weights
        weights = HybridScoringStrategy.get_weights_for_strategy(search_strategy)
        
        # Generate query embedding
        query_embedding = []
        if weights["semantic_weight"] > 0 and search_query:
            query_embedding = await embedding_service.generate_query_embedding(search_query)
        
        # Perform hybrid search
        results = hybrid_repo.hybrid_vc_search(
            query_embedding=query_embedding,
            keyword_query=search_query,
            startup_sector=sector,
            startup_stage=stage,
            funding_amount=funding_amount,
            limit=limit * 2,
            keyword_weight=weights["keyword_weight"],
            semantic_weight=weights["semantic_weight"],
            min_score=weights["min_score"]
        )
        
        # Apply business logic boosts
        enhanced_results = []
        for result in results:
            boosted_score, boost_reasons = HybridScoringStrategy.apply_business_logic_boosts(
                result["hybrid_score"],
                result,
                "vc",
                {"funding_amount": funding_amount}
            )
            
            result["final_score"] = boosted_score
            result["boost_reasons"] = boost_reasons
            
            # Add match explanation if requested
            if explain_matches and result.get('id'):
                explanation = hybrid_repo.explain_hybrid_match(
                    entity_type="vc",
                    entity_id=result["id"],
                    query_embedding=query_embedding,
                    keyword_query=search_query
                )
                result["match_explanation"] = explanation
            
            enhanced_results.append(result)
        
        # Sort and limit
        enhanced_results.sort(key=lambda x: x["final_score"], reverse=True)
        final_results = enhanced_results[:limit]
        
        return {
            "search_query": search_query,
            "search_strategy": search_strategy,
            "filters": {
                "sector": sector,
                "stage": stage,
                "funding_amount": funding_amount
            },
            "discovered_vcs": final_results,
            "search_metadata": {
                "total_candidates": len(results),
                "after_filtering": len(enhanced_results),
                "final_results": len(final_results),
                "weights_used": weights,
                "hybrid_enabled": True
            }
        }
        
    except Exception as e:
        logger.error(f"Error in hybrid VC discovery: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Search error: {str(e)}")


@router.get("/search-status")
async def get_search_status(
    current_user_id: UUID = Depends(get_current_user),
    db: Session = Depends(get_database)
) -> Dict[str, Any]:
    """Get the status of hybrid search capabilities."""
    
    try:
        hybrid_repo = HybridSearchRepository(db)
        
        # Get entities without embeddings
        startups_without = hybrid_repo.get_entities_without_embeddings("startup", limit=10)
        vcs_without = hybrid_repo.get_entities_without_embeddings("vc", limit=10)
        
        # Check if vector extension is installed
        result = db.execute(text("SELECT * FROM pg_extension WHERE extname = 'vector'"))
        vector_installed = result.fetchone() is not None
        
        return {
            "hybrid_search_enabled": True,
            "vector_extension_installed": vector_installed,
            "embeddings_status": {
                "startups_without_embeddings": len(startups_without),
                "vcs_without_embeddings": len(vcs_without),
                "sample_startups_needing_embeddings": [
                    {"id": str(s["id"]), "name": s["name"]} for s in startups_without[:5]
                ],
                "sample_vcs_needing_embeddings": [
                    {"id": str(v["id"]), "name": v["firm_name"]} for v in vcs_without[:5]
                ]
            },
            "available_strategies": ["precise", "balanced", "exploratory", "semantic"],
            "default_strategy": "balanced"
        }
        
    except Exception as e:
        logger.error(f"Error checking search status: {str(e)}")
        return {
            "hybrid_search_enabled": False,
            "error": str(e)
        }