"""Market Intelligence Dashboard API endpoints."""

from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func, text
import logging

from src.api.v1.deps import get_database, get_current_user_optional
from src.database.repositories.startup_repository import StartupRepository
from src.database.repositories.vc_repository import VCRepository
from src.database.repositories.match_repository import MatchRepository

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/market-intelligence", tags=["market-intelligence"])


@router.get("/overview")
async def get_market_overview(
    db: Session = Depends(get_database),
    current_user: str = Depends(get_current_user_optional)
) -> Dict[str, Any]:
    """Get high-level market overview statistics."""
    try:
        startup_repo = StartupRepository(db)
        vc_repo = VCRepository(db)
        match_repo = MatchRepository(db)
        
        # Get counts
        total_startups = await startup_repo.count()
        total_vcs = await vc_repo.count()
        total_matches = await match_repo.count()
        
        # Get recent activity (last 30 days)
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        
        recent_startups = await startup_repo.count_created_after(thirty_days_ago)
        recent_vcs = await vc_repo.count_created_after(thirty_days_ago)
        recent_matches = await match_repo.count_created_after(thirty_days_ago)
        
        # Calculate growth rates
        growth_rate_startups = (recent_startups / max(total_startups - recent_startups, 1)) * 100
        growth_rate_vcs = (recent_vcs / max(total_vcs - recent_vcs, 1)) * 100
        
        return {
            "overview": {
                "total_startups": total_startups,
                "total_vcs": total_vcs,
                "total_matches": total_matches,
                "total_connections": total_startups * total_vcs  # Potential connections
            },
            "recent_activity": {
                "new_startups_30d": recent_startups,
                "new_vcs_30d": recent_vcs,
                "new_matches_30d": recent_matches
            },
            "growth_rates": {
                "startups_growth_30d": round(growth_rate_startups, 2),
                "vcs_growth_30d": round(growth_rate_vcs, 2)
            },
            "market_efficiency": {
                "match_rate": round((total_matches / max(total_startups * total_vcs, 1)) * 100, 2),
                "avg_matches_per_startup": round(total_matches / max(total_startups, 1), 2),
                "avg_matches_per_vc": round(total_matches / max(total_vcs, 1), 2)
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting market overview: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/sector-analysis")
async def get_sector_analysis(
    timeframe: str = Query(default="all", description="all, 1y, 6m, 3m, 1m"),
    db: Session = Depends(get_database)
) -> Dict[str, Any]:
    """Analyze market trends by sector."""
    try:
        # Determine date filter
        date_filter = None
        if timeframe == "1y":
            date_filter = datetime.utcnow() - timedelta(days=365)
        elif timeframe == "6m":
            date_filter = datetime.utcnow() - timedelta(days=180)
        elif timeframe == "3m":
            date_filter = datetime.utcnow() - timedelta(days=90)
        elif timeframe == "1m":
            date_filter = datetime.utcnow() - timedelta(days=30)
        
        # Get sector distribution for startups
        startup_sectors = db.execute(
            text("""
                SELECT sector, COUNT(*) as count
                FROM startups
                WHERE (:date_filter IS NULL OR created_at >= :date_filter)
                GROUP BY sector
                ORDER BY count DESC
            """),
            {"date_filter": date_filter}
        ).fetchall()
        
        # Get sector preferences for VCs
        vc_sectors = db.execute(
            text("""
                SELECT unnest(sectors) as sector, COUNT(*) as count
                FROM vcs
                WHERE (:date_filter IS NULL OR created_at >= :date_filter)
                GROUP BY sector
                ORDER BY count DESC
            """),
            {"date_filter": date_filter}
        ).fetchall()
        
        # Get sector match rates
        sector_matches = db.execute(
            text("""
                SELECT s.sector, 
                       COUNT(DISTINCT m.id) as match_count,
                       COUNT(DISTINCT s.id) as startup_count,
                       ROUND(COUNT(DISTINCT m.id)::FLOAT / NULLIF(COUNT(DISTINCT s.id), 0) * 100, 2) as match_rate
                FROM startups s
                LEFT JOIN matches m ON s.id = m.startup_id
                WHERE (:date_filter IS NULL OR s.created_at >= :date_filter)
                GROUP BY s.sector
                ORDER BY match_rate DESC
            """),
            {"date_filter": date_filter}
        ).fetchall()
        
        return {
            "timeframe": timeframe,
            "startup_sectors": [
                {"sector": row.sector, "count": row.count}
                for row in startup_sectors
            ],
            "vc_sector_interests": [
                {"sector": row.sector, "count": row.count}
                for row in vc_sectors
            ],
            "sector_performance": [
                {
                    "sector": row.sector,
                    "startup_count": row.startup_count,
                    "match_count": row.match_count,
                    "match_rate": float(row.match_rate) if row.match_rate else 0
                }
                for row in sector_matches
            ],
            "hot_sectors": [row.sector for row in startup_sectors[:5]],
            "underserved_sectors": [
                row.sector for row in sector_matches 
                if row.match_rate and float(row.match_rate) < 10
            ][:5]
        }
        
    except Exception as e:
        logger.error(f"Error in sector analysis: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/stage-analysis")
async def get_stage_analysis(
    db: Session = Depends(get_database)
) -> Dict[str, Any]:
    """Analyze market trends by funding stage."""
    try:
        # Stage distribution for startups
        startup_stages = db.execute(
            text("""
                SELECT stage, COUNT(*) as count
                FROM startups
                GROUP BY stage
                ORDER BY 
                    CASE stage
                        WHEN 'Pre-seed' THEN 1
                        WHEN 'Seed' THEN 2
                        WHEN 'Series A' THEN 3
                        WHEN 'Series B' THEN 4
                        WHEN 'Series C' THEN 5
                        ELSE 6
                    END
            """)
        ).fetchall()
        
        # Stage preferences for VCs
        vc_stages = db.execute(
            text("""
                SELECT unnest(stages) as stage, COUNT(*) as count
                FROM vcs
                GROUP BY stage
                ORDER BY count DESC
            """)
        ).fetchall()
        
        # Funding gap analysis
        funding_gaps = db.execute(
            text("""
                WITH startup_demand AS (
                    SELECT stage, COUNT(*) as demand
                    FROM startups
                    GROUP BY stage
                ),
                vc_supply AS (
                    SELECT unnest(stages) as stage, COUNT(*) as supply
                    FROM vcs
                    GROUP BY stage
                )
                SELECT 
                    COALESCE(d.stage, s.stage) as stage,
                    COALESCE(d.demand, 0) as startup_demand,
                    COALESCE(s.supply, 0) as vc_supply,
                    COALESCE(d.demand, 0) - COALESCE(s.supply, 0) as gap
                FROM startup_demand d
                FULL OUTER JOIN vc_supply s ON d.stage = s.stage
                ORDER BY gap DESC
            """)
        ).fetchall()
        
        return {
            "startup_stages": [
                {"stage": row.stage, "count": row.count}
                for row in startup_stages
            ],
            "vc_stage_preferences": [
                {"stage": row.stage, "count": row.count}
                for row in vc_stages
            ],
            "funding_gaps": [
                {
                    "stage": row.stage,
                    "startup_demand": row.startup_demand,
                    "vc_supply": row.vc_supply,
                    "gap": row.gap,
                    "gap_type": "oversupply" if row.gap < 0 else "undersupply"
                }
                for row in funding_gaps
            ],
            "insights": {
                "most_competitive_stage": funding_gaps[0].stage if funding_gaps and funding_gaps[0].gap > 0 else None,
                "best_funded_stage": funding_gaps[-1].stage if funding_gaps and funding_gaps[-1].gap < 0 else None
            }
        }
        
    except Exception as e:
        logger.error(f"Error in stage analysis: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/geographic-distribution")
async def get_geographic_distribution(
    db: Session = Depends(get_database)
) -> Dict[str, Any]:
    """Analyze geographic distribution of startups and VCs."""
    try:
        # This would need location data in the models
        # For now, return mock data structure
        return {
            "startup_locations": {
                "San Francisco Bay Area": 145,
                "New York": 89,
                "London": 67,
                "Berlin": 45,
                "Singapore": 38,
                "Other": 156
            },
            "vc_locations": {
                "San Francisco Bay Area": 78,
                "New York": 45,
                "London": 32,
                "Boston": 28,
                "Los Angeles": 22,
                "Other": 95
            },
            "cross_border_activity": {
                "international_matches": 89,
                "domestic_matches": 267,
                "cross_border_rate": 25.0
            }
        }
        
    except Exception as e:
        logger.error(f"Error in geographic analysis: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/trending-metrics")
async def get_trending_metrics(
    period: str = Query(default="7d", description="7d, 30d, 90d"),
    db: Session = Depends(get_database)
) -> Dict[str, Any]:
    """Get trending metrics and signals."""
    try:
        # Parse period
        days = 7
        if period == "30d":
            days = 30
        elif period == "90d":
            days = 90
        
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        # Hot startups (most matched recently)
        hot_startups = db.execute(
            text("""
                SELECT s.id, s.name, s.sector, COUNT(m.id) as match_count
                FROM startups s
                JOIN matches m ON s.id = m.startup_id
                WHERE m.created_at >= :cutoff_date
                GROUP BY s.id, s.name, s.sector
                ORDER BY match_count DESC
                LIMIT 10
            """),
            {"cutoff_date": cutoff_date}
        ).fetchall()
        
        # Active VCs (most matches recently)
        active_vcs = db.execute(
            text("""
                SELECT v.id, v.name, COUNT(m.id) as match_count
                FROM vcs v
                JOIN matches m ON v.id = m.vc_id
                WHERE m.created_at >= :cutoff_date
                GROUP BY v.id, v.name
                ORDER BY match_count DESC
                LIMIT 10
            """),
            {"cutoff_date": cutoff_date}
        ).fetchall()
        
        # Emerging sectors (highest growth)
        emerging_sectors = db.execute(
            text("""
                WITH recent_counts AS (
                    SELECT sector, COUNT(*) as recent_count
                    FROM startups
                    WHERE created_at >= :cutoff_date
                    GROUP BY sector
                ),
                total_counts AS (
                    SELECT sector, COUNT(*) as total_count
                    FROM startups
                    WHERE created_at < :cutoff_date
                    GROUP BY sector
                )
                SELECT 
                    r.sector,
                    r.recent_count,
                    COALESCE(t.total_count, 1) as baseline_count,
                    ROUND((r.recent_count::FLOAT / COALESCE(t.total_count, 1)) * 100, 2) as growth_rate
                FROM recent_counts r
                LEFT JOIN total_counts t ON r.sector = t.sector
                ORDER BY growth_rate DESC
                LIMIT 5
            """),
            {"cutoff_date": cutoff_date}
        ).fetchall()
        
        return {
            "period": period,
            "hot_startups": [
                {
                    "id": str(row.id),
                    "name": row.name,
                    "sector": row.sector,
                    "match_count": row.match_count
                }
                for row in hot_startups
            ],
            "most_active_vcs": [
                {
                    "id": str(row.id),
                    "name": row.name,
                    "match_count": row.match_count
                }
                for row in active_vcs
            ],
            "emerging_sectors": [
                {
                    "sector": row.sector,
                    "new_startups": row.recent_count,
                    "growth_rate": float(row.growth_rate)
                }
                for row in emerging_sectors
            ]
        }
        
    except Exception as e:
        logger.error(f"Error getting trending metrics: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/match-analytics")
async def get_match_analytics(
    db: Session = Depends(get_database)
) -> Dict[str, Any]:
    """Get detailed match analytics."""
    try:
        # Match success metrics
        match_stats = db.execute(
            text("""
                SELECT 
                    COUNT(*) as total_matches,
                    AVG(score) as avg_match_score,
                    MIN(score) as min_score,
                    MAX(score) as max_score,
                    PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY score) as median_score
                FROM matches
            """)
        ).fetchone()
        
        # Match reasons distribution
        match_reasons = db.execute(
            text("""
                SELECT 
                    unnest(string_to_array(reasons, ',')) as reason,
                    COUNT(*) as count
                FROM matches
                WHERE reasons IS NOT NULL
                GROUP BY reason
                ORDER BY count DESC
                LIMIT 10
            """)
        ).fetchall()
        
        # Time to match analysis
        time_to_match = db.execute(
            text("""
                WITH startup_matches AS (
                    SELECT 
                        s.id,
                        s.created_at as startup_created,
                        MIN(m.created_at) as first_match
                    FROM startups s
                    LEFT JOIN matches m ON s.id = m.startup_id
                    GROUP BY s.id, s.created_at
                )
                SELECT 
                    AVG(EXTRACT(day FROM (first_match - startup_created))) as avg_days_to_match,
                    COUNT(CASE WHEN first_match IS NOT NULL THEN 1 END) as matched_startups,
                    COUNT(CASE WHEN first_match IS NULL THEN 1 END) as unmatched_startups
                FROM startup_matches
            """)
        ).fetchone()
        
        return {
            "match_statistics": {
                "total_matches": match_stats.total_matches if match_stats else 0,
                "average_score": float(match_stats.avg_match_score) if match_stats and match_stats.avg_match_score else 0,
                "score_range": {
                    "min": float(match_stats.min_score) if match_stats and match_stats.min_score else 0,
                    "max": float(match_stats.max_score) if match_stats and match_stats.max_score else 0,
                    "median": float(match_stats.median_score) if match_stats and match_stats.median_score else 0
                }
            },
            "top_match_reasons": [
                {"reason": row.reason.strip(), "count": row.count}
                for row in match_reasons
            ] if match_reasons else [],
            "time_to_match": {
                "avg_days": float(time_to_match.avg_days_to_match) if time_to_match and time_to_match.avg_days_to_match else 0,
                "matched_startups": time_to_match.matched_startups if time_to_match else 0,
                "unmatched_startups": time_to_match.unmatched_startups if time_to_match else 0,
                "match_rate": round(
                    (time_to_match.matched_startups / (time_to_match.matched_startups + time_to_match.unmatched_startups)) * 100, 2
                ) if time_to_match and (time_to_match.matched_startups + time_to_match.unmatched_startups) > 0 else 0
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting match analytics: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")