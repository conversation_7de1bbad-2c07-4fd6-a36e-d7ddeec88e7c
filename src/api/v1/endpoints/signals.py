"""API endpoints for signal monitoring system."""

from fastapi import APIRout<PERSON>, Depends, HTTPException, Query, BackgroundTasks
from typing import Dict, Any, List, Optional
from uuid import UUID
from sqlalchemy.orm import Session
from redis import Redis
import logging

from src.api.v1.deps import get_current_user, get_database, get_redis_client
from src.core.services.signal_monitoring_service import (
    SignalMonitoringService,
    SignalType,
    SignalRule
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/signals", tags=["signals"])


@router.post("/rules")
async def create_signal_rule(
    rule_data: Dict[str, Any],
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_database),
    redis: Redis = Depends(get_redis_client)
) -> Dict[str, Any]:
    """
    Create a new signal monitoring rule.
    
    Rule format:
    {
        "name": "Monitor AI startups",
        "type": "new_startup_in_sector",
        "conditions": {
            "sector": ["AI/ML", "Deep Tech"],
            "stage": ["Seed", "Series A"]
        },
        "enabled": true
    }
    """
    try:
        service = SignalMonitoringService(db, redis)
        
        # Validate signal type
        try:
            signal_type = SignalType(rule_data["type"])
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid signal type. Must be one of: {[t.value for t in SignalType]}"
            )
        
        # Create rule
        rule = await service.create_signal_rule(
            user_id=UUID(current_user),
            rule_data=rule_data
        )
        
        return {
            "status": "success",
            "rule": {
                "id": rule.id,
                "name": rule.name,
                "type": rule.type.value,
                "conditions": rule.conditions,
                "enabled": rule.enabled,
                "created_at": rule.created_at.isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating signal rule: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/rules")
async def get_signal_rules(
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_database),
    redis: Redis = Depends(get_redis_client)
) -> Dict[str, Any]:
    """Get all signal rules for the current user."""
    try:
        service = SignalMonitoringService(db, redis)
        rules = await service.get_user_rules(UUID(current_user))
        
        return {
            "status": "success",
            "count": len(rules),
            "rules": [
                {
                    "id": rule.id,
                    "name": rule.name,
                    "type": rule.type.value,
                    "conditions": rule.conditions,
                    "enabled": rule.enabled,
                    "created_at": rule.created_at.isoformat()
                }
                for rule in rules
            ]
        }
        
    except Exception as e:
        logger.error(f"Error getting signal rules: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/feed")
async def get_signal_feed(
    limit: int = Query(default=50, le=200),
    signal_type: Optional[str] = None,
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_database),
    redis: Redis = Depends(get_redis_client)
) -> Dict[str, Any]:
    """Get signal feed for the current user."""
    try:
        service = SignalMonitoringService(db, redis)
        
        # Parse signal type if provided
        type_filter = None
        if signal_type:
            try:
                type_filter = SignalType(signal_type)
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid signal type")
        
        # Get signals
        signals = await service.get_user_signals(
            user_id=UUID(current_user),
            limit=limit,
            signal_type=type_filter
        )
        
        return {
            "status": "success",
            "count": len(signals),
            "signals": [
                {
                    "id": signal.id,
                    "type": signal.type.value,
                    "title": signal.title,
                    "description": signal.description,
                    "severity": signal.severity,
                    "entity_type": signal.entity_type,
                    "entity_id": signal.entity_id,
                    "metadata": signal.metadata,
                    "created_at": signal.created_at.isoformat()
                }
                for signal in signals
            ]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting signal feed: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/mark-read/{signal_id}")
async def mark_signal_read(
    signal_id: str,
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_database),
    redis: Redis = Depends(get_redis_client)
) -> Dict[str, Any]:
    """Mark a signal as read."""
    try:
        service = SignalMonitoringService(db, redis)
        await service.mark_signal_read(UUID(current_user), signal_id)
        
        return {
            "status": "success",
            "message": "Signal marked as read"
        }
        
    except Exception as e:
        logger.error(f"Error marking signal read: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/stats")
async def get_signal_stats(
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_database),
    redis: Redis = Depends(get_redis_client)
) -> Dict[str, Any]:
    """Get signal statistics for the current user."""
    try:
        service = SignalMonitoringService(db, redis)
        stats = await service.get_signal_stats(UUID(current_user))
        
        return {
            "status": "success",
            "stats": stats
        }
        
    except Exception as e:
        logger.error(f"Error getting signal stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/check")
async def trigger_signal_check(
    background_tasks: BackgroundTasks,
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_database),
    redis: Redis = Depends(get_redis_client)
) -> Dict[str, Any]:
    """
    Manually trigger signal checking (admin only).
    
    In production, this would run on a schedule.
    """
    try:
        service = SignalMonitoringService(db, redis)
        
        # Run check in background
        background_tasks.add_task(service.check_signals)
        
        return {
            "status": "success",
            "message": "Signal check triggered in background"
        }
        
    except Exception as e:
        logger.error(f"Error triggering signal check: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/types")
async def get_signal_types() -> Dict[str, Any]:
    """Get available signal types and their descriptions."""
    return {
        "status": "success",
        "signal_types": [
            {
                "type": signal_type.value,
                "name": signal_type.name.replace("_", " ").title(),
                "description": get_signal_type_description(signal_type)
            }
            for signal_type in SignalType
        ]
    }


def get_signal_type_description(signal_type: SignalType) -> str:
    """Get description for a signal type."""
    descriptions = {
        SignalType.NEW_STARTUP_IN_SECTOR: "Alert when new startups join sectors you're watching",
        SignalType.NEW_VC_IN_SECTOR: "Alert when new VCs start investing in your sectors",
        SignalType.HOT_STARTUP: "Alert when startups get unusual VC attention",
        SignalType.FUNDING_ROUND: "Alert on new funding rounds in your network",
        SignalType.SECTOR_TREND: "Alert on emerging sector trends and growth",
        SignalType.MATCH_OPPORTUNITY: "Alert on high-quality match opportunities",
        SignalType.COMPETITOR_ACTIVITY: "Alert on competitor movements",
        SignalType.THESIS_MATCH: "Alert when companies match your investment thesis"
    }
    
    return descriptions.get(signal_type, "Market signal alert")