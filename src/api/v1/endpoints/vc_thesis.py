"""VC Thesis extraction endpoints."""

from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from uuid import UUID
from sqlalchemy.orm import Session
import logging

from src.api.v1.deps import get_database, get_current_user_optional
from src.database.repositories.vc_repository import PostgresVCRepository
from src.scrapers.vc_thesis_extractor import extract_vc_thesis

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/vc-thesis", tags=["vc-thesis"])


@router.post("/extract/{vc_id}")
async def extract_thesis_for_vc(
    vc_id: UUID,
    background_tasks: BackgroundTasks,
    update_vc: bool = True,
    db: Session = Depends(get_database),
    current_user: str = Depends(get_current_user_optional)
) -> Dict[str, Any]:
    """
    Extract investment thesis from a VC's website.
    
    This endpoint:
    1. Fetches the VC's website
    2. Extracts thesis using AI (mock in MVP)
    3. Parses into structured preferences
    4. Optionally updates the VC record
    """
    # Get the VC
    vc_repo = PostgresVCRepository(db)
    vc = vc_repo.get(vc_id)
    
    if not vc:
        raise HTTPException(status_code=404, detail=f"VC {vc_id} not found")
    
    if not vc.website:
        raise HTTPException(status_code=400, detail="VC has no website URL")
    
    # Extract thesis
    result = await extract_vc_thesis(vc.firm_name, vc.website)
    
    if not result["success"]:
        raise HTTPException(status_code=500, detail="Failed to extract thesis")
    
    # Update VC if requested
    if update_vc:
        preferences = result["preferences"]
        
        # Update VC fields
        vc.thesis = preferences.get("investment_thesis", vc.thesis)
        vc.sectors = preferences.get("preferred_sectors", vc.sectors)
        vc.stages = preferences.get("preferred_stages", vc.stages)
        
        # Update check sizes if provided
        if preferences.get("check_size_min"):
            vc.check_size_min = preferences["check_size_min"]
        if preferences.get("check_size_max"):
            vc.check_size_max = preferences["check_size_max"]
        
        # Save updates
        updated_vc = vc_repo.update(vc_id, vc)
        
        return {
            "vc": {
                "id": str(updated_vc.id),
                "firm_name": updated_vc.firm_name,
                "website": updated_vc.website,
                "updated_thesis": updated_vc.thesis,
                "updated_sectors": updated_vc.sectors,
                "updated_stages": updated_vc.stages,
                "check_sizes": {
                    "min": updated_vc.check_size_min,
                    "max": updated_vc.check_size_max
                }
            },
            "extraction": result["extraction"],
            "preferences": result["preferences"],
            "updated": True
        }
    
    return {
        "vc": {
            "id": str(vc.id),
            "firm_name": vc.firm_name,
            "website": vc.website
        },
        "extraction": result["extraction"],
        "preferences": result["preferences"],
        "updated": False
    }


@router.post("/batch-extract")
async def batch_extract_thesis(
    vc_ids: list[UUID],
    background_tasks: BackgroundTasks,
    update_vcs: bool = True,
    db: Session = Depends(get_database),
    current_user: str = Depends(get_current_user_optional)
) -> Dict[str, Any]:
    """
    Extract thesis for multiple VCs in batch.
    
    In production, this would be queued as background tasks.
    """
    if len(vc_ids) > 10:
        raise HTTPException(status_code=400, detail="Maximum 10 VCs per batch")
    
    vc_repo = PostgresVCRepository(db)
    results = []
    
    for vc_id in vc_ids:
        try:
            vc = vc_repo.get(vc_id)
            if not vc or not vc.website:
                results.append({
                    "vc_id": str(vc_id),
                    "success": False,
                    "error": "VC not found or no website"
                })
                continue
            
            # Extract thesis
            extraction_result = await extract_vc_thesis(vc.firm_name, vc.website)
            
            if update_vcs and extraction_result["success"]:
                preferences = extraction_result["preferences"]
                
                # Update VC
                vc.thesis = preferences.get("investment_thesis", vc.thesis)
                vc.sectors = preferences.get("preferred_sectors", vc.sectors)
                vc.stages = preferences.get("preferred_stages", vc.stages)
                
                if preferences.get("check_size_min"):
                    vc.check_size_min = preferences["check_size_min"]
                if preferences.get("check_size_max"):
                    vc.check_size_max = preferences["check_size_max"]
                
                vc_repo.update(vc_id, vc)
            
            results.append({
                "vc_id": str(vc_id),
                "firm_name": vc.firm_name,
                "success": True,
                "thesis_extracted": extraction_result["preferences"]["investment_thesis"][:100] + "...",
                "sectors": extraction_result["preferences"]["preferred_sectors"],
                "stages": extraction_result["preferences"]["preferred_stages"]
            })
            
        except Exception as e:
            logger.error(f"Failed to extract thesis for VC {vc_id}: {e}")
            results.append({
                "vc_id": str(vc_id),
                "success": False,
                "error": str(e)
            })
    
    successful = sum(1 for r in results if r["success"])
    
    return {
        "total_processed": len(results),
        "successful": successful,
        "failed": len(results) - successful,
        "results": results
    }


@router.get("/extraction-status")
async def get_extraction_status(
    db: Session = Depends(get_database),
    current_user: str = Depends(get_current_user_optional)
) -> Dict[str, Any]:
    """
    Get status of thesis extraction across all VCs.
    """
    vc_repo = PostgresVCRepository(db)
    all_vcs = vc_repo.list(limit=1000)
    
    with_thesis = sum(1 for vc in all_vcs if vc.thesis and len(vc.thesis) > 50)
    without_thesis = len(all_vcs) - with_thesis
    
    # Find VCs that need extraction
    need_extraction = [
        {
            "id": str(vc.id),
            "firm_name": vc.firm_name,
            "website": vc.website
        }
        for vc in all_vcs
        if vc.website and (not vc.thesis or len(vc.thesis) < 50)
    ]
    
    return {
        "total_vcs": len(all_vcs),
        "with_thesis": with_thesis,
        "without_thesis": without_thesis,
        "need_extraction": need_extraction[:10],  # Show first 10
        "extraction_coverage": f"{(with_thesis / len(all_vcs) * 100):.1f}%" if all_vcs else "0%"
    }