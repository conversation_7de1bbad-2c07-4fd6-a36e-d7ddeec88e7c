"""API endpoints for web scraping and data enrichment."""

from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, BackgroundTasks
from typing import Dict, Any, Optional
from uuid import UUID
import logging
from sqlalchemy.orm import Session

from src.api.v1.deps import get_current_user, get_database
from src.core.services.scraper_service import ScraperService
from src.database.repositories.startup_repository import PostgresStartupRepository as StartupRepository
from src.database.models import User

logger = logging.getLogger(__name__)

router = APIRouter(tags=["scraping"])


@router.post("/yc/scrape")
async def scrape_yc_companies(
    limit: Optional[int] = 50,
    background_tasks: BackgroundTasks = BackgroundTasks(),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_database)
) -> Dict[str, Any]:
    """
    Scrape YC companies and add them to the database.
    
    - **limit**: Maximum number of companies to scrape (default: 50)
    
    This endpoint requires authentication and will run the scraping in the background.
    """
    try:
        # Initialize repositories and scraper service
        startup_repo = StartupRepository(db)
        scraper_service = ScraperService(startup_repo)
        
        # For immediate feedback, we'll run synchronously for small limits
        if limit <= 10:
            results = await scraper_service.scrape_yc_companies(limit=limit)
            return {
                "status": "completed",
                "results": results
            }
        else:
            # For larger scrapes, run in background
            background_tasks.add_task(
                scraper_service.scrape_yc_companies,
                limit=limit
            )
            return {
                "status": "started",
                "message": f"Scraping {limit} YC companies in background",
                "task_id": None  # In production, would return a task ID
            }
    
    except Exception as e:
        logger.error(f"YC scraping endpoint error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/yc/scrape-recent")
async def scrape_recent_yc_companies(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_database)
) -> Dict[str, Any]:
    """
    Scrape only recent YC companies (last 2 years).
    
    This endpoint focuses on discovering new YC companies from recent batches.
    """
    try:
        startup_repo = StartupRepository(db)
        scraper_service = ScraperService(startup_repo)
        results = await scraper_service.scrape_recent_yc_companies()
        
        return {
            "status": "completed",
            "results": results
        }
    
    except Exception as e:
        logger.error(f"Recent YC scraping error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/stats")
async def get_scraping_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_database)
) -> Dict[str, Any]:
    """
    Get statistics about scraped data.
    
    Returns information about:
    - Total companies by source
    - YC batch distribution
    - Recent scraping activity
    """
    try:
        startup_repo = StartupRepository(db)
        scraper_service = ScraperService(startup_repo)
        stats = await scraper_service.get_scraping_stats()
        
        return {
            "status": "success",
            "stats": stats
        }
    
    except Exception as e:
        logger.error(f"Stats endpoint error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/yc/sample")
async def get_yc_sample_data(
    limit: int = 3,
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get a sample of YC company data without storing it.
    
    Useful for testing and previewing data before running a full scrape.
    """
    try:
        from src.scrapers.yc_scraper import YCCompanyScraper
        
        scraper = YCCompanyScraper()
        companies = await scraper.scrape_companies(limit=limit)
        
        return {
            "status": "success",
            "sample_size": len(companies),
            "companies": companies
        }
    
    except Exception as e:
        logger.error(f"Sample data error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))