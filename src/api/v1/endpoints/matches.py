"""Refactored API endpoints for match operations using the service layer.

This demonstrates proper separation of concerns:
- API layer handles HTTP concerns (requests, responses, status codes)
- Service layer handles business logic
- Repository layer handles data persistence
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query
from uuid import UUID
from datetime import datetime

from src.core.schemas.match import (
    MatchRequest,
    BatchMatchRequest,
    MatchUpdate,
    MatchResponse,
    MatchListResponse,
    MatchStatus,
    MatchType
)
from src.core.models.match import Match
from src.core.services.match_service import MatchService
from src.core.repositories.startup_repository import StartupRepository
from src.core.repositories.vc_repository import VCRepository
from src.core.repositories.match_repository import MatchRepository
from src.database.repositories.startup_repository import PostgresStartupRepository
from src.database.repositories.vc_repository import PostgresVCRepository
from src.database.repositories.match_repository_sync import PostgresMatchRepository
from src.core.ports.ai_port import AIPort
from src.api.v1.deps import (
    get_current_user_optional, get_current_user,
    rate_limit_per_minute,
    PaginationParams,
    get_ai_port
)
from src.api.errors import NotFoundError, BadRequestError
from src.api.v1.error_handlers import handle_api_errors

router = APIRouter(prefix="/matches", tags=["matches"])


# Import database dependency
from src.api.v1.deps import get_database
from sqlalchemy.orm import Session


# Dependency injection for services
async def get_startup_repository(db: Session = Depends(get_database)) -> StartupRepository:
    """Get the startup repository instance."""
    return PostgresStartupRepository(db)


async def get_vc_repository(db: Session = Depends(get_database)) -> VCRepository:
    """Get the VC repository instance."""
    return PostgresVCRepository(db)


async def get_match_repository(db: Session = Depends(get_database)) -> MatchRepository:
    """Get the match repository instance."""
    return PostgresMatchRepository(db)


async def get_matching_service(
    startup_repo: StartupRepository = Depends(get_startup_repository),
    vc_repo: VCRepository = Depends(get_vc_repository),
    match_repo: MatchRepository = Depends(get_match_repository),
    ai_port: AIPort = Depends(get_ai_port)
) -> MatchService:
    """Get the matching service instance with dependencies injected."""
    return MatchService(
        startup_repository=startup_repo,
        vc_repository=vc_repo,
        match_repository=match_repo,
        ai_port=ai_port
    )


@router.post(
    "",
    response_model=MatchResponse,
    status_code=status.HTTP_201_CREATED,
    dependencies=[Depends(rate_limit_per_minute)]
)
@handle_api_errors
async def create_match(
    match_request: MatchRequest,
    service: MatchService = Depends(get_matching_service),
    current_user: str = Depends(get_current_user)
) -> MatchResponse:
    """
    Create a new match between a startup and VC.
    
    Requires authentication.
    """
    # Check for circular reference (same ID for startup and VC)
    if match_request.startup_id == match_request.vc_id:
        raise BadRequestError("Cannot create match between same entity")
    
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required"
        )
    
    try:
        # Use service to create match
        match = await service.create_match(
            startup_id=UUID(match_request.startup_id),
            vc_id=UUID(match_request.vc_id),
            match_type=match_request.match_type,
            notes=match_request.notes
        )
        
        # Convert to response
        return MatchResponse.from_domain(match)
        
    except ValueError as e:
        raise BadRequestError(str(e))
    except Exception as e:
        import logging
        logging.error(f"Failed to create match: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create match: {str(e)}"
        )


@router.get(
    "",
    response_model=MatchListResponse,
    dependencies=[Depends(rate_limit_per_minute)]
)
async def list_matches(
    pagination: PaginationParams = Depends(),
    startup_id: Optional[str] = Query(None, description="Filter by startup ID"),
    vc_id: Optional[str] = Query(None, description="Filter by VC ID"),
    status: Optional[MatchStatus] = Query(None, description="Filter by status"),
    min_score: Optional[float] = Query(None, ge=0.0, le=1.0, description="Minimum score"),
    service: MatchService = Depends(get_matching_service)
) -> MatchListResponse:
    """
    List matches with optional filtering and pagination.
    
    The service layer handles the business logic of filtering.
    """
    # Convert string IDs to UUIDs
    startup_uuid = UUID(startup_id) if startup_id else None
    vc_uuid = UUID(vc_id) if vc_id else None
    
    # Use service to get matches with filters
    matches = await service.list_matches(
        startup_id=startup_uuid,
        vc_id=vc_uuid,
        status=status,
        min_score=min_score
    )
    
    # Apply pagination
    total = len(matches)
    start = pagination.offset
    end = start + pagination.size
    paginated_items = matches[start:end]
    
    # Calculate average score
    average_score = sum(m.score for m in matches) / len(matches) if matches else 0.0
    
    return MatchListResponse(
        items=[MatchResponse.from_domain(match) for match in paginated_items],
        total=total,
        page=pagination.page,
        size=pagination.size,
        pages=(total + pagination.size - 1) // pagination.size,
        average_score=average_score
    )


@router.get(
    "/{match_id}",
    response_model=MatchResponse,
    dependencies=[Depends(rate_limit_per_minute)]
)
async def get_match(
    match_id: UUID,
    service: MatchService = Depends(get_matching_service)
) -> MatchResponse:
    """Get a specific match by ID."""
    try:
        match = await service.get_match(match_id)
        return MatchResponse.from_domain(match)
    except ValueError as e:
        raise NotFoundError("Match", str(match_id))


@router.put(
    "/{match_id}",
    response_model=MatchResponse,
    dependencies=[Depends(rate_limit_per_minute)]
)
async def update_match_status(
    match_id: UUID,
    match_update: MatchUpdate,
    service: MatchService = Depends(get_matching_service),
    current_user: Optional[str] = Depends(get_current_user_optional)
) -> MatchResponse:
    """
    Update a match's status and notes.
    
    Requires authentication.
    """
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required"
        )
    
    try:
        # Get update data excluding unset fields
        update_data = match_update.model_dump(exclude_unset=True)
        
        # Use service to update
        updated_match = await service.update_match(match_id, update_data)
        
        return MatchResponse.from_domain(updated_match)
        
    except ValueError as e:
        if "not found" in str(e):
            raise NotFoundError("Match", str(match_id))
        raise BadRequestError(str(e))


@router.delete(
    "/{match_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    dependencies=[Depends(rate_limit_per_minute)]
)
async def delete_match(
    match_id: UUID,
    service: MatchService = Depends(get_matching_service),
    current_user: str = Depends(get_current_user_optional)
) -> None:
    """
    Delete a match.
    
    Requires authentication.
    """
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required"
        )
    
    deleted = await service.delete_match(match_id)
    if not deleted:
        raise NotFoundError("Match", str(match_id))
    
    return None


@router.post(
    "/batch",
    response_model=Dict[str, Any],
    dependencies=[Depends(rate_limit_per_minute)]
)
async def batch_match(
    batch_request: BatchMatchRequest,
    service: MatchService = Depends(get_matching_service),
    current_user: Optional[str] = Depends(get_current_user_optional)
) -> Dict[str, Any]:
    """
    Create matches for multiple startups and VCs.
    
    This endpoint uses the matching engine to find and create matches
    between the provided lists of startups and VCs.
    
    Requires authentication.
    """
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required for batch matching"
        )
    
    # Validate input
    if not batch_request.startup_ids or not batch_request.vc_ids:
        raise BadRequestError("Both startup_ids and vc_ids must be non-empty")
    
    try:
        # Convert string IDs to UUIDs
        startup_uuids = [UUID(sid) for sid in batch_request.startup_ids]
        vc_uuids = [UUID(vid) for vid in batch_request.vc_ids]
        
        # Use service to perform batch matching
        matches = await service.batch_match(
            startup_ids=startup_uuids,
            vc_ids=vc_uuids,
            match_type=batch_request.match_type,
            min_score_threshold=batch_request.min_score_threshold
        )
        
        # Return summary
        return {
            "created": len(matches),
            "total_combinations": len(startup_uuids) * len(vc_uuids),
            "matches": [MatchResponse.from_domain(match) for match in matches],
            "timestamp": datetime.utcnow()
        }
        
    except ValueError as e:
        raise BadRequestError(str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Batch matching failed: {str(e)}"
        )