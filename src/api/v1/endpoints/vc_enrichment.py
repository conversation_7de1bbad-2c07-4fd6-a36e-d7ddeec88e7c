"""API endpoints for VC data enrichment and thesis extraction."""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from typing import Dict, Any, List, Optional
from uuid import UUID
import logging
from sqlalchemy.orm import Session

from src.api.v1.deps import get_current_user, get_database, get_settings
from src.core.services.vc_thesis_extractor import VCThesisExtractor
from src.database.repositories.vc_repository import VCRepository
from src.database.models import User
from src.core.config import Settings

logger = logging.getLogger(__name__)

router = APIRouter(tags=["vc-enrichment"])


@router.post("/vcs/{vc_id}/extract-thesis")
async def extract_vc_thesis(
    vc_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_database),
    settings: Settings = Depends(get_settings)
) -> Dict[str, Any]:
    """
    Extract investment thesis from a VC's website.
    
    This endpoint will:
    1. Fetch the VC's website URL
    2. Scrape relevant pages
    3. Use AI to extract structured thesis information
    4. Update the VC's profile with the extracted data
    """
    try:
        # Get VC from database
        vc_repo = VCRepository(db)
        vc = await vc_repo.get(vc_id)
        
        if not vc:
            raise HTTPException(status_code=404, detail="VC not found")
        
        if not vc.website:
            raise HTTPException(
                status_code=400, 
                detail="VC does not have a website URL"
            )
        
        # Initialize thesis extractor
        extractor = VCThesisExtractor(settings.openai_api_key)
        
        # Extract thesis
        logger.info(f"Extracting thesis for VC {vc_id} from {vc.website}")
        result = await extractor.extract_thesis_from_website(vc.website)
        
        if result["success"]:
            # Update VC with extracted thesis
            updated_vc = await extractor.update_vc_with_thesis(vc, result)
            await vc_repo.update(updated_vc)
            
            # Generate summary
            summary = extractor.generate_thesis_summary(result)
            
            return {
                "status": "success",
                "vc_id": str(vc_id),
                "thesis": result["thesis"],
                "summary": summary,
                "extracted_at": result["extracted_at"]
            }
        else:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to extract thesis: {result.get('error', 'Unknown error')}"
            )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error extracting thesis for VC {vc_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/vcs/batch-extract-thesis")
async def batch_extract_thesis(
    vc_ids: List[UUID],
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_database),
    settings: Settings = Depends(get_settings)
) -> Dict[str, Any]:
    """
    Extract thesis for multiple VCs in batch.
    
    This runs as a background task for larger batches.
    """
    if len(vc_ids) > 10:
        raise HTTPException(
            status_code=400,
            detail="Maximum 10 VCs can be processed in a single batch"
        )
    
    try:
        # Get VCs from database
        vc_repo = VCRepository(db)
        vcs = []
        
        for vc_id in vc_ids:
            vc = await vc_repo.get(vc_id)
            if vc and vc.website:
                vcs.append({
                    "id": str(vc.id),
                    "name": vc.name,
                    "website": vc.website
                })
        
        if not vcs:
            raise HTTPException(
                status_code=400,
                detail="No valid VCs with websites found"
            )
        
        # For small batches, process synchronously
        if len(vcs) <= 3:
            extractor = VCThesisExtractor(settings.openai_api_key)
            results = await extractor.extract_thesis_for_multiple_vcs(vcs)
            
            # Update VCs with results
            success_count = 0
            for i, result in enumerate(results):
                if result["success"]:
                    vc_id = UUID(vcs[i]["id"])
                    vc = await vc_repo.get(vc_id)
                    if vc:
                        updated_vc = await extractor.update_vc_with_thesis(vc, result)
                        await vc_repo.update(updated_vc)
                        success_count += 1
            
            return {
                "status": "completed",
                "total_vcs": len(vcs),
                "successful": success_count,
                "results": results
            }
        else:
            # For larger batches, use background task
            background_tasks.add_task(
                _process_batch_thesis_extraction,
                vcs,
                db,
                settings.openai_api_key
            )
            
            return {
                "status": "started",
                "message": f"Extracting thesis for {len(vcs)} VCs in background",
                "vc_count": len(vcs)
            }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in batch thesis extraction: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/vcs/{vc_id}/thesis")
async def get_vc_thesis(
    vc_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_database)
) -> Dict[str, Any]:
    """
    Get the extracted thesis for a VC.
    """
    try:
        vc_repo = VCRepository(db)
        vc = await vc_repo.get(vc_id)
        
        if not vc:
            raise HTTPException(status_code=404, detail="VC not found")
        
        # Extract thesis data from VC
        metadata = getattr(vc, 'metadata', {})
        
        if not metadata.get('thesis_summary'):
            raise HTTPException(
                status_code=404,
                detail="No thesis extracted for this VC yet"
            )
        
        return {
            "vc_id": str(vc_id),
            "vc_name": vc.name,
            "thesis": {
                "summary": metadata.get("thesis_summary"),
                "sectors": vc.sectors,
                "stages": vc.stages,
                "check_sizes": {
                    "min": vc.check_size_min,
                    "max": vc.check_size_max,
                    "sweet_spot": vc.sweet_spot_check_size
                },
                "geography": metadata.get("geography", []),
                "key_criteria": metadata.get("key_criteria", []),
                "avoid_criteria": metadata.get("avoid_criteria", []),
                "portfolio_examples": metadata.get("portfolio_examples", []),
                "confidence_score": metadata.get("extraction_confidence", 0)
            },
            "extracted_at": metadata.get("thesis_extracted_at")
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting thesis for VC {vc_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/vcs/extract-from-url")
async def extract_thesis_from_url(
    url: str,
    current_user: User = Depends(get_current_user),
    settings: Settings = Depends(get_settings)
) -> Dict[str, Any]:
    """
    Extract investment thesis from a URL without saving to database.
    
    Useful for testing or previewing thesis extraction.
    """
    try:
        extractor = VCThesisExtractor(settings.openai_api_key)
        result = await extractor.extract_thesis_from_website(url)
        
        if result["success"]:
            summary = extractor.generate_thesis_summary(result)
            
            return {
                "status": "success",
                "url": url,
                "thesis": result["thesis"],
                "summary": summary,
                "raw_data": result.get("raw_data", {})
            }
        else:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to extract thesis: {result.get('error', 'Unknown error')}"
            )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error extracting thesis from URL {url}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


async def _process_batch_thesis_extraction(
    vcs: List[Dict[str, str]], 
    db: Session,
    openai_api_key: str
):
    """Background task to process batch thesis extraction."""
    try:
        extractor = VCThesisExtractor(openai_api_key)
        vc_repo = VCRepository(db)
        
        results = await extractor.extract_thesis_for_multiple_vcs(vcs)
        
        # Update VCs with results
        for i, result in enumerate(results):
            if result["success"]:
                vc_id = UUID(vcs[i]["id"])
                vc = await vc_repo.get(vc_id)
                if vc:
                    updated_vc = await extractor.update_vc_with_thesis(vc, result)
                    await vc_repo.update(updated_vc)
                    logger.info(f"Updated VC {vc_id} with extracted thesis")
            else:
                logger.error(f"Failed to extract thesis for {vcs[i]['name']}: {result.get('error')}")
    
    except Exception as e:
        logger.error(f"Error in batch thesis extraction background task: {str(e)}")


@router.get("/vcs/thesis-stats")
async def get_thesis_extraction_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_database)
) -> Dict[str, Any]:
    """
    Get statistics about thesis extraction across all VCs.
    """
    try:
        vc_repo = VCRepository(db)
        all_vcs = await vc_repo.find_all()
        
        total_vcs = len(all_vcs)
        vcs_with_thesis = 0
        vcs_with_website = 0
        
        sector_distribution = {}
        stage_distribution = {}
        
        for vc in all_vcs:
            if vc.website:
                vcs_with_website += 1
            
            metadata = getattr(vc, 'metadata', {})
            if metadata.get('thesis_summary'):
                vcs_with_thesis += 1
                
                # Count sectors
                for sector in vc.sectors:
                    sector_distribution[sector] = sector_distribution.get(sector, 0) + 1
                
                # Count stages
                for stage in vc.stages:
                    stage_distribution[stage] = stage_distribution.get(stage, 0) + 1
        
        return {
            "total_vcs": total_vcs,
            "vcs_with_website": vcs_with_website,
            "vcs_with_extracted_thesis": vcs_with_thesis,
            "extraction_coverage": round(vcs_with_thesis / total_vcs * 100, 2) if total_vcs > 0 else 0,
            "sector_distribution": sector_distribution,
            "stage_distribution": stage_distribution
        }
    
    except Exception as e:
        logger.error(f"Error getting thesis stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))