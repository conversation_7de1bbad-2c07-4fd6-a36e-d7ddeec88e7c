"""Refactored API endpoints for Startup operations using the service layer.

This demonstrates the proper separation of concerns:
- API layer handles HTTP concerns (requests, responses, status codes)
- Service layer handles business logic
- Repository layer handles data persistence
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from uuid import UUID
from datetime import datetime

from src.core.schemas.startup import (
    StartupCreate,
    StartupUpdate,
    StartupResponse,
    StartupListResponse,
    StartupSearchParams
)
from src.core.models.startup import Startup
from src.core.services.startup_service import StartupService
from src.core.repositories.startup_repository import StartupRepository, InMemoryStartupRepository
from src.database.repositories.startup_repository import PostgresStartupRepository
from src.core.ports.ai_port import AIPort
from src.api.v1.deps import (
    get_current_user_optional, get_current_user,
    rate_limit_per_minute,
    PaginationParams,
    get_ai_port,
    get_database
)
from src.api.errors import NotFoundError, BadRequestError
from src.api.v1.error_handlers import handle_api_errors
from sqlalchemy.orm import Session

router = APIRouter(prefix="/startups", tags=["startups"])


# Dependency injection for services
async def get_startup_repository(db: Session = Depends(get_database)) -> StartupRepository:
    """Get the startup repository instance."""
    # Use database-backed repository when session is available
    if db:
        return PostgresStartupRepository(db)
    # Fallback to in-memory repository
    return InMemoryStartupRepository()


async def get_startup_service(
    repository: StartupRepository = Depends(get_startup_repository),
    ai_port: AIPort = Depends(get_ai_port)
) -> StartupService:
    """Get the startup service instance with dependencies injected."""
    return StartupService(repository=repository, ai_port=ai_port)


@router.post(
    "",
    response_model=StartupResponse,
    status_code=status.HTTP_201_CREATED,
    dependencies=[Depends(rate_limit_per_minute)]
)
@handle_api_errors
async def create_startup(
    startup_data: StartupCreate,
    service: StartupService = Depends(get_startup_service),
    current_user: str = Depends(get_current_user)
) -> StartupResponse:
    """
    Create a new startup.
    
    The API layer only handles HTTP concerns, delegating business logic to the service.
    """
    # Convert schema to domain model with HTML sanitization
    import html
    startup = Startup(
        name=html.escape(startup_data.name) if startup_data.name else startup_data.name,
        sector=startup_data.sector,
        stage=startup_data.stage,
        description=html.escape(startup_data.description) if startup_data.description else startup_data.description,
        website=startup_data.website if startup_data.website is not None else "",
        team_size=startup_data.team_size if startup_data.team_size is not None else 0,
        monthly_revenue=startup_data.monthly_revenue if startup_data.monthly_revenue is not None else 0.0
    )
    
    # Use service to create startup (handles ID assignment, validation, persistence)
    created_startup = await service.create_startup(startup)
    
    # Convert domain model back to response schema
    return StartupResponse.from_domain(created_startup)


@router.get(
    "",
    response_model=StartupListResponse,
    dependencies=[Depends(rate_limit_per_minute)]
)
async def list_startups(
    pagination: PaginationParams = Depends(),
    search_params: StartupSearchParams = Depends(),
    service: StartupService = Depends(get_startup_service)
) -> StartupListResponse:
    """
    List startups with optional filtering and pagination.
    
    The service layer handles the business logic of filtering.
    """
    # Use service to get startups with filters
    startups = await service.list_startups(
        sector=search_params.sector,
        stage=search_params.stage
    )
    
    # Apply additional filters that aren't in the service yet
    if search_params.min_team_size is not None:
        startups = [s for s in startups if s.team_size >= search_params.min_team_size]
    if search_params.max_team_size is not None:
        startups = [s for s in startups if s.team_size <= search_params.max_team_size]
    if search_params.min_revenue is not None:
        startups = [s for s in startups if s.monthly_revenue >= search_params.min_revenue]
    if search_params.max_revenue is not None:
        startups = [s for s in startups if s.monthly_revenue <= search_params.max_revenue]
    if search_params.is_fundable is not None:
        startups = [s for s in startups if s.is_fundable() == search_params.is_fundable]
    
    # Apply search query
    if search_params.query:
        query_lower = search_params.query.lower()
        startups = [
            s for s in startups
            if query_lower in s.name.lower() or
               (s.description and query_lower in s.description.lower())
        ]
    
    # Apply pagination
    total = len(startups)
    start = pagination.offset
    end = start + pagination.size
    paginated_items = startups[start:end]
    
    return StartupListResponse(
        items=[StartupResponse.from_domain(s) for s in paginated_items],
        total=total,
        page=pagination.page,
        size=pagination.size,
        pages=(total + pagination.size - 1) // pagination.size
    )


@router.get(
    "/{startup_id}",
    response_model=StartupResponse,
    dependencies=[Depends(rate_limit_per_minute)]
)
async def get_startup(
    startup_id: UUID,
    service: StartupService = Depends(get_startup_service)
) -> StartupResponse:
    """Get a specific startup by ID."""
    try:
        startup = await service.get_startup(startup_id)
        return StartupResponse.from_domain(startup)
    except ValueError as e:
        raise NotFoundError("Startup", str(startup_id))


@router.put(
    "/{startup_id}",
    response_model=StartupResponse,
    dependencies=[Depends(rate_limit_per_minute)]
)
@handle_api_errors
async def update_startup(
    startup_id: UUID,
    startup_update: StartupUpdate,
    service: StartupService = Depends(get_startup_service),
    current_user: str = Depends(get_current_user)
) -> StartupResponse:
    """
    Update a startup's information.
    
    All fields are optional - only provided fields will be updated.
    """
    # Get update data excluding unset fields
    update_data = startup_update.model_dump(exclude_unset=True)
    
    # Use service to update
    updated_startup = await service.update_startup(startup_id, update_data)
    
    if not updated_startup:
        raise NotFoundError("Startup", str(startup_id))
    
    return StartupResponse.from_domain(updated_startup)


@router.delete(
    "/{startup_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    dependencies=[Depends(rate_limit_per_minute)]
)
@handle_api_errors
async def delete_startup(
    startup_id: UUID,
    service: StartupService = Depends(get_startup_service),
    current_user: str = Depends(get_current_user)
) -> None:
    """
    Delete a startup.
    
    Requires authentication.
    """
    # Authentication is now enforced by the dependency
    
    deleted = await service.delete_startup(startup_id)
    if not deleted:
        raise NotFoundError("Startup", str(startup_id))
    
    return None


@router.post(
    "/{startup_id}/analyze",
    response_model=dict,
    dependencies=[Depends(rate_limit_per_minute)]
)
async def analyze_startup(
    startup_id: UUID,
    force_refresh: bool = Query(default=False, description="Force refresh from cache"),
    service: StartupService = Depends(get_startup_service),
    current_user: str = Depends(get_current_user)
) -> dict:
    """
    Analyze a startup using AI to extract key information.
    
    This endpoint uses the service layer to coordinate between repository and AI analyzer.
    """
    # Authentication is now enforced by the dependency
    
    try:
        analysis = await service.analyze_startup(
            startup_id,
            force_refresh=force_refresh
        )
        
        return {
            "startup_id": str(startup_id),
            "analysis": {
                "sectors": analysis.sectors,
                "value_proposition": analysis.value_proposition,
                "target_customers": analysis.target_customers,
                "competitive_advantages": analysis.competitive_advantages,
                "business_model_type": analysis.business_model.type.value,
                "confidence_score": analysis.confidence_score
            },
            "analyzed_at": datetime.utcnow().isoformat()
        }
        
    except ValueError as e:
        if "not found" in str(e):
            raise NotFoundError("Startup", str(startup_id))
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AI analysis failed: {str(e)}"
        )