"""API endpoints for connection and warm intro functionality."""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Body
from uuid import UUID
from sqlalchemy.orm import Session
import logging

from src.api.v1.deps import get_database, get_current_user
from src.database.repositories.connection_repository import (
    PostgresConnectionRepository,
    PostgresIntroductionRepository
)
from src.database.repositories.user_repository_wrapper import PostgresUserRepository
from src.database.repositories.startup_repository import PostgresStartupRepository
from src.database.repositories.vc_repository import PostgresVCRepository
from src.core.services.warm_intro_service import WarmIntroService
from src.core.models.connection import RelationshipType

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/connections", tags=["connections"])


# Request/Response Models
from pydantic import BaseModel, Field
from datetime import datetime


class CreateConnectionRequest(BaseModel):
    """Request to create a connection."""
    other_user_id: UUID = Field(..., description="ID of the user to connect with")
    relationship_type: str = Field(..., description="Type of relationship")
    notes: Optional[str] = Field(None, description="Optional notes about the connection")
    trust_score: float = Field(0.5, ge=0.0, le=1.0, description="Initial trust score")


class ConnectionResponse(BaseModel):
    """Response for a connection."""
    id: str
    other_user: Dict[str, Any]
    relationship_type: str
    strength: str
    metrics: Dict[str, Any]
    notes: Optional[str]
    tags: List[str]
    created_at: datetime
    is_active: bool


class IntroPathResponse(BaseModel):
    """Response for introduction paths."""
    target_user: Dict[str, Any]
    path: List[Dict[str, Any]]
    strength_score: float
    hop_count: int
    can_request_intro: bool


class RequestIntroductionRequest(BaseModel):
    """Request to make an introduction."""
    target_id: UUID = Field(..., description="ID of the person to be introduced to")
    connector_id: UUID = Field(..., description="ID of the connector")
    message: str = Field(..., description="Message explaining the introduction request", min_length=10)


class RespondToIntroRequest(BaseModel):
    """Response to an introduction request."""
    accept: bool = Field(..., description="Whether to accept or decline the request")
    notes: Optional[str] = Field(None, description="Optional notes about the decision")


@router.post("/", response_model=ConnectionResponse)
async def create_connection(
    request: CreateConnectionRequest,
    db: Session = Depends(get_database),
    current_user: UUID = Depends(get_current_user)
) -> ConnectionResponse:
    """Create a new connection between the current user and another user."""
    # Initialize repositories
    connection_repo = PostgresConnectionRepository(db)
    introduction_repo = PostgresIntroductionRepository(db)
    user_repo = PostgresUserRepository(db)
    startup_repo = PostgresStartupRepository(db)
    vc_repo = PostgresVCRepository(db)
    
    # Initialize service
    service = WarmIntroService(
        connection_repo=connection_repo,
        introduction_repo=introduction_repo,
        user_repo=user_repo,
        startup_repo=startup_repo,
        vc_repo=vc_repo
    )
    
    try:
        # Validate relationship type
        relationship_type = RelationshipType(request.relationship_type)
        
        # Create connection
        connection = await service.create_connection(
            user_a_id=current_user,
            user_b_id=request.other_user_id,
            relationship_type=relationship_type,
            notes=request.notes,
            trust_score=request.trust_score
        )
        
        # Get other user info
        other_user = await user_repo.get(request.other_user_id)
        
        return ConnectionResponse(
            id=str(connection.id),
            other_user={
                "id": str(other_user.id),
                "name": other_user.name,
                "email": other_user.email
            },
            relationship_type=connection.relationship_type.value,
            strength=connection.strength.value,
            metrics={
                "interaction_frequency": connection.metrics.interaction_frequency,
                "last_interaction_days": connection.metrics.last_interaction_days,
                "mutual_connections_count": connection.metrics.mutual_connections_count,
                "trust_score": connection.metrics.trust_score
            },
            notes=connection.notes,
            tags=connection.tags,
            created_at=connection.created_at,
            is_active=connection.is_active
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating connection: {e}")
        raise HTTPException(status_code=500, detail="Failed to create connection")


@router.get("/", response_model=List[ConnectionResponse])
async def get_my_connections(
    strength: Optional[str] = Query(None, description="Filter by connection strength"),
    relationship_type: Optional[str] = Query(None, description="Filter by relationship type"),
    db: Session = Depends(get_database),
    current_user: UUID = Depends(get_current_user)
) -> List[ConnectionResponse]:
    """Get all connections for the current user."""
    connection_repo = PostgresConnectionRepository(db)
    user_repo = PostgresUserRepository(db)
    
    # Build filters
    filters = {}
    if strength:
        filters['strength'] = strength
    if relationship_type:
        filters['relationship_type'] = relationship_type
    
    # Get connections
    connections = await connection_repo.search_connections(current_user, filters)
    
    # Build response
    response = []
    for conn in connections:
        other_user_id = conn.get_other_user(current_user)
        other_user = await user_repo.get(other_user_id)
        
        response.append(ConnectionResponse(
            id=str(conn.id),
            other_user={
                "id": str(other_user.id),
                "name": other_user.name,
                "email": other_user.email
            } if other_user else {"id": str(other_user_id), "name": "Unknown", "email": None},
            relationship_type=conn.relationship_type.value,
            strength=conn.strength.value,
            metrics={
                "interaction_frequency": conn.metrics.interaction_frequency,
                "last_interaction_days": conn.metrics.last_interaction_days,
                "mutual_connections_count": conn.metrics.mutual_connections_count,
                "trust_score": conn.metrics.trust_score
            },
            notes=conn.notes,
            tags=conn.tags,
            created_at=conn.created_at,
            is_active=conn.is_active
        ))
    
    return response


@router.get("/paths/to-startup/{startup_id}", response_model=List[IntroPathResponse])
async def find_intro_paths_to_startup(
    startup_id: UUID,
    max_depth: int = Query(3, ge=1, le=4, description="Maximum path depth"),
    db: Session = Depends(get_database),
    current_user: UUID = Depends(get_current_user)
) -> List[IntroPathResponse]:
    """Find introduction paths from current user to startup team members."""
    # Initialize repositories
    connection_repo = PostgresConnectionRepository(db)
    introduction_repo = PostgresIntroductionRepository(db)
    user_repo = PostgresUserRepository(db)
    startup_repo = PostgresStartupRepository(db)
    vc_repo = PostgresVCRepository(db)
    
    # Initialize service
    service = WarmIntroService(
        connection_repo=connection_repo,
        introduction_repo=introduction_repo,
        user_repo=user_repo,
        startup_repo=startup_repo,
        vc_repo=vc_repo
    )
    
    try:
        # Find paths
        paths = await service.find_intro_paths_for_match(
            requester_id=current_user,
            startup_id=startup_id,
            max_depth=max_depth
        )
        
        # Convert to response
        return [IntroPathResponse(**path) for path in paths]
        
    except Exception as e:
        logger.error(f"Error finding intro paths: {e}")
        raise HTTPException(status_code=500, detail="Failed to find introduction paths")


@router.get("/paths/to-vc/{vc_id}", response_model=List[IntroPathResponse])
async def find_intro_paths_to_vc(
    vc_id: UUID,
    max_depth: int = Query(3, ge=1, le=4, description="Maximum path depth"),
    db: Session = Depends(get_database),
    current_user: UUID = Depends(get_current_user)
) -> List[IntroPathResponse]:
    """Find introduction paths from current user to VC partners."""
    # Initialize repositories
    connection_repo = PostgresConnectionRepository(db)
    introduction_repo = PostgresIntroductionRepository(db)
    user_repo = PostgresUserRepository(db)
    startup_repo = PostgresStartupRepository(db)
    vc_repo = PostgresVCRepository(db)
    
    # Initialize service
    service = WarmIntroService(
        connection_repo=connection_repo,
        introduction_repo=introduction_repo,
        user_repo=user_repo,
        startup_repo=startup_repo,
        vc_repo=vc_repo
    )
    
    try:
        # Find paths
        paths = await service.find_intro_paths_for_match(
            requester_id=current_user,
            vc_id=vc_id,
            max_depth=max_depth
        )
        
        # Convert to response
        return [IntroPathResponse(**path) for path in paths]
        
    except Exception as e:
        logger.error(f"Error finding intro paths: {e}")
        raise HTTPException(status_code=500, detail="Failed to find introduction paths")


@router.post("/introductions/request")
async def request_introduction(
    request: RequestIntroductionRequest,
    db: Session = Depends(get_database),
    current_user: UUID = Depends(get_current_user)
) -> Dict[str, Any]:
    """Request an introduction through a connector."""
    # Initialize repositories
    connection_repo = PostgresConnectionRepository(db)
    introduction_repo = PostgresIntroductionRepository(db)
    user_repo = PostgresUserRepository(db)
    startup_repo = PostgresStartupRepository(db)
    vc_repo = PostgresVCRepository(db)
    
    # Initialize service
    service = WarmIntroService(
        connection_repo=connection_repo,
        introduction_repo=introduction_repo,
        user_repo=user_repo,
        startup_repo=startup_repo,
        vc_repo=vc_repo
    )
    
    try:
        # Request introduction
        intro_request = await service.request_introduction(
            requester_id=current_user,
            target_id=request.target_id,
            connector_id=request.connector_id,
            message=request.message
        )
        
        return {
            "request_id": str(intro_request.id),
            "status": intro_request.status.value,
            "message": "Introduction request sent successfully",
            "expires_at": intro_request.expires_at.isoformat() if intro_request.expires_at else None
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error requesting introduction: {e}")
        raise HTTPException(status_code=500, detail="Failed to request introduction")


@router.get("/introductions/pending")
async def get_pending_intro_requests(
    db: Session = Depends(get_database),
    current_user: UUID = Depends(get_current_user)
) -> List[Dict[str, Any]]:
    """Get pending introduction requests where current user is the connector."""
    # Initialize repositories
    connection_repo = PostgresConnectionRepository(db)
    introduction_repo = PostgresIntroductionRepository(db)
    user_repo = PostgresUserRepository(db)
    startup_repo = PostgresStartupRepository(db)
    vc_repo = PostgresVCRepository(db)
    
    # Initialize service
    service = WarmIntroService(
        connection_repo=connection_repo,
        introduction_repo=introduction_repo,
        user_repo=user_repo,
        startup_repo=startup_repo,
        vc_repo=vc_repo
    )
    
    try:
        # Get pending requests
        requests = await service.get_pending_intro_requests(current_user)
        return requests
        
    except Exception as e:
        logger.error(f"Error getting pending intro requests: {e}")
        raise HTTPException(status_code=500, detail="Failed to get pending requests")


@router.put("/introductions/{request_id}/respond")
async def respond_to_intro_request(
    request_id: UUID,
    response: RespondToIntroRequest,
    db: Session = Depends(get_database),
    current_user: UUID = Depends(get_current_user)
) -> Dict[str, Any]:
    """Accept or decline an introduction request."""
    # Initialize repositories
    connection_repo = PostgresConnectionRepository(db)
    introduction_repo = PostgresIntroductionRepository(db)
    user_repo = PostgresUserRepository(db)
    startup_repo = PostgresStartupRepository(db)
    vc_repo = PostgresVCRepository(db)
    
    # Initialize service
    service = WarmIntroService(
        connection_repo=connection_repo,
        introduction_repo=introduction_repo,
        user_repo=user_repo,
        startup_repo=startup_repo,
        vc_repo=vc_repo
    )
    
    try:
        # Respond to request
        updated_request = await service.respond_to_intro_request(
            request_id=request_id,
            connector_id=current_user,
            accept=response.accept,
            notes=response.notes
        )
        
        return {
            "request_id": str(updated_request.id),
            "status": updated_request.status.value,
            "message": f"Introduction request {'accepted' if response.accept else 'declined'}"
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error responding to intro request: {e}")
        raise HTTPException(status_code=500, detail="Failed to respond to request")


@router.get("/analytics")
async def get_connection_analytics(
    db: Session = Depends(get_database),
    current_user: UUID = Depends(get_current_user)
) -> Dict[str, Any]:
    """Get analytics about the current user's connections."""
    # Initialize repositories
    connection_repo = PostgresConnectionRepository(db)
    introduction_repo = PostgresIntroductionRepository(db)
    user_repo = PostgresUserRepository(db)
    startup_repo = PostgresStartupRepository(db)
    vc_repo = PostgresVCRepository(db)
    
    # Initialize service
    service = WarmIntroService(
        connection_repo=connection_repo,
        introduction_repo=introduction_repo,
        user_repo=user_repo,
        startup_repo=startup_repo,
        vc_repo=vc_repo
    )
    
    try:
        # Get analytics
        analytics = await service.get_connection_analytics(current_user)
        return analytics
        
    except Exception as e:
        logger.error(f"Error getting connection analytics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get analytics")