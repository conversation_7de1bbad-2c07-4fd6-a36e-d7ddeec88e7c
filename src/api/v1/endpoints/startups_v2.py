"""Updated Startup API endpoints with async/sync repository support.

This version demonstrates how to use the smart repository getters
that switch between sync and async based on feature flags.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from uuid import UUID
import html

from src.core.schemas.startup import (
    StartupCreate,
    StartupUpdate,
    StartupResponse,
    StartupListResponse,
    StartupSearchParams
)
from src.core.models.startup import Startup
from src.core.services.startup_service import StartupService
from src.core.ports.ai_port import AIPort
from src.api.v1.deps import (
    get_current_user_optional, get_current_user,
    rate_limit_per_minute,
    PaginationParams,
    get_ai_port
)
from src.api.v1.async_deps import get_startup_repository
from src.api.errors import NotFoundError, BadRequestError
from src.core.config import settings

router = APIRouter(prefix="/startups", tags=["startups"])


async def get_startup_service(
    repository = Depends(get_startup_repository),
    ai_port: AIPort = Depends(get_ai_port)
) -> StartupService:
    """Get the startup service instance with smart repository injection."""
    return StartupService(repository=repository, ai_port=ai_port)


@router.post(
    "",
    response_model=StartupResponse,
    status_code=status.HTTP_201_CREATED,
    dependencies=[Depends(rate_limit_per_minute)]
)
async def create_startup(
    startup_data: StartupCreate,
    service: StartupService = Depends(get_startup_service),
    current_user: str = Depends(get_current_user)
) -> StartupResponse:
    """
    Create a new startup.
    
    Works with both sync and async repositories transparently.
    """
    try:
        # Convert schema to domain model with HTML sanitization
        startup = Startup(
            name=html.escape(startup_data.name) if startup_data.name else startup_data.name,
            sector=startup_data.sector,
            stage=startup_data.stage,
            description=html.escape(startup_data.description) if startup_data.description else startup_data.description,
            website=startup_data.website if startup_data.website is not None else "",
            team_size=startup_data.team_size if startup_data.team_size is not None else 0,
            monthly_revenue=startup_data.monthly_revenue if startup_data.monthly_revenue is not None else 0.0
        )
        
        # The service method is already async, so it works with both repository types
        created_startup = await service.create_startup(startup)
        
        # Convert domain model back to response schema
        return StartupResponse.from_domain(created_startup)
        
    except ValueError as e:
        raise BadRequestError(str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create startup: {str(e)}"
        )


@router.get(
    "",
    response_model=StartupListResponse,
    dependencies=[Depends(rate_limit_per_minute)]
)
async def list_startups(
    pagination: PaginationParams = Depends(),
    search_params: StartupSearchParams = Depends(),
    service: StartupService = Depends(get_startup_service)
) -> StartupListResponse:
    """
    List startups with optional filtering and pagination.
    
    Works with both sync and async repositories transparently.
    """
    # Use service to get startups with filters
    startups = await service.list_startups(
        sector=search_params.sector,
        stage=search_params.stage
    )
    
    # Apply additional filters
    if search_params.min_team_size is not None:
        startups = [s for s in startups if s.team_size >= search_params.min_team_size]
    if search_params.max_team_size is not None:
        startups = [s for s in startups if s.team_size <= search_params.max_team_size]
    if search_params.min_revenue is not None:
        startups = [s for s in startups if s.monthly_revenue >= search_params.min_revenue]
    if search_params.max_revenue is not None:
        startups = [s for s in startups if s.monthly_revenue <= search_params.max_revenue]
    if search_params.is_fundable is not None:
        startups = [s for s in startups if s.is_fundable() == search_params.is_fundable]
    
    # Apply search query
    if search_params.query:
        query_lower = search_params.query.lower()
        startups = [
            s for s in startups
            if query_lower in s.name.lower() or
               (s.description and query_lower in s.description.lower())
        ]
    
    # Apply pagination
    total = len(startups)
    start = pagination.offset
    end = start + pagination.size
    paginated_items = startups[start:end]
    
    # Convert to response
    return StartupListResponse(
        items=[StartupResponse.from_domain(s) for s in paginated_items],
        total=total,
        page=pagination.page,
        size=pagination.size,
        pages=(total + pagination.size - 1) // pagination.size
    )


@router.get(
    "/{startup_id}",
    response_model=StartupResponse,
    dependencies=[Depends(rate_limit_per_minute)]
)
async def get_startup(
    startup_id: UUID,
    service: StartupService = Depends(get_startup_service)
) -> StartupResponse:
    """
    Get a specific startup by ID.
    
    Works with both sync and async repositories transparently.
    """
    try:
        startup = await service.get_startup(startup_id)
        return StartupResponse.from_domain(startup)
    except ValueError:
        raise NotFoundError(f"Startup {startup_id} not found")


@router.put(
    "/{startup_id}",
    response_model=StartupResponse,
    dependencies=[Depends(rate_limit_per_minute)]
)
async def update_startup(
    startup_id: UUID,
    startup_update: StartupUpdate,
    service: StartupService = Depends(get_startup_service),
    current_user: Optional[str] = Depends(get_current_user_optional)
) -> StartupResponse:
    """
    Update a startup.
    
    Works with both sync and async repositories transparently.
    """
    try:
        # Prepare update dict with HTML sanitization
        updates = {}
        if startup_update.name is not None:
            updates["name"] = html.escape(startup_update.name)
        if startup_update.sector is not None:
            updates["sector"] = startup_update.sector
        if startup_update.stage is not None:
            updates["stage"] = startup_update.stage
        if startup_update.description is not None:
            updates["description"] = html.escape(startup_update.description)
        if startup_update.website is not None:
            updates["website"] = startup_update.website
        if startup_update.team_size is not None:
            updates["team_size"] = startup_update.team_size
        if startup_update.monthly_revenue is not None:
            updates["monthly_revenue"] = startup_update.monthly_revenue
        
        updated_startup = await service.update_startup(startup_id, updates)
        return StartupResponse.from_domain(updated_startup)
        
    except ValueError as e:
        if "not found" in str(e).lower():
            raise NotFoundError(f"Startup {startup_id} not found")
        raise BadRequestError(str(e))


@router.delete(
    "/{startup_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    dependencies=[Depends(rate_limit_per_minute)]
)
async def delete_startup(
    startup_id: UUID,
    service: StartupService = Depends(get_startup_service),
    current_user: Optional[str] = Depends(get_current_user_optional)
) -> None:
    """
    Delete a startup.
    
    Works with both sync and async repositories transparently.
    """
    success = await service.delete_startup(startup_id)
    if not success:
        raise NotFoundError(f"Startup {startup_id} not found")


@router.post(
    "/{startup_id}/analyze",
    response_model=dict,
    dependencies=[Depends(rate_limit_per_minute)]
)
async def analyze_startup(
    startup_id: UUID,
    force_refresh: bool = Query(False, description="Force refresh of cached analysis"),
    service: StartupService = Depends(get_startup_service)
) -> dict:
    """
    Get AI analysis for a startup.
    
    Works with both sync and async repositories transparently.
    """
    try:
        insights = await service.analyze_startup(startup_id, force_refresh=force_refresh)
        
        # Convert insights to dict for response
        return {
            "startup_id": str(startup_id),
            "summary": insights.summary,
            "strengths": insights.strengths,
            "weaknesses": insights.weaknesses,
            "opportunities": insights.opportunities,
            "market_fit_score": insights.market_fit_score,
            "investment_readiness_score": insights.investment_readiness_score,
            "recommended_investors": insights.recommended_investors,
            "key_metrics": insights.key_metrics,
            "analyzed_at": insights.analyzed_at.isoformat() if insights.analyzed_at else None
        }
        
    except ValueError as e:
        if "not found" in str(e).lower():
            raise NotFoundError(f"Startup {startup_id} not found")
        raise BadRequestError(str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Analysis failed: {str(e)}"
        )


# Debug endpoint to check which repository type is being used
@router.get("/debug/repository-type")
async def get_repository_type() -> dict:
    """Debug endpoint to check if async or sync repository is being used."""
    return {
        "use_async_db": settings.use_async_db,
        "repository_type": "async" if settings.use_async_db else "sync"
    }