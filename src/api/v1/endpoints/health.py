"""Health check endpoints for system monitoring."""

from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
import logging

from src.infrastructure.redis.connection import RedisConnectionFactory
from src.infrastructure.redis.adapter import RedisAdapter
from src.database.setup import get_db
from src.core.config import settings
from src.workers.health import (
    check_celery_status,
    check_celery_beat_status,
    get_queue_info
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/health", tags=["health"])


@router.get("/")
async def health_check() -> Dict[str, Any]:
    """Basic health check endpoint.
    
    Returns:
        Basic health status
    """
    return {
        "status": "healthy",
        "service": "VC Matching Platform API",
        "version": settings.app_version
    }


@router.get("/detailed")
async def detailed_health_check() -> Dict[str, Any]:
    """Detailed health check including all dependencies.
    
    Returns:
        Comprehensive health status for all system components
    """
    health_status = {
        "status": "healthy",
        "service": "VC Matching Platform API",
        "version": settings.app_version,
        "components": {}
    }
    
    overall_healthy = True
    
    # Check Redis health
    try:
        redis_health = await RedisConnectionFactory.health_check()
        health_status["components"]["redis"] = redis_health
        
        if not redis_health["healthy"]:
            overall_healthy = False
            
    except Exception as e:
        logger.error(f"Redis health check failed: {e}")
        health_status["components"]["redis"] = {
            "healthy": False,
            "error": str(e)
        }
        overall_healthy = False
    
    # Check Database health
    try:
        db = next(get_db())
        # Try a simple query
        result = db.execute("SELECT 1").fetchone()
        db.close()
        
        health_status["components"]["database"] = {
            "healthy": True,
            "connection": "active"
        }
        
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        health_status["components"]["database"] = {
            "healthy": False,
            "error": str(e)
        }
        overall_healthy = False
    
    # Check Celery health
    try:
        celery_health = check_celery_status()
        health_status["components"]["celery"] = celery_health
        
        if not celery_health["healthy"]:
            overall_healthy = False
            
    except Exception as e:
        logger.error(f"Celery health check failed: {e}")
        health_status["components"]["celery"] = {
            "healthy": False,
            "error": str(e)
        }
        overall_healthy = False
    
    # Check Celery Beat health
    try:
        beat_health = check_celery_beat_status()
        health_status["components"]["celery_beat"] = beat_health
        
        # Beat being unhealthy is a warning, not critical
        if not beat_health["healthy"]:
            health_status["warnings"] = health_status.get("warnings", [])
            health_status["warnings"].append("Celery Beat scheduler may not be configured")
            
    except Exception as e:
        logger.error(f"Celery Beat health check failed: {e}")
        health_status["components"]["celery_beat"] = {
            "healthy": False,
            "error": str(e)
        }
    
    # Check AI Cache functionality
    try:
        adapter = RedisAdapter(key_prefix="health_check")
        cache_healthy = await adapter.health_check()
        
        # Test basic cache operations
        if cache_healthy:
            test_key = "health_test"
            test_value = "test_value"
            
            # Test set operation
            set_success = await adapter.set(test_key, test_value, ttl=10)
            
            if set_success:
                # Test get operation
                retrieved_value = await adapter.get(test_key)
                
                if retrieved_value == test_value:
                    # Test delete operation
                    delete_success = await adapter.delete(test_key)
                    
                    health_status["components"]["cache"] = {
                        "healthy": True,
                        "operations": {
                            "set": True,
                            "get": True,
                            "delete": delete_success
                        }
                    }
                else:
                    health_status["components"]["cache"] = {
                        "healthy": False,
                        "error": "Cache get operation failed"
                    }
                    overall_healthy = False
            else:
                health_status["components"]["cache"] = {
                    "healthy": False,
                    "error": "Cache set operation failed"
                }
                overall_healthy = False
        else:
            health_status["components"]["cache"] = {
                "healthy": False,
                "error": "Cache connection failed"
            }
            overall_healthy = False
            
    except Exception as e:
        logger.error(f"Cache health check failed: {e}")
        health_status["components"]["cache"] = {
            "healthy": False,
            "error": str(e)
        }
        overall_healthy = False
    
    # Set overall status
    health_status["status"] = "healthy" if overall_healthy else "unhealthy"
    
    # Return appropriate HTTP status
    if not overall_healthy:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=health_status
        )
    
    return health_status


@router.get("/redis")
async def redis_health_check() -> Dict[str, Any]:
    """Specific Redis health check endpoint.
    
    Returns:
        Redis-specific health information
    """
    try:
        health_info = await RedisConnectionFactory.health_check()
        
        # Add connection info
        connection_info = RedisConnectionFactory.get_connection_info()
        health_info["connection_info"] = connection_info
        
        # Add Redis adapter stats if available
        try:
            adapter = RedisAdapter(key_prefix="health")
            stats = adapter.get_stats()
            health_info["stats"] = stats
        except Exception as e:
            health_info["stats_error"] = str(e)
        
        if not health_info["healthy"]:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail=health_info
            )
        
        return health_info
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Redis health check failed: {e}")
        error_response = {
            "healthy": False,
            "error": str(e)
        }
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=error_response
        )


@router.get("/cache/stats")
async def cache_stats() -> Dict[str, Any]:
    """Get cache statistics and performance metrics.
    
    Returns:
        Cache performance statistics
    """
    try:
        adapter = RedisAdapter(key_prefix="vc_platform")
        stats = adapter.get_stats()
        
        # Add additional metadata
        stats["cache_config"] = {
            "default_ttl": adapter.default_ttl,
            "key_prefix": adapter.key_prefix,
            "redis_url": settings.redis_url.replace(
                settings.redis_url.split('@')[0].split('://')[1], 
                "***"
            ) if '@' in settings.redis_url else settings.redis_url
        }
        
        return stats
        
    except Exception as e:
        logger.error(f"Failed to get cache stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"error": "Failed to retrieve cache statistics", "details": str(e)}
        )


@router.post("/cache/clear")
async def clear_cache(pattern: str = "*") -> Dict[str, Any]:
    """Clear cache entries matching pattern.
    
    Args:
        pattern: Pattern to match for cache key deletion
        
    Returns:
        Number of keys deleted
    """
    try:
        adapter = RedisAdapter(key_prefix="vc_platform")
        
        # Safety check - don't allow clearing all Redis keys
        if pattern == "*" and not adapter.key_prefix:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot clear all Redis keys without a key prefix"
            )
        
        deleted_count = await adapter.clear_prefix(pattern)
        
        logger.info(f"Cleared {deleted_count} cache entries matching pattern: {pattern}")
        
        return {
            "pattern": pattern,
            "deleted_keys": deleted_count,
            "status": "success"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to clear cache: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"error": "Failed to clear cache", "details": str(e)}
        )


@router.get("/celery")
async def celery_health_check() -> Dict[str, Any]:
    """Specific Celery health check endpoint.
    
    Returns:
        Celery-specific health information
    """
    try:
        # Get basic Celery status
        celery_status = check_celery_status()
        
        # Get Beat status
        beat_status = check_celery_beat_status()
        
        # Get queue information
        queue_info = get_queue_info()
        
        health_info = {
            "celery": celery_status,
            "beat": beat_status,
            "queues": queue_info,
            "overall_healthy": celery_status["healthy"] and queue_info["healthy"]
        }
        
        if not health_info["overall_healthy"]:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail=health_info
            )
        
        return health_info
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Celery health check failed: {e}")
        error_response = {
            "healthy": False,
            "error": str(e)
        }
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=error_response
        )


@router.get("/dependencies")
async def check_dependencies() -> Dict[str, Any]:
    """Check all external dependencies.
    
    Returns:
        Status of all external dependencies
    """
    dependencies = {}
    
    # Check Redis
    try:
        redis_health = await RedisConnectionFactory.health_check()
        dependencies["redis"] = {
            "status": "healthy" if redis_health["healthy"] else "unhealthy",
            "details": redis_health
        }
    except Exception as e:
        dependencies["redis"] = {
            "status": "error",
            "error": str(e)
        }
    
    # Check Database
    try:
        db = next(get_db())
        db.execute("SELECT 1").fetchone()
        db.close()
        dependencies["database"] = {
            "status": "healthy",
            "connection": "active"
        }
    except Exception as e:
        dependencies["database"] = {
            "status": "error",
            "error": str(e)
        }
    
    # Check Celery
    try:
        celery_health = check_celery_status()
        dependencies["celery"] = {
            "status": "healthy" if celery_health["healthy"] else "unhealthy",
            "worker_count": celery_health.get("worker_count", 0),
            "details": celery_health
        }
    except Exception as e:
        dependencies["celery"] = {
            "status": "error",
            "error": str(e)
        }
    
    # Check OpenAI API key availability
    dependencies["openai"] = {
        "status": "configured" if settings.openai_api_key else "not_configured",
        "model": settings.openai_model
    }
    
    return {
        "dependencies": dependencies,
        "overall_status": "healthy" if all(
            dep.get("status") in ["healthy", "configured"] 
            for dep in dependencies.values()
        ) else "issues_detected"
    }