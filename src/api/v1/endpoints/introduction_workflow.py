"""API endpoints for the complete introduction request workflow."""

from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, BackgroundTasks
from typing import Dict, Any, List, Optional
from uuid import UUID
from datetime import datetime
import logging
from sqlalchemy.orm import Session

from src.api.v1.deps import get_current_user, get_database, get_redis_client
from src.core.services.warm_intro_service import WarmIntroService
# NotificationService is defined locally in this file
from src.database.repositories.connection_repository import ConnectionRepository, IntroductionRepository
from src.database.repositories.user_repository import UserRepository
from src.database.models import User
from redis import Redis

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/introductions", tags=["introductions"])


# Create a simple notification service for the workflow
class NotificationService:
    """Simple notification service for intro requests."""
    
    def __init__(self, redis_client: Redis):
        self.redis = redis_client
    
    async def send_intro_request_notification(
        self,
        connector: User,
        requester: User,
        target: User,
        request_id: str
    ):
        """Send notification to connector about new intro request."""
        notification = {
            "type": "intro_request",
            "to_user_id": str(connector.id),
            "from_user_id": str(requester.id),
            "request_id": request_id,
            "message": f"{requester.full_name or requester.username} requested an introduction to {target.full_name or target.username}",
            "created_at": datetime.utcnow().isoformat()
        }
        
        # Store in Redis queue
        key = f"notifications:{connector.id}"
        self.redis.lpush(key, str(notification))
        self.redis.expire(key, 86400 * 30)  # 30 days
        
        logger.info(f"Sent intro request notification to {connector.email}")
    
    async def send_intro_accepted_notification(
        self,
        requester: User,
        target: User,
        connector: User
    ):
        """Send notification when intro is accepted."""
        # Notify requester
        notification = {
            "type": "intro_accepted",
            "to_user_id": str(requester.id),
            "message": f"{connector.full_name or connector.username} accepted your introduction request to {target.full_name or target.username}",
            "created_at": datetime.utcnow().isoformat()
        }
        
        key = f"notifications:{requester.id}"
        self.redis.lpush(key, str(notification))
        
        # Notify target
        notification = {
            "type": "intro_incoming",
            "to_user_id": str(target.id),
            "message": f"{connector.full_name or connector.username} is introducing you to {requester.full_name or requester.username}",
            "created_at": datetime.utcnow().isoformat()
        }
        
        key = f"notifications:{target.id}"
        self.redis.lpush(key, str(notification))
    
    async def send_intro_declined_notification(
        self,
        requester: User,
        connector: User,
        reason: Optional[str] = None
    ):
        """Send notification when intro is declined."""
        message = f"{connector.full_name or connector.username} declined your introduction request"
        if reason:
            message += f": {reason}"
        
        notification = {
            "type": "intro_declined",
            "to_user_id": str(requester.id),
            "message": message,
            "created_at": datetime.utcnow().isoformat()
        }
        
        key = f"notifications:{requester.id}"
        self.redis.lpush(key, str(notification))


@router.post("/request")
async def request_introduction(
    request_data: Dict[str, Any],
    background_tasks: BackgroundTasks,
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_database),
    redis: Redis = Depends(get_redis_client)
) -> Dict[str, Any]:
    """
    Request an introduction to a target user through a mutual connection.
    
    Request format:
    {
        "target_user_id": "uuid",
        "connector_user_id": "uuid",
        "message": "Hi, I'd love to connect with X about Y...",
        "context": {
            "reason": "investment opportunity",
            "startup_id": "uuid (optional)",
            "vc_id": "uuid (optional)"
        }
    }
    """
    try:
        # Validate request
        target_id = UUID(request_data.get("target_user_id"))
        connector_id = UUID(request_data.get("connector_user_id"))
        message = request_data.get("message", "")
        context = request_data.get("context", {})
        
        if not message:
            raise HTTPException(status_code=400, detail="Message is required")
        
        # Initialize services
        connection_repo = ConnectionRepository(db)
        introduction_repo = IntroductionRepository(db)
        user_repo = UserRepository(db)
        warm_intro_service = WarmIntroService(
            connection_repo,
            introduction_repo,
            user_repo,
            None,  # startup_repo not needed for this
            None   # vc_repo not needed for this
        )
        notification_service = NotificationService(redis)
        
        # Create introduction request
        intro_request = await warm_intro_service.request_introduction(
            requester_id=UUID(current_user),
            target_id=target_id,
            connector_id=connector_id,
            message=message
        )
        
        # Get user objects for notifications
        requester = await user_repo.get(UUID(current_user))
        target = await user_repo.get(target_id)
        connector = await user_repo.get(connector_id)
        
        # Send notification to connector
        background_tasks.add_task(
            notification_service.send_intro_request_notification,
            connector,
            requester,
            target,
            str(intro_request.id)
        )
        
        # Store context in Redis for later use
        if context:
            context_key = f"intro_context:{intro_request.id}"
            redis.set(context_key, str(context), ex=86400 * 7)  # 7 days
        
        return {
            "status": "success",
            "request_id": str(intro_request.id),
            "message": "Introduction request sent successfully",
            "details": {
                "requester": requester.full_name or requester.username,
                "target": target.full_name or target.username,
                "connector": connector.full_name or connector.username,
                "status": intro_request.status.value,
                "expires_at": intro_request.expires_at.isoformat() if intro_request.expires_at else None
            }
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating intro request: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/pending")
async def get_pending_intro_requests(
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_database)
) -> Dict[str, Any]:
    """Get all pending introduction requests where user is the connector."""
    try:
        # Initialize services
        connection_repo = ConnectionRepository(db)
        introduction_repo = IntroductionRepository(db)
        user_repo = UserRepository(db)
        warm_intro_service = WarmIntroService(
            connection_repo,
            introduction_repo,
            user_repo,
            None,
            None
        )
        
        # Get pending requests
        pending_requests = await warm_intro_service.get_pending_intro_requests(UUID(current_user))
        
        return {
            "status": "success",
            "count": len(pending_requests),
            "requests": pending_requests
        }
        
    except Exception as e:
        logger.error(f"Error getting pending requests: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/{request_id}/respond")
async def respond_to_intro_request(
    request_id: UUID,
    response_data: Dict[str, Any],
    background_tasks: BackgroundTasks,
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_database),
    redis: Redis = Depends(get_redis_client)
) -> Dict[str, Any]:
    """
    Respond to an introduction request (accept or decline).
    
    Request format:
    {
        "action": "accept" or "decline",
        "notes": "Optional message",
        "intro_message": "Message to include with introduction (if accepting)"
    }
    """
    try:
        action = response_data.get("action")
        notes = response_data.get("notes")
        intro_message = response_data.get("intro_message")
        
        if action not in ["accept", "decline"]:
            raise HTTPException(status_code=400, detail="Action must be 'accept' or 'decline'")
        
        # Initialize services
        connection_repo = ConnectionRepository(db)
        introduction_repo = IntroductionRepository(db)
        user_repo = UserRepository(db)
        warm_intro_service = WarmIntroService(
            connection_repo,
            introduction_repo,
            user_repo,
            None,
            None
        )
        notification_service = NotificationService(redis)
        
        # Respond to request
        updated_request = await warm_intro_service.respond_to_intro_request(
            request_id=request_id,
            connector_id=UUID(current_user),
            accept=(action == "accept"),
            notes=notes
        )
        
        # Get users for notifications
        requester = await user_repo.get(updated_request.requester_id)
        target = await user_repo.get(updated_request.target_id)
        connector = await user_repo.get(updated_request.connector_id)
        
        if action == "accept":
            # Send acceptance notifications
            background_tasks.add_task(
                notification_service.send_intro_accepted_notification,
                requester,
                target,
                connector
            )
            
            # Create introduction email/message template
            if intro_message:
                intro_key = f"intro_message:{request_id}"
                redis.set(intro_key, intro_message, ex=86400 * 30)  # 30 days
            
            return {
                "status": "success",
                "message": "Introduction accepted and parties notified",
                "introduction": {
                    "requester": {
                        "name": requester.full_name or requester.username,
                        "email": requester.email
                    },
                    "target": {
                        "name": target.full_name or target.username,
                        "email": target.email
                    },
                    "status": updated_request.status.value
                }
            }
        else:
            # Send decline notification
            background_tasks.add_task(
                notification_service.send_intro_declined_notification,
                requester,
                connector,
                notes
            )
            
            return {
                "status": "success",
                "message": "Introduction declined",
                "details": {
                    "requester_notified": True,
                    "reason_provided": bool(notes)
                }
            }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error responding to intro request: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/my-requests")
async def get_my_intro_requests(
    status: Optional[str] = None,
    role: Optional[str] = None,
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_database)
) -> Dict[str, Any]:
    """
    Get introduction requests involving the current user.
    
    Params:
    - status: Filter by status (pending, accepted, declined, expired, completed)
    - role: Filter by role (requester, target, connector)
    """
    try:
        introduction_repo = IntroductionRepository(db)
        user_repo = UserRepository(db)
        user_id = UUID(current_user)
        
        # Get all requests involving user
        all_requests = await introduction_repo.get_user_requests(user_id)
        
        # Filter by status if provided
        if status:
            all_requests = [r for r in all_requests if r.status.value == status]
        
        # Categorize by role
        as_requester = []
        as_target = []
        as_connector = []
        
        for request in all_requests:
            # Get all users involved
            requester = await user_repo.get(request.requester_id)
            target = await user_repo.get(request.target_id)
            connector = await user_repo.get(request.connector_id)
            
            request_data = {
                "id": str(request.id),
                "status": request.status.value,
                "created_at": request.created_at.isoformat(),
                "expires_at": request.expires_at.isoformat() if request.expires_at else None,
                "message": request.message,
                "requester": {
                    "id": str(requester.id),
                    "name": requester.full_name or requester.username,
                    "email": requester.email
                },
                "target": {
                    "id": str(target.id),
                    "name": target.full_name or target.username,
                    "email": target.email
                },
                "connector": {
                    "id": str(connector.id),
                    "name": connector.full_name or connector.username,
                    "email": connector.email
                }
            }
            
            if request.requester_id == user_id:
                as_requester.append(request_data)
            if request.target_id == user_id:
                as_target.append(request_data)
            if request.connector_id == user_id:
                as_connector.append(request_data)
        
        # Filter by role if specified
        if role == "requester":
            filtered_requests = as_requester
        elif role == "target":
            filtered_requests = as_target
        elif role == "connector":
            filtered_requests = as_connector
        else:
            filtered_requests = all_requests
        
        return {
            "status": "success",
            "summary": {
                "total": len(all_requests),
                "as_requester": len(as_requester),
                "as_target": len(as_target),
                "as_connector": len(as_connector)
            },
            "requests": {
                "as_requester": as_requester if not role or role == "requester" else [],
                "as_target": as_target if not role or role == "target" else [],
                "as_connector": as_connector if not role or role == "connector" else []
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting user intro requests: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/{request_id}/complete")
async def mark_introduction_complete(
    request_id: UUID,
    feedback: Optional[Dict[str, Any]] = None,
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_database)
) -> Dict[str, Any]:
    """
    Mark an introduction as complete and optionally provide feedback.
    
    Feedback format:
    {
        "outcome": "connected|no_response|not_interested",
        "rating": 1-5,
        "notes": "Optional feedback"
    }
    """
    try:
        introduction_repo = IntroductionRepository(db)
        user_id = UUID(current_user)
        
        # Get the request
        request = await introduction_repo.get_request(request_id)
        
        if not request:
            raise HTTPException(status_code=404, detail="Introduction request not found")
        
        # Only requester or target can mark as complete
        if request.requester_id != user_id and request.target_id != user_id:
            raise HTTPException(status_code=403, detail="Not authorized to complete this introduction")
        
        # Update status
        request.mark_complete()
        
        # Store feedback if provided
        if feedback:
            if not hasattr(request, 'feedback'):
                request.feedback = {}
            
            request.feedback[str(user_id)] = {
                "outcome": feedback.get("outcome"),
                "rating": feedback.get("rating"),
                "notes": feedback.get("notes"),
                "submitted_at": datetime.utcnow().isoformat()
            }
        
        # Save updates
        updated_request = await introduction_repo.update_request(request)
        
        return {
            "status": "success",
            "message": "Introduction marked as complete",
            "request_id": str(request_id),
            "feedback_provided": bool(feedback)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error completing introduction: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/stats")
async def get_introduction_stats(
    current_user: str = Depends(get_current_user),
    db: Session = Depends(get_database)
) -> Dict[str, Any]:
    """Get statistics about user's introduction activity."""
    try:
        connection_repo = ConnectionRepository(db)
        introduction_repo = IntroductionRepository(db)
        user_repo = UserRepository(db)
        warm_intro_service = WarmIntroService(
            connection_repo,
            introduction_repo,
            user_repo,
            None,
            None
        )
        
        # Get connection analytics (includes intro stats)
        analytics = await warm_intro_service.get_connection_analytics(UUID(current_user))
        
        return {
            "status": "success",
            "stats": {
                "connections": {
                    "total": analytics["total_connections"],
                    "by_strength": analytics["strength_distribution"],
                    "by_type": analytics["relationship_distribution"]
                },
                "introductions": analytics["introduction_stats"],
                "network": {
                    "reach": analytics["network_reach"],
                    "key_connectors": analytics["key_connectors"][:5]  # Top 5
                }
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting intro stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")