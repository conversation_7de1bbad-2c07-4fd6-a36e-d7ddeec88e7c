"""Refactored API endpoints for VC operations using the service layer.

This demonstrates proper separation of concerns:
- API layer handles HTTP concerns (requests, responses, status codes)
- Service layer handles business logic
- Repository layer handles data persistence
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from uuid import UUID
from datetime import datetime

from src.core.schemas.vc import (
    VCCreate,
    VCUpdate,
    VCResponse,
    VCListResponse,
    ThesisExtractionRequest,
    ThesisExtractionResponse
)
from src.core.models.vc import VC
from src.core.services.vc_service import VCService
from src.core.repositories.vc_repository import VCRepository
from src.database.repositories.vc_repository import PostgresVCRepository
from src.core.ports.ai_port import AIPort
from src.api.v1.deps import (
    get_current_user_optional, get_current_user,
    rate_limit_per_minute,
    PaginationParams,
    get_ai_port,
    get_database
)
from src.api.errors import NotFoundError, BadRequestError
from src.api.v1.error_handlers import handle_api_errors
from sqlalchemy.orm import Session

router = APIRouter(prefix="/vcs", tags=["vcs"])


# Dependency injection for services
async def get_vc_repository(db: Session = Depends(get_database)) -> VCRepository:
    """Get the VC repository instance."""
    return PostgresVCRepository(db)


async def get_vc_service(
    repository: VCRepository = Depends(get_vc_repository),
    ai_port: AIPort = Depends(get_ai_port)
) -> VCService:
    """Get the VC service instance with dependencies injected."""
    return VCService(repository=repository, ai_port=ai_port)


@router.post(
    "",
    response_model=VCResponse,
    status_code=status.HTTP_201_CREATED,
    dependencies=[Depends(rate_limit_per_minute)]
)
@handle_api_errors
async def create_vc(
    vc_data: VCCreate,
    service: VCService = Depends(get_vc_service),
    current_user: str = Depends(get_current_user)
) -> VCResponse:
    """
    Create a new VC firm.
    
    The API layer only handles HTTP concerns, delegating business logic to the service.
    """
    # Convert schema to domain model
    vc = VC(
        firm_name=vc_data.firm_name,
        website=vc_data.website,
        thesis=vc_data.thesis,
        sectors=vc_data.sectors,
        stages=vc_data.stages,
        check_size_min=vc_data.check_size_min,
        check_size_max=vc_data.check_size_max,
        portfolio_companies=vc_data.portfolio_companies,
        partners=vc_data.partners
    )
    
    # Use service to create VC (handles ID assignment, validation, persistence)
    created_vc = await service.create_vc(vc)
    
    # Convert domain model back to response schema
    return VCResponse.from_domain(created_vc)


@router.get(
    "",
    response_model=VCListResponse,
    dependencies=[Depends(rate_limit_per_minute)]
)
async def list_vcs(
    pagination: PaginationParams = Depends(),
    sector: Optional[str] = Query(None, description="Filter by investment sector"),
    stage: Optional[str] = Query(None, description="Filter by investment stage"),
    service: VCService = Depends(get_vc_service)
) -> VCListResponse:
    """
    List VCs with optional filtering and pagination.
    
    The service layer handles the business logic of filtering.
    """
    # Use service to get VCs with filters
    vcs = await service.list_vcs(
        sector=sector,
        stage=stage
    )
    
    # Apply pagination
    total = len(vcs)
    start = pagination.offset
    end = start + pagination.size
    paginated_items = vcs[start:end]
    
    return VCListResponse(
        items=[VCResponse.from_domain(vc) for vc in paginated_items],
        total=total,
        page=pagination.page,
        size=pagination.size,
        pages=(total + pagination.size - 1) // pagination.size
    )


@router.get(
    "/{vc_id}",
    response_model=VCResponse,
    dependencies=[Depends(rate_limit_per_minute)]
)
async def get_vc(
    vc_id: UUID,
    service: VCService = Depends(get_vc_service)
) -> VCResponse:
    """Get a specific VC by ID."""
    try:
        vc = await service.get_vc(vc_id)
        return VCResponse.from_domain(vc)
    except ValueError as e:
        raise NotFoundError("VC", str(vc_id))


@router.put(
    "/{vc_id}",
    response_model=VCResponse,
    dependencies=[Depends(rate_limit_per_minute)]
)
@handle_api_errors
async def update_vc(
    vc_id: UUID,
    vc_update: VCUpdate,
    service: VCService = Depends(get_vc_service),
    current_user: Optional[str] = Depends(get_current_user_optional)
) -> VCResponse:
    """
    Update a VC's information.
    
    All fields are optional - only provided fields will be updated.
    """
    # Get update data excluding unset fields
    update_data = vc_update.model_dump(exclude_unset=True)
    
    # Use service to update
    updated_vc = await service.update_vc(vc_id, update_data)
    
    if not updated_vc:
        raise NotFoundError("VC", str(vc_id))
    
    return VCResponse.from_domain(updated_vc)


@router.delete(
    "/{vc_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    dependencies=[Depends(rate_limit_per_minute)]
)
@handle_api_errors
async def delete_vc(
    vc_id: UUID,
    service: VCService = Depends(get_vc_service),
    current_user: str = Depends(get_current_user_optional)
) -> None:
    """
    Delete a VC.
    
    Requires authentication.
    """
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required"
        )
    
    deleted = await service.delete_vc(vc_id)
    if not deleted:
        raise NotFoundError("VC", str(vc_id))
    
    return None


@router.post(
    "/{vc_id}/extract-thesis",
    response_model=ThesisExtractionResponse,
    dependencies=[Depends(rate_limit_per_minute)]
)
async def extract_thesis(
    vc_id: UUID,
    request: ThesisExtractionRequest,
    service: VCService = Depends(get_vc_service),
    current_user: Optional[str] = Depends(get_current_user_optional)
) -> ThesisExtractionResponse:
    """
    Extract investment thesis from VC's website content using AI.
    
    This endpoint uses the service layer to coordinate between repository and AI analyzer.
    """
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required for thesis extraction"
        )
    
    try:
        analysis = await service.extract_thesis(
            vc_id,
            request.website_content,
            force_refresh=request.force_refresh
        )
        
        return ThesisExtractionResponse(
            vc_id=str(vc_id),
            thesis={
                "summary": analysis.thesis_summary,
                "sectors": analysis.investment_focus.sectors,
                "stages": analysis.investment_focus.stages,
                "check_sizes": analysis.check_size_range,
                "geographic_focus": analysis.investment_focus.geographical_focus,
                "portfolio_traits": analysis.portfolio_themes,
                "competitive_advantages": analysis.key_criteria,
                "confidence_score": analysis.confidence_score
            },
            extracted_at=datetime.utcnow()
        )
        
    except ValueError as e:
        if "not found" in str(e):
            raise NotFoundError("VC", str(vc_id))
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Thesis extraction failed: {str(e)}"
        )