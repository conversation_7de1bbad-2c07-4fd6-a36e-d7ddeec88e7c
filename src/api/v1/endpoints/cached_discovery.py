"""Cached discovery endpoints for improved performance."""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from uuid import UUID
from sqlalchemy.orm import Session
import logging

from src.api.v1.deps import get_database, get_current_user_optional, get_redis_client
from src.core.services.cached_discovery_service import CachedDiscoveryService
from redis import Redis

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/cached-discovery", tags=["cached-discovery"])


@router.post("/vcs/{vc_id}/discover-startups")
async def discover_startups_for_vc_cached(
    vc_id: UUID,
    limit: int = Query(default=20, le=100),
    min_score: float = Query(default=0.7, ge=0.0, le=1.0),
    page: int = Query(default=1, ge=1),
    use_cache: bool = Query(default=True, description="Enable caching"),
    db: Session = Depends(get_database),
    redis: Redis = Depends(get_redis_client),
    current_user: str = Depends(get_current_user_optional)
) -> Dict[str, Any]:
    """
    Cached version of VC startup discovery.
    
    Results are cached for 1 hour by default to improve performance.
    """
    try:
        # Initialize cached discovery service
        discovery_service = CachedDiscoveryService(db, redis)
        
        # Perform discovery with caching
        result = await discovery_service.discover_startups_for_vc(
            vc_id=vc_id,
            limit=limit,
            min_score=min_score,
            page=page,
            use_cache=use_cache
        )
        
        # Add cache status to response
        result["cache_info"] = {
            "cache_enabled": use_cache,
            "cache_status": "hit" if use_cache and "cached_at" in result else "miss"
        }
        
        return result
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error in cached VC discovery: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/startups/{startup_id}/discover-vcs")
async def discover_vcs_for_startup_cached(
    startup_id: UUID,
    limit: int = Query(default=20, le=100),
    min_score: float = Query(default=0.7, ge=0.0, le=1.0),
    page: int = Query(default=1, ge=1),
    use_cache: bool = Query(default=True, description="Enable caching"),
    db: Session = Depends(get_database),
    redis: Redis = Depends(get_redis_client),
    current_user: str = Depends(get_current_user_optional)
) -> Dict[str, Any]:
    """
    Cached version of startup VC discovery.
    
    Results are cached for 1 hour by default to improve performance.
    """
    try:
        # Initialize cached discovery service
        discovery_service = CachedDiscoveryService(db, redis)
        
        # Perform discovery with caching
        result = await discovery_service.discover_vcs_for_startup(
            startup_id=startup_id,
            limit=limit,
            min_score=min_score,
            page=page,
            use_cache=use_cache
        )
        
        # Add cache status to response
        result["cache_info"] = {
            "cache_enabled": use_cache,
            "cache_status": "hit" if use_cache and "cached_at" in result else "miss"
        }
        
        return result
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error in cached startup discovery: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/search")
async def search_with_cache(
    q: str = Query(..., description="Search query"),
    type: str = Query(default="all", description="Entity type: all, startup, vc"),
    sectors: Optional[List[str]] = Query(default=None, description="Filter by sectors"),
    stages: Optional[List[str]] = Query(default=None, description="Filter by stages"),
    limit: int = Query(default=20, le=100),
    use_cache: bool = Query(default=True, description="Enable caching"),
    db: Session = Depends(get_database),
    redis: Redis = Depends(get_redis_client)
) -> Dict[str, Any]:
    """
    Search across entities with caching.
    
    Results are cached for 1 hour to improve performance.
    """
    try:
        # Initialize cached discovery service
        discovery_service = CachedDiscoveryService(db, redis)
        
        # Perform search with caching
        results = await discovery_service.search_with_cache(
            query=q,
            entity_type=type,
            sectors=sectors,
            stages=stages,
            limit=limit,
            use_cache=use_cache
        )
        
        return {
            "query": q,
            "type": type,
            "results": results,
            "count": len(results),
            "filters": {
                "sectors": sectors,
                "stages": stages
            },
            "cache_info": {
                "cache_enabled": use_cache,
                "cache_status": "hit" if use_cache and results else "miss"
            }
        }
        
    except Exception as e:
        logger.error(f"Error in cached search: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/match-score/{startup_id}/{vc_id}")
async def get_match_score_cached(
    startup_id: UUID,
    vc_id: UUID,
    use_cache: bool = Query(default=True, description="Enable caching"),
    db: Session = Depends(get_database),
    redis: Redis = Depends(get_redis_client)
) -> Dict[str, Any]:
    """
    Get detailed matching score between startup and VC with caching.
    
    Scores are cached for 6 hours to improve performance.
    """
    try:
        # Initialize cached discovery service
        discovery_service = CachedDiscoveryService(db, redis)
        
        # Get matching score with caching
        score_data = await discovery_service.get_matching_score_with_cache(
            startup_id=startup_id,
            vc_id=vc_id,
            use_cache=use_cache
        )
        
        # Add cache status
        score_data["cache_info"] = {
            "cache_enabled": use_cache,
            "cache_status": "hit" if use_cache and "cached_at" in score_data else "miss"
        }
        
        return score_data
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error getting cached match score: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


# Cache management endpoints

@router.delete("/cache/vc/{vc_id}")
async def invalidate_vc_cache(
    vc_id: UUID,
    db: Session = Depends(get_database),
    redis: Redis = Depends(get_redis_client),
    current_user: str = Depends(get_current_user_optional)
) -> Dict[str, Any]:
    """
    Invalidate all caches related to a specific VC.
    
    Use this when VC data is updated.
    """
    try:
        discovery_service = CachedDiscoveryService(db, redis)
        deleted_count = await discovery_service.invalidate_vc_cache(vc_id)
        
        return {
            "status": "success",
            "vc_id": str(vc_id),
            "cache_entries_deleted": deleted_count
        }
        
    except Exception as e:
        logger.error(f"Error invalidating VC cache: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/cache/startup/{startup_id}")
async def invalidate_startup_cache(
    startup_id: UUID,
    db: Session = Depends(get_database),
    redis: Redis = Depends(get_redis_client),
    current_user: str = Depends(get_current_user_optional)
) -> Dict[str, Any]:
    """
    Invalidate all caches related to a specific startup.
    
    Use this when startup data is updated.
    """
    try:
        discovery_service = CachedDiscoveryService(db, redis)
        deleted_count = await discovery_service.invalidate_startup_cache(startup_id)
        
        return {
            "status": "success",
            "startup_id": str(startup_id),
            "cache_entries_deleted": deleted_count
        }
        
    except Exception as e:
        logger.error(f"Error invalidating startup cache: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/cache/all")
async def clear_all_discovery_caches(
    db: Session = Depends(get_database),
    redis: Redis = Depends(get_redis_client),
    current_user: str = Depends(get_current_user_optional)
) -> Dict[str, Any]:
    """
    Clear all discovery-related caches.
    
    Use with caution - this will force all discovery queries to be recalculated.
    """
    try:
        discovery_service = CachedDiscoveryService(db, redis)
        cache_service = discovery_service.cache
        
        # Clear different cache types
        discovery_deleted = await cache_service.invalidate_discovery_caches()
        matching_deleted = await cache_service.invalidate_matching_caches()
        
        return {
            "status": "success",
            "caches_cleared": {
                "discovery": discovery_deleted,
                "matching": matching_deleted,
                "total": discovery_deleted + matching_deleted
            }
        }
        
    except Exception as e:
        logger.error(f"Error clearing caches: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/cache/stats")
async def get_cache_statistics(
    db: Session = Depends(get_database),
    redis: Redis = Depends(get_redis_client)
) -> Dict[str, Any]:
    """
    Get cache statistics and performance metrics.
    """
    try:
        discovery_service = CachedDiscoveryService(db, redis)
        stats = await discovery_service.get_cache_statistics()
        
        return {
            "status": "success",
            "cache_stats": stats
        }
        
    except Exception as e:
        logger.error(f"Error getting cache stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")