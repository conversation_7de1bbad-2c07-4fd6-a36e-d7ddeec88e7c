"""Authentication endpoints."""

from datetime import <PERSON><PERSON><PERSON>
from typing import Optional
from fastapi import API<PERSON><PERSON>er, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from pydantic import BaseModel, EmailStr, Field

from src.core.config import settings
from src.core.security import (
    verify_password,
    get_password_hash,
    create_access_token,
    Token
)
from src.database.models.user import User
from src.database.repositories.user_repository import UserRepository
from src.api.v1.deps import get_database, get_current_user
from src.api.errors import BadRequestError, UnauthorizedError, ConflictError


router = APIRouter(prefix="/auth", tags=["authentication"])


# Request/Response models
class UserRegister(BaseModel):
    """User registration request."""
    email: EmailStr
    username: str = Field(..., min_length=3, max_length=50)
    password: str = Field(..., min_length=8, max_length=100)
    full_name: Optional[str] = None


class UserResponse(BaseModel):
    """User response model."""
    id: str
    email: str
    username: str
    full_name: Optional[str]
    is_active: bool
    roles: list
    
    class Config:
        from_attributes = True


class LoginResponse(BaseModel):
    """Login response with token and user info."""
    access_token: str
    token_type: str = "bearer"
    user: UserResponse


# Endpoints
@router.post("/register", response_model=UserResponse)
async def register(
    user_data: UserRegister,
    db: Session = Depends(get_database)
):
    """Register a new user account."""
    user_repo = UserRepository()
    
    # Check if email already exists
    if user_repo.find_by_email(db, user_data.email):
        raise ConflictError("Email already registered")
    
    # Check if username already exists
    if user_repo.find_by_username(db, user_data.username):
        raise ConflictError("Username already taken")
    
    # Create new user
    hashed_password = get_password_hash(user_data.password)
    
    user = User(
        email=user_data.email,
        username=user_data.username,
        hashed_password=hashed_password,
        full_name=user_data.full_name,
        roles=["user"]  # Default role
    )
    
    # Save to database
    created_user = user_repo.create(db, user)
    
    return UserResponse(
        id=str(created_user.id),
        email=created_user.email,
        username=created_user.username,
        full_name=created_user.full_name,
        is_active=created_user.is_active,
        roles=created_user.roles or []
    )


@router.post("/login", response_model=LoginResponse)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_database)
):
    """Login with username/email and password."""
    user_repo = UserRepository()
    
    # Find user by username or email
    user = user_repo.find_by_email_or_username(db, form_data.username)
    
    if not user:
        raise UnauthorizedError("Invalid credentials")
    
    # Verify password
    if not verify_password(form_data.password, user.hashed_password):
        raise UnauthorizedError("Invalid credentials")
    
    # Check if user is active
    if not user.is_active:
        raise UnauthorizedError("Account is inactive")
    
    # Update last login
    user_repo.update_last_login(db, user.id)
    
    # Create access token
    access_token = create_access_token(
        data={
            "sub": str(user.id),
            "email": user.email,
            "username": user.username,
            "roles": user.roles or []
        },
        expires_delta=timedelta(minutes=settings.access_token_expire_minutes)
    )
    
    return LoginResponse(
        access_token=access_token,
        user=UserResponse(
            id=str(user.id),
            email=user.email,
            username=user.username,
            full_name=user.full_name,
            is_active=user.is_active,
            roles=user.roles or []
        )
    )


@router.get("/me", response_model=UserResponse)
async def get_current_user_profile(
    current_user_id: str = Depends(get_current_user),
    db: Session = Depends(get_database)
):
    """Get current user profile."""
    user_repo = UserRepository()
    
    user = user_repo.get_by_id(db, current_user_id)
    if not user:
        raise UnauthorizedError("User not found")
    
    return UserResponse(
        id=str(user.id),
        email=user.email,
        username=user.username,
        full_name=user.full_name,
        is_active=user.is_active,
        roles=user.roles or []
    )


@router.post("/refresh", response_model=Token)
async def refresh_token(
    current_user_id: str = Depends(get_current_user),
    db: Session = Depends(get_database)
):
    """Refresh access token."""
    user_repo = UserRepository()
    
    user = user_repo.get_by_id(db, current_user_id)
    if not user or not user.is_active:
        raise UnauthorizedError("Invalid user")
    
    # Create new access token
    access_token = create_access_token(
        data={
            "sub": str(user.id),
            "email": user.email,
            "username": user.username,
            "roles": user.roles or []
        },
        expires_delta=timedelta(minutes=settings.access_token_expire_minutes)
    )
    
    return Token(access_token=access_token)


class ChangePasswordRequest(BaseModel):
    """Change password request."""
    old_password: str = Field(..., description="Current password")
    new_password: str = Field(..., min_length=8, description="New password")


@router.put("/change-password")
async def change_password(
    request: ChangePasswordRequest,
    current_user_id: str = Depends(get_current_user),
    db: Session = Depends(get_database)
):
    """Change user password."""
    user_repo = UserRepository()
    
    user = user_repo.get_by_id(db, current_user_id)
    if not user:
        raise UnauthorizedError("User not found")
    
    # Verify old password
    if not verify_password(request.old_password, user.hashed_password):
        raise BadRequestError("Invalid current password")
    
    # Update password
    new_hashed_password = get_password_hash(request.new_password)
    user_repo.update(db, user.id, {"hashed_password": new_hashed_password})
    
    return {"message": "Password updated successfully"}