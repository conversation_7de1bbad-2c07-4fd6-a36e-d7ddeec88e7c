"""Shared dependencies for API endpoints."""

from typing import Generator, Optional, Callable
from fastapi import Depends, HTTPException, status, Header, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from redis import Redis
import logging

from src.core.config import settings, Settings
from src.core.security import decode_access_token
from src.api.errors import UnauthorizedError, RateLimitError
# Import actual database functions
from src.database.setup import get_db as get_database_session

# Import Redis infrastructure
from src.infrastructure.redis import RedisAdapter, RedisConnectionFactory

def get_db():
    """Get database session."""
    return get_database_session()

def get_redis():
    """Get Redis client instance."""
    try:
        return RedisConnectionFactory.get_sync_client()
    except Exception as e:
        logger.error(f"Failed to get Redis client: {e}")
        return None
from src.core.ai.analyzer import AIAnalyzerService
from src.core.ai.cache import AICache
from src.core.services.matching_engine import MatchingEngine
from src.core.ports.ai_port import AIPort
from src.adapters.ai.langchain_adapter import LangChainAIAdapter

logger = logging.getLogger(__name__)

# Security scheme
security = HTTPBearer(auto_error=False)


# Database dependencies
def get_database() -> Generator[Session, None, None]:
    """Get database session."""
    db = next(get_db())
    try:
        yield db
    finally:
        if db:
            db.close()


def get_redis_client() -> Generator[Redis, None, None]:
    """Get Redis client."""
    redis = get_redis()
    try:
        yield redis
    finally:
        pass  # Connection pooling handles cleanup


async def get_redis_adapter() -> RedisAdapter:
    """Get Redis adapter instance for cache operations."""
    return RedisAdapter(
        default_ttl=settings.redis_ttl,
        key_prefix="vc_platform"
    )


# Authentication dependencies
async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[str]:
    """Get current user if authenticated, None otherwise."""
    if not credentials:
        return None
    
    token_data = decode_access_token(credentials.credentials)
    if not token_data:
        return None
    
    return str(token_data.sub) if token_data.sub else None


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> str:
    """Get current authenticated user."""
    if not credentials:
        raise UnauthorizedError()
    
    token_data = decode_access_token(credentials.credentials)
    if not token_data or not token_data.sub:
        raise UnauthorizedError()
    
    return str(token_data.sub)


async def require_admin(
    current_user: str = Depends(get_current_user)
) -> str:
    """Require admin privileges."""
    # TODO: Implement proper role checking
    # For now, just return the user
    return current_user


# Service dependencies
def get_ai_analyzer(
    redis: Redis = Depends(get_redis_client)
) -> AIAnalyzerService:
    """Get AI analyzer service instance."""
    # Only create cache if Redis is available
    cache = AICache(redis_client=redis) if redis else None
    
    return AIAnalyzerService(
        openai_api_key=settings.openai_api_key,
        model_name=settings.openai_model,
        temperature=settings.openai_temperature,
        cache=cache,
        max_retries=settings.ai_max_retries,
        enable_streaming=False
    )


def get_matching_engine() -> MatchingEngine:
    """Get matching engine instance."""
    return MatchingEngine()


def get_ai_port() -> AIPort:
    """Get AI port instance using LangChain adapter."""
    return LangChainAIAdapter(
        openai_api_key=settings.openai_api_key,
        model_name=settings.openai_model,
        temperature=settings.openai_temperature,
        enable_caching=True
    )


# Rate limiting dependencies
class RateLimiter:
    """Simple rate limiter using Redis."""
    
    def __init__(self, redis: Redis, key_prefix: str = "rate_limit"):
        self.redis = redis
        self.key_prefix = key_prefix
    
    async def check_rate_limit(
        self,
        identifier: str,
        limit: int,
        window: int
    ) -> bool:
        """
        Check if rate limit is exceeded.
        
        Args:
            identifier: Unique identifier (e.g., user ID, IP)
            limit: Maximum number of requests
            window: Time window in seconds
            
        Returns:
            True if within limit, False if exceeded
        """
        # If Redis is not available, allow request
        if not self.redis:
            return True
            
        key = f"{self.key_prefix}:{identifier}:{window}"
        
        try:
            current = self.redis.incr(key)
            if current == 1:
                self.redis.expire(key, window)
            
            return current <= limit
        except Exception as e:
            logger.error(f"Rate limit check failed: {e}")
            # Fail open - allow request if Redis fails
            return True


def get_rate_limiter(
    redis: Redis = Depends(get_redis_client)
) -> RateLimiter:
    """Get rate limiter instance."""
    return RateLimiter(redis)


async def rate_limit_per_minute(
    request: Request,
    rate_limiter: RateLimiter = Depends(get_rate_limiter),
    current_user: Optional[str] = Depends(get_current_user_optional)
):
    """Apply per-minute rate limiting."""
    if not settings.rate_limit_enabled:
        return
    
    # Use user ID if authenticated, otherwise use IP
    identifier = current_user or request.client.host
    
    if not await rate_limiter.check_rate_limit(
        identifier=identifier,
        limit=settings.rate_limit_per_minute,
        window=60
    ):
        raise RateLimitError(
            f"Rate limit exceeded: {settings.rate_limit_per_minute} requests per minute",
            retry_after=60
        )


async def rate_limit_per_hour(
    request: Request,
    rate_limiter: RateLimiter = Depends(get_rate_limiter),
    current_user: Optional[str] = Depends(get_current_user_optional)
):
    """Apply per-hour rate limiting."""
    if not settings.rate_limit_enabled:
        return
    
    # Use user ID if authenticated, otherwise use IP
    identifier = current_user or request.client.host
    
    if not await rate_limiter.check_rate_limit(
        identifier=identifier,
        limit=settings.rate_limit_per_hour,
        window=3600
    ):
        raise RateLimitError(
            f"Rate limit exceeded: {settings.rate_limit_per_hour} requests per hour",
            retry_after=3600
        )


# Pagination dependencies
class PaginationParams:
    """Common pagination parameters."""
    
    def __init__(
        self,
        page: int = 1,
        size: int = 20
    ):
        if page < 1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Page must be greater than 0"
            )
        if size < 1 or size > 100:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Size must be between 1 and 100"
            )
        
        self.page = page
        self.size = size
        self.offset = (page - 1) * size


# API key dependencies (alternative to JWT)
async def get_api_key(
    x_api_key: Optional[str] = Header(None)
) -> Optional[str]:
    """Extract API key from headers."""
    return x_api_key


async def require_api_key(
    api_key: Optional[str] = Depends(get_api_key),
    db: Session = Depends(get_database)
) -> str:
    """Require valid API key."""
    if not api_key:
        raise UnauthorizedError("API key required")
    
    # Import here to avoid circular imports
    from src.database.repositories.api_key_repository import PostgresAPIKeyRepository
    
    # Validate API key against database
    repo = PostgresAPIKeyRepository(db)
    api_key_obj = repo.validate_and_track(api_key)
    
    if not api_key_obj:
        raise UnauthorizedError("Invalid API key")
    
    return api_key


# Settings dependency
def get_settings() -> Settings:
    """Get application settings."""
    return settings