"""
API endpoints for background task management.
"""
from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
from celery.result import AsyncResult
from src.workers import celery_app
from src.workers.tasks.ai_tasks import (
    analyze_startup_task,
    analyze_vc_task,
    batch_match_task,
    get_task_status
)
from src.workers.tasks.data_tasks import (
    scrape_vc_website_task,
    enrich_startup_data_task
)
from src.api.v1.deps import get_current_user
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


class TaskRequest(BaseModel):
    """Base request for task operations."""
    task_type: str
    entity_id: Optional[str] = None
    params: Optional[Dict[str, Any]] = None


class BatchMatchTaskRequest(BaseModel):
    """Request for batch matching task."""
    startup_ids: List[str]
    vc_ids: Optional[List[str]] = None
    use_ai: bool = True
    threshold: float = 0.7


class TaskResponse(BaseModel):
    """Response for task creation."""
    task_id: str
    status: str
    message: str


class TaskStatusResponse(BaseModel):
    """Response for task status check."""
    task_id: str
    state: str
    status: str
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    progress: Optional[Dict[str, Any]] = None


@router.post("/tasks/analyze/startup/{startup_id}", response_model=TaskResponse)
async def create_startup_analysis_task(
    startup_id: str,
    current_user: Dict = Depends(get_current_user)
) -> TaskResponse:
    """
    Create a background task to analyze a startup with AI.
    """
    try:
        task = analyze_startup_task.delay(startup_id)
        
        return TaskResponse(
            task_id=task.id,
            status="queued",
            message=f"Analysis task created for startup {startup_id}"
        )
    except Exception as e:
        logger.error(f"Error creating startup analysis task: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/analyze/vc/{vc_id}", response_model=TaskResponse)
async def create_vc_analysis_task(
    vc_id: str,
    current_user: Dict = Depends(get_current_user)
) -> TaskResponse:
    """
    Create a background task to analyze a VC with AI.
    """
    try:
        task = analyze_vc_task.delay(vc_id)
        
        return TaskResponse(
            task_id=task.id,
            status="queued",
            message=f"Analysis task created for VC {vc_id}"
        )
    except Exception as e:
        logger.error(f"Error creating VC analysis task: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/batch-match", response_model=TaskResponse)
async def create_batch_match_task(
    request: BatchMatchTaskRequest,
    current_user: Dict = Depends(get_current_user)
) -> TaskResponse:
    """
    Create a background task for batch matching.
    """
    try:
        task = batch_match_task.delay(
            startup_ids=request.startup_ids,
            vc_ids=request.vc_ids,
            use_ai=request.use_ai,
            threshold=request.threshold
        )
        
        return TaskResponse(
            task_id=task.id,
            status="queued",
            message=f"Batch match task created for {len(request.startup_ids)} startups"
        )
    except Exception as e:
        logger.error(f"Error creating batch match task: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/enrich/vc/{vc_id}", response_model=TaskResponse)
async def create_vc_enrichment_task(
    vc_id: str,
    current_user: Dict = Depends(get_current_user)
) -> TaskResponse:
    """
    Create a background task to scrape and enrich VC data.
    """
    try:
        task = scrape_vc_website_task.delay(vc_id)
        
        return TaskResponse(
            task_id=task.id,
            status="queued",
            message=f"Data enrichment task created for VC {vc_id}"
        )
    except Exception as e:
        logger.error(f"Error creating VC enrichment task: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/enrich/startup/{startup_id}", response_model=TaskResponse)
async def create_startup_enrichment_task(
    startup_id: str,
    sources: Optional[List[str]] = None,
    current_user: Dict = Depends(get_current_user)
) -> TaskResponse:
    """
    Create a background task to enrich startup data.
    """
    try:
        task = enrich_startup_data_task.delay(startup_id, sources or ['news'])
        
        return TaskResponse(
            task_id=task.id,
            status="queued",
            message=f"Data enrichment task created for startup {startup_id}"
        )
    except Exception as e:
        logger.error(f"Error creating startup enrichment task: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/{task_id}", response_model=TaskStatusResponse)
async def get_task_status_endpoint(
    task_id: str,
    current_user: Dict = Depends(get_current_user)
) -> TaskStatusResponse:
    """
    Get the status of a background task.
    """
    try:
        status = get_task_status(task_id)
        
        # Extract progress information if available
        progress = None
        if status['state'] == 'PROCESSING' and 'current' in status:
            progress = {
                'current': status.get('current'),
                'total': status.get('total'),
                'percentage': (status.get('current', 0) / status.get('total', 1)) * 100
            }
        
        return TaskStatusResponse(
            task_id=task_id,
            state=status['state'],
            status=status['status'],
            result=status.get('result'),
            error=status.get('error'),
            progress=progress
        )
    except Exception as e:
        logger.error(f"Error getting task status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/tasks/{task_id}")
async def cancel_task(
    task_id: str,
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, str]:
    """
    Cancel a running task.
    """
    try:
        task = AsyncResult(task_id, app=celery_app)
        task.revoke(terminate=True)
        
        return {
            "task_id": task_id,
            "status": "cancelled",
            "message": "Task cancellation requested"
        }
    except Exception as e:
        logger.error(f"Error cancelling task: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/queue/stats")
async def get_queue_statistics(
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get statistics about task queues.
    """
    try:
        # Get queue lengths
        inspect = celery_app.control.inspect()
        
        stats = {
            'active': len(inspect.active() or {}),
            'scheduled': len(inspect.scheduled() or {}),
            'reserved': len(inspect.reserved() or {}),
            'queues': {}
        }
        
        # Get queue-specific stats
        queue_names = ['default', 'ai_analysis', 'data_enrichment', 'notifications', 'scheduled']
        for queue in queue_names:
            # This would require additional Redis queries in production
            stats['queues'][queue] = {
                'length': 0,  # Placeholder
                'processing': 0  # Placeholder
            }
        
        return stats
    except Exception as e:
        logger.error(f"Error getting queue statistics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))