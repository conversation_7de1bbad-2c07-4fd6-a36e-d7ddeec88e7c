"""Async database setup and configuration for SQLAlchemy."""

from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import (
    create_async_engine,
    async_sessionmaker,
    AsyncSession,
    AsyncEngine
)
from sqlalchemy.pool import NullPool
import logging

from src.core.config import settings

logger = logging.getLogger(__name__)


# Create async engine
def create_async_db_engine() -> AsyncEngine:
    """Create async SQLAlchemy engine with proper configuration."""
    # Convert sync database URL to async
    # postgresql://... -> postgresql+asyncpg://...
    async_url = settings.database_url.replace(
        "postgresql://", "postgresql+asyncpg://"
    ).replace(
        "postgres://", "postgresql+asyncpg://"
    )
    
    engine_args = {
        "echo": settings.debug,
        "echo_pool": settings.debug,
        "pool_pre_ping": True,  # Verify connections before use
        "future": True,  # Use SQLAlchemy 2.0 style
    }
    
    # Use NullPool for testing to avoid connection issues
    if settings.testing:
        engine_args["poolclass"] = NullPool
    else:
        # Production connection pool settings
        engine_args.update({
            "pool_size": 20,
            "max_overflow": 10,
            "pool_timeout": 30,
            "pool_recycle": 3600,  # Recycle connections after 1 hour
        })
    
    engine = create_async_engine(async_url, **engine_args)
    
    logger.info(f"Created async database engine: {async_url.split('@')[1]}")
    return engine


# Create global async engine
async_engine = create_async_db_engine()

# Create async session factory
AsyncSessionLocal = async_sessionmaker(
    async_engine,
    class_=AsyncSession,
    expire_on_commit=False,  # Don't expire objects after commit
    autoflush=False,  # Don't auto-flush before queries
    autocommit=False  # Use transactions
)


async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency to get async database session.
    
    Usage in FastAPI:
        @router.post("/items")
        async def create_item(
            item: ItemCreate,
            db: AsyncSession = Depends(get_async_db)
        ):
            # Use db session here
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def init_async_db():
    """Initialize async database (create tables if needed)."""
    # Import models to ensure they're registered
    from src.database.models import Base
    
    async with async_engine.begin() as conn:
        # For development only - production should use migrations
        if settings.debug:
            # Create all tables
            await conn.run_sync(Base.metadata.create_all)
            logger.info("Created all database tables (async)")


async def close_async_db():
    """Close async database connections."""
    await async_engine.dispose()
    logger.info("Closed async database connections")


# Health check function
async def check_async_db_health() -> bool:
    """Check if async database is healthy."""
    try:
        async with async_engine.connect() as conn:
            result = await conn.execute("SELECT 1")
            await result.scalar()
        return True
    except Exception as e:
        logger.error(f"Async database health check failed: {e}")
        return False