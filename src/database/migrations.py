"""Database migration utilities using Alembic."""

import logging
import subprocess
import sys
from pathlib import Path
from typing import Optional

from alembic import command
from alembic.config import Config
from sqlalchemy import inspect, text
from sqlalchemy.orm import Session

from src.database.setup import get_engine

logger = logging.getLogger(__name__)


def get_alembic_config() -> Config:
    """Get Alembic configuration.
    
    Returns:
        Alembic Config object
    """
    # Find alembic.ini file
    root_dir = Path(__file__).parent.parent.parent
    alembic_ini = root_dir / "alembic.ini"
    
    if not alembic_ini.exists():
        raise FileNotFoundError(f"alembic.ini not found at {alembic_ini}")
    
    config = Config(str(alembic_ini))
    config.set_main_option("script_location", str(root_dir / "alembic"))
    
    return config


def is_database_initialized() -> bool:
    """Check if database has been initialized with migrations.
    
    Returns:
        True if alembic_version table exists, False otherwise
    """
    engine = get_engine()
    inspector = inspect(engine)
    
    return "alembic_version" in inspector.get_table_names()


def get_current_revision() -> Optional[str]:
    """Get current database revision.
    
    Returns:
        Current revision ID or None if not initialized
    """
    if not is_database_initialized():
        return None
    
    engine = get_engine()
    with engine.connect() as conn:
        result = conn.execute(text("SELECT version_num FROM alembic_version"))
        row = result.first()
        return row[0] if row else None


def init_db_with_migrations() -> None:
    """Initialize database using Alembic migrations.
    
    This replaces the dangerous create_all() approach with
    proper migration-based initialization.
    """
    logger.info("Initializing database with migrations")
    
    # Check if database is already initialized
    if is_database_initialized():
        current = get_current_revision()
        logger.info(f"Database already initialized at revision: {current}")
        
        # Run any pending migrations
        upgrade_database()
    else:
        logger.info("Fresh database detected, running all migrations")
        
        # Stamp with base revision to mark as initialized
        config = get_alembic_config()
        command.stamp(config, "base")
        
        # Run all migrations
        upgrade_database()


def upgrade_database(revision: str = "head") -> None:
    """Upgrade database to specified revision.
    
    Args:
        revision: Target revision (default: "head" for latest)
    """
    logger.info(f"Upgrading database to revision: {revision}")
    
    config = get_alembic_config()
    command.upgrade(config, revision)
    
    logger.info("Database upgrade completed")


def downgrade_database(revision: str) -> None:
    """Downgrade database to specified revision.
    
    Args:
        revision: Target revision
    """
    logger.info(f"Downgrading database to revision: {revision}")
    
    config = get_alembic_config()
    command.downgrade(config, revision)
    
    logger.info("Database downgrade completed")


def create_migration(message: str, autogenerate: bool = True) -> None:
    """Create a new migration.
    
    Args:
        message: Migration message
        autogenerate: Whether to autogenerate based on model changes
    """
    logger.info(f"Creating migration: {message}")
    
    config = get_alembic_config()
    
    if autogenerate:
        command.revision(config, message=message, autogenerate=True)
    else:
        command.revision(config, message=message)
    
    logger.info("Migration created successfully")


def show_migration_history() -> None:
    """Show migration history."""
    config = get_alembic_config()
    command.history(config)


def get_pending_migrations() -> list[str]:
    """Get list of pending migrations.
    
    Returns:
        List of pending migration revision IDs
    """
    # Use subprocess to capture alembic output
    result = subprocess.run(
        [sys.executable, "-m", "alembic", "history", "-r", "current:head"],
        capture_output=True,
        text=True
    )
    
    if result.returncode != 0:
        logger.error(f"Failed to get migration history: {result.stderr}")
        return []
    
    # Parse output to get revision IDs
    pending = []
    for line in result.stdout.split('\n'):
        if '->' in line and '(head)' in line:
            # Extract revision ID from line like "abc123 -> def456 (head), message"
            parts = line.split()
            if len(parts) >= 3:
                pending.append(parts[2])
    
    return pending


def ensure_migrations_current() -> bool:
    """Ensure all migrations have been applied.
    
    Returns:
        True if all migrations applied, False otherwise
    """
    pending = get_pending_migrations()
    
    if pending:
        logger.warning(f"Found {len(pending)} pending migrations: {pending}")
        return False
    
    logger.info("All migrations are current")
    return True


# For backward compatibility with existing code
def init_db(engine=None) -> None:
    """Initialize database (using migrations).
    
    This maintains the same interface as the old init_db
    but uses Alembic migrations instead of create_all().
    
    Args:
        engine: SQLAlchemy engine (ignored, kept for compatibility)
    """
    init_db_with_migrations()


async def async_init_db(engine=None) -> None:
    """Initialize database asynchronously.
    
    Since Alembic doesn't support async operations directly,
    this just calls the sync version.
    
    Args:
        engine: Async engine (ignored, kept for compatibility)
    """
    init_db_with_migrations()