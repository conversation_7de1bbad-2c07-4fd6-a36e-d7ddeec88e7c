"""SQLAlchemy API Key model."""

from datetime import datetime
from sqlalchemy import Column, DateTime, String, Boolean, Foreign<PERSON>ey, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
import uuid

from src.database.setup import Base


class APIKey(Base):
    """API Key database model for authentication."""
    
    __tablename__ = "api_keys"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    key = Column(String(255), unique=True, nullable=False, index=True)
    name = Column(String(255), nullable=False)
    description = Column(String(500))
    
    # Link to user who created the key
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Permissions and status
    is_active = Column(Boolean, default=True)
    scopes = Column(String(1000))  # Comma-separated list of scopes
    
    # Usage tracking
    last_used_at = Column(DateTime)
    usage_count = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    expires_at = Column(DateTime)  # Optional expiration
    revoked_at = Column(DateTime)  # When key was revoked
    
    # Relationships
    user = relationship("User", backref="api_keys")
    
    def is_valid(self) -> bool:
        """Check if API key is valid."""
        if not self.is_active:
            return False
        if self.revoked_at:
            return False
        if self.expires_at and self.expires_at < datetime.utcnow():
            return False
        return True
    
    def has_scope(self, scope: str) -> bool:
        """Check if API key has a specific scope."""
        if not self.scopes:
            return True  # No scopes means full access
        scopes_list = [s.strip() for s in self.scopes.split(",")]
        return scope in scopes_list or "*" in scopes_list