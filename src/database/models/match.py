"""SQLAlchemy Match model."""

from datetime import datetime
from sqlalchemy import Column, DateTime, Float, ForeignKey, String, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from src.database.setup import Base


class Match(Base):
    """Match database model."""
    
    __tablename__ = "matches"
    
    id = Column(UUID(as_uuid=True), primary_key=True)
    startup_id = Column(UUID(as_uuid=True), ForeignKey("startups.id"), nullable=False)
    vc_id = Column(UUID(as_uuid=True), ForeignKey("vcs.id"), nullable=False)
    score = Column(Float, nullable=False)
    
    # Additional fields
    reasons = Column(JSON)  # List of matching reasons
    status = Column(String(50), default="pending")
    match_type = Column(String(50), nullable=True)
    notes = Column(String, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    startup = relationship("Startup", back_populates="matches")
    vc = relationship("VC", back_populates="matches")