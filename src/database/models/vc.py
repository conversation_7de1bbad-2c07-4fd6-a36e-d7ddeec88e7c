"""SQLAlchemy VC model."""

from datetime import datetime
from sqlalchemy import Column, DateTime, Float, String, Text, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from src.database.setup import Base


class VC(Base):
    """VC database model."""
    
    __tablename__ = "vcs"
    
    id = Column(UUID(as_uuid=True), primary_key=True)
    firm_name = Column(String(255), nullable=False)
    website = Column(String(255))
    thesis = Column(Text)
    check_size_min = Column(Float)
    check_size_max = Column(Float)
    
    # JSON fields for flexible data
    sectors = Column(JSON)  # List of sectors
    stages = Column(JSON)   # List of stages
    portfolio_companies = Column(JSON)  # List of portfolio company names
    partners = Column(JSON)  # List of partner names
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    matches = relationship("Match", back_populates="vc", cascade="all, delete-orphan")