"""SQLAlchemy User model for authentication."""

from datetime import datetime
from sqlalchemy import Column, DateTime, String, Boolean, JSON
from sqlalchemy.dialects.postgresql import UUID
import uuid

from src.database.setup import Base


class User(Base):
    """User database model for authentication."""
    
    __tablename__ = "users"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String(255), unique=True, nullable=False, index=True)
    username = Column(String(100), unique=True, nullable=False, index=True)
    hashed_password = Column(String(255), nullable=False)
    
    # User profile
    full_name = Column(String(255))
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    
    # Role-based access control
    roles = Column(JSON, default=list)  # List of roles: ["admin", "user", "investor"]
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    last_login = Column(DateTime)
    
    # Additional metadata
    metadata_field = Column("metadata", JSON)
    
    def has_role(self, role: str) -> bool:
        """Check if user has a specific role."""
        return role in (self.roles or [])
    
    def add_role(self, role: str):
        """Add a role to the user."""
        if self.roles is None:
            self.roles = []
        if role not in self.roles:
            self.roles = self.roles + [role]  # Create new list for SQLAlchemy change detection
    
    def remove_role(self, role: str):
        """Remove a role from the user."""
        if self.roles and role in self.roles:
            self.roles = [r for r in self.roles if r != role]