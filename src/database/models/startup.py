"""SQLAlchemy Startup model."""

from datetime import datetime
from sqlalchemy import Column, DateTime, Float, Integer, String, Text, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from src.database.setup import Base


class Startup(Base):
    """Startup database model."""
    
    __tablename__ = "startups"
    
    id = Column(UUID(as_uuid=True), primary_key=True)
    name = Column(String(255), nullable=False)
    sector = Column(String(100), nullable=False)
    stage = Column(String(50), nullable=False)
    description = Column(Text)
    website = Column(String(255))
    team_size = Column(Integer, default=0)
    monthly_revenue = Column(Float, default=0)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Flexible metadata field for additional data
    meta_data = Column("metadata", JSON)
    
    # Relationships
    matches = relationship("Match", back_populates="startup", cascade="all, delete-orphan")