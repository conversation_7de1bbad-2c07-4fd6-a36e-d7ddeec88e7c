"""Database models for connections and introduction requests."""

from datetime import datetime
from sqlalchemy import Column, DateTime, String, Boolean, JSON, Float, Integer, Text, Index
from sqlalchemy.dialects.postgresql import UUID, ENUM
import uuid

from src.database.models import Base


class Connection(Base):
    """Database model for user connections."""
    
    __tablename__ = "connections"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_a_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    user_b_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    relationship_type = Column(ENUM(
        'colleague', 'business_partner', 'mentor_mentee', 
        'investor_founder', 'industry_peer', 'referral', 'social',
        name='relationship_type_enum'
    ), nullable=False)
    
    strength = Column(ENUM(
        'strong', 'medium', 'weak',
        name='connection_strength_enum'
    ), nullable=False)
    
    # Metrics stored as JSON for flexibility
    metrics = Column(JSON, default={})
    
    notes = Column(Text)
    tags = Column(JSON, default=list)
    
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    is_active = Column(Boolean, default=True)
    
    # Indexes for efficient path finding
    __table_args__ = (
        Index('idx_connections_users', 'user_a_id', 'user_b_id'),
        Index('idx_connections_strength', 'strength', 'is_active'),
        Index('idx_connections_relationship', 'relationship_type', 'is_active'),
        Index('idx_connections_active_users', 'user_a_id', 'user_b_id', 'is_active'),
    )


class IntroductionRequest(Base):
    """Database model for introduction requests."""
    
    __tablename__ = "introduction_requests"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    requester_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    target_id = Column(UUID(as_uuid=True), nullable=False)
    connector_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    status = Column(ENUM(
        'pending', 'accepted', 'declined', 'completed', 'expired',
        name='introduction_status_enum'
    ), nullable=False, default='pending')
    
    message = Column(Text, nullable=False)
    connector_notes = Column(Text)
    
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    expires_at = Column(DateTime)
    completed_at = Column(DateTime)
    
    __table_args__ = (
        Index('idx_intro_requests_status', 'status', 'connector_id'),
        Index('idx_intro_requests_expiry', 'expires_at', 'status'),
        Index('idx_intro_requests_users', 'requester_id', 'target_id', 'connector_id'),
    )