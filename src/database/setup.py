"""Database setup and configuration."""

from typing import As<PERSON><PERSON>enerator, Generator

from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.orm import Session, sessionmaker

from src.core.config import settings
from src.database.base import Base, LegacyBase


# Use async Base for new models, LegacyBase for backward compatibility
# Base is imported from base.py which includes AsyncAttrs

# Global engine instances (created lazily)
_engine = None
_async_engine = None
_SessionLocal = None
_AsyncSessionLocal = None


def get_engine():
    """Get or create sync engine."""
    global _engine
    if _engine is None:
        _engine = create_engine(
            settings.database_url,
            pool_pre_ping=True,
            pool_size=settings.database_pool_size,
            max_overflow=settings.database_max_overflow,
            echo=settings.database_echo,
        )
    return _engine


def get_async_engine():
    """Get or create async engine."""
    global _async_engine
    if _async_engine is None:
        _async_engine = create_async_engine(
            settings.async_database_url,
            pool_pre_ping=True,
            pool_size=settings.database_pool_size,
            max_overflow=settings.database_max_overflow,
            echo=settings.database_echo,
        )
    return _async_engine


def get_session_factory():
    """Get or create sync session factory."""
    global _SessionLocal
    if _SessionLocal is None:
        _SessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=get_engine(),
        )
    return _SessionLocal


def get_async_session_factory():
    """Get or create async session factory."""
    global _AsyncSessionLocal
    if _AsyncSessionLocal is None:
        _AsyncSessionLocal = async_sessionmaker(
            get_async_engine(),
            class_=AsyncSession,
            autocommit=False,
            autoflush=False,
            expire_on_commit=False,
        )
    return _AsyncSessionLocal


# For backward compatibility
@property
def engine():
    return get_engine()


@property
def async_engine():
    return get_async_engine()


@property
def SessionLocal():
    return get_session_factory()


@property  
def AsyncSessionLocal():
    return get_async_session_factory()


def get_db() -> Generator[Session, None, None]:
    """Get database session dependency."""
    SessionLocal = get_session_factory()
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """Get async database session dependency."""
    AsyncSessionLocal = get_async_session_factory()
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()


def init_db(engine=None) -> None:
    """Initialize database with all tables using migrations.
    
    DEPRECATED: This method now uses Alembic migrations instead of create_all().
    Direct use of create_all() is dangerous in production as it can cause
    data loss or schema conflicts.
    
    Args:
        engine: SQLAlchemy engine (optional, kept for backward compatibility)
    """
    from src.database.migrations import init_db_with_migrations
    
    # Import all models to ensure they're registered with Base
    from src.database.models import startup, vc, match, user  # noqa
    
    # Use migration-based initialization
    init_db_with_migrations()


async def async_init_db(engine=None) -> None:
    """Initialize database with all tables asynchronously.
    
    DEPRECATED: This method now uses Alembic migrations.
    Since Alembic doesn't support async operations directly,
    this calls the sync version.
    
    Args:
        engine: Async engine (optional, kept for backward compatibility)
    """
    from src.database.migrations import init_db_with_migrations
    
    # Import all models to ensure they're registered with Base
    from src.database.models import startup, vc, match, user  # noqa
    
    # Use migration-based initialization
    init_db_with_migrations()


def get_db_context():
    """Get database context manager for worker tasks.
    
    This provides a context manager that automatically handles
    session creation and cleanup for background tasks.
    
    Returns:
        Generator yielding a database session
    """
    return get_db()


def get_session():
    """Get a new database session for Celery tasks.
    
    This returns a new session instance that must be
    manually closed when done.
    
    Returns:
        Session: A new SQLAlchemy session
    """
    SessionLocal = get_session_factory()
    return SessionLocal()