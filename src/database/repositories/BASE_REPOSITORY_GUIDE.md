# Base Repository Pattern Guide

## Overview

The `BaseRepository` class provides a generic implementation of common CRUD operations to reduce code duplication across repository implementations. All repository classes should inherit from this base class.

## Benefits

1. **DRY Principle**: Eliminates duplicate CRUD code across repositories
2. **Consistency**: Ensures all repositories have the same interface
3. **Type Safety**: Uses Python generics for type checking
4. **Error Handling**: Centralized error handling for database operations
5. **Extensibility**: Easy to add new common operations

## Implementation Guide

### 1. Define Your Repository Class

```python
from src.database.repositories.base_repository import BaseRepository
from src.database.models import YourModel as YourModelDB
from src.core.models.your_model import YourModel as YourModelDomain

class YourRepository(BaseRepository[YourModelDB, YourModelDomain], IYourRepository):
    """Your repository implementation."""
    
    model_class = YourModelDB  # Required: Set the database model class
    
    def __init__(self, session: Session):
        super().__init__(session)
```

### 2. Implement Required Methods

You must implement these two methods to handle model conversion:

```python
def _to_domain(self, db_model: YourModelDB) -> YourModelDomain:
    """Convert database model to domain model."""
    return YourModelDomain(
        id=db_model.id,
        name=db_model.name,
        # ... map all fields
    )

def _to_db(self, domain_model: YourModelDomain, existing: Optional[YourModelDB] = None) -> YourModelDB:
    """Convert domain model to database model."""
    if existing:
        # Update existing model
        existing.name = domain_model.name
        # ... update all fields
        return existing
    else:
        # Create new model
        return YourModelDB(
            id=domain_model.id,
            name=domain_model.name,
            # ... set all fields
        )
```

### 3. Available Base Methods

The base repository provides these methods out of the box:

#### Basic CRUD Operations
- `create(entity)` - Create a new entity
- `get(entity_id)` - Get by UUID
- `get_by_id(entity_id)` - Get by string ID
- `update(entity)` - Update existing entity
- `save(entity)` - Create or update based on ID existence
- `delete(entity_id)` - Delete by ID
- `exists(entity_id)` - Check if entity exists

#### Query Operations
- `list(limit, offset)` - List with pagination
- `count(filters)` - Count entities with optional filters
- `find_by_field(field_name, value)` - Find by single field
- `find_one_by_field(field_name, value)` - Find one by field
- `search(filters, limit, offset)` - Search with multiple filters

#### Bulk Operations
- `bulk_create(entities)` - Create multiple entities in one transaction

### 4. Adding Custom Methods

You can add repository-specific methods while still using the base functionality:

```python
class StartupRepository(BaseRepository[StartupDB, StartupDomain]):
    
    def search_by_name(self, name: str) -> List[StartupDomain]:
        """Custom search by name with partial matching."""
        db_entities = self.session.query(self.model_class).filter(
            self.model_class.name.ilike(f"%{name}%")
        ).all()
        return [self._to_domain(entity) for entity in db_entities]
    
    def find_by_sectors(self, sectors: List[str]) -> List[StartupDomain]:
        """Custom method for array field searching."""
        db_entities = self.session.query(self.model_class).filter(
            self.model_class.sectors.overlap(sectors)
        ).all()
        return [self._to_domain(entity) for entity in db_entities]
```

## Migration Guide

To migrate an existing repository to use the base class:

1. **Backup your existing repository** (rename to `_old.py`)

2. **Create new repository** inheriting from `BaseRepository`

3. **Set the model_class attribute**

4. **Implement `_to_domain` and `_to_db` methods**

5. **Remove redundant CRUD methods** that are now handled by base class

6. **Keep custom business logic methods**

7. **Update imports** in services and tests

## Example: Complete Repository

```python
from typing import List, Optional
from uuid import UUID
from sqlalchemy.orm import Session

from src.database.repositories.base_repository import BaseRepository
from src.database.models import Startup as StartupDB
from src.core.models.startup import Startup as StartupDomain
from src.core.repositories.startup_repository import IStartupRepository


class StartupRepository(BaseRepository[StartupDB, StartupDomain], IStartupRepository):
    """PostgreSQL implementation of startup repository."""
    
    model_class = StartupDB
    
    def __init__(self, session: Session):
        super().__init__(session)
    
    def _to_domain(self, db_model: StartupDB) -> StartupDomain:
        return StartupDomain(
            id=db_model.id,
            name=db_model.name,
            sector=db_model.sector,
            # ... other fields
        )
    
    def _to_db(self, domain_model: StartupDomain, existing: Optional[StartupDB] = None) -> StartupDB:
        if existing:
            existing.name = domain_model.name
            existing.sector = domain_model.sector
            # ... update other fields
            return existing
        else:
            return StartupDB(
                id=domain_model.id,
                name=domain_model.name,
                sector=domain_model.sector,
                # ... other fields
            )
    
    # Interface implementation (if needed for async compatibility)
    async def save(self, startup: StartupDomain) -> StartupDomain:
        return super().save(startup)
    
    async def find_by_id(self, startup_id: UUID) -> Optional[StartupDomain]:
        return super().get(startup_id)
    
    # Custom methods
    def find_by_sector(self, sector: str) -> List[StartupDomain]:
        return self.find_by_field('sector', sector)
```

## Best Practices

1. **Always use transactions**: The base repository handles transactions, don't add nested transactions

2. **Type hints**: Always specify the generic types when inheriting

3. **Error handling**: Let the base repository handle SQLAlchemy errors

4. **Field mapping**: Ensure all fields are mapped in both conversion methods

5. **Custom queries**: For complex queries, add custom methods rather than modifying base methods

6. **Testing**: Test both base functionality and custom methods

## Common Pitfalls

1. **Forgetting to set model_class**: This will cause AttributeError

2. **Incomplete field mapping**: Missing fields in `_to_domain` or `_to_db`

3. **Not handling nullable fields**: Check for None values in conversions

4. **Array field queries**: Use PostgreSQL-specific operators for array fields

5. **Async/sync mismatch**: Ensure interface methods match expected signatures