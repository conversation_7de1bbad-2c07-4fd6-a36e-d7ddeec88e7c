"""PostgreSQL implementation of VCRepository."""

from typing import List, Optional, Dict, Any
from uuid import UUID

from sqlalchemy import select, and_, or_, func, text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from src.core.models.vc import VC as VCDomain
from src.core.repositories.vc_repository import VCRepository
from src.database.models.vc import VC as VCDB


class PostgresVCRepository(VCRepository):
    """PostgreSQL implementation of VC repository."""
    
    def __init__(self, session: Session):
        """Initialize with database session."""
        self.session = session
    
    def create(self, vc: VCDomain) -> VCDomain:
        """Create a new VC."""
        # Convert domain model to DB model
        db_vc = VCDB(
            id=vc.id,
            firm_name=vc.firm_name,
            website=vc.website,
            thesis=vc.thesis,
            check_size_min=vc.check_size_min,
            check_size_max=vc.check_size_max,
            sectors=vc.sectors,
            stages=vc.stages,
            portfolio_companies=getattr(vc, 'portfolio_companies', []),
            partners=getattr(vc, 'partners', [])
        )
        
        self.session.add(db_vc)
        self.session.commit()
        self.session.refresh(db_vc)
        
        # Convert back to domain model
        return self._to_domain(db_vc)
    
    def get(self, vc_id: UUID) -> Optional[VCDomain]:
        """Get a VC by ID."""
        db_vc = self.session.query(VCDB).filter_by(id=vc_id).first()
        
        if db_vc is None:
            return None
            
        return self._to_domain(db_vc)
    
    def list(
        self, 
        limit: int = 100,
        offset: int = 0
    ) -> List[VCDomain]:
        """List VCs."""
        db_vcs = self.session.query(VCDB).offset(offset).limit(limit).all()
        
        return [self._to_domain(vc) for vc in db_vcs]
    
    def update(self, vc_id: UUID, vc: VCDomain) -> Optional[VCDomain]:
        """Update a VC."""
        db_vc = self.session.query(VCDB).filter_by(id=vc_id).first()
        
        if db_vc is None:
            return None
        
        # Update fields
        db_vc.firm_name = vc.firm_name
        db_vc.website = vc.website
        db_vc.thesis = vc.thesis
        db_vc.check_size_min = vc.check_size_min
        db_vc.check_size_max = vc.check_size_max
        db_vc.sectors = vc.sectors
        db_vc.stages = vc.stages
        db_vc.portfolio_companies = getattr(vc, 'portfolio_companies', [])
        db_vc.partners = getattr(vc, 'partners', [])
        
        self.session.commit()
        self.session.refresh(db_vc)
        
        return self._to_domain(db_vc)
    
    def delete(self, vc_id: UUID) -> bool:
        """Delete a VC."""
        db_vc = self.session.query(VCDB).filter_by(id=vc_id).first()
        
        if db_vc is None:
            return False
            
        self.session.delete(db_vc)
        self.session.commit()
        
        return True
    
    async def save(self, vc: VCDomain) -> VCDomain:
        """Save a VC (create or update based on whether it has an ID)."""
        if vc.id and self.get(vc.id):
            # Update existing
            return self.update(vc.id, vc)
        else:
            # Create new
            return self.create(vc)
    
    def search(self, query: str) -> List[VCDomain]:
        """Search VCs by name or thesis."""
        search_pattern = f"%{query}%"
        
        db_vcs = self.session.query(VCDB).filter(
            (VCDB.firm_name.ilike(search_pattern)) |
            (VCDB.thesis.ilike(search_pattern))
        ).limit(50).all()
        
        return [self._to_domain(vc) for vc in db_vcs]
    
    def search_by_sector_focus(self, sector: str) -> List[VCDomain]:
        """Search VCs by sector focus."""
        # Check database type
        dialect_name = self.session.bind.dialect.name
        
        if dialect_name == 'postgresql':
            # PostgreSQL JSON contains operator
            db_vcs = self.session.query(VCDB).filter(
                VCDB.sectors.op('@>')([sector])
            ).all()
        else:
            # SQLite fallback - fetch all and filter in Python
            all_vcs = self.session.query(VCDB).all()
            db_vcs = [vc for vc in all_vcs if vc.sectors and sector in vc.sectors]
        
        return [self._to_domain(vc) for vc in db_vcs]
    
    def search_by_stage_focus(self, stage: str) -> List[VCDomain]:
        """Search VCs by stage focus."""
        # Check database type
        dialect_name = self.session.bind.dialect.name
        
        if dialect_name == 'postgresql':
            # PostgreSQL JSON contains operator
            db_vcs = self.session.query(VCDB).filter(
                VCDB.stages.op('@>')([stage])
            ).all()
        else:
            # SQLite fallback - fetch all and filter in Python
            all_vcs = self.session.query(VCDB).all()
            db_vcs = [vc for vc in all_vcs if vc.stages and stage in vc.stages]
        
        return [self._to_domain(vc) for vc in db_vcs]
    
    def search_by_check_size_range(self, amount: float) -> List[VCDomain]:
        """Search VCs that could write a check of given size."""
        db_vcs = self.session.query(VCDB).filter(
            (VCDB.check_size_min <= amount) & 
            (VCDB.check_size_max >= amount)
        ).all()
        
        return [self._to_domain(vc) for vc in db_vcs]
    
    def get_active(self) -> List[VCDomain]:
        """Get active VCs (those with thesis and check sizes defined)."""
        db_vcs = self.session.query(VCDB).filter(
            VCDB.thesis.isnot(None),
            VCDB.check_size_min.isnot(None),
            VCDB.check_size_max.isnot(None)
        ).all()
        
        return [self._to_domain(vc) for vc in db_vcs]
    
    async def find_all(self) -> List[VCDomain]:
        """Get all VCs."""
        return self.list(limit=1000)
    
    async def find_by_sector(self, sector: str) -> List[VCDomain]:
        """Find VCs by sector."""
        db_vcs = self.session.query(VCDB).filter(
            VCDB.sectors.contains([sector])
        ).all()
        return [self._to_domain(vc) for vc in db_vcs]
    
    async def find_by_stage(self, stage: str) -> List[VCDomain]:
        """Find VCs by stage."""
        db_vcs = self.session.query(VCDB).filter(
            VCDB.stages.contains([stage])
        ).all()
        return [self._to_domain(vc) for vc in db_vcs]
    
    async def find_by_id(self, vc_id: UUID) -> Optional[VCDomain]:
        """Find a VC by ID."""
        return self.get(vc_id)
    
    def _to_domain(self, db_vc: VCDB) -> VCDomain:
        """Convert database model to domain model."""
        domain_vc = VCDomain(
            firm_name=db_vc.firm_name,
            website=db_vc.website or "",
            thesis=db_vc.thesis or "",
            check_size_min=db_vc.check_size_min or 0,
            check_size_max=db_vc.check_size_max or 0,
            sectors=db_vc.sectors or [],
            stages=db_vc.stages or [],
            portfolio_companies=db_vc.portfolio_companies or [],
            partners=db_vc.partners or [],
            id=db_vc.id
        )
        
        return domain_vc
    
    def find_for_startup_discovery(
        self,
        startup_sector: str,
        startup_stage: str,
        funding_amount: float,
        description_keywords: List[str] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[VCDomain]:
        """Optimized query to find VCs matching startup criteria using database filtering."""
        query = self.session.query(VCDB)
        
        # Use GIN indexes for JSON array containment queries
        if startup_sector:
            # PostgreSQL JSON containment operator with GIN index
            query = query.filter(VCDB.sectors.op('@>')([startup_sector]))
        
        if startup_stage:
            # Check for exact stage match or adjacent stages
            adjacent_stages = self._get_adjacent_stages(startup_stage)
            all_stages = [startup_stage] + adjacent_stages
            query = query.filter(VCDB.stages.op('&&')(all_stages))  # Overlap operator
        
        # Check size range using composite index
        if funding_amount > 0:
            query = query.filter(
                and_(
                    VCDB.check_size_min <= funding_amount,
                    VCDB.check_size_max >= funding_amount
                )
            )
        
        # Full-text search on thesis for startup description alignment
        if description_keywords:
            # Use the full-text search index we created
            search_query = ' | '.join(description_keywords)  # OR search
            query = query.filter(
                text("to_tsvector('english', COALESCE(thesis, '')) @@ plainto_tsquery('english', :search)")
            ).params(search=search_query)
        
        # Only active VCs (use partial index)
        query = query.filter(
            and_(
                VCDB.check_size_min.isnot(None),
                VCDB.check_size_max.isnot(None),
                VCDB.thesis.isnot(None)
            )
        )
        
        # Order by check size descending for consistent results
        query = query.order_by(VCDB.check_size_max.desc(), VCDB.firm_name)
        
        # Apply pagination
        db_vcs = query.offset(offset).limit(limit).all()
        
        return [self._to_domain(vc) for vc in db_vcs]
    
    def count_for_startup_discovery(
        self,
        startup_sector: str,
        startup_stage: str,
        funding_amount: float,
        description_keywords: List[str] = None
    ) -> int:
        """Count VCs matching startup criteria for pagination."""
        query = self.session.query(func.count(VCDB.id))
        
        # Apply same filters as find_for_startup_discovery
        if startup_sector:
            query = query.filter(VCDB.sectors.op('@>')([startup_sector]))
        
        if startup_stage:
            adjacent_stages = self._get_adjacent_stages(startup_stage)
            all_stages = [startup_stage] + adjacent_stages
            query = query.filter(VCDB.stages.op('&&')(all_stages))
        
        if funding_amount > 0:
            query = query.filter(
                and_(
                    VCDB.check_size_min <= funding_amount,
                    VCDB.check_size_max >= funding_amount
                )
            )
        
        if description_keywords:
            search_query = ' | '.join(description_keywords)
            query = query.filter(
                text("to_tsvector('english', COALESCE(thesis, '')) @@ plainto_tsquery('english', :search)")
            ).params(search=search_query)
        
        query = query.filter(
            and_(
                VCDB.check_size_min.isnot(None),
                VCDB.check_size_max.isnot(None),
                VCDB.thesis.isnot(None)
            )
        )
        
        return query.scalar() or 0
    
    def _get_adjacent_stages(self, stage: str) -> List[str]:
        """Get adjacent funding stages for expanded matching."""
        stage_order = ["Pre-seed", "Seed", "Series A", "Series B", "Series C", "Growth"]
        
        if stage not in stage_order:
            return []
        
        stage_index = stage_order.index(stage)
        adjacent = []
        
        # Add previous stage
        if stage_index > 0:
            adjacent.append(stage_order[stage_index - 1])
        
        # Add next stage
        if stage_index < len(stage_order) - 1:
            adjacent.append(stage_order[stage_index + 1])
        
        return adjacent
    
    def get_sectors_with_counts(self) -> Dict[str, int]:
        """Get all sectors with their VC counts for discovery filters."""
        # Use raw SQL for efficient aggregation with GIN index
        result = self.session.execute(text("""
            SELECT sector, COUNT(*) as vc_count
            FROM (
                SELECT jsonb_array_elements_text(sectors) as sector
                FROM vcs 
                WHERE sectors IS NOT NULL 
                AND check_size_min IS NOT NULL 
                AND check_size_max IS NOT NULL
            ) sector_expansion
            GROUP BY sector
            ORDER BY vc_count DESC
        """))
        
        return {row[0]: row[1] for row in result}
    
    def get_stages_with_counts(self) -> Dict[str, int]:
        """Get all stages with their VC counts for discovery filters."""
        # Use raw SQL for efficient aggregation with GIN index
        result = self.session.execute(text("""
            SELECT stage, COUNT(*) as vc_count
            FROM (
                SELECT jsonb_array_elements_text(stages) as stage
                FROM vcs 
                WHERE stages IS NOT NULL 
                AND check_size_min IS NOT NULL 
                AND check_size_max IS NOT NULL
            ) stage_expansion
            GROUP BY stage
            ORDER BY vc_count DESC
        """))
        
        return {row[0]: row[1] for row in result}


class AsyncPostgresVCRepository(VCRepository):
    """Async PostgreSQL implementation of VC repository."""
    
    def __init__(self, session: AsyncSession):
        """Initialize with async database session."""
        self.session = session
    
    async def create(self, vc: VCDomain) -> VCDomain:
        """Create a new VC."""
        db_vc = VCDB(
            id=vc.id,
            firm_name=vc.firm_name,
            website=vc.website,
            thesis=vc.thesis,
            check_size_min=vc.check_size_min,
            check_size_max=vc.check_size_max,
            sectors=vc.sectors,
            stages=vc.stages
        )
        
        self.session.add(db_vc)
        await self.session.commit()
        await self.session.refresh(db_vc)
        
        return self._to_domain(db_vc)
    
    async def get(self, vc_id: UUID) -> Optional[VCDomain]:
        """Get a VC by ID."""
        result = await self.session.execute(
            select(VCDB).filter_by(id=vc_id)
        )
        db_vc = result.scalar_one_or_none()
        
        if db_vc is None:
            return None
            
        return self._to_domain(db_vc)
    
    async def list(
        self, 
        limit: int = 100,
        offset: int = 0
    ) -> List[VCDomain]:
        """List VCs."""
        query = select(VCDB).offset(offset).limit(limit)
        
        result = await self.session.execute(query)
        db_vcs = result.scalars().all()
        
        return [self._to_domain(vc) for vc in db_vcs]
    
    async def update(self, vc_id: UUID, vc: VCDomain) -> Optional[VCDomain]:
        """Update a VC."""
        result = await self.session.execute(
            select(VCDB).filter_by(id=vc_id)
        )
        db_vc = result.scalar_one_or_none()
        
        if db_vc is None:
            return None
        
        # Update fields
        db_vc.firm_name = vc.firm_name
        db_vc.website = vc.website
        db_vc.thesis = vc.thesis
        db_vc.check_size_min = vc.check_size_min
        db_vc.check_size_max = vc.check_size_max
        db_vc.sectors = vc.sectors
        db_vc.stages = vc.stages
        
        await self.session.commit()
        await self.session.refresh(db_vc)
        
        return self._to_domain(db_vc)
    
    async def delete(self, vc_id: UUID) -> bool:
        """Delete a VC."""
        result = await self.session.execute(
            select(VCDB).filter_by(id=vc_id)
        )
        db_vc = result.scalar_one_or_none()
        
        if db_vc is None:
            return False
            
        await self.session.delete(db_vc)
        await self.session.commit()
        
        return True
    
    async def search(self, query: str) -> List[VCDomain]:
        """Search VCs by name or thesis."""
        search_pattern = f"%{query}%"
        
        result = await self.session.execute(
            select(VCDB).filter(
                (VCDB.firm_name.ilike(search_pattern)) |
                (VCDB.thesis.ilike(search_pattern))
            ).limit(50)
        )
        db_vcs = result.scalars().all()
        
        return [self._to_domain(vc) for vc in db_vcs]
    
    def _to_domain(self, db_vc: VCDB) -> VCDomain:
        """Convert database model to domain model."""
        domain_vc = VCDomain(
            firm_name=db_vc.firm_name,
            website=db_vc.website or "",
            thesis=db_vc.thesis or "",
            check_size_min=db_vc.check_size_min or 0,
            check_size_max=db_vc.check_size_max or 0,
            sectors=db_vc.sectors or [],
            stages=db_vc.stages or [],
            portfolio_companies=db_vc.portfolio_companies or [],
            partners=db_vc.partners or [],
            id=db_vc.id
        )
        
        return domain_vc