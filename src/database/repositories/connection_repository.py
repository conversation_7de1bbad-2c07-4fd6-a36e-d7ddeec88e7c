"""PostgreSQL implementation of ConnectionRepository."""

from typing import List, Optional, Dict, Any
from uuid import UUID
from datetime import datetime

from sqlalchemy import select, and_, or_, func, text, case
from sqlalchemy.orm import Session
from sqlalchemy.dialects.postgresql import array

from src.core.models.connection import (
    Connection as ConnectionDomain,
    ConnectionId,
    ConnectionMetrics,
    ConnectionStrength,
    RelationshipType,
    ConnectionPath,
    IntroductionRequest as IntroductionRequestDomain,
    IntroductionStatus
)
from src.core.repositories.connection_repository import ConnectionRepository, IntroductionRepository
from src.database.models.connection import Connection as ConnectionDB, IntroductionRequest as IntroductionRequestDB


class PostgresConnectionRepository(ConnectionRepository):
    """PostgreSQL implementation of connection repository."""
    
    def __init__(self, session: Session):
        """Initialize with database session."""
        self.session = session
    
    async def create_connection(self, connection: ConnectionDomain) -> ConnectionDomain:
        """Create a new connection."""
        # Ensure user ordering
        user_a_id, user_b_id = sorted([connection.user_a_id, connection.user_b_id])
        
        # Check if connection already exists
        existing = self.session.query(ConnectionDB).filter(
            ConnectionDB.user_a_id == user_a_id,
            ConnectionDB.user_b_id == user_b_id
        ).first()
        
        if existing and existing.is_active:
            raise ValueError("Active connection already exists between these users")
        
        # Create new connection
        db_connection = ConnectionDB(
            id=connection.id.value,
            user_a_id=user_a_id,
            user_b_id=user_b_id,
            relationship_type=connection.relationship_type.value,
            strength=connection.strength.value,
            metrics={
                "interaction_frequency": connection.metrics.interaction_frequency,
                "last_interaction_days": connection.metrics.last_interaction_days,
                "mutual_connections_count": connection.metrics.mutual_connections_count,
                "trust_score": connection.metrics.trust_score
            },
            notes=connection.notes,
            tags=connection.tags,
            created_at=connection.created_at,
            updated_at=connection.updated_at,
            is_active=connection.is_active
        )
        
        self.session.add(db_connection)
        self.session.commit()
        self.session.refresh(db_connection)
        
        return self._to_domain(db_connection)
    
    async def get_connection(self, user_a_id: UUID, user_b_id: UUID) -> Optional[ConnectionDomain]:
        """Get connection between two users."""
        # Ensure consistent ordering
        user_a_id, user_b_id = sorted([user_a_id, user_b_id])
        
        db_connection = self.session.query(ConnectionDB).filter(
            ConnectionDB.user_a_id == user_a_id,
            ConnectionDB.user_b_id == user_b_id,
            ConnectionDB.is_active == True
        ).first()
        
        if db_connection:
            return self._to_domain(db_connection)
        return None
    
    async def get_user_connections(self, user_id: UUID) -> List[ConnectionDomain]:
        """Get all connections for a user."""
        db_connections = self.session.query(ConnectionDB).filter(
            and_(
                or_(
                    ConnectionDB.user_a_id == user_id,
                    ConnectionDB.user_b_id == user_id
                ),
                ConnectionDB.is_active == True
            )
        ).all()
        
        return [self._to_domain(conn) for conn in db_connections]
    
    async def update_connection(self, connection: ConnectionDomain) -> ConnectionDomain:
        """Update an existing connection."""
        db_connection = self.session.query(ConnectionDB).filter(
            ConnectionDB.id == connection.id.value
        ).first()
        
        if not db_connection:
            raise ValueError(f"Connection {connection.id} not found")
        
        # Update fields
        db_connection.relationship_type = connection.relationship_type.value
        db_connection.strength = connection.strength.value
        db_connection.metrics = {
            "interaction_frequency": connection.metrics.interaction_frequency,
            "last_interaction_days": connection.metrics.last_interaction_days,
            "mutual_connections_count": connection.metrics.mutual_connections_count,
            "trust_score": connection.metrics.trust_score
        }
        db_connection.notes = connection.notes
        db_connection.tags = connection.tags
        db_connection.updated_at = connection.updated_at
        db_connection.is_active = connection.is_active
        
        self.session.commit()
        self.session.refresh(db_connection)
        
        return self._to_domain(db_connection)
    
    async def delete_connection(self, connection_id: UUID) -> bool:
        """Soft delete a connection."""
        db_connection = self.session.query(ConnectionDB).filter(
            ConnectionDB.id == connection_id
        ).first()
        
        if not db_connection:
            return False
        
        db_connection.is_active = False
        db_connection.updated_at = datetime.utcnow()
        
        self.session.commit()
        return True
    
    async def find_shortest_paths(
        self, 
        source_user_id: UUID, 
        target_user_id: UUID, 
        max_depth: int = 3
    ) -> List[ConnectionPath]:
        """Find shortest paths between two users using PostgreSQL function."""
        # Call the PostgreSQL function we created in the migration
        result = self.session.execute(
            text("""
                SELECT path, depth, strength_score 
                FROM find_connection_paths(:source_id, :target_id, :max_depth)
                ORDER BY depth, strength_score DESC
                LIMIT 5
            """),
            {
                "source_id": source_user_id,
                "target_id": target_user_id,
                "max_depth": max_depth
            }
        )
        
        paths = []
        for row in result:
            path_user_ids = row.path
            
            # Get connections that form this path
            connections = []
            for i in range(len(path_user_ids) - 1):
                user_a, user_b = sorted([path_user_ids[i], path_user_ids[i + 1]])
                conn = await self.get_connection(user_a, user_b)
                if conn:
                    connections.append(conn)
            
            # Create ConnectionPath object
            path = ConnectionPath(
                source_user_id=source_user_id,
                target_user_id=target_user_id,
                path=path_user_ids,
                connections=connections,
                total_strength_score=float(row.strength_score)
            )
            paths.append(path)
        
        return paths
    
    async def get_mutual_connections(self, user_a_id: UUID, user_b_id: UUID) -> List[ConnectionDomain]:
        """Get mutual connections between two users."""
        # Find users connected to both user_a and user_b
        query = text("""
            SELECT DISTINCT c1.*
            FROM connections c1
            WHERE c1.is_active = true
            AND EXISTS (
                SELECT 1 FROM connections c2
                WHERE c2.is_active = true
                AND (
                    (c1.user_a_id = c2.user_a_id OR c1.user_a_id = c2.user_b_id OR 
                     c1.user_b_id = c2.user_a_id OR c1.user_b_id = c2.user_b_id)
                )
                AND (
                    (c2.user_a_id = :user_a AND c2.user_b_id != :user_b) OR
                    (c2.user_b_id = :user_a AND c2.user_a_id != :user_b)
                )
            )
            AND EXISTS (
                SELECT 1 FROM connections c3
                WHERE c3.is_active = true
                AND (
                    (c1.user_a_id = c3.user_a_id OR c1.user_a_id = c3.user_b_id OR 
                     c1.user_b_id = c3.user_a_id OR c1.user_b_id = c3.user_b_id)
                )
                AND (
                    (c3.user_a_id = :user_b AND c3.user_b_id != :user_a) OR
                    (c3.user_b_id = :user_b AND c3.user_a_id != :user_a)
                )
            )
        """)
        
        result = self.session.execute(query, {"user_a": user_a_id, "user_b": user_b_id})
        db_connections = result.fetchall()
        
        return [self._to_domain_from_row(row) for row in db_connections]
    
    async def search_connections(
        self,
        user_id: UUID,
        filters: dict = None
    ) -> List[ConnectionDomain]:
        """Search connections with filters."""
        query = self.session.query(ConnectionDB).filter(
            and_(
                or_(
                    ConnectionDB.user_a_id == user_id,
                    ConnectionDB.user_b_id == user_id
                ),
                ConnectionDB.is_active == True
            )
        )
        
        if filters:
            if 'strength' in filters:
                query = query.filter(ConnectionDB.strength == filters['strength'])
            
            if 'relationship_type' in filters:
                query = query.filter(ConnectionDB.relationship_type == filters['relationship_type'])
            
            if 'tags' in filters and filters['tags']:
                # Filter by tags using JSON containment
                query = query.filter(
                    ConnectionDB.tags.contains(filters['tags'])
                )
        
        db_connections = query.all()
        return [self._to_domain(conn) for conn in db_connections]
    
    def _to_domain(self, db_connection: ConnectionDB) -> ConnectionDomain:
        """Convert database model to domain model."""
        metrics_data = db_connection.metrics or {}
        
        metrics = ConnectionMetrics(
            interaction_frequency=metrics_data.get('interaction_frequency', 0),
            last_interaction_days=metrics_data.get('last_interaction_days'),
            mutual_connections_count=metrics_data.get('mutual_connections_count', 0),
            trust_score=metrics_data.get('trust_score', 0.0)
        )
        
        connection = ConnectionDomain(
            id=ConnectionId(value=db_connection.id),
            user_a_id=db_connection.user_a_id,
            user_b_id=db_connection.user_b_id,
            relationship_type=RelationshipType(db_connection.relationship_type),
            strength=ConnectionStrength(db_connection.strength),
            metrics=metrics,
            notes=db_connection.notes,
            tags=db_connection.tags or [],
            created_at=db_connection.created_at,
            updated_at=db_connection.updated_at,
            is_active=db_connection.is_active
        )
        
        return connection
    
    def _to_domain_from_row(self, row) -> ConnectionDomain:
        """Convert database row to domain model."""
        metrics = ConnectionMetrics(
            interaction_frequency=row.metrics.get('interaction_frequency', 0) if row.metrics else 0,
            last_interaction_days=row.metrics.get('last_interaction_days') if row.metrics else None,
            mutual_connections_count=row.metrics.get('mutual_connections_count', 0) if row.metrics else 0,
            trust_score=row.metrics.get('trust_score', 0.0) if row.metrics else 0.0
        )
        
        return ConnectionDomain(
            id=ConnectionId(value=row.id),
            user_a_id=row.user_a_id,
            user_b_id=row.user_b_id,
            relationship_type=RelationshipType(row.relationship_type),
            strength=ConnectionStrength(row.strength),
            metrics=metrics,
            notes=row.notes,
            tags=row.tags or [],
            created_at=row.created_at,
            updated_at=row.updated_at,
            is_active=row.is_active
        )


class PostgresIntroductionRepository(IntroductionRepository):
    """PostgreSQL implementation of introduction repository."""
    
    def __init__(self, session: Session):
        """Initialize with database session."""
        self.session = session
    
    async def create_request(self, request: IntroductionRequestDomain) -> IntroductionRequestDomain:
        """Create a new introduction request."""
        # Check for existing pending request
        existing = self.session.query(IntroductionRequestDB).filter(
            IntroductionRequestDB.requester_id == request.requester_id,
            IntroductionRequestDB.target_id == request.target_id,
            IntroductionRequestDB.connector_id == request.connector_id,
            IntroductionRequestDB.status == 'pending'
        ).first()
        
        if existing:
            raise ValueError("Pending introduction request already exists")
        
        db_request = IntroductionRequestDB(
            id=request.id,
            requester_id=request.requester_id,
            target_id=request.target_id,
            connector_id=request.connector_id,
            status=request.status.value,
            message=request.message,
            connector_notes=request.connector_notes,
            created_at=request.created_at,
            updated_at=request.updated_at,
            expires_at=request.expires_at,
            completed_at=request.completed_at
        )
        
        self.session.add(db_request)
        self.session.commit()
        self.session.refresh(db_request)
        
        return self._to_domain(db_request)
    
    async def get_request(self, request_id: UUID) -> Optional[IntroductionRequestDomain]:
        """Get introduction request by ID."""
        db_request = self.session.query(IntroductionRequestDB).filter(
            IntroductionRequestDB.id == request_id
        ).first()
        
        if db_request:
            return self._to_domain(db_request)
        return None
    
    async def get_pending_requests(self, connector_id: UUID) -> List[IntroductionRequestDomain]:
        """Get pending requests for a connector."""
        db_requests = self.session.query(IntroductionRequestDB).filter(
            IntroductionRequestDB.connector_id == connector_id,
            IntroductionRequestDB.status == 'pending'
        ).order_by(IntroductionRequestDB.created_at.desc()).all()
        
        return [self._to_domain(req) for req in db_requests]
    
    async def update_request(self, request: IntroductionRequestDomain) -> IntroductionRequestDomain:
        """Update introduction request."""
        db_request = self.session.query(IntroductionRequestDB).filter(
            IntroductionRequestDB.id == request.id
        ).first()
        
        if not db_request:
            raise ValueError(f"Introduction request {request.id} not found")
        
        db_request.status = request.status.value
        db_request.connector_notes = request.connector_notes
        db_request.updated_at = request.updated_at
        db_request.completed_at = request.completed_at
        
        self.session.commit()
        self.session.refresh(db_request)
        
        return self._to_domain(db_request)
    
    async def get_user_requests(self, user_id: UUID) -> List[IntroductionRequestDomain]:
        """Get all requests involving a user."""
        db_requests = self.session.query(IntroductionRequestDB).filter(
            or_(
                IntroductionRequestDB.requester_id == user_id,
                IntroductionRequestDB.target_id == user_id,
                IntroductionRequestDB.connector_id == user_id
            )
        ).order_by(IntroductionRequestDB.created_at.desc()).all()
        
        return [self._to_domain(req) for req in db_requests]
    
    async def cleanup_expired_requests(self) -> int:
        """Remove expired requests and return count."""
        expired_count = self.session.query(IntroductionRequestDB).filter(
            IntroductionRequestDB.expires_at < datetime.utcnow(),
            IntroductionRequestDB.status == 'pending'
        ).update({
            'status': 'expired',
            'updated_at': datetime.utcnow()
        })
        
        self.session.commit()
        return expired_count
    
    def _to_domain(self, db_request: IntroductionRequestDB) -> IntroductionRequestDomain:
        """Convert database model to domain model."""
        return IntroductionRequestDomain(
            id=db_request.id,
            requester_id=db_request.requester_id,
            target_id=db_request.target_id,
            connector_id=db_request.connector_id,
            status=IntroductionStatus(db_request.status),
            message=db_request.message,
            connector_notes=db_request.connector_notes,
            created_at=db_request.created_at,
            updated_at=db_request.updated_at,
            expires_at=db_request.expires_at,
            completed_at=db_request.completed_at
        )