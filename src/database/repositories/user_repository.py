"""Repository for User database operations."""

from typing import Optional, List
from uuid import UUID
from sqlalchemy.orm import Session
from sqlalchemy import or_

from src.database.models.user import User


class UserRepository:
    """Repository for managing User entities."""
    
    def __init__(self):
        """Initialize the user repository."""
        self.model = User
    
    def find_by_email(self, db: Session, email: str) -> Optional[User]:
        """Find a user by email address."""
        return db.query(self.model).filter(
            self.model.email == email
        ).first()
    
    def find_by_username(self, db: Session, username: str) -> Optional[User]:
        """Find a user by username."""
        return db.query(self.model).filter(
            self.model.username == username
        ).first()
    
    def find_by_email_or_username(
        self, 
        db: Session, 
        email_or_username: str
    ) -> Optional[User]:
        """Find a user by email or username."""
        return db.query(self.model).filter(
            or_(
                self.model.email == email_or_username,
                self.model.username == email_or_username
            )
        ).first()
    
    def get_active_users(self, db: Session) -> List[User]:
        """Get all active users."""
        return db.query(self.model).filter(
            self.model.is_active == True
        ).all()
    
    def get_users_by_role(self, db: Session, role: str) -> List[User]:
        """Get users with a specific role."""
        # JSON contains query for PostgreSQL
        return db.query(self.model).filter(
            self.model.roles.contains([role])
        ).all()
    
    def update_last_login(self, db: Session, user_id: UUID) -> Optional[User]:
        """Update user's last login timestamp."""
        from datetime import datetime
        
        user = self.get_by_id(db, user_id)
        if user:
            user.last_login = datetime.utcnow()
            db.commit()
            db.refresh(user)
        return user
    
    def activate_user(self, db: Session, user_id: UUID) -> Optional[User]:
        """Activate a user account."""
        return self.update(db, user_id, {"is_active": True})
    
    def deactivate_user(self, db: Session, user_id: UUID) -> Optional[User]:
        """Deactivate a user account."""
        return self.update(db, user_id, {"is_active": False})
    
    def create(self, db: Session, user: User) -> User:
        """Create a new user."""
        db.add(user)
        db.commit()
        db.refresh(user)
        return user
    
    def get_by_id(self, db: Session, user_id: UUID) -> Optional[User]:
        """Get user by ID."""
        return db.query(self.model).filter(self.model.id == user_id).first()
    
    def update(self, db: Session, user_id: UUID, updates: dict) -> Optional[User]:
        """Update a user."""
        user = self.get_by_id(db, user_id)
        if user:
            for key, value in updates.items():
                setattr(user, key, value)
            db.commit()
            db.refresh(user)
        return user
    
    def delete(self, db: Session, user_id: UUID) -> bool:
        """Delete a user."""
        user = self.get_by_id(db, user_id)
        if user:
            db.delete(user)
            db.commit()
            return True
        return False
    
    def get_users_by_startup(self, db: Session, startup_id: UUID) -> List[User]:
        """Get all users associated with a startup."""
        # TODO: Implement based on user-startup relationship
        # For now, return empty list
        return []
    
    def get_users_by_vc(self, db: Session, vc_id: UUID) -> List[User]:
        """Get all users associated with a VC firm."""
        # TODO: Implement based on user-VC relationship
        # For now, return empty list
        return []