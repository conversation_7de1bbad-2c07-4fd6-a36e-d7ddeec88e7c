"""PostgreSQL implementation of MatchRepository using BaseRepository."""

from typing import List, Optional
from uuid import UUID

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from src.core.models.match import Match as MatchDomain
from src.core.repositories.match_repository import MatchRepository
from src.database.models import Match as MatchDB
from src.database.repositories.base_repository import BaseRepository


class PostgresMatchRepository(BaseRepository[MatchDB, MatchDomain], MatchRepository):
    """PostgreSQL implementation of match repository using base repository."""
    
    model_class = MatchDB
    
    def __init__(self, session: Session):
        """Initialize with database session."""
        super().__init__(session)
    
    def _to_domain(self, db_match: MatchDB) -> MatchDomain:
        """Convert database model to domain model."""
        return MatchDomain(
            id=db_match.id,
            startup_id=db_match.startup_id,
            vc_id=db_match.vc_id,
            score=db_match.score,
            reasons=db_match.reasons,
            created_at=db_match.created_at,
            updated_at=db_match.updated_at,
            status=db_match.status,
            feedback=db_match.feedback,
            metadata=db_match.metadata
        )
    
    def _to_db(self, match: MatchDomain, existing: Optional[MatchDB] = None) -> MatchDB:
        """Convert domain model to database model."""
        if existing:
            # Update existing model
            existing.startup_id = match.startup_id
            existing.vc_id = match.vc_id
            existing.score = match.score
            existing.reasons = match.reasons
            existing.status = match.status
            existing.feedback = match.feedback
            existing.metadata = match.metadata
            return existing
        else:
            # Create new model
            return MatchDB(
                id=match.id,
                startup_id=match.startup_id,
                vc_id=match.vc_id,
                score=match.score,
                reasons=match.reasons,
                status=match.status,
                feedback=match.feedback,
                metadata=match.metadata
            )
    
    # MatchRepository interface implementation
    async def save(self, match: MatchDomain) -> MatchDomain:
        """Save a match entity."""
        return super().save(match)
    
    async def find_by_id(self, match_id: UUID) -> Optional[MatchDomain]:
        """Find a match by ID."""
        return super().get(match_id)
    
    async def find_by_startup(self, startup_id: UUID) -> List[MatchDomain]:
        """Find all matches for a startup."""
        return super().find_by_field('startup_id', startup_id)
    
    async def find_by_vc(self, vc_id: UUID) -> List[MatchDomain]:
        """Find all matches for a VC."""
        return super().find_by_field('vc_id', vc_id)
    
    async def find_by_score_range(self, min_score: float, max_score: float) -> List[MatchDomain]:
        """Find matches within a score range."""
        db_matches = self.session.query(MatchDB).filter(
            and_(
                MatchDB.score >= min_score,
                MatchDB.score <= max_score
            )
        ).all()
        return [self._to_domain(match) for match in db_matches]
    
    async def delete(self, match_id: UUID) -> bool:
        """Delete a match by ID."""
        return super().delete(match_id)
    
    # Additional convenience methods
    def find_existing_match(self, startup_id: UUID, vc_id: UUID) -> Optional[MatchDomain]:
        """Find an existing match between a startup and VC."""
        db_match = self.session.query(MatchDB).filter(
            and_(
                MatchDB.startup_id == startup_id,
                MatchDB.vc_id == vc_id
            )
        ).first()
        return self._to_domain(db_match) if db_match else None
    
    def list_by_startup(self, startup_id: UUID, limit: int = 100, offset: int = 0) -> List[MatchDomain]:
        """List matches for a startup with pagination."""
        db_matches = self.session.query(MatchDB).filter(
            MatchDB.startup_id == startup_id
        ).order_by(MatchDB.score.desc()).limit(limit).offset(offset).all()
        return [self._to_domain(match) for match in db_matches]
    
    def list_by_vc(self, vc_id: UUID, limit: int = 100, offset: int = 0) -> List[MatchDomain]:
        """List matches for a VC with pagination."""
        db_matches = self.session.query(MatchDB).filter(
            MatchDB.vc_id == vc_id
        ).order_by(MatchDB.score.desc()).limit(limit).offset(offset).all()
        return [self._to_domain(match) for match in db_matches]
    
    def update_score(self, match_id: UUID, new_score: float, new_reasons: List[str]) -> Optional[MatchDomain]:
        """Update match score and reasons."""
        db_match = self.session.query(MatchDB).filter_by(id=match_id).first()
        if db_match:
            db_match.score = new_score
            db_match.reasons = new_reasons
            self.session.commit()
            self.session.refresh(db_match)
            return self._to_domain(db_match)
        return None
    
    def update_status(self, match_id: UUID, status: str) -> Optional[MatchDomain]:
        """Update match status."""
        db_match = self.session.query(MatchDB).filter_by(id=match_id).first()
        if db_match:
            db_match.status = status
            self.session.commit()
            self.session.refresh(db_match)
            return self._to_domain(db_match)
        return None
    
    def get_top_matches_for_startup(self, startup_id: UUID, limit: int = 10) -> List[MatchDomain]:
        """Get top matches for a startup by score."""
        return self.list_by_startup(startup_id, limit=limit)
    
    def get_top_matches_for_vc(self, vc_id: UUID, limit: int = 10) -> List[MatchDomain]:
        """Get top matches for a VC by score."""
        return self.list_by_vc(vc_id, limit=limit)
    
    def find_by_status(self, status: str) -> List[MatchDomain]:
        """Find matches by status."""
        return self.find_by_field('status', status)
    
    def bulk_create_matches(self, matches: List[MatchDomain]) -> List[MatchDomain]:
        """Create multiple matches in a single transaction."""
        return self.bulk_create(matches)