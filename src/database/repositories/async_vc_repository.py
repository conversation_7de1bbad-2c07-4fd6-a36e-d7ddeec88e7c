"""Async repository for VC entities."""

from typing import List, Optional, Set
from uuid import UUID
import logging

from sqlalchemy import select, or_, and_, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from src.database.repositories.async_base import AsyncRepository
from src.database.models.vc import VC
from src.core.repositories.vc_repository import VCRepository as IVCRepository
from src.core.models.vc import VC as VCDomainModel

logger = logging.getLogger(__name__)


class AsyncVCRepository(AsyncRepository[VC], IVCRepository):
    """
    Async repository for VC entities.
    
    Implements the VC repository interface using async SQLAlchemy.
    """
    
    def __init__(self, session: AsyncSession):
        """Initialize with VC model and session."""
        super().__init__(VC, session)
    
    async def save(self, vc: VCDomainModel) -> VCDomainModel:
        """
        Save a VC (create or update).
        
        Args:
            vc: Domain model VC
            
        Returns:
            Saved VC domain model
        """
        try:
            # Check if VC exists
            if vc.id:
                existing = await self.get(vc.id)
                if existing:
                    # Update existing
                    updated = await self.update(
                        vc.id,
                        firm_name=vc.firm_name,
                        website=vc.website,
                        thesis=vc.thesis,
                        sectors=vc.sectors,
                        stages=vc.stages,
                        check_size_min=vc.check_size_min,
                        check_size_max=vc.check_size_max
                    )
                    return self._to_domain_model(updated)
            
            # Create new
            db_vc = await self.create(
                id=vc.id,
                firm_name=vc.firm_name,
                website=vc.website,
                thesis=vc.thesis,
                sectors=vc.sectors,
                stages=vc.stages,
                check_size_min=vc.check_size_min,
                check_size_max=vc.check_size_max
            )
            return self._to_domain_model(db_vc)
            
        except Exception as e:
            logger.error(f"Error saving VC: {e}")
            raise
    
    async def find_by_id(self, vc_id: UUID) -> Optional[VCDomainModel]:
        """
        Find a VC by ID.
        
        Args:
            vc_id: VC UUID
            
        Returns:
            VC domain model or None
        """
        db_vc = await self.get(vc_id)
        return self._to_domain_model(db_vc) if db_vc else None
    
    async def find_all(self) -> List[VCDomainModel]:
        """
        Get all VCs.
        
        Returns:
            List of VC domain models
        """
        db_vcs = await self.get_all()
        return [self._to_domain_model(vc) for vc in db_vcs]
    
    async def find_by_sectors(self, sectors: List[str]) -> List[VCDomainModel]:
        """
        Find VCs investing in specific sectors.
        
        Args:
            sectors: List of sector names
            
        Returns:
            List of VC domain models
        """
        try:
            # Use PostgreSQL JSON operations
            stmt = select(self.model)
            
            # Find VCs where their sectors JSON array contains any of the requested sectors
            conditions = []
            for sector in sectors:
                # Use JSON containment with proper casting
                conditions.append(
                    self.model.sectors.op('@>')([sector])
                )
            
            if conditions:
                stmt = stmt.where(or_(*conditions))
            
            result = await self.session.execute(stmt)
            db_vcs = list(result.scalars().all())
            return [self._to_domain_model(vc) for vc in db_vcs]
            
        except Exception as e:
            logger.error(f"Error finding VCs by sectors: {e}")
            # Fallback to simple query if JSON operations fail
            try:
                db_vcs = await self.get_all()
                # Filter in Python
                filtered = []
                for vc in db_vcs:
                    if vc.sectors and any(s in vc.sectors for s in sectors):
                        filtered.append(vc)
                return [self._to_domain_model(vc) for vc in filtered]
            except:
                raise e
    
    async def find_by_stages(self, stages: List[str]) -> List[VCDomainModel]:
        """
        Find VCs investing in specific stages.
        
        Args:
            stages: List of stage names
            
        Returns:
            List of VC domain models
        """
        try:
            stmt = select(self.model)
            
            # Find VCs where their stages JSON array contains any of the requested stages
            conditions = []
            for stage in stages:
                # Use JSON containment with proper casting
                conditions.append(
                    self.model.stages.op('@>')([stage])
                )
            
            if conditions:
                stmt = stmt.where(or_(*conditions))
            
            result = await self.session.execute(stmt)
            db_vcs = list(result.scalars().all())
            return [self._to_domain_model(vc) for vc in db_vcs]
            
        except Exception as e:
            logger.error(f"Error finding VCs by stages: {e}")
            # Fallback to simple query if JSON operations fail
            try:
                db_vcs = await self.get_all()
                # Filter in Python
                filtered = []
                for vc in db_vcs:
                    if vc.stages and any(s in vc.stages for s in stages):
                        filtered.append(vc)
                return [self._to_domain_model(vc) for vc in filtered]
            except:
                raise e
    
    async def find_by_check_size(
        self,
        min_amount: Optional[float] = None,
        max_amount: Optional[float] = None
    ) -> List[VCDomainModel]:
        """
        Find VCs by check size range.
        
        Args:
            min_amount: Minimum check size
            max_amount: Maximum check size
            
        Returns:
            List of VC domain models
        """
        try:
            stmt = select(self.model)
            
            conditions = []
            if min_amount is not None:
                conditions.append(self.model.check_size_max >= min_amount)
            if max_amount is not None:
                conditions.append(self.model.check_size_min <= max_amount)
            
            if conditions:
                stmt = stmt.where(and_(*conditions))
            
            result = await self.session.execute(stmt)
            db_vcs = list(result.scalars().all())
            return [self._to_domain_model(vc) for vc in db_vcs]
            
        except Exception as e:
            logger.error(f"Error finding VCs by check size: {e}")
            raise
    
    async def search(self, query: str) -> List[VCDomainModel]:
        """
        Search VCs by firm name or thesis.
        
        Args:
            query: Search query
            
        Returns:
            List of matching VC domain models
        """
        try:
            search_term = f"%{query}%"
            stmt = select(self.model).where(
                or_(
                    self.model.firm_name.ilike(search_term),
                    self.model.thesis.ilike(search_term)
                )
            )
            
            result = await self.session.execute(stmt)
            db_vcs = list(result.scalars().all())
            return [self._to_domain_model(vc) for vc in db_vcs]
            
        except Exception as e:
            logger.error(f"Error searching VCs: {e}")
            raise
    
    async def delete(self, vc_id: UUID) -> bool:
        """
        Delete a VC by ID.
        
        Args:
            vc_id: VC UUID
            
        Returns:
            True if deleted, False if not found
        """
        return await super().delete(vc_id)
    
    async def get_with_matches(self, vc_id: UUID) -> Optional[VC]:
        """
        Get a VC with its matches eagerly loaded.
        
        Args:
            vc_id: VC UUID
            
        Returns:
            VC with matches or None
        """
        try:
            stmt = (
                select(self.model)
                .where(self.model.id == vc_id)
                .options(selectinload(self.model.matches))
            )
            
            result = await self.session.execute(stmt)
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error(f"Error getting VC with matches: {e}")
            raise
    
    def _to_domain_model(self, db_model: VC) -> VCDomainModel:
        """
        Convert database model to domain model.
        
        Args:
            db_model: Database VC model
            
        Returns:
            Domain VC model
        """
        return VCDomainModel(
            id=db_model.id,
            firm_name=db_model.firm_name,
            website=db_model.website,
            thesis=db_model.thesis,
            sectors=db_model.sectors or [],
            stages=db_model.stages or [],
            check_size_min=db_model.check_size_min,
            check_size_max=db_model.check_size_max
        )