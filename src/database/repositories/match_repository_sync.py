"""Synchronous PostgreSQL implementation of MatchRepository."""

from typing import List, Optional
from uuid import UUID, uuid4

from sqlalchemy import select, desc
from sqlalchemy.orm import Session, joinedload

from src.core.models.match import Match as MatchDomain
from src.core.models.startup import Startup as StartupDomain
from src.core.models.vc import VC as VCDomain
from src.core.repositories.match_repository import MatchRepository
from src.database.models.match import Match as MatchDB
from src.database.models.startup import Startup as StartupDB
from src.database.models.vc import VC as VCDB


class PostgresMatchRepository(MatchRepository):
    """PostgreSQL implementation of match repository with sync methods."""
    
    def __init__(self, session: Session):
        """Initialize with database session."""
        self.session = session
    
    async def create(self, match: MatchDomain) -> MatchDomain:
        """Create a new match."""
        # Convert domain model to DB model
        db_match = MatchDB(
            id=match.id or uuid4(),
            startup_id=match.startup.id,
            vc_id=match.vc.id,
            score=match.score,
            reasons=match.reasons,
            status=str(match.status.value) if match.status else "pending",
            match_type=str(match.match_type.value) if match.match_type else None,
            notes=match.notes
        )
        
        self.session.add(db_match)
        self.session.commit()
        self.session.refresh(db_match)
        
        # Load relationships
        db_match = self.session.query(MatchDB).options(
            joinedload(MatchDB.startup),
            joinedload(MatchDB.vc)
        ).filter_by(id=db_match.id).first()
        
        # Convert back to domain model
        return self._to_domain(db_match)
    
    async def get(self, match_id: UUID) -> Optional[MatchDomain]:
        """Get a match by ID."""
        db_match = self.session.query(MatchDB).options(
            joinedload(MatchDB.startup),
            joinedload(MatchDB.vc)
        ).filter_by(id=match_id).first()
        
        if db_match is None:
            return None
            
        return self._to_domain(db_match)
    
    async def list_by_startup(self, startup_id: UUID) -> List[MatchDomain]:
        """List all matches for a startup."""
        db_matches = self.session.query(MatchDB).options(
            joinedload(MatchDB.startup),
            joinedload(MatchDB.vc)
        ).filter_by(startup_id=startup_id).all()
        
        return [self._to_domain(match) for match in db_matches]
    
    async def list_by_vc(self, vc_id: UUID) -> List[MatchDomain]:
        """List all matches for a VC."""
        db_matches = self.session.query(MatchDB).options(
            joinedload(MatchDB.startup),
            joinedload(MatchDB.vc)
        ).filter_by(vc_id=vc_id).all()
        
        return [self._to_domain(match) for match in db_matches]
    
    async def delete(self, match_id: UUID) -> bool:
        """Delete a match."""
        db_match = self.session.query(MatchDB).filter_by(id=match_id).first()
        
        if db_match is None:
            return False
            
        self.session.delete(db_match)
        self.session.commit()
        
        return True
    
    async def exists(self, startup_id: UUID, vc_id: UUID) -> bool:
        """Check if a match exists between startup and VC."""
        db_match = self.session.query(MatchDB).filter_by(
            startup_id=startup_id,
            vc_id=vc_id
        ).first()
        
        return db_match is not None
    
    async def update_score(self, match_id: UUID, score: float) -> Optional[MatchDomain]:
        """Update the score of an existing match."""
        db_match = self.session.query(MatchDB).filter_by(id=match_id).first()
        
        if db_match is None:
            return None
        
        # Update score
        db_match.score = score
        
        self.session.commit()
        self.session.refresh(db_match)
        
        # Load relationships
        db_match = self.session.query(MatchDB).options(
            joinedload(MatchDB.startup),
            joinedload(MatchDB.vc)
        ).filter_by(id=db_match.id).first()
        
        return self._to_domain(db_match)
    
    def _to_domain(self, db_match: MatchDB) -> MatchDomain:
        """Convert database model to domain model."""
        # Import status and type enums
        from src.core.schemas.match import MatchStatus, MatchType
        
        # Convert startup
        startup = StartupDomain(
            name=db_match.startup.name,
            sector=db_match.startup.sector,
            stage=db_match.startup.stage,
            description=db_match.startup.description or "",
            website=db_match.startup.website or "",
            team_size=db_match.startup.team_size,
            monthly_revenue=db_match.startup.monthly_revenue
        )
        startup.id = db_match.startup.id
        
        # Convert VC
        vc = VCDomain(
            firm_name=db_match.vc.firm_name,
            website=db_match.vc.website or "",
            thesis=db_match.vc.thesis or "",
            check_size_min=db_match.vc.check_size_min or 0,
            check_size_max=db_match.vc.check_size_max or 0,
            sectors=db_match.vc.sectors or [],
            stages=db_match.vc.stages or []
        )
        vc.id = db_match.vc.id
        
        # Create match
        domain_match = MatchDomain(
            startup=startup,
            vc=vc,
            score=db_match.score,
            reasons=db_match.reasons or [],
            status=MatchStatus(db_match.status) if db_match.status else MatchStatus.PENDING,
            match_type=MatchType(db_match.match_type) if db_match.match_type else None,
            notes=db_match.notes,
            created_at=db_match.created_at,
            updated_at=db_match.updated_at
        )
        domain_match.id = db_match.id
        
        return domain_match