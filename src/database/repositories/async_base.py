"""Base async repository implementing common CRUD operations."""

from typing import TypeVar, Generic, Type, Optional, List, Dict, Any
from uuid import UUID
from abc import ABC, abstractmethod
import logging

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, func
from sqlalchemy.orm import selectinload
from sqlalchemy.exc import IntegrityError, SQLAlchemyError

from src.database.base import Base

# Type variable for generic model type
ModelType = TypeVar("ModelType", bound=Base)

logger = logging.getLogger(__name__)


class AsyncRepository(Generic[ModelType], ABC):
    """
    Generic async repository providing basic CRUD operations.
    
    This base class implements common database operations using async SQLAlchemy,
    following the repository pattern for clean architecture.
    """
    
    def __init__(self, model: Type[ModelType], session: AsyncSession):
        """
        Initialize repository with model class and session.
        
        Args:
            model: SQLAlchemy model class
            session: Async database session
        """
        self.model = model
        self.session = session
    
    async def create(self, **kwargs) -> ModelType:
        """
        Create a new record.
        
        Args:
            **kwargs: Model field values
            
        Returns:
            Created model instance
            
        Raises:
            IntegrityError: If unique constraint violated
            SQLAlchemyError: For other database errors
        """
        try:
            instance = self.model(**kwargs)
            self.session.add(instance)
            await self.session.commit()
            await self.session.refresh(instance)
            return instance
        except IntegrityError as e:
            await self.session.rollback()
            logger.error(f"Integrity error creating {self.model.__name__}: {e}")
            raise
        except SQLAlchemyError as e:
            await self.session.rollback()
            logger.error(f"Database error creating {self.model.__name__}: {e}")
            raise
    
    async def get(self, id: UUID) -> Optional[ModelType]:
        """
        Get a record by ID.
        
        Args:
            id: Record UUID
            
        Returns:
            Model instance or None if not found
        """
        try:
            stmt = select(self.model).where(self.model.id == id)
            result = await self.session.execute(stmt)
            return result.scalar_one_or_none()
        except SQLAlchemyError as e:
            logger.error(f"Database error getting {self.model.__name__} {id}: {e}")
            raise
    
    async def get_all(
        self, 
        skip: int = 0, 
        limit: int = 100,
        **filters
    ) -> List[ModelType]:
        """
        Get all records with optional pagination and filters.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            **filters: Field filters (field=value)
            
        Returns:
            List of model instances
        """
        try:
            stmt = select(self.model)
            
            # Apply filters
            for field, value in filters.items():
                if hasattr(self.model, field):
                    stmt = stmt.where(getattr(self.model, field) == value)
            
            # Apply pagination
            stmt = stmt.offset(skip).limit(limit)
            
            result = await self.session.execute(stmt)
            return list(result.scalars().all())
        except SQLAlchemyError as e:
            logger.error(f"Database error getting all {self.model.__name__}: {e}")
            raise
    
    async def update(self, id: UUID, **kwargs) -> Optional[ModelType]:
        """
        Update a record by ID.
        
        Args:
            id: Record UUID
            **kwargs: Fields to update
            
        Returns:
            Updated model instance or None if not found
        """
        try:
            # First check if record exists
            instance = await self.get(id)
            if not instance:
                return None
            
            # Update fields
            stmt = (
                update(self.model)
                .where(self.model.id == id)
                .values(**kwargs)
                .execution_options(synchronize_session="fetch")
            )
            
            await self.session.execute(stmt)
            await self.session.commit()
            
            # Refresh and return
            await self.session.refresh(instance)
            return instance
        except IntegrityError as e:
            await self.session.rollback()
            logger.error(f"Integrity error updating {self.model.__name__} {id}: {e}")
            raise
        except SQLAlchemyError as e:
            await self.session.rollback()
            logger.error(f"Database error updating {self.model.__name__} {id}: {e}")
            raise
    
    async def delete(self, id: UUID) -> bool:
        """
        Delete a record by ID.
        
        Args:
            id: Record UUID
            
        Returns:
            True if deleted, False if not found
        """
        try:
            stmt = delete(self.model).where(self.model.id == id)
            result = await self.session.execute(stmt)
            await self.session.commit()
            return result.rowcount > 0
        except SQLAlchemyError as e:
            await self.session.rollback()
            logger.error(f"Database error deleting {self.model.__name__} {id}: {e}")
            raise
    
    async def count(self, **filters) -> int:
        """
        Count records with optional filters.
        
        Args:
            **filters: Field filters (field=value)
            
        Returns:
            Number of records
        """
        try:
            stmt = select(func.count()).select_from(self.model)
            
            # Apply filters
            for field, value in filters.items():
                if hasattr(self.model, field):
                    stmt = stmt.where(getattr(self.model, field) == value)
            
            result = await self.session.execute(stmt)
            return result.scalar() or 0
        except SQLAlchemyError as e:
            logger.error(f"Database error counting {self.model.__name__}: {e}")
            raise
    
    async def exists(self, id: UUID) -> bool:
        """
        Check if a record exists by ID.
        
        Args:
            id: Record UUID
            
        Returns:
            True if exists, False otherwise
        """
        try:
            stmt = select(func.count()).select_from(self.model).where(self.model.id == id)
            result = await self.session.execute(stmt)
            return (result.scalar() or 0) > 0
        except SQLAlchemyError as e:
            logger.error(f"Database error checking existence {self.model.__name__} {id}: {e}")
            raise
    
    async def bulk_create(self, instances: List[Dict[str, Any]]) -> List[ModelType]:
        """
        Create multiple records in a single transaction.
        
        Args:
            instances: List of dictionaries with model field values
            
        Returns:
            List of created model instances
        """
        try:
            models = [self.model(**data) for data in instances]
            self.session.add_all(models)
            await self.session.commit()
            
            # Refresh all instances
            for model in models:
                await self.session.refresh(model)
            
            return models
        except IntegrityError as e:
            await self.session.rollback()
            logger.error(f"Integrity error bulk creating {self.model.__name__}: {e}")
            raise
        except SQLAlchemyError as e:
            await self.session.rollback()
            logger.error(f"Database error bulk creating {self.model.__name__}: {e}")
            raise
    
    async def find_by(self, **filters) -> Optional[ModelType]:
        """
        Find a single record by filters.
        
        Args:
            **filters: Field filters (field=value)
            
        Returns:
            First matching model instance or None
        """
        try:
            stmt = select(self.model)
            
            for field, value in filters.items():
                if hasattr(self.model, field):
                    stmt = stmt.where(getattr(self.model, field) == value)
            
            result = await self.session.execute(stmt)
            return result.scalar_one_or_none()
        except SQLAlchemyError as e:
            logger.error(f"Database error finding {self.model.__name__}: {e}")
            raise
    
    async def find_all_by(self, **filters) -> List[ModelType]:
        """
        Find all records matching filters.
        
        Args:
            **filters: Field filters (field=value)
            
        Returns:
            List of matching model instances
        """
        try:
            stmt = select(self.model)
            
            for field, value in filters.items():
                if hasattr(self.model, field):
                    stmt = stmt.where(getattr(self.model, field) == value)
            
            result = await self.session.execute(stmt)
            return list(result.scalars().all())
        except SQLAlchemyError as e:
            logger.error(f"Database error finding all {self.model.__name__}: {e}")
            raise