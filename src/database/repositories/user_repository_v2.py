"""PostgreSQL implementation of UserRepository using BaseRepository."""

from typing import Optional
from uuid import UUID

from sqlalchemy.orm import Session

from src.core.models.user import User as UserDomain
from src.core.repositories.user_repository import UserRepository
from src.database.models import User as UserDB
from src.database.repositories.base_repository import BaseRepository


class PostgresUserRepository(BaseRepository[UserDB, UserDomain], UserRepository):
    """PostgreSQL implementation of user repository using base repository."""
    
    model_class = UserDB
    
    def __init__(self, session: Session):
        """Initialize with database session."""
        super().__init__(session)
    
    def _to_domain(self, db_user: UserDB) -> UserDomain:
        """Convert database model to domain model."""
        return UserDomain(
            id=db_user.id,
            email=db_user.email,
            username=db_user.username,
            hashed_password=db_user.hashed_password,
            is_active=db_user.is_active,
            is_superuser=db_user.is_superuser,
            created_at=db_user.created_at,
            updated_at=db_user.updated_at
        )
    
    def _to_db(self, user: UserDomain, existing: Optional[UserDB] = None) -> UserDB:
        """Convert domain model to database model."""
        if existing:
            # Update existing model
            existing.email = user.email
            existing.username = user.username
            existing.hashed_password = user.hashed_password
            existing.is_active = user.is_active
            existing.is_superuser = user.is_superuser
            return existing
        else:
            # Create new model
            return UserDB(
                id=user.id,
                email=user.email,
                username=user.username,
                hashed_password=user.hashed_password,
                is_active=user.is_active,
                is_superuser=user.is_superuser
            )
    
    # UserRepository interface implementation
    async def save(self, user: UserDomain) -> UserDomain:
        """Save a user entity."""
        return super().save(user)
    
    async def find_by_id(self, user_id: UUID) -> Optional[UserDomain]:
        """Find a user by ID."""
        return super().get(user_id)
    
    async def find_by_email(self, email: str) -> Optional[UserDomain]:
        """Find a user by email."""
        return super().find_one_by_field('email', email)
    
    async def find_by_username(self, username: str) -> Optional[UserDomain]:
        """Find a user by username."""
        return super().find_one_by_field('username', username)
    
    async def delete(self, user_id: UUID) -> bool:
        """Delete a user by ID."""
        return super().delete(user_id)
    
    # Additional convenience methods
    def get_by_email(self, email: str) -> Optional[UserDomain]:
        """Get user by email (sync version)."""
        return self.find_one_by_field('email', email)
    
    def get_by_username(self, username: str) -> Optional[UserDomain]:
        """Get user by username (sync version)."""
        return self.find_one_by_field('username', username)
    
    def authenticate(self, email: str, password: str) -> Optional[UserDomain]:
        """
        Authenticate a user by email and password.
        Note: This should use password hashing verification in production.
        """
        user = self.get_by_email(email)
        if user:
            # In production, verify password hash here
            # For now, we just return the user if found
            return user
        return None
    
    def list_active_users(self, limit: int = 100, offset: int = 0):
        """List all active users."""
        return self.search({'is_active': True}, limit=limit, offset=offset)