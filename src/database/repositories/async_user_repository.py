"""Async repository for User entities."""

from typing import List, Optional
from uuid import UUID
from datetime import datetime
import logging

from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession

from src.database.repositories.async_base import AsyncRepository
from src.database.models.user import User
from src.core.repositories.user_repository import UserRepository as IUserRepository

logger = logging.getLogger(__name__)


class AsyncUserRepository(AsyncRepository[User], IUserRepository):
    """
    Async repository for User entities.
    
    Implements the user repository interface using async SQLAlchemy.
    """
    
    def __init__(self, session: AsyncSession):
        """Initialize with User model and session."""
        super().__init__(User, session)
    
    async def create(self, user: User) -> User:
        """
        Create a new user.
        
        Args:
            user: User model
            
        Returns:
            Created user
        """
        return await super().create(
            email=user.email,
            username=user.username,
            hashed_password=user.hashed_password,
            full_name=user.full_name,
            is_active=user.is_active,
            is_superuser=user.is_superuser,
            roles=user.roles or []
        )
    
    async def get_by_email(self, email: str) -> Optional[User]:
        """
        Get user by email.
        
        Args:
            email: User email
            
        Returns:
            User or None
        """
        return await self.find_by(email=email)
    
    async def get_by_username(self, username: str) -> Optional[User]:
        """
        Get user by username.
        
        Args:
            username: User username
            
        Returns:
            User or None
        """
        return await self.find_by(username=username)
    
    async def get_by_email_or_username(self, identifier: str) -> Optional[User]:
        """
        Get user by email or username.
        
        Args:
            identifier: Email or username
            
        Returns:
            User or None
        """
        try:
            stmt = select(self.model).where(
                (self.model.email == identifier) | 
                (self.model.username == identifier)
            )
            result = await self.session.execute(stmt)
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error(f"Error getting user by email/username: {e}")
            raise
    
    async def update_last_login(self, user_id: UUID) -> Optional[User]:
        """
        Update user's last login timestamp.
        
        Args:
            user_id: User UUID
            
        Returns:
            Updated user or None
        """
        return await self.update(user_id, last_login=datetime.utcnow())
    
    async def update_password(self, user_id: UUID, hashed_password: str) -> Optional[User]:
        """
        Update user's password.
        
        Args:
            user_id: User UUID
            hashed_password: New hashed password
            
        Returns:
            Updated user or None
        """
        return await self.update(user_id, hashed_password=hashed_password)
    
    async def activate(self, user_id: UUID) -> Optional[User]:
        """
        Activate a user.
        
        Args:
            user_id: User UUID
            
        Returns:
            Updated user or None
        """
        return await self.update(user_id, is_active=True)
    
    async def deactivate(self, user_id: UUID) -> Optional[User]:
        """
        Deactivate a user.
        
        Args:
            user_id: User UUID
            
        Returns:
            Updated user or None
        """
        return await self.update(user_id, is_active=False)
    
    async def add_role(self, user_id: UUID, role: str) -> Optional[User]:
        """
        Add a role to a user.
        
        Args:
            user_id: User UUID
            role: Role to add
            
        Returns:
            Updated user or None
        """
        try:
            user = await self.get(user_id)
            if not user:
                return None
            
            roles = user.roles or []
            if role not in roles:
                roles = roles + [role]  # Create new list for change detection
                return await self.update(user_id, roles=roles)
            
            return user
            
        except Exception as e:
            logger.error(f"Error adding role to user: {e}")
            raise
    
    async def remove_role(self, user_id: UUID, role: str) -> Optional[User]:
        """
        Remove a role from a user.
        
        Args:
            user_id: User UUID
            role: Role to remove
            
        Returns:
            Updated user or None
        """
        try:
            user = await self.get(user_id)
            if not user:
                return None
            
            roles = user.roles or []
            if role in roles:
                roles = [r for r in roles if r != role]
                return await self.update(user_id, roles=roles)
            
            return user
            
        except Exception as e:
            logger.error(f"Error removing role from user: {e}")
            raise
    
    async def get_by_role(self, role: str) -> List[User]:
        """
        Get all users with a specific role.
        
        Args:
            role: Role to filter by
            
        Returns:
            List of users with the role
        """
        try:
            stmt = select(self.model).where(
                self.model.roles.op('@>')([role])
            )
            result = await self.session.execute(stmt)
            return list(result.scalars().all())
            
        except Exception as e:
            logger.error(f"Error getting users by role: {e}")
            # Fallback to simple query if JSON operations fail
            try:
                all_users = await self.get_all()
                # Filter in Python
                return [u for u in all_users if u.roles and role in u.roles]
            except:
                raise e
    
    async def get_active_users(self) -> List[User]:
        """
        Get all active users.
        
        Returns:
            List of active users
        """
        return await self.find_all_by(is_active=True)
    
    async def get_superusers(self) -> List[User]:
        """
        Get all superusers.
        
        Returns:
            List of superusers
        """
        return await self.find_all_by(is_superuser=True)
    
    async def search(self, query: str) -> List[User]:
        """
        Search users by email, username, or full name.
        
        Args:
            query: Search query
            
        Returns:
            List of matching users
        """
        try:
            search_term = f"%{query}%"
            stmt = select(self.model).where(
                (self.model.email.ilike(search_term)) |
                (self.model.username.ilike(search_term)) |
                (self.model.full_name.ilike(search_term))
            )
            
            result = await self.session.execute(stmt)
            return list(result.scalars().all())
            
        except Exception as e:
            logger.error(f"Error searching users: {e}")
            raise
    
    async def exists_by_email(self, email: str) -> bool:
        """
        Check if a user exists with the given email.
        
        Args:
            email: Email to check
            
        Returns:
            True if exists
        """
        try:
            stmt = select(func.count()).select_from(self.model).where(
                self.model.email == email
            )
            result = await self.session.execute(stmt)
            return (result.scalar() or 0) > 0
            
        except Exception as e:
            logger.error(f"Error checking email existence: {e}")
            raise
    
    async def exists_by_username(self, username: str) -> bool:
        """
        Check if a user exists with the given username.
        
        Args:
            username: Username to check
            
        Returns:
            True if exists
        """
        try:
            stmt = select(func.count()).select_from(self.model).where(
                self.model.username == username
            )
            result = await self.session.execute(stmt)
            return (result.scalar() or 0) > 0
            
        except Exception as e:
            logger.error(f"Error checking username existence: {e}")
            raise