"""Repository for hybrid keyword + semantic search."""

from typing import List, Dict, Any, Optional, Tuple
from uuid import UUID
import logging

from sqlalchemy import text, func
from sqlalchemy.orm import Session

from src.core.models.startup import Startup as StartupDomain
from src.core.models.vc import VC as VCDomain

logger = logging.getLogger(__name__)


class HybridSearchRepository:
    """Repository for hybrid keyword + semantic search operations."""
    
    def __init__(self, session: Session):
        """Initialize with database session."""
        self.session = session
        
    def hybrid_startup_search(
        self,
        query_embedding: List[float],
        keyword_query: str,
        vc_sectors: Optional[List[str]] = None,
        vc_stages: Optional[List[str]] = None,
        min_funding: float = 0,
        max_funding: float = float('inf'),
        limit: int = 50,
        keyword_weight: float = 0.4,
        semantic_weight: float = 0.6,
        min_score: float = 0.1
    ) -> List[Dict[str, Any]]:
        """
        Perform hybrid search combining keyword and semantic matching.
        
        Args:
            query_embedding: Vector embedding of search query
            keyword_query: Text query for full-text search
            vc_sectors: Filter by sectors
            vc_stages: Filter by stages
            min_funding: Minimum monthly revenue
            max_funding: Maximum monthly revenue
            limit: Maximum results to return
            keyword_weight: Weight for keyword score (0-1)
            semantic_weight: Weight for semantic score (0-1)
            min_score: Minimum hybrid score threshold
            
        Returns:
            List of startups with hybrid scores
        """
        
        # Convert embedding to PostgreSQL vector format
        vector_str = f"[{','.join(map(str, query_embedding))}]" if query_embedding else None
        
        # Build the hybrid search query
        query = text("""
        WITH keyword_scores AS (
            SELECT 
                s.id,
                s.name,
                s.sector,
                s.stage,
                s.description,
                s.monthly_revenue,
                s.team_size,
                s.website,
                -- Keyword relevance score using full-text search
                CASE 
                    WHEN :keyword_query = '' THEN 0
                    ELSE COALESCE(
                        ts_rank_cd(
                            to_tsvector('english', 
                                COALESCE(s.name, '') || ' ' || 
                                COALESCE(s.description, '') || ' ' || 
                                COALESCE(s.sector, '')
                            ),
                            plainto_tsquery('english', :keyword_query)
                        ), 0
                    )
                END as keyword_score
            FROM startups s
            WHERE 
                -- Sector filter
                (:vc_sectors_filter = '' OR s.sector = ANY(string_to_array(:vc_sectors_filter, ',')))
                -- Stage filter
                AND (:vc_stages_filter = '' OR s.stage = ANY(string_to_array(:vc_stages_filter, ',')))
                -- Revenue filter
                AND s.monthly_revenue >= :min_funding
                AND (:max_funding_str = 'inf' OR s.monthly_revenue <= :max_funding)
                -- Keyword filter (if provided)
                AND (
                    :keyword_query = '' OR
                    to_tsvector('english', 
                        COALESCE(s.name, '') || ' ' || 
                        COALESCE(s.description, '') || ' ' || 
                        COALESCE(s.sector, '')
                    ) @@ plainto_tsquery('english', :keyword_query)
                )
        ),
        semantic_scores AS (
            SELECT 
                s.id,
                -- Semantic similarity score using cosine distance
                CASE
                    WHEN :query_vector IS NULL OR s.combined_vector IS NULL THEN 0
                    ELSE (1 - (text_to_vector(s.combined_vector) <=> text_to_vector(:query_vector)))
                END as semantic_score
            FROM startups s
            WHERE s.combined_vector IS NOT NULL OR :query_vector IS NULL
        ),
        hybrid_results AS (
            SELECT 
                k.id,
                k.name,
                k.sector,
                k.stage,
                k.description,
                k.monthly_revenue,
                k.team_size,
                k.website,
                k.keyword_score,
                COALESCE(sem.semantic_score, 0) as semantic_score,
                -- Combined hybrid score
                (:keyword_weight * k.keyword_score + :semantic_weight * COALESCE(sem.semantic_score, 0)) as hybrid_score,
                -- Individual score components for debugging
                k.keyword_score as debug_keyword_score,
                COALESCE(sem.semantic_score, 0) as debug_semantic_score
            FROM keyword_scores k
            LEFT JOIN semantic_scores sem ON k.id = sem.id
        )
        SELECT 
            id,
            name,
            sector,
            stage,
            description,
            monthly_revenue,
            team_size,
            website,
            hybrid_score,
            debug_keyword_score,
            debug_semantic_score
        FROM hybrid_results
        WHERE hybrid_score >= :min_score
        ORDER BY hybrid_score DESC, monthly_revenue DESC
        LIMIT :limit
        """)
        
        # Prepare parameters
        sectors_filter = ','.join(vc_sectors) if vc_sectors else ''
        stages_filter = ','.join(vc_stages) if vc_stages else ''
        max_funding_str = 'inf' if max_funding == float('inf') else str(max_funding)
        
        result = self.session.execute(query, {
            'query_vector': vector_str,
            'keyword_query': keyword_query or '',
            'vc_sectors_filter': sectors_filter,
            'vc_stages_filter': stages_filter,
            'min_funding': min_funding,
            'max_funding': max_funding if max_funding != float('inf') else 999999999,
            'max_funding_str': max_funding_str,
            'keyword_weight': keyword_weight,
            'semantic_weight': semantic_weight,
            'min_score': min_score,
            'limit': limit
        })
        
        return [dict(row._mapping) for row in result]
    
    def hybrid_vc_search(
        self,
        query_embedding: List[float],
        keyword_query: str,
        startup_sector: Optional[str] = None,
        startup_stage: Optional[str] = None,
        funding_amount: Optional[float] = None,
        limit: int = 50,
        keyword_weight: float = 0.4,
        semantic_weight: float = 0.6,
        min_score: float = 0.1
    ) -> List[Dict[str, Any]]:
        """
        Perform hybrid search for VCs based on startup criteria.
        
        Args:
            query_embedding: Vector embedding of search query
            keyword_query: Text query for full-text search
            startup_sector: Startup's sector
            startup_stage: Startup's stage
            funding_amount: Desired funding amount
            limit: Maximum results to return
            keyword_weight: Weight for keyword score (0-1)
            semantic_weight: Weight for semantic score (0-1)
            min_score: Minimum hybrid score threshold
            
        Returns:
            List of VCs with hybrid scores
        """
        
        # Convert embedding to PostgreSQL vector format
        vector_str = f"[{','.join(map(str, query_embedding))}]" if query_embedding else None
        
        query = text("""
        WITH keyword_scores AS (
            SELECT 
                v.id,
                v.firm_name,
                v.thesis,
                v.website,
                v.sectors,
                v.stages,
                v.check_size_min,
                v.check_size_max,
                -- Keyword relevance score
                CASE 
                    WHEN :keyword_query = '' THEN 0
                    ELSE COALESCE(
                        ts_rank_cd(
                            to_tsvector('english', 
                                COALESCE(v.firm_name, '') || ' ' || 
                                COALESCE(v.thesis, '')
                            ),
                            plainto_tsquery('english', :keyword_query)
                        ), 0
                    )
                END as keyword_score
            FROM vcs v
            WHERE 
                -- Sector match (if specified)
                (:startup_sector IS NULL OR :startup_sector = ANY(v.sectors) OR array_length(v.sectors, 1) IS NULL)
                -- Stage match (if specified)
                AND (:startup_stage IS NULL OR :startup_stage = ANY(v.stages) OR array_length(v.stages, 1) IS NULL)
                -- Funding amount match (if specified)
                AND (
                    :funding_amount IS NULL OR
                    (v.check_size_min <= :funding_amount AND v.check_size_max >= :funding_amount) OR
                    (v.check_size_min = 0 AND v.check_size_max = 0)
                )
        ),
        semantic_scores AS (
            SELECT 
                v.id,
                -- Semantic similarity score
                CASE
                    WHEN :query_vector IS NULL OR v.combined_vector IS NULL THEN 0
                    ELSE (1 - (text_to_vector(v.combined_vector) <=> text_to_vector(:query_vector)))
                END as semantic_score
            FROM vcs v
            WHERE v.combined_vector IS NOT NULL OR :query_vector IS NULL
        ),
        hybrid_results AS (
            SELECT 
                k.id,
                k.firm_name,
                k.thesis,
                k.website,
                k.sectors,
                k.stages,
                k.check_size_min,
                k.check_size_max,
                k.keyword_score,
                COALESCE(sem.semantic_score, 0) as semantic_score,
                -- Combined hybrid score
                (:keyword_weight * k.keyword_score + :semantic_weight * COALESCE(sem.semantic_score, 0)) as hybrid_score
            FROM keyword_scores k
            LEFT JOIN semantic_scores sem ON k.id = sem.id
        )
        SELECT 
            id,
            firm_name,
            thesis,
            website,
            sectors,
            stages,
            check_size_min,
            check_size_max,
            hybrid_score,
            keyword_score as debug_keyword_score,
            semantic_score as debug_semantic_score
        FROM hybrid_results
        WHERE hybrid_score >= :min_score
        ORDER BY hybrid_score DESC
        LIMIT :limit
        """)
        
        result = self.session.execute(query, {
            'query_vector': vector_str,
            'keyword_query': keyword_query or '',
            'startup_sector': startup_sector,
            'startup_stage': startup_stage,
            'funding_amount': funding_amount,
            'keyword_weight': keyword_weight,
            'semantic_weight': semantic_weight,
            'min_score': min_score,
            'limit': limit
        })
        
        return [dict(row._mapping) for row in result]
    
    def explain_hybrid_match(
        self,
        entity_type: str,  # 'startup' or 'vc'
        entity_id: UUID,
        query_embedding: List[float],
        keyword_query: str
    ) -> Dict[str, Any]:
        """
        Explain why a specific entity matches the search query.
        
        Args:
            entity_type: Type of entity ('startup' or 'vc')
            entity_id: ID of the entity
            query_embedding: Vector embedding of search query
            keyword_query: Text query
            
        Returns:
            Detailed explanation of the match
        """
        
        vector_str = f"[{','.join(map(str, query_embedding))}]" if query_embedding else None
        
        if entity_type == 'startup':
            query = text("""
            SELECT 
                s.id,
                s.name,
                s.description,
                s.sector,
                s.stage,
                -- Keyword matching details
                ts_rank_cd(
                    to_tsvector('english', 
                        COALESCE(s.name, '') || ' ' || 
                        COALESCE(s.description, '') || ' ' || 
                        COALESCE(s.sector, '')
                    ),
                    plainto_tsquery('english', :keyword_query)
                ) as keyword_relevance,
                
                -- Show matching keywords with highlights
                ts_headline('english', 
                    COALESCE(s.name, '') || ' - ' || COALESCE(s.description, ''), 
                    plainto_tsquery('english', :keyword_query),
                    'StartSel=<mark>, StopSel=</mark>, MaxWords=50, MinWords=20'
                ) as highlighted_text,
                
                -- Semantic similarity
                CASE
                    WHEN :query_vector IS NULL OR s.combined_vector IS NULL THEN 0
                    ELSE (1 - (text_to_vector(s.combined_vector) <=> text_to_vector(:query_vector)))
                END as semantic_similarity,
                
                -- Additional metadata
                s.combined_vector IS NOT NULL as has_embedding,
                s.embedding_updated_at
            FROM startups s
            WHERE s.id = :entity_id::uuid
            """)
        else:  # vc
            query = text("""
            SELECT 
                v.id,
                v.firm_name as name,
                v.thesis as description,
                v.sectors,
                v.stages,
                -- Keyword matching details
                ts_rank_cd(
                    to_tsvector('english', 
                        COALESCE(v.firm_name, '') || ' ' || 
                        COALESCE(v.thesis, '')
                    ),
                    plainto_tsquery('english', :keyword_query)
                ) as keyword_relevance,
                
                -- Show matching keywords with highlights
                ts_headline('english', 
                    COALESCE(v.firm_name, '') || ' - ' || COALESCE(v.thesis, ''), 
                    plainto_tsquery('english', :keyword_query),
                    'StartSel=<mark>, StopSel=</mark>, MaxWords=50, MinWords=20'
                ) as highlighted_text,
                
                -- Semantic similarity
                CASE
                    WHEN :query_vector IS NULL OR v.combined_vector IS NULL THEN 0
                    ELSE (1 - (text_to_vector(v.combined_vector) <=> text_to_vector(:query_vector)))
                END as semantic_similarity,
                
                -- Additional metadata
                v.combined_vector IS NOT NULL as has_embedding,
                v.embedding_updated_at
            FROM vcs v
            WHERE v.id = :entity_id::uuid
            """)
        
        result = self.session.execute(query, {
            'query_vector': vector_str,
            'keyword_query': keyword_query or '',
            'entity_id': str(entity_id)
        }).fetchone()
        
        if not result:
            return {
                'entity_id': str(entity_id),
                'entity_type': entity_type,
                'found': False,
                'explanation': 'Entity not found'
            }
            
        result_dict = dict(result._mapping)
        
        # Generate human-readable explanation
        explanation_parts = []
        
        keyword_score = float(result_dict.get('keyword_relevance', 0) or 0)
        semantic_score = float(result_dict.get('semantic_similarity', 0) or 0)
        
        if keyword_score > 0.5:
            explanation_parts.append(f"Strong keyword match (relevance: {keyword_score:.2f})")
        elif keyword_score > 0.1:
            explanation_parts.append(f"Moderate keyword match (relevance: {keyword_score:.2f})")
        elif keyword_score > 0:
            explanation_parts.append(f"Weak keyword match (relevance: {keyword_score:.2f})")
        
        if semantic_score > 0.8:
            explanation_parts.append(f"Very high semantic similarity ({semantic_score:.2%})")
        elif semantic_score > 0.6:
            explanation_parts.append(f"High semantic similarity ({semantic_score:.2%})")
        elif semantic_score > 0.4:
            explanation_parts.append(f"Moderate semantic similarity ({semantic_score:.2%})")
        elif semantic_score > 0.2:
            explanation_parts.append(f"Low semantic similarity ({semantic_score:.2%})")
        
        if not result_dict.get('has_embedding'):
            explanation_parts.append("Note: No semantic embeddings available for this entity")
        
        return {
            'entity_id': str(entity_id),
            'entity_type': entity_type,
            'entity_name': result_dict.get('name', ''),
            'found': True,
            'keyword_relevance': keyword_score,
            'semantic_similarity': semantic_score,
            'highlighted_text': result_dict.get('highlighted_text', ''),
            'has_embedding': result_dict.get('has_embedding', False),
            'embedding_updated_at': result_dict.get('embedding_updated_at'),
            'explanation': '; '.join(explanation_parts) if explanation_parts else 'No significant match'
        }
    
    def get_entities_without_embeddings(
        self, 
        entity_type: str, 
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Get entities that don't have embeddings yet.
        
        Args:
            entity_type: 'startup' or 'vc'
            limit: Maximum number of entities to return
            
        Returns:
            List of entities needing embeddings
        """
        
        if entity_type == 'startup':
            query = text("""
                SELECT 
                    id,
                    name,
                    sector,
                    stage,
                    description,
                    team_size,
                    monthly_revenue,
                    website
                FROM startups 
                WHERE combined_vector IS NULL 
                   OR embedding_updated_at IS NULL
                   OR embedding_updated_at < updated_at
                ORDER BY monthly_revenue DESC
                LIMIT :limit
            """)
        else:  # vc
            query = text("""
                SELECT 
                    id,
                    firm_name,
                    thesis,
                    website,
                    sectors,
                    stages,
                    check_size_min,
                    check_size_max
                FROM vcs 
                WHERE combined_vector IS NULL 
                   OR embedding_updated_at IS NULL
                   OR embedding_updated_at < updated_at
                ORDER BY check_size_max DESC
                LIMIT :limit
            """)
        
        result = self.session.execute(query, {"limit": limit})
        return [dict(row._mapping) for row in result]