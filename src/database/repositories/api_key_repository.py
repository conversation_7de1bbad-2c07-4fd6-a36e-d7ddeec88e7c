"""PostgreSQL implementation of APIKeyRepository."""

from typing import Optional
from datetime import datetime
from uuid import UUID

from sqlalchemy import select, update
from sqlalchemy.orm import Session

from src.database.models.api_key import APIKey as APIKeyDB


class PostgresAPIKeyRepository:
    """PostgreSQL implementation of API key repository."""
    
    def __init__(self, session: Session):
        """Initialize with database session."""
        self.session = session
    
    def get_by_key(self, key: str) -> Optional[APIKeyDB]:
        """Get API key by key value."""
        stmt = select(APIKeyDB).where(APIKeyDB.key == key)
        return self.session.execute(stmt).scalar_one_or_none()
    
    def validate_and_track(self, key: str) -> Optional[APIKeyDB]:
        """Validate API key and track usage."""
        api_key = self.get_by_key(key)
        
        if not api_key:
            return None
        
        if not api_key.is_valid():
            return None
        
        # Update usage stats
        stmt = (
            update(APIKeyDB)
            .where(APIKeyDB.id == api_key.id)
            .values(
                last_used_at=datetime.utcnow(),
                usage_count=APIKeyDB.usage_count + 1
            )
        )
        self.session.execute(stmt)
        self.session.commit()
        
        return api_key
    
    def create(self, key: str, name: str, user_id: UUID, description: str = None, scopes: str = None) -> APIKeyDB:
        """Create a new API key."""
        api_key = APIKeyDB(
            key=key,
            name=name,
            user_id=user_id,
            description=description,
            scopes=scopes
        )
        self.session.add(api_key)
        self.session.commit()
        self.session.refresh(api_key)
        return api_key
    
    def revoke(self, key_id: UUID) -> bool:
        """Revoke an API key."""
        stmt = (
            update(APIKeyDB)
            .where(APIKeyDB.id == key_id)
            .values(
                is_active=False,
                revoked_at=datetime.utcnow()
            )
        )
        result = self.session.execute(stmt)
        self.session.commit()
        return result.rowcount > 0