"""Base repository class with common CRUD operations."""

from typing import TypeV<PERSON>, Generic, List, Optional, Type, Dict, Any
from abc import ABC
from uuid import UUID
from datetime import datetime

from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import select, and_

from src.database.base import Base

# Generic type variables
TModel = TypeVar("TModel", bound=Base)  # Database model
TDomain = TypeVar("TDomain")  # Domain model


class BaseRepository(Generic[TModel, TDomain], ABC):
    """
    Base repository class with common CRUD operations.
    
    Provides generic implementation of common database operations
    to reduce duplication across specific repositories.
    """
    
    model_class: Type[TModel]  # To be set by subclasses
    
    def __init__(self, session: Session):
        """Initialize with database session."""
        self.session = session
    
    def _to_domain(self, db_model: TModel) -> TDomain:
        """
        Convert database model to domain model.
        Must be implemented by subclasses.
        """
        raise NotImplementedError("Subclasses must implement _to_domain")
    
    def _to_db(self, domain_model: TDomain, existing: Optional[TModel] = None) -> TModel:
        """
        Convert domain model to database model.
        Must be implemented by subclasses.
        
        Args:
            domain_model: Domain model to convert
            existing: Existing database model to update (for updates)
        """
        raise NotImplementedError("Subclasses must implement _to_db")
    
    def create(self, entity: TDomain) -> TDomain:
        """Create a new entity."""
        try:
            db_entity = self._to_db(entity)
            self.session.add(db_entity)
            self.session.commit()
            self.session.refresh(db_entity)
            return self._to_domain(db_entity)
        except SQLAlchemyError as e:
            self.session.rollback()
            raise Exception(f"Error creating entity: {str(e)}")
    
    def get(self, entity_id: UUID) -> Optional[TDomain]:
        """Get an entity by ID."""
        try:
            db_entity = self.session.query(self.model_class).filter_by(id=entity_id).first()
            return self._to_domain(db_entity) if db_entity else None
        except SQLAlchemyError as e:
            raise Exception(f"Error retrieving entity: {str(e)}")
    
    def get_by_id(self, entity_id: str) -> Optional[TDomain]:
        """Get an entity by string ID."""
        try:
            return self.get(UUID(entity_id))
        except ValueError:
            return None
    
    def list(self, limit: int = 100, offset: int = 0) -> List[TDomain]:
        """List entities with pagination."""
        try:
            db_entities = self.session.query(self.model_class)\
                .limit(limit)\
                .offset(offset)\
                .all()
            return [self._to_domain(entity) for entity in db_entities]
        except SQLAlchemyError as e:
            raise Exception(f"Error listing entities: {str(e)}")
    
    def update(self, entity: TDomain) -> TDomain:
        """Update an existing entity."""
        try:
            # Get existing entity
            db_entity = self.session.query(self.model_class).filter_by(id=entity.id).first()
            if not db_entity:
                raise ValueError(f"Entity with id {entity.id} not found")
            
            # Update fields
            updated_entity = self._to_db(entity, existing=db_entity)
            
            # Set updated_at if model has it
            if hasattr(updated_entity, 'updated_at'):
                updated_entity.updated_at = datetime.utcnow()
            
            self.session.commit()
            self.session.refresh(updated_entity)
            return self._to_domain(updated_entity)
        except SQLAlchemyError as e:
            self.session.rollback()
            raise Exception(f"Error updating entity: {str(e)}")
    
    def save(self, entity: TDomain) -> TDomain:
        """Save an entity (create or update)."""
        if hasattr(entity, 'id') and entity.id:
            # Check if exists
            existing = self.session.query(self.model_class).filter_by(id=entity.id).first()
            if existing:
                return self.update(entity)
        return self.create(entity)
    
    def delete(self, entity_id: UUID) -> bool:
        """Delete an entity by ID."""
        try:
            db_entity = self.session.query(self.model_class).filter_by(id=entity_id).first()
            if db_entity:
                self.session.delete(db_entity)
                self.session.commit()
                return True
            return False
        except SQLAlchemyError as e:
            self.session.rollback()
            raise Exception(f"Error deleting entity: {str(e)}")
    
    def exists(self, entity_id: UUID) -> bool:
        """Check if an entity exists."""
        try:
            return self.session.query(
                self.session.query(self.model_class).filter_by(id=entity_id).exists()
            ).scalar()
        except SQLAlchemyError as e:
            raise Exception(f"Error checking existence: {str(e)}")
    
    def count(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """Count entities with optional filters."""
        try:
            query = self.session.query(self.model_class)
            if filters:
                for key, value in filters.items():
                    if hasattr(self.model_class, key):
                        query = query.filter(getattr(self.model_class, key) == value)
            return query.count()
        except SQLAlchemyError as e:
            raise Exception(f"Error counting entities: {str(e)}")
    
    def find_by_field(self, field_name: str, value: Any) -> List[TDomain]:
        """Find entities by a specific field value."""
        try:
            if not hasattr(self.model_class, field_name):
                raise ValueError(f"Field {field_name} does not exist on model")
            
            db_entities = self.session.query(self.model_class).filter(
                getattr(self.model_class, field_name) == value
            ).all()
            return [self._to_domain(entity) for entity in db_entities]
        except SQLAlchemyError as e:
            raise Exception(f"Error finding by field: {str(e)}")
    
    def find_one_by_field(self, field_name: str, value: Any) -> Optional[TDomain]:
        """Find a single entity by a specific field value."""
        try:
            if not hasattr(self.model_class, field_name):
                raise ValueError(f"Field {field_name} does not exist on model")
            
            db_entity = self.session.query(self.model_class).filter(
                getattr(self.model_class, field_name) == value
            ).first()
            return self._to_domain(db_entity) if db_entity else None
        except SQLAlchemyError as e:
            raise Exception(f"Error finding by field: {str(e)}")
    
    def bulk_create(self, entities: List[TDomain]) -> List[TDomain]:
        """Create multiple entities in a single transaction."""
        try:
            db_entities = [self._to_db(entity) for entity in entities]
            self.session.bulk_save_objects(db_entities, return_defaults=True)
            self.session.commit()
            
            # Refresh and return
            created_entities = []
            for db_entity in db_entities:
                self.session.refresh(db_entity)
                created_entities.append(self._to_domain(db_entity))
            
            return created_entities
        except SQLAlchemyError as e:
            self.session.rollback()
            raise Exception(f"Error bulk creating entities: {str(e)}")
    
    def search(self, filters: Dict[str, Any], limit: int = 100, offset: int = 0) -> List[TDomain]:
        """Search entities with multiple filters."""
        try:
            query = self.session.query(self.model_class)
            
            # Apply filters
            conditions = []
            for key, value in filters.items():
                if hasattr(self.model_class, key):
                    if isinstance(value, list):
                        # Handle list values with IN clause
                        conditions.append(getattr(self.model_class, key).in_(value))
                    elif value is not None:
                        conditions.append(getattr(self.model_class, key) == value)
            
            if conditions:
                query = query.filter(and_(*conditions))
            
            # Apply pagination
            db_entities = query.limit(limit).offset(offset).all()
            return [self._to_domain(entity) for entity in db_entities]
        except SQLAlchemyError as e:
            raise Exception(f"Error searching entities: {str(e)}")