"""Wrapper to make UserRepository async-compatible."""

from typing import List, Optional
from uuid import UUID

from sqlalchemy.orm import Session

from src.core.models.user import User as UserDomain
from src.core.repositories.user_repository import UserRepository as UserRepositoryInterface
from src.database.repositories.user_repository import UserRepository
from src.database.models.user import User as UserDB


class PostgresUserRepository(UserRepositoryInterface):
    """Async wrapper for PostgreSQL user repository."""
    
    def __init__(self, session: Session):
        """Initialize with database session."""
        self.session = session
        self.repo = UserRepository()
    
    async def create(self, user: UserDomain) -> UserDomain:
        """Create a new user."""
        db_user = UserDB(
            id=user.id,
            email=user.email,
            username=user.email.split('@')[0],  # Simple username from email
            hashed_password=user.hashed_password,
            name=user.name,
            is_active=user.is_active,
            is_superuser=user.is_superuser,
            created_at=user.created_at,
            updated_at=user.updated_at
        )
        
        created = self.repo.create(self.session, db_user)
        return self._to_domain(created)
    
    async def get(self, user_id: UUID) -> Optional[UserDomain]:
        """Get user by ID."""
        db_user = self.repo.get_by_id(self.session, user_id)
        
        if db_user:
            return self._to_domain(db_user)
        return None
    
    async def get_by_email(self, email: str) -> Optional[UserDomain]:
        """Get user by email."""
        db_user = self.repo.find_by_email(self.session, email)
        
        if db_user:
            return self._to_domain(db_user)
        return None
    
    async def update(self, user: UserDomain) -> UserDomain:
        """Update user."""
        updates = {
            "email": user.email,
            "hashed_password": user.hashed_password,
            "name": user.name,
            "is_active": user.is_active,
            "is_superuser": user.is_superuser,
            "updated_at": user.updated_at
        }
        
        updated = self.repo.update(self.session, user.id, updates)
        if not updated:
            raise ValueError(f"User {user.id} not found")
        
        return self._to_domain(updated)
    
    async def delete(self, user_id: UUID) -> bool:
        """Delete user."""
        return self.repo.delete(self.session, user_id)
    
    async def list(self, skip: int = 0, limit: int = 100) -> List[UserDomain]:
        """List users with pagination."""
        # Simple implementation - get all and paginate
        all_users = self.repo.get_active_users(self.session)
        paginated = all_users[skip:skip + limit]
        
        return [self._to_domain(user) for user in paginated]
    
    async def get_users_by_startup(self, startup_id: UUID) -> List[UserDomain]:
        """Get all users associated with a startup."""
        db_users = self.repo.get_users_by_startup(self.session, startup_id)
        return [self._to_domain(user) for user in db_users]
    
    async def get_users_by_vc(self, vc_id: UUID) -> List[UserDomain]:
        """Get all users associated with a VC firm."""
        db_users = self.repo.get_users_by_vc(self.session, vc_id)
        return [self._to_domain(user) for user in db_users]
    
    def _to_domain(self, db_user: UserDB) -> UserDomain:
        """Convert database model to domain model."""
        return UserDomain(
            id=db_user.id,
            email=db_user.email,
            hashed_password=db_user.hashed_password,
            name=db_user.name or db_user.username,  # Fallback to username if name is empty
            is_active=db_user.is_active,
            is_superuser=db_user.is_superuser,
            created_at=db_user.created_at,
            updated_at=db_user.updated_at
        )