"""Async repository for Startup entities."""

from typing import List, Optional
from uuid import UUID
import logging

from sqlalchemy import select, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from src.database.repositories.async_base import AsyncRepository
from src.database.models.startup import Startup
from src.core.repositories.startup_repository import StartupRepository as IStartupRepository
from src.core.models.startup import Startup as StartupDomainModel

logger = logging.getLogger(__name__)


class AsyncStartupRepository(AsyncRepository[Startup], IStartupRepository):
    """
    Async repository for Startup entities.
    
    Implements the startup repository interface using async SQLAlchemy.
    """
    
    def __init__(self, session: AsyncSession):
        """Initialize with Startup model and session."""
        super().__init__(Startup, session)
    
    async def save(self, startup: StartupDomainModel) -> StartupDomainModel:
        """
        Save a startup (create or update).
        
        Args:
            startup: Domain model startup
            
        Returns:
            Saved startup domain model
        """
        try:
            # Check if startup exists
            if startup.id:
                existing = await self.get(startup.id)
                if existing:
                    # Update existing
                    updated = await self.update(
                        startup.id,
                        name=startup.name,
                        sector=startup.sector,
                        stage=startup.stage,
                        description=startup.description,
                        website=startup.website,
                        team_size=startup.team_size,
                        monthly_revenue=startup.monthly_revenue
                    )
                    return self._to_domain_model(updated)
            
            # Create new
            db_startup = await self.create(
                id=startup.id,
                name=startup.name,
                sector=startup.sector,
                stage=startup.stage,
                description=startup.description,
                website=startup.website,
                team_size=startup.team_size,
                monthly_revenue=startup.monthly_revenue
            )
            return self._to_domain_model(db_startup)
            
        except Exception as e:
            logger.error(f"Error saving startup: {e}")
            raise
    
    async def find_by_id(self, startup_id: UUID) -> Optional[StartupDomainModel]:
        """
        Find a startup by ID.
        
        Args:
            startup_id: Startup UUID
            
        Returns:
            Startup domain model or None
        """
        db_startup = await self.get(startup_id)
        return self._to_domain_model(db_startup) if db_startup else None
    
    async def find_all(self) -> List[StartupDomainModel]:
        """
        Get all startups.
        
        Returns:
            List of startup domain models
        """
        db_startups = await self.get_all()
        return [self._to_domain_model(s) for s in db_startups]
    
    async def find_by_sector(self, sector: str) -> List[StartupDomainModel]:
        """
        Find startups by sector.
        
        Args:
            sector: Sector name
            
        Returns:
            List of startup domain models
        """
        db_startups = await self.find_all_by(sector=sector)
        return [self._to_domain_model(s) for s in db_startups]
    
    async def find_by_stage(self, stage: str) -> List[StartupDomainModel]:
        """
        Find startups by funding stage.
        
        Args:
            stage: Funding stage
            
        Returns:
            List of startup domain models
        """
        db_startups = await self.find_all_by(stage=stage)
        return [self._to_domain_model(s) for s in db_startups]
    
    async def search(self, query: str) -> List[StartupDomainModel]:
        """
        Search startups by name or description.
        
        Args:
            query: Search query
            
        Returns:
            List of matching startup domain models
        """
        try:
            search_term = f"%{query}%"
            stmt = select(self.model).where(
                or_(
                    self.model.name.ilike(search_term),
                    self.model.description.ilike(search_term)
                )
            )
            
            result = await self.session.execute(stmt)
            db_startups = list(result.scalars().all())
            return [self._to_domain_model(s) for s in db_startups]
            
        except Exception as e:
            logger.error(f"Error searching startups: {e}")
            raise
    
    async def delete(self, startup_id: UUID) -> bool:
        """
        Delete a startup by ID.
        
        Args:
            startup_id: Startup UUID
            
        Returns:
            True if deleted, False if not found
        """
        return await super().delete(startup_id)
    
    async def get_with_matches(self, startup_id: UUID) -> Optional[Startup]:
        """
        Get a startup with its matches eagerly loaded.
        
        Args:
            startup_id: Startup UUID
            
        Returns:
            Startup with matches or None
        """
        try:
            stmt = (
                select(self.model)
                .where(self.model.id == startup_id)
                .options(selectinload(self.model.matches))
            )
            
            result = await self.session.execute(stmt)
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error(f"Error getting startup with matches: {e}")
            raise
    
    def _to_domain_model(self, db_model: Startup) -> StartupDomainModel:
        """
        Convert database model to domain model.
        
        Args:
            db_model: Database startup model
            
        Returns:
            Domain startup model
        """
        return StartupDomainModel(
            id=db_model.id,
            name=db_model.name,
            sector=db_model.sector,
            stage=db_model.stage,
            description=db_model.description,
            website=db_model.website,
            team_size=db_model.team_size,
            monthly_revenue=db_model.monthly_revenue
        )