"""Async repository for Match entities."""

from typing import List, Optional
from uuid import UUID
import logging

from sqlalchemy import select, and_, or_, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from src.database.repositories.async_base import AsyncRepository
from src.database.models.match import Match
from src.database.models.startup import Startup
from src.database.models.vc import VC
from src.core.repositories.match_repository import MatchRepository as IMatchRepository
from src.core.models.match import Match as MatchDomainModel
from src.core.models.startup import Startup as StartupDomainModel
from src.core.models.vc import VC as VCDomainModel
from src.core.schemas.match import MatchStatus, MatchType

logger = logging.getLogger(__name__)


class AsyncMatchRepository(AsyncRepository[Match], IMatchRepository):
    """
    Async repository for Match entities.
    
    Implements the match repository interface using async SQLAlchemy.
    """
    
    def __init__(self, session: AsyncSession):
        """Initialize with Match model and session."""
        super().__init__(Match, session)
    
    async def create(self, match: MatchDomainModel) -> MatchDomainModel:
        """
        Create a new match.
        
        Args:
            match: Domain model match
            
        Returns:
            Created match domain model
        """
        try:
            # Create match record with foreign keys
            db_match = await super().create(
                id=match.id,
                startup_id=match.startup.id,
                vc_id=match.vc.id,
                score=match.score,
                reasons=match.reasons,
                status=match.status.value if match.status else MatchStatus.PENDING.value,
                match_type=match.match_type.value if match.match_type else None,
                notes=match.notes
            )
            
            # Load relationships for complete domain model
            return await self._get_complete_match(db_match.id)
            
        except Exception as e:
            logger.error(f"Error creating match: {e}")
            raise
    
    async def get(self, match_id: UUID) -> Optional[MatchDomainModel]:
        """
        Get a match by ID with relationships.
        
        Args:
            match_id: Match UUID
            
        Returns:
            Match domain model or None
        """
        return await self._get_complete_match(match_id)
    
    async def exists(self, startup_id: UUID, vc_id: UUID) -> bool:
        """
        Check if a match exists between startup and VC.
        
        Args:
            startup_id: Startup UUID
            vc_id: VC UUID
            
        Returns:
            True if match exists
        """
        try:
            stmt = select(func.count()).select_from(self.model).where(
                and_(
                    self.model.startup_id == startup_id,
                    self.model.vc_id == vc_id
                )
            )
            result = await self.session.execute(stmt)
            return (result.scalar() or 0) > 0
            
        except Exception as e:
            logger.error(f"Error checking match existence: {e}")
            raise
    
    async def find_by_startup(self, startup_id: UUID) -> List[MatchDomainModel]:
        """
        Find all matches for a startup.
        
        Args:
            startup_id: Startup UUID
            
        Returns:
            List of match domain models
        """
        try:
            stmt = (
                select(self.model)
                .where(self.model.startup_id == startup_id)
                .options(
                    selectinload(self.model.startup),
                    selectinload(self.model.vc)
                )
                .order_by(self.model.score.desc())
            )
            
            result = await self.session.execute(stmt)
            db_matches = list(result.scalars().all())
            return [self._to_domain_model(m) for m in db_matches]
            
        except Exception as e:
            logger.error(f"Error finding matches by startup: {e}")
            raise
    
    async def list_by_startup(self, startup_id: UUID) -> List[MatchDomainModel]:
        """List all matches for a startup (interface method)."""
        return await self.find_by_startup(startup_id)
    
    async def list_by_vc(self, vc_id: UUID) -> List[MatchDomainModel]:
        """List all matches for a VC (interface method)."""
        return await self.find_by_vc(vc_id)
    
    async def update_score(self, match_id: UUID, score: float) -> Optional[MatchDomainModel]:
        """Update the score of an existing match."""
        try:
            updated = await self.update(match_id, score=score)
            if updated:
                return await self._get_complete_match(match_id)
            return None
        except Exception as e:
            logger.error(f"Error updating match score: {e}")
            raise
    
    async def find_by_vc(self, vc_id: UUID) -> List[MatchDomainModel]:
        """
        Find all matches for a VC.
        
        Args:
            vc_id: VC UUID
            
        Returns:
            List of match domain models
        """
        try:
            stmt = (
                select(self.model)
                .where(self.model.vc_id == vc_id)
                .options(
                    selectinload(self.model.startup),
                    selectinload(self.model.vc)
                )
                .order_by(self.model.score.desc())
            )
            
            result = await self.session.execute(stmt)
            db_matches = list(result.scalars().all())
            return [self._to_domain_model(m) for m in db_matches]
            
        except Exception as e:
            logger.error(f"Error finding matches by VC: {e}")
            raise
    
    async def find_by_status(self, status: MatchStatus) -> List[MatchDomainModel]:
        """
        Find matches by status.
        
        Args:
            status: Match status
            
        Returns:
            List of match domain models
        """
        try:
            stmt = (
                select(self.model)
                .where(self.model.status == status.value)
                .options(
                    selectinload(self.model.startup),
                    selectinload(self.model.vc)
                )
                .order_by(self.model.created_at.desc())
            )
            
            result = await self.session.execute(stmt)
            db_matches = list(result.scalars().all())
            return [self._to_domain_model(m) for m in db_matches]
            
        except Exception as e:
            logger.error(f"Error finding matches by status: {e}")
            raise
    
    async def find_with_filters(
        self,
        startup_id: Optional[UUID] = None,
        vc_id: Optional[UUID] = None,
        status: Optional[MatchStatus] = None,
        min_score: Optional[float] = None,
        match_type: Optional[MatchType] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[MatchDomainModel]:
        """
        Find matches with multiple filters.
        
        Args:
            startup_id: Filter by startup
            vc_id: Filter by VC
            status: Filter by status
            min_score: Minimum score filter
            match_type: Filter by match type
            limit: Maximum results
            offset: Results offset
            
        Returns:
            List of match domain models
        """
        try:
            stmt = select(self.model).options(
                selectinload(self.model.startup),
                selectinload(self.model.vc)
            )
            
            # Apply filters
            conditions = []
            if startup_id:
                conditions.append(self.model.startup_id == startup_id)
            if vc_id:
                conditions.append(self.model.vc_id == vc_id)
            if status:
                conditions.append(self.model.status == status.value)
            if min_score is not None:
                conditions.append(self.model.score >= min_score)
            if match_type:
                conditions.append(self.model.match_type == match_type.value)
            
            if conditions:
                stmt = stmt.where(and_(*conditions))
            
            # Order and paginate
            stmt = stmt.order_by(self.model.score.desc()).limit(limit).offset(offset)
            
            result = await self.session.execute(stmt)
            db_matches = list(result.scalars().all())
            return [self._to_domain_model(m) for m in db_matches]
            
        except Exception as e:
            logger.error(f"Error finding matches with filters: {e}")
            raise
    
    async def update_status(self, match_id: UUID, status: MatchStatus) -> Optional[MatchDomainModel]:
        """
        Update match status.
        
        Args:
            match_id: Match UUID
            status: New status
            
        Returns:
            Updated match or None
        """
        try:
            updated = await self.update(match_id, status=status.value)
            if updated:
                return await self._get_complete_match(match_id)
            return None
            
        except Exception as e:
            logger.error(f"Error updating match status: {e}")
            raise
    
    async def delete(self, match_id: UUID) -> bool:
        """
        Delete a match by ID.
        
        Args:
            match_id: Match UUID
            
        Returns:
            True if deleted, False if not found
        """
        return await super().delete(match_id)
    
    async def _get_complete_match(self, match_id: UUID) -> Optional[MatchDomainModel]:
        """
        Get a match with all relationships loaded.
        
        Args:
            match_id: Match UUID
            
        Returns:
            Complete match domain model or None
        """
        try:
            stmt = (
                select(self.model)
                .where(self.model.id == match_id)
                .options(
                    selectinload(self.model.startup),
                    selectinload(self.model.vc)
                )
            )
            
            result = await self.session.execute(stmt)
            db_match = result.scalar_one_or_none()
            
            return self._to_domain_model(db_match) if db_match else None
            
        except Exception as e:
            logger.error(f"Error getting complete match: {e}")
            raise
    
    def _to_domain_model(self, db_model: Match) -> MatchDomainModel:
        """
        Convert database model to domain model.
        
        Args:
            db_model: Database match model with loaded relationships
            
        Returns:
            Domain match model
        """
        # Convert related models
        startup_domain = StartupDomainModel(
            id=db_model.startup.id,
            name=db_model.startup.name,
            sector=db_model.startup.sector,
            stage=db_model.startup.stage,
            description=db_model.startup.description,
            website=db_model.startup.website,
            team_size=db_model.startup.team_size,
            monthly_revenue=db_model.startup.monthly_revenue
        )
        
        vc_domain = VCDomainModel(
            id=db_model.vc.id,
            firm_name=db_model.vc.firm_name,
            website=db_model.vc.website,
            thesis=db_model.vc.thesis,
            sectors=db_model.vc.sectors or [],
            stages=db_model.vc.stages or [],
            check_size_min=db_model.vc.check_size_min,
            check_size_max=db_model.vc.check_size_max
        )
        
        return MatchDomainModel(
            id=db_model.id,
            startup=startup_domain,
            vc=vc_domain,
            score=db_model.score,
            reasons=db_model.reasons or [],
            status=MatchStatus(db_model.status) if db_model.status else MatchStatus.PENDING,
            match_type=MatchType(db_model.match_type) if db_model.match_type else None,
            notes=db_model.notes,
            created_at=db_model.created_at,
            updated_at=db_model.updated_at
        )