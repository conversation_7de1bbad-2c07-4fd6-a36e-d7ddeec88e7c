"""PostgreSQL implementation of StartupRepository."""

from typing import List, Optional, Dict, Any
from uuid import UUID

from sqlalchemy import select, and_, or_, func, text, desc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from src.core.models.startup import Startup as StartupDomain
from src.core.repositories.startup_repository import StartupRepository
from src.database.models.startup import Startup as StartupDB


class PostgresStartupRepository(StartupRepository):
    """PostgreSQL implementation of startup repository."""
    
    def __init__(self, session: Session):
        """Initialize with database session."""
        self.session = session
    
    async def save(self, startup: StartupDomain) -> StartupDomain:
        """Save a startup entity."""
        return self.create(startup)
    
    async def find_by_id(self, startup_id: UUID) -> Optional[StartupDomain]:
        """Find a startup by ID."""
        return self.get(startup_id)
    
    async def find_by_sector(self, sector: str) -> List[StartupDomain]:
        """Find all startups in a specific sector."""
        return self.search_by_sector(sector)
    
    async def find_by_stage(self, stage: str) -> List[StartupDomain]:
        """Find all startups at a specific funding stage."""
        return self.search_by_stage(stage)
    
    async def find_all(self) -> List[StartupDomain]:
        """Retrieve all startups."""
        db_startups = self.session.query(StartupDB).all()
        return [self._to_domain(startup) for startup in db_startups]
    
    async def delete(self, startup_id: UUID) -> bool:
        """Delete a startup by its ID."""
        return self._delete_sync(startup_id)
    
    def create(self, startup: StartupDomain) -> StartupDomain:
        """Create a new startup."""
        # Generate ID if not provided
        if not startup.id:
            import uuid
            startup.id = uuid.uuid4()
            
        # Convert domain model to DB model
        db_startup = StartupDB(
            id=startup.id,
            name=startup.name,
            sector=startup.sector,
            stage=startup.stage,
            description=startup.description,
            website=startup.website,
            team_size=startup.team_size,
            monthly_revenue=startup.monthly_revenue
        )
        
        self.session.add(db_startup)
        self.session.commit()
        self.session.refresh(db_startup)
        
        # Convert back to domain model
        return self._to_domain(db_startup)
    
    def get(self, startup_id: UUID) -> Optional[StartupDomain]:
        """Get a startup by ID."""
        db_startup = self.session.query(StartupDB).filter_by(id=startup_id).first()
        
        if db_startup is None:
            return None
            
        return self._to_domain(db_startup)
    
    def list(
        self, 
        sector: Optional[str] = None,
        stage: Optional[str] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[StartupDomain]:
        """List startups with optional filters."""
        query = self.session.query(StartupDB)
        
        if sector:
            query = query.filter(StartupDB.sector == sector)
        if stage:
            query = query.filter(StartupDB.stage == stage)
            
        db_startups = query.offset(offset).limit(limit).all()
        
        return [self._to_domain(startup) for startup in db_startups]
    
    def update(self, startup_id: UUID, startup: StartupDomain) -> Optional[StartupDomain]:
        """Update a startup."""
        db_startup = self.session.query(StartupDB).filter_by(id=startup_id).first()
        
        if db_startup is None:
            return None
        
        # Update fields
        db_startup.name = startup.name
        db_startup.sector = startup.sector
        db_startup.stage = startup.stage
        db_startup.description = startup.description
        db_startup.website = startup.website
        db_startup.team_size = startup.team_size
        db_startup.monthly_revenue = startup.monthly_revenue
        
        self.session.commit()
        self.session.refresh(db_startup)
        
        return self._to_domain(db_startup)
    
    def _delete_sync(self, startup_id: UUID) -> bool:
        """Delete a startup."""
        db_startup = self.session.query(StartupDB).filter_by(id=startup_id).first()
        
        if db_startup is None:
            return False
            
        self.session.delete(db_startup)
        self.session.commit()
        
        return True
    
    def search(self, query: str) -> List[StartupDomain]:
        """Search startups by name or description."""
        # Simple LIKE search for now
        search_pattern = f"%{query}%"
        
        db_startups = self.session.query(StartupDB).filter(
            (StartupDB.name.ilike(search_pattern)) |
            (StartupDB.description.ilike(search_pattern))
        ).limit(50).all()
        
        return [self._to_domain(startup) for startup in db_startups]
    
    def search_by_sector(self, sector: str) -> List[StartupDomain]:
        """Search startups by sector."""
        db_startups = self.session.query(StartupDB).filter(
            StartupDB.sector == sector
        ).all()
        
        return [self._to_domain(startup) for startup in db_startups]
    
    def search_by_stage(self, stage: str) -> List[StartupDomain]:
        """Search startups by stage."""
        db_startups = self.session.query(StartupDB).filter(
            StartupDB.stage == stage
        ).all()
        
        return [self._to_domain(startup) for startup in db_startups]
    
    def _to_domain(self, db_startup: StartupDB) -> StartupDomain:
        """Convert database model to domain model."""
        domain_startup = StartupDomain(
            name=db_startup.name,
            sector=db_startup.sector,
            stage=db_startup.stage,
            description=db_startup.description or "",
            website=db_startup.website or "",
            team_size=db_startup.team_size,
            monthly_revenue=db_startup.monthly_revenue
        )
        domain_startup.id = db_startup.id
        
        return domain_startup
    
    def find_for_vc_discovery(
        self,
        vc_sectors: List[str],
        vc_stages: List[str],
        min_funding: float,
        max_funding: float,
        thesis_keywords: List[str] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[StartupDomain]:
        """Optimized query to find startups matching VC criteria using database filtering."""
        query = self.session.query(StartupDB)
        
        # Sector filtering using index
        if vc_sectors:
            query = query.filter(StartupDB.sector.in_(vc_sectors))
        
        # Stage filtering using index
        if vc_stages:
            query = query.filter(StartupDB.stage.in_(vc_stages))
        
        # Funding range filtering based on estimated needs
        # Using CASE statement that matches our expression index
        if min_funding > 0 or max_funding > 0:
            conditions = []
            
            if min_funding > 0:
                conditions.append(text("""
                    (CASE 
                        WHEN stage = 'Pre-seed' THEN 500000 * (CASE WHEN monthly_revenue > 100000 THEN 1.5 ELSE 1 END)
                        WHEN stage = 'Seed' THEN 2000000 * (CASE WHEN monthly_revenue > 100000 THEN 1.5 ELSE 1 END)
                        WHEN stage = 'Series A' THEN 10000000 * (CASE WHEN monthly_revenue > 500000 THEN 2 WHEN monthly_revenue > 100000 THEN 1.5 ELSE 1 END)
                        WHEN stage = 'Series B' THEN 25000000 * (CASE WHEN monthly_revenue > 500000 THEN 2 WHEN monthly_revenue > 100000 THEN 1.5 ELSE 1 END)
                        WHEN stage = 'Series C' THEN 50000000 * (CASE WHEN monthly_revenue > 500000 THEN 2 WHEN monthly_revenue > 100000 THEN 1.5 ELSE 1 END)
                        WHEN stage = 'Growth' THEN 100000000 * (CASE WHEN monthly_revenue > 500000 THEN 2 WHEN monthly_revenue > 100000 THEN 1.5 ELSE 1 END)
                        ELSE 5000000
                    END) >= :min_funding
                """).params(min_funding=min_funding))
            
            if max_funding > 0:
                conditions.append(text("""
                    (CASE 
                        WHEN stage = 'Pre-seed' THEN 500000 * (CASE WHEN monthly_revenue > 100000 THEN 1.5 ELSE 1 END)
                        WHEN stage = 'Seed' THEN 2000000 * (CASE WHEN monthly_revenue > 100000 THEN 1.5 ELSE 1 END)
                        WHEN stage = 'Series A' THEN 10000000 * (CASE WHEN monthly_revenue > 500000 THEN 2 WHEN monthly_revenue > 100000 THEN 1.5 ELSE 1 END)
                        WHEN stage = 'Series B' THEN 25000000 * (CASE WHEN monthly_revenue > 500000 THEN 2 WHEN monthly_revenue > 100000 THEN 1.5 ELSE 1 END)
                        WHEN stage = 'Series C' THEN 50000000 * (CASE WHEN monthly_revenue > 500000 THEN 2 WHEN monthly_revenue > 100000 THEN 1.5 ELSE 1 END)
                        WHEN stage = 'Growth' THEN 100000000 * (CASE WHEN monthly_revenue > 500000 THEN 2 WHEN monthly_revenue > 100000 THEN 1.5 ELSE 1 END)
                        ELSE 5000000
                    END) <= :max_funding
                """).params(max_funding=max_funding))
            
            # Apply all funding conditions
            for condition in conditions:
                query = query.filter(condition)
        
        # Full-text search on description for thesis alignment
        if thesis_keywords:
            # Use the full-text search index we created
            search_query = ' | '.join(thesis_keywords)  # OR search
            query = query.filter(
                text("to_tsvector('english', COALESCE(description, '')) @@ plainto_tsquery('english', :search)")
            ).params(search=search_query)
        
        # Only active startups (those with some data)
        query = query.filter(StartupDB.monthly_revenue >= 0)  # Use partial index
        
        # Order by stage priority and revenue for consistent results
        stage_priority = text("""
            CASE 
                WHEN stage = 'Pre-seed' THEN 1
                WHEN stage = 'Seed' THEN 2
                WHEN stage = 'Series A' THEN 3
                WHEN stage = 'Series B' THEN 4
                WHEN stage = 'Series C' THEN 5
                WHEN stage = 'Growth' THEN 6
                ELSE 0
            END
        """)
        
        query = query.order_by(desc(stage_priority), StartupDB.monthly_revenue.desc())
        
        # Apply pagination
        db_startups = query.offset(offset).limit(limit).all()
        
        return [self._to_domain(startup) for startup in db_startups]
    
    def count_for_vc_discovery(
        self,
        vc_sectors: List[str],
        vc_stages: List[str],
        min_funding: float,
        max_funding: float,
        thesis_keywords: List[str] = None
    ) -> int:
        """Count startups matching VC criteria for pagination."""
        query = self.session.query(func.count(StartupDB.id))
        
        # Apply same filters as find_for_vc_discovery
        if vc_sectors:
            query = query.filter(StartupDB.sector.in_(vc_sectors))
        
        if vc_stages:
            query = query.filter(StartupDB.stage.in_(vc_stages))
        
        if min_funding > 0 or max_funding > 0:
            conditions = []
            
            if min_funding > 0:
                conditions.append(text("""
                    (CASE 
                        WHEN stage = 'Pre-seed' THEN 500000 * (CASE WHEN monthly_revenue > 100000 THEN 1.5 ELSE 1 END)
                        WHEN stage = 'Seed' THEN 2000000 * (CASE WHEN monthly_revenue > 100000 THEN 1.5 ELSE 1 END)
                        WHEN stage = 'Series A' THEN 10000000 * (CASE WHEN monthly_revenue > 500000 THEN 2 WHEN monthly_revenue > 100000 THEN 1.5 ELSE 1 END)
                        WHEN stage = 'Series B' THEN 25000000 * (CASE WHEN monthly_revenue > 500000 THEN 2 WHEN monthly_revenue > 100000 THEN 1.5 ELSE 1 END)
                        WHEN stage = 'Series C' THEN 50000000 * (CASE WHEN monthly_revenue > 500000 THEN 2 WHEN monthly_revenue > 100000 THEN 1.5 ELSE 1 END)
                        WHEN stage = 'Growth' THEN 100000000 * (CASE WHEN monthly_revenue > 500000 THEN 2 WHEN monthly_revenue > 100000 THEN 1.5 ELSE 1 END)
                        ELSE 5000000
                    END) >= :min_funding
                """).params(min_funding=min_funding))
            
            if max_funding > 0:
                conditions.append(text("""
                    (CASE 
                        WHEN stage = 'Pre-seed' THEN 500000 * (CASE WHEN monthly_revenue > 100000 THEN 1.5 ELSE 1 END)
                        WHEN stage = 'Seed' THEN 2000000 * (CASE WHEN monthly_revenue > 100000 THEN 1.5 ELSE 1 END)
                        WHEN stage = 'Series A' THEN 10000000 * (CASE WHEN monthly_revenue > 500000 THEN 2 WHEN monthly_revenue > 100000 THEN 1.5 ELSE 1 END)
                        WHEN stage = 'Series B' THEN 25000000 * (CASE WHEN monthly_revenue > 500000 THEN 2 WHEN monthly_revenue > 100000 THEN 1.5 ELSE 1 END)
                        WHEN stage = 'Series C' THEN 50000000 * (CASE WHEN monthly_revenue > 500000 THEN 2 WHEN monthly_revenue > 100000 THEN 1.5 ELSE 1 END)
                        WHEN stage = 'Growth' THEN 100000000 * (CASE WHEN monthly_revenue > 500000 THEN 2 WHEN monthly_revenue > 100000 THEN 1.5 ELSE 1 END)
                        ELSE 5000000
                    END) <= :max_funding
                """).params(max_funding=max_funding))
            
            # Apply all funding conditions
            for condition in conditions:
                query = query.filter(condition)
        
        if thesis_keywords:
            search_query = ' | '.join(thesis_keywords)
            query = query.filter(
                text("to_tsvector('english', COALESCE(description, '')) @@ plainto_tsquery('english', :search)")
            ).params(search=search_query)
        
        query = query.filter(StartupDB.monthly_revenue >= 0)
        
        return query.scalar() or 0


class AsyncPostgresStartupRepository(StartupRepository):
    """Async PostgreSQL implementation of startup repository."""
    
    def __init__(self, session: AsyncSession):
        """Initialize with async database session."""
        self.session = session
    
    async def create(self, startup: StartupDomain) -> StartupDomain:
        """Create a new startup."""
        db_startup = StartupDB(
            id=startup.id,
            name=startup.name,
            sector=startup.sector,
            stage=startup.stage,
            description=startup.description,
            website=startup.website,
            team_size=startup.team_size,
            monthly_revenue=startup.monthly_revenue
        )
        
        self.session.add(db_startup)
        await self.session.commit()
        await self.session.refresh(db_startup)
        
        return self._to_domain(db_startup)
    
    async def get(self, startup_id: UUID) -> Optional[StartupDomain]:
        """Get a startup by ID."""
        result = await self.session.execute(
            select(StartupDB).filter_by(id=startup_id)
        )
        db_startup = result.scalar_one_or_none()
        
        if db_startup is None:
            return None
            
        return self._to_domain(db_startup)
    
    async def list(
        self, 
        sector: Optional[str] = None,
        stage: Optional[str] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[StartupDomain]:
        """List startups with optional filters."""
        query = select(StartupDB)
        
        if sector:
            query = query.filter(StartupDB.sector == sector)
        if stage:
            query = query.filter(StartupDB.stage == stage)
            
        query = query.offset(offset).limit(limit)
        
        result = await self.session.execute(query)
        db_startups = result.scalars().all()
        
        return [self._to_domain(startup) for startup in db_startups]
    
    async def update(self, startup: StartupDomain) -> StartupDomain:
        """Update a startup."""
        result = await self.session.execute(
            select(StartupDB).filter_by(id=startup.id)
        )
        db_startup = result.scalar_one_or_none()
        
        if db_startup is None:
            raise ValueError(f"Startup with id {startup.id} not found")
        
        # Update fields
        db_startup.name = startup.name
        db_startup.sector = startup.sector
        db_startup.stage = startup.stage
        db_startup.description = startup.description
        db_startup.website = startup.website
        db_startup.team_size = startup.team_size
        db_startup.monthly_revenue = startup.monthly_revenue
        
        await self.session.commit()
        await self.session.refresh(db_startup)
        
        return self._to_domain(db_startup)
    
    async def delete(self, startup_id: UUID) -> bool:
        """Delete a startup."""
        result = await self.session.execute(
            select(StartupDB).filter_by(id=startup_id)
        )
        db_startup = result.scalar_one_or_none()
        
        if db_startup is None:
            return False
            
        await self.session.delete(db_startup)
        await self.session.commit()
        
        return True
    
    async def search(self, query: str) -> List[StartupDomain]:
        """Search startups by name or description."""
        search_pattern = f"%{query}%"
        
        result = await self.session.execute(
            select(StartupDB).filter(
                (StartupDB.name.ilike(search_pattern)) |
                (StartupDB.description.ilike(search_pattern))
            ).limit(50)
        )
        db_startups = result.scalars().all()
        
        return [self._to_domain(startup) for startup in db_startups]
    
    def _to_domain(self, db_startup: StartupDB) -> StartupDomain:
        """Convert database model to domain model."""
        domain_startup = StartupDomain(
            name=db_startup.name,
            sector=db_startup.sector,
            stage=db_startup.stage,
            description=db_startup.description or "",
            website=db_startup.website or "",
            team_size=db_startup.team_size,
            monthly_revenue=db_startup.monthly_revenue
        )
        domain_startup.id = db_startup.id
        
        return domain_startup