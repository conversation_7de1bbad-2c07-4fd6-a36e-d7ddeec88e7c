"""PostgreSQL implementation of VCRepository using BaseRepository."""

from typing import List, Optional
from uuid import UUID

from sqlalchemy.orm import Session

from src.core.models.vc import VC as VCDomain
from src.core.repositories.vc_repository import VCRepository
from src.database.models.vc import VC as VCDB
from src.database.repositories.base_repository import BaseRepository


class PostgresVCRepository(BaseRepository[VCDB, VCDomain], VCRepository):
    """PostgreSQL implementation of VC repository using base repository."""
    
    model_class = VCDB
    
    def __init__(self, session: Session):
        """Initialize with database session."""
        super().__init__(session)
    
    def _to_domain(self, db_vc: VCDB) -> VCDomain:
        """Convert database model to domain model."""
        return VCDomain(
            id=db_vc.id,
            firm_name=db_vc.firm_name,
            website=db_vc.website,
            thesis=db_vc.thesis,
            check_size_min=db_vc.check_size_min,
            check_size_max=db_vc.check_size_max,
            sectors=db_vc.sectors,
            stages=db_vc.stages,
            description=db_vc.description,
            linkedin_url=db_vc.linkedin_url,
            twitter_url=db_vc.twitter_url,
            contact_email=db_vc.contact_email,
            partner_names=db_vc.partner_names,
            portfolio_companies=db_vc.portfolio_companies,
            metadata=db_vc.metadata
        )
    
    def _to_db(self, vc: VCDomain, existing: Optional[VCDB] = None) -> VCDB:
        """Convert domain model to database model."""
        if existing:
            # Update existing model
            existing.firm_name = vc.firm_name
            existing.website = vc.website
            existing.thesis = vc.thesis
            existing.check_size_min = vc.check_size_min
            existing.check_size_max = vc.check_size_max
            existing.sectors = vc.sectors
            existing.stages = vc.stages
            existing.description = vc.description
            existing.linkedin_url = vc.linkedin_url
            existing.twitter_url = vc.twitter_url
            existing.contact_email = vc.contact_email
            existing.partner_names = vc.partner_names
            existing.portfolio_companies = vc.portfolio_companies
            existing.metadata = vc.metadata
            return existing
        else:
            # Create new model
            return VCDB(
                id=vc.id,
                firm_name=vc.firm_name,
                website=vc.website,
                thesis=vc.thesis,
                check_size_min=vc.check_size_min,
                check_size_max=vc.check_size_max,
                sectors=vc.sectors,
                stages=vc.stages,
                description=vc.description,
                linkedin_url=vc.linkedin_url,
                twitter_url=vc.twitter_url,
                contact_email=vc.contact_email,
                partner_names=vc.partner_names,
                portfolio_companies=vc.portfolio_companies,
                metadata=vc.metadata
            )
    
    # VCRepository interface implementation
    async def save(self, vc: VCDomain) -> VCDomain:
        """Save a VC entity."""
        return super().save(vc)
    
    async def find_by_id(self, vc_id: UUID) -> Optional[VCDomain]:
        """Find a VC by ID."""
        return super().get(vc_id)
    
    async def find_by_sector(self, sector: str) -> List[VCDomain]:
        """Find all VCs interested in a specific sector."""
        # Use search to handle array field
        db_vcs = self.session.query(VCDB).filter(
            VCDB.sectors.contains([sector])
        ).all()
        return [self._to_domain(vc) for vc in db_vcs]
    
    async def find_by_check_size_range(self, min_size: float, max_size: float) -> List[VCDomain]:
        """Find VCs within a check size range."""
        db_vcs = self.session.query(VCDB).filter(
            VCDB.check_size_min >= min_size,
            VCDB.check_size_max <= max_size
        ).all()
        return [self._to_domain(vc) for vc in db_vcs]
    
    async def find_all(self) -> List[VCDomain]:
        """Retrieve all VCs."""
        return super().list(limit=1000)  # Set a reasonable limit
    
    async def delete(self, vc_id: UUID) -> bool:
        """Delete a VC by its ID."""
        return super().delete(vc_id)
    
    # Additional convenience methods
    def search_by_name(self, name: str) -> List[VCDomain]:
        """Search VCs by firm name (partial match)."""
        db_vcs = self.session.query(VCDB).filter(
            VCDB.firm_name.ilike(f"%{name}%")
        ).all()
        return [self._to_domain(vc) for vc in db_vcs]
    
    def find_by_sectors(self, sectors: List[str]) -> List[VCDomain]:
        """Find VCs interested in any of the given sectors."""
        db_vcs = self.session.query(VCDB).filter(
            VCDB.sectors.overlap(sectors)
        ).all()
        return [self._to_domain(vc) for vc in db_vcs]
    
    def find_by_stages(self, stages: List[str]) -> List[VCDomain]:
        """Find VCs interested in any of the given stages."""
        db_vcs = self.session.query(VCDB).filter(
            VCDB.stages.overlap(stages)
        ).all()
        return [self._to_domain(vc) for vc in db_vcs]
    
    def search(self, filters: dict) -> List[VCDomain]:
        """Search VCs with multiple filters."""
        query = self.session.query(VCDB)
        
        # Handle special array fields
        if 'sectors' in filters:
            sectors = filters.pop('sectors')
            if isinstance(sectors, list):
                query = query.filter(VCDB.sectors.overlap(sectors))
        
        if 'stages' in filters:
            stages = filters.pop('stages')
            if isinstance(stages, list):
                query = query.filter(VCDB.stages.overlap(stages))
        
        # Handle check size range
        if 'check_size_min' in filters:
            query = query.filter(VCDB.check_size_min >= filters.pop('check_size_min'))
        
        if 'check_size_max' in filters:
            query = query.filter(VCDB.check_size_max <= filters.pop('check_size_max'))
        
        # Apply remaining filters
        for key, value in filters.items():
            if hasattr(VCDB, key) and value is not None:
                query = query.filter(getattr(VCDB, key) == value)
        
        db_vcs = query.all()
        return [self._to_domain(vc) for vc in db_vcs]