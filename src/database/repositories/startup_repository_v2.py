"""PostgreSQL implementation of StartupRepository using BaseRepository."""

from typing import List, Optional
from uuid import UUID
from datetime import datetime

from sqlalchemy.orm import Session

from src.core.models.startup import Startup as StartupDomain
from src.core.repositories.startup_repository import StartupRepository
from src.database.models.startup import Startup as StartupDB
from src.database.repositories.base_repository import BaseRepository


class PostgresStartupRepository(BaseRepository[StartupDB, StartupDomain], StartupRepository):
    """PostgreSQL implementation of startup repository using base repository."""
    
    model_class = StartupDB
    
    def __init__(self, session: Session):
        """Initialize with database session."""
        super().__init__(session)
    
    def _to_domain(self, db_startup: StartupDB) -> StartupDomain:
        """Convert database model to domain model."""
        return StartupDomain(
            id=db_startup.id,
            name=db_startup.name,
            description=db_startup.description,
            founding_date=db_startup.founding_date,
            website=db_startup.website,
            industry=db_startup.industry,
            team_size=db_startup.team_size,
            funding_stage=db_startup.funding_stage,
            total_funding=db_startup.total_funding,
            revenue_range=db_startup.revenue_range,
            growth_rate=db_startup.growth_rate,
            sector=db_startup.sector,
            business_model=db_startup.business_model,
            target_market=db_startup.target_market,
            competitive_advantage=db_startup.competitive_advantage,
            technologies=db_startup.technologies,
            founders=db_startup.founders,
            contact_email=db_startup.contact_email,
            linkedin_url=db_startup.linkedin_url,
            twitter_url=db_startup.twitter_url,
            metadata=db_startup.metadata
        )
    
    def _to_db(self, startup: StartupDomain, existing: Optional[StartupDB] = None) -> StartupDB:
        """Convert domain model to database model."""
        if existing:
            # Update existing model
            existing.name = startup.name
            existing.description = startup.description
            existing.founding_date = startup.founding_date
            existing.website = startup.website
            existing.industry = startup.industry
            existing.team_size = startup.team_size
            existing.funding_stage = startup.funding_stage
            existing.total_funding = startup.total_funding
            existing.revenue_range = startup.revenue_range
            existing.growth_rate = startup.growth_rate
            existing.sector = startup.sector
            existing.business_model = startup.business_model
            existing.target_market = startup.target_market
            existing.competitive_advantage = startup.competitive_advantage
            existing.technologies = startup.technologies
            existing.founders = startup.founders
            existing.contact_email = startup.contact_email
            existing.linkedin_url = startup.linkedin_url
            existing.twitter_url = startup.twitter_url
            existing.metadata = startup.metadata
            return existing
        else:
            # Create new model
            return StartupDB(
                id=startup.id,
                name=startup.name,
                description=startup.description,
                founding_date=startup.founding_date,
                website=startup.website,
                industry=startup.industry,
                team_size=startup.team_size,
                funding_stage=startup.funding_stage,
                total_funding=startup.total_funding,
                revenue_range=startup.revenue_range,
                growth_rate=startup.growth_rate,
                sector=startup.sector,
                business_model=startup.business_model,
                target_market=startup.target_market,
                competitive_advantage=startup.competitive_advantage,
                technologies=startup.technologies,
                founders=startup.founders,
                contact_email=startup.contact_email,
                linkedin_url=startup.linkedin_url,
                twitter_url=startup.twitter_url,
                metadata=startup.metadata
            )
    
    # StartupRepository interface implementation
    async def save(self, startup: StartupDomain) -> StartupDomain:
        """Save a startup entity."""
        return super().save(startup)
    
    async def find_by_id(self, startup_id: UUID) -> Optional[StartupDomain]:
        """Find a startup by ID."""
        return super().get(startup_id)
    
    async def find_by_sector(self, sector: str) -> List[StartupDomain]:
        """Find all startups in a specific sector."""
        return super().find_by_field('sector', sector)
    
    async def find_by_stage(self, stage: str) -> List[StartupDomain]:
        """Find all startups at a specific funding stage."""
        return super().find_by_field('funding_stage', stage)
    
    async def find_all(self) -> List[StartupDomain]:
        """Retrieve all startups."""
        return super().list(limit=1000)  # Set a reasonable limit
    
    async def delete(self, startup_id: UUID) -> bool:
        """Delete a startup by its ID."""
        return super().delete(startup_id)
    
    # Additional convenience methods from original implementation
    def search_by_name(self, name: str) -> List[StartupDomain]:
        """Search startups by name (partial match)."""
        db_startups = self.session.query(StartupDB).filter(
            StartupDB.name.ilike(f"%{name}%")
        ).all()
        return [self._to_domain(startup) for startup in db_startups]
    
    def search_by_sector(self, sector: str) -> List[StartupDomain]:
        """Search startups by sector."""
        return self.find_by_field('sector', sector)
    
    def search_by_stage(self, stage: str) -> List[StartupDomain]:
        """Search startups by funding stage."""
        return self.find_by_field('funding_stage', stage)
    
    def search(self, filters: dict) -> List[StartupDomain]:
        """Search startups with multiple filters."""
        return super().search(filters)
    
    def create_startup(self, startup: StartupDomain) -> StartupDomain:
        """Create a new startup."""
        return self.create(startup)