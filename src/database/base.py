"""Base model configuration for async SQLAlchemy."""

from sqlalchemy.ext.asyncio import AsyncAttrs
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column
from sqlalchemy import DateTime, func
from datetime import datetime
from typing import Optional


class Base(AsyncAttrs, DeclarativeBase):
    """
    Base model for all database models with async support.
    
    AsyncAttrs mixin enables async lazy loading of relationships.
    All models inheriting from this base will support async operations.
    """
    __abstract__ = True
    
    # Common timestamp columns for all models
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False
    )
    
    def __repr__(self) -> str:
        """Default string representation."""
        return f"<{self.__class__.__name__}(id={getattr(self, 'id', 'N/A')})>"
    
    def to_dict(self) -> dict:
        """Convert model to dictionary."""
        return {
            column.name: getattr(self, column.name)
            for column in self.__table__.columns
        }


# For backward compatibility with existing imports
from sqlalchemy.ext.declarative import declarative_base

# Create a legacy Base for sync operations (to be phased out)
LegacyBase = declarative_base()