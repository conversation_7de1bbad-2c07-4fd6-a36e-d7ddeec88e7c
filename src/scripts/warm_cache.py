#!/usr/bin/env python3
"""Script to warm up the discovery cache with common queries."""

import asyncio
import logging
from typing import List
import os
import sys

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

from src.core.services.cached_discovery_service import CachedDiscoveryService
from src.database import get_session
from src.infrastructure.redis import RedisConnectionFactory
from src.database.repositories.startup_repository import StartupRepository
from src.database.repositories.vc_repository import VCRepository

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def warm_discovery_cache():
    """Warm up discovery cache with common queries."""
    # Get database session
    db = next(get_session())
    
    # Get Redis client
    redis = await RedisConnectionFactory.get_async_client()
    
    # Initialize service
    discovery_service = CachedDiscoveryService(db, redis)
    
    # Get repositories
    startup_repo = StartupRepository(db)
    vc_repo = VCRepository(db)
    
    try:
        # Get sample VCs and startups
        logger.info("Fetching VCs and startups...")
        vcs = await vc_repo.find_all(limit=20)
        startups = await startup_repo.find_all(limit=20)
        
        logger.info(f"Found {len(vcs)} VCs and {len(startups)} startups")
        
        # Warm VC discovery cache
        logger.info("Warming VC discovery cache...")
        vc_count = 0
        for vc in vcs[:10]:  # Top 10 VCs
            try:
                await discovery_service.discover_startups_for_vc(
                    vc_id=vc.id,
                    limit=20,
                    min_score=0.7,
                    page=1,
                    use_cache=True
                )
                vc_count += 1
                logger.info(f"Cached discovery for VC: {vc.name}")
            except Exception as e:
                logger.error(f"Error caching VC {vc.name}: {str(e)}")
        
        # Warm startup discovery cache
        logger.info("Warming startup discovery cache...")
        startup_count = 0
        for startup in startups[:10]:  # Top 10 startups
            try:
                await discovery_service.discover_vcs_for_startup(
                    startup_id=startup.id,
                    limit=20,
                    min_score=0.7,
                    page=1,
                    use_cache=True
                )
                startup_count += 1
                logger.info(f"Cached discovery for startup: {startup.name}")
            except Exception as e:
                logger.error(f"Error caching startup {startup.name}: {str(e)}")
        
        # Warm search cache with common queries
        logger.info("Warming search cache...")
        common_searches = [
            "AI",
            "B2B SaaS",
            "Fintech",
            "Healthcare",
            "Seed stage",
            "Series A",
            "San Francisco",
            "New York"
        ]
        
        search_count = 0
        for query in common_searches:
            try:
                await discovery_service.search_with_cache(
                    query=query,
                    entity_type="all",
                    limit=20,
                    use_cache=True
                )
                search_count += 1
                logger.info(f"Cached search: {query}")
            except Exception as e:
                logger.error(f"Error caching search '{query}': {str(e)}")
        
        # Warm matching scores for top pairs
        logger.info("Warming matching score cache...")
        match_count = 0
        for vc in vcs[:5]:
            for startup in startups[:5]:
                try:
                    await discovery_service.get_matching_score_with_cache(
                        startup_id=startup.id,
                        vc_id=vc.id,
                        use_cache=True
                    )
                    match_count += 1
                except Exception as e:
                    logger.error(f"Error caching match score: {str(e)}")
        
        # Get cache statistics
        stats = await discovery_service.get_cache_statistics()
        
        logger.info("\n=== Cache Warming Complete ===")
        logger.info(f"VCs cached: {vc_count}")
        logger.info(f"Startups cached: {startup_count}")
        logger.info(f"Searches cached: {search_count}")
        logger.info(f"Match scores cached: {match_count}")
        logger.info(f"\nCache Statistics:")
        logger.info(f"Total keys: {stats.get('total_keys', 0)}")
        logger.info(f"Memory used: {stats.get('memory_used', 'N/A')}")
        logger.info(f"Keys by type: {stats.get('keys_by_type', {})}")
        
    except Exception as e:
        logger.error(f"Error warming cache: {str(e)}")
    finally:
        db.close()
        await redis.close()


async def clear_all_caches():
    """Clear all caches."""
    # Get database session
    db = next(get_session())
    
    # Get Redis client
    redis = await RedisConnectionFactory.get_async_client()
    
    # Initialize service
    discovery_service = CachedDiscoveryService(db, redis)
    
    try:
        logger.info("Clearing all caches...")
        
        # Clear discovery caches
        discovery_deleted = await discovery_service.cache.invalidate_discovery_caches()
        logger.info(f"Deleted {discovery_deleted} discovery cache entries")
        
        # Clear matching caches
        matching_deleted = await discovery_service.cache.invalidate_matching_caches()
        logger.info(f"Deleted {matching_deleted} matching cache entries")
        
        logger.info("All caches cleared successfully")
        
    except Exception as e:
        logger.error(f"Error clearing caches: {str(e)}")
    finally:
        db.close()
        await redis.close()


async def show_cache_stats():
    """Show current cache statistics."""
    # Get database session
    db = next(get_session())
    
    # Get Redis client
    redis = await RedisConnectionFactory.get_async_client()
    
    # Initialize service
    discovery_service = CachedDiscoveryService(db, redis)
    
    try:
        stats = await discovery_service.get_cache_statistics()
        
        print("\n=== Current Cache Statistics ===")
        print(f"Memory used: {stats.get('memory_used', 'N/A')}")
        print(f"Memory peak: {stats.get('memory_peak', 'N/A')}")
        print(f"Total keys: {stats.get('total_keys', 0)}")
        print(f"Hit rate: {stats.get('hit_rate', 0)}%")
        print(f"Evicted keys: {stats.get('evicted_keys', 0)}")
        print(f"Connected clients: {stats.get('connected_clients', 0)}")
        print("\nKeys by type:")
        for key_type, count in stats.get('keys_by_type', {}).items():
            print(f"  {key_type}: {count}")
        
    except Exception as e:
        logger.error(f"Error getting cache stats: {str(e)}")
    finally:
        db.close()
        await redis.close()


def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Cache management script")
    parser.add_argument(
        "command",
        choices=["warm", "clear", "stats"],
        help="Command to run"
    )
    
    args = parser.parse_args()
    
    if args.command == "warm":
        asyncio.run(warm_discovery_cache())
    elif args.command == "clear":
        asyncio.run(clear_all_caches())
    elif args.command == "stats":
        asyncio.run(show_cache_stats())


if __name__ == "__main__":
    main()