#!/usr/bin/env python3
"""
Integration test script to verify data persistence for BiLat VC Matching Platform.
Tests complete CRUD operations for startups, VCs, and matches via API endpoints.
"""

import asyncio
import sys
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional
import httpx
from colorama import init, Fore, Style

# Add project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

from src.database.setup import get_engine, init_db, AsyncSessionLocal
from src.core.config import settings
from sqlalchemy import text

# Initialize colorama for colored output
init(autoreset=True)

# Test configuration
BASE_URL = "http://localhost:8000"
API_V1 = f"{BASE_URL}/api/v1"

class IntegrationTestRunner:
    """Comprehensive integration test runner for data persistence."""
    
    def __init__(self):
        self.client = httpx.AsyncClient(base_url=BASE_URL, timeout=30.0)
        self.test_results = {
            "passed": 0,
            "failed": 0,
            "errors": []
        }
        self.created_resources = {
            "startups": [],
            "vcs": [],
            "matches": []
        }
    
    async def setup(self):
        """Set up test environment."""
        print(f"\n{Fore.CYAN}🚀 Setting up Integration Test Environment{Style.RESET_ALL}")
        
        # Initialize database
        engine = get_engine()
        init_db(engine)
        print(f"{Fore.GREEN}✅ Database tables created{Style.RESET_ALL}")
        
        # Verify database connection
        async with AsyncSessionLocal() as session:
            result = await session.execute(text("SELECT 1"))
            assert result.scalar() == 1
            print(f"{Fore.GREEN}✅ Database connection verified{Style.RESET_ALL}")
    
    async def teardown(self):
        """Clean up test resources."""
        print(f"\n{Fore.YELLOW}🧹 Cleaning up test resources{Style.RESET_ALL}")
        
        # Delete created resources in reverse order
        for match_id in self.created_resources["matches"]:
            try:
                await self.client.delete(f"{API_V1}/matches/{match_id}")
            except:
                pass
        
        for vc_id in self.created_resources["vcs"]:
            try:
                await self.client.delete(f"{API_V1}/vcs/{vc_id}")
            except:
                pass
        
        for startup_id in self.created_resources["startups"]:
            try:
                await self.client.delete(f"{API_V1}/startups/{startup_id}")
            except:
                pass
        
        await self.client.aclose()
        print(f"{Fore.GREEN}✅ Cleanup completed{Style.RESET_ALL}")
    
    def log_test_result(self, test_name: str, passed: bool, error: Optional[str] = None):
        """Log test result with colored output."""
        if passed:
            self.test_results["passed"] += 1
            print(f"{Fore.GREEN}✅ {test_name}{Style.RESET_ALL}")
        else:
            self.test_results["failed"] += 1
            self.test_results["errors"].append({"test": test_name, "error": error})
            print(f"{Fore.RED}❌ {test_name}: {error}{Style.RESET_ALL}")
    
    async def test_startup_crud(self):
        """Test complete CRUD operations for startups."""
        print(f"\n{Fore.CYAN}📊 Testing Startup CRUD Operations{Style.RESET_ALL}")
        
        # Test 1: Create Startup
        startup_data = {
            "name": f"InnovateTech_{datetime.now().strftime('%Y%m%d%H%M%S')}",
            "sector": "B2B SaaS",
            "stage": "Seed",
            "description": "AI-powered analytics platform",
            "website": "https://innovatetech.example.com",
            "team_size": 15,
            "monthly_revenue": 50000
        }
        
        try:
            response = await self.client.post(f"{API_V1}/startups", json=startup_data)
            if response.status_code == 201:
                startup = response.json()
                self.created_resources["startups"].append(startup["id"])
                self.log_test_result(f"Create Startup", True)
                print(f"  → Created startup with ID: {startup['id']}")
            else:
                self.log_test_result("Create Startup", False, f"Status {response.status_code}: {response.text}")
                return
        except Exception as e:
            self.log_test_result("Create Startup", False, str(e))
            return
        
        startup_id = startup["id"]
        
        # Test 2: Get Startup by ID
        try:
            response = await self.client.get(f"{API_V1}/startups/{startup_id}")
            if response.status_code == 200:
                retrieved_startup = response.json()
                if retrieved_startup["name"] == startup_data["name"]:
                    self.log_test_result("Get Startup by ID", True)
                    print(f"  → Retrieved startup: {retrieved_startup['name']}")
                else:
                    self.log_test_result("Get Startup by ID", False, "Data mismatch")
            else:
                self.log_test_result("Get Startup by ID", False, f"Status {response.status_code}")
        except Exception as e:
            self.log_test_result("Get Startup by ID", False, str(e))
        
        # Test 3: List All Startups
        try:
            response = await self.client.get(f"{API_V1}/startups")
            if response.status_code == 200:
                startups = response.json()
                if isinstance(startups, list) and any(s["id"] == startup_id for s in startups):
                    self.log_test_result("List Startups", True)
                    print(f"  → Found {len(startups)} startups")
                else:
                    self.log_test_result("List Startups", False, "Created startup not in list")
            else:
                self.log_test_result("List Startups", False, f"Status {response.status_code}")
        except Exception as e:
            self.log_test_result("List Startups", False, str(e))
        
        # Test 4: Update Startup
        update_data = {"team_size": 25, "monthly_revenue": 75000}
        try:
            response = await self.client.put(f"{API_V1}/startups/{startup_id}", json=update_data)
            if response.status_code == 200:
                updated_startup = response.json()
                if updated_startup["team_size"] == 25:
                    self.log_test_result("Update Startup", True)
                    print(f"  → Updated team size to {updated_startup['team_size']}")
                else:
                    self.log_test_result("Update Startup", False, "Update not applied")
            else:
                self.log_test_result("Update Startup", False, f"Status {response.status_code}")
        except Exception as e:
            self.log_test_result("Update Startup", False, str(e))
    
    async def test_vc_crud(self):
        """Test complete CRUD operations for VCs."""
        print(f"\n{Fore.CYAN}🏢 Testing VC CRUD Operations{Style.RESET_ALL}")
        
        # Test 1: Create VC
        vc_data = {
            "firm_name": f"TechVentures_{datetime.now().strftime('%Y%m%d%H%M%S')}",
            "sectors": ["B2B SaaS", "AI/ML", "Analytics"],
            "stages": ["Seed", "Series A"],
            "thesis": "We invest in AI-first B2B SaaS companies with strong technical teams",
            "website": "https://techventures.example.com",
            "fund_size": 150000000,
            "typical_check_size": 2000000
        }
        
        try:
            response = await self.client.post(f"{API_V1}/vcs", json=vc_data)
            if response.status_code == 201:
                vc = response.json()
                self.created_resources["vcs"].append(vc["id"])
                self.log_test_result("Create VC", True)
                print(f"  → Created VC with ID: {vc['id']}")
            else:
                self.log_test_result("Create VC", False, f"Status {response.status_code}: {response.text}")
                return
        except Exception as e:
            self.log_test_result("Create VC", False, str(e))
            return
        
        vc_id = vc["id"]
        
        # Test 2: Get VC by ID
        try:
            response = await self.client.get(f"{API_V1}/vcs/{vc_id}")
            if response.status_code == 200:
                retrieved_vc = response.json()
                if retrieved_vc["firm_name"] == vc_data["firm_name"]:
                    self.log_test_result("Get VC by ID", True)
                    print(f"  → Retrieved VC: {retrieved_vc['firm_name']}")
                else:
                    self.log_test_result("Get VC by ID", False, "Data mismatch")
            else:
                self.log_test_result("Get VC by ID", False, f"Status {response.status_code}")
        except Exception as e:
            self.log_test_result("Get VC by ID", False, str(e))
        
        # Test 3: List All VCs
        try:
            response = await self.client.get(f"{API_V1}/vcs")
            if response.status_code == 200:
                vcs = response.json()
                if isinstance(vcs, list) and any(v["id"] == vc_id for v in vcs):
                    self.log_test_result("List VCs", True)
                    print(f"  → Found {len(vcs)} VCs including ours")
                else:
                    self.log_test_result("List VCs", False, "Created VC not in list")
            else:
                self.log_test_result("List VCs", False, f"Status {response.status_code}")
        except Exception as e:
            self.log_test_result("List VCs", False, str(e))
    
    async def test_match_operations(self):
        """Test match creation and retrieval."""
        print(f"\n{Fore.CYAN}🔗 Testing Match Operations{Style.RESET_ALL}")
        
        # Ensure we have a startup and VC to match
        if not self.created_resources["startups"] or not self.created_resources["vcs"]:
            self.log_test_result("Match Operations", False, "No startup or VC available for matching")
            return
        
        startup_id = self.created_resources["startups"][0]
        vc_id = self.created_resources["vcs"][0]
        
        # Create Match
        match_data = {
            "startup_id": startup_id,
            "vc_id": vc_id,
            "score": 0.85,
            "reasons": ["Strong sector alignment", "Stage match", "Team expertise"]
        }
        
        try:
            response = await self.client.post(f"{API_V1}/matches", json=match_data)
            if response.status_code == 201:
                match = response.json()
                self.created_resources["matches"].append(match["id"])
                self.log_test_result("Create Match", True)
                print(f"  → Created match with score: {match['score']}")
            else:
                self.log_test_result("Create Match", False, f"Status {response.status_code}: {response.text}")
        except Exception as e:
            self.log_test_result("Create Match", False, str(e))
    
    async def test_database_persistence(self):
        """Verify data actually persists in PostgreSQL."""
        print(f"\n{Fore.CYAN}💾 Testing PostgreSQL Data Persistence{Style.RESET_ALL}")
        
        async with AsyncSessionLocal() as session:
            # Check startup persistence
            if self.created_resources["startups"]:
                startup_id = self.created_resources["startups"][0]
                result = await session.execute(
                    text("SELECT name, sector, stage FROM startups WHERE id = :id"),
                    {"id": startup_id}
                )
                row = result.first()
                if row:
                    self.log_test_result("Startup Persistence", True)
                    print(f"  → Data persisted in PostgreSQL: {row.name}")
                else:
                    self.log_test_result("Startup Persistence", False, "Data not found in database")
            
            # Check VC persistence
            if self.created_resources["vcs"]:
                vc_id = self.created_resources["vcs"][0]
                result = await session.execute(
                    text("SELECT firm_name, sectors FROM vcs WHERE id = :id"),
                    {"id": vc_id}
                )
                row = result.first()
                if row:
                    self.log_test_result("VC Persistence", True)
                    print(f"  → Data persisted in PostgreSQL: {row.firm_name}")
                else:
                    self.log_test_result("VC Persistence", False, "Data not found in database")
            
            # Check match persistence
            if self.created_resources["matches"]:
                match_id = self.created_resources["matches"][0]
                result = await session.execute(
                    text("SELECT score, reasons FROM matches WHERE id = :id"),
                    {"id": match_id}
                )
                row = result.first()
                if row:
                    self.log_test_result("Match Persistence", True)
                    print(f"  → Data persisted with score: {row.score}")
                else:
                    self.log_test_result("Match Persistence", False, "Data not found in database")
    
    async def test_error_scenarios(self):
        """Test error handling and validation."""
        print(f"\n{Fore.CYAN}⚠️  Testing Error Scenarios{Style.RESET_ALL}")
        
        # Test invalid startup data
        invalid_startup = {
            "name": "",  # Empty name should fail
            "sector": "InvalidSector",  # Invalid sector
            "stage": "Unknown"  # Invalid stage
        }
        
        try:
            response = await self.client.post(f"{API_V1}/startups", json=invalid_startup)
            if response.status_code in [400, 422]:
                self.log_test_result("Invalid Data Validation", True)
                print(f"  → Properly rejected invalid data")
            else:
                self.log_test_result("Invalid Data Validation", False, 
                                   f"Expected error but got status {response.status_code}")
        except Exception as e:
            self.log_test_result("Invalid Data Validation", False, str(e))
        
        # Test non-existent resource
        try:
            response = await self.client.get(f"{API_V1}/startups/non-existent-id")
            if response.status_code == 404:
                self.log_test_result("Non-existent Resource", True)
                print(f"  → Properly handled 404 error")
            else:
                self.log_test_result("Non-existent Resource", False, 
                                   f"Expected 404 but got {response.status_code}")
        except Exception as e:
            self.log_test_result("Non-existent Resource", False, str(e))
    
    async def test_concurrent_operations(self):
        """Test concurrent database operations."""
        print(f"\n{Fore.CYAN}🔄 Testing Concurrent Operations{Style.RESET_ALL}")
        
        # Create multiple startups concurrently
        async def create_startup(index: int):
            startup_data = {
                "name": f"ConcurrentStartup_{index}_{datetime.now().timestamp()}",
                "sector": "Fintech",
                "stage": "Seed",
                "description": f"Test startup {index}"
            }
            response = await self.client.post(f"{API_V1}/startups", json=startup_data)
            if response.status_code == 201:
                return response.json()["id"]
            return None
        
        try:
            # Create 5 startups concurrently
            tasks = [create_startup(i) for i in range(5)]
            startup_ids = await asyncio.gather(*tasks)
            
            # Track created resources for cleanup
            self.created_resources["startups"].extend([id for id in startup_ids if id])
            
            successful = sum(1 for id in startup_ids if id is not None)
            if successful == 5:
                self.log_test_result("Concurrent Operations", True)
                print(f"  → Successfully created {successful} startups concurrently")
            else:
                self.log_test_result("Concurrent Operations", False, 
                                   f"Only {successful}/5 concurrent operations succeeded")
        except Exception as e:
            self.log_test_result("Concurrent Operations", False, str(e))
    
    async def test_matching_algorithm(self):
        """Test the matching algorithm with realistic data."""
        print(f"\n{Fore.CYAN}🎯 Testing Matching Algorithm{Style.RESET_ALL}")
        
        # Create a highly compatible startup and VC pair
        startup_data = {
            "name": f"PerfectMatch_Startup_{datetime.now().timestamp()}",
            "sector": "B2B SaaS",
            "stage": "Seed",
            "description": "AI-powered B2B SaaS platform for sales automation",
            "team_size": 10,
            "monthly_revenue": 30000
        }
        
        vc_data = {
            "firm_name": f"PerfectMatch_VC_{datetime.now().timestamp()}",
            "sectors": ["B2B SaaS", "AI/ML"],
            "stages": ["Seed", "Series A"],
            "thesis": "We invest in B2B SaaS companies leveraging AI",
            "typical_check_size": 1500000
        }
        
        try:
            # Create startup and VC
            startup_response = await self.client.post(f"{API_V1}/startups", json=startup_data)
            vc_response = await self.client.post(f"{API_V1}/vcs", json=vc_data)
            
            if startup_response.status_code == 201 and vc_response.status_code == 201:
                startup_id = startup_response.json()["id"]
                vc_id = vc_response.json()["id"]
                
                self.created_resources["startups"].append(startup_id)
                self.created_resources["vcs"].append(vc_id)
                
                # Test matching endpoint if it exists
                match_response = await self.client.get(f"{API_V1}/startups/{startup_id}/matches")
                if match_response.status_code == 200:
                    matches = match_response.json()
                    if isinstance(matches, list) and len(matches) > 0:
                        self.log_test_result("Matching Algorithm", True)
                        print(f"  → Found {len(matches)} potential matches")
                    else:
                        self.log_test_result("Matching Algorithm", True)
                        print(f"  → Matching endpoint exists but no matches found yet")
                else:
                    # If no matching endpoint, just verify creation
                    self.log_test_result("Matching Algorithm Setup", True)
                    print(f"  → Created compatible startup and VC for future matching")
            else:
                self.log_test_result("Matching Algorithm", False, "Failed to create test data")
        except Exception as e:
            self.log_test_result("Matching Algorithm", False, str(e))
    
    async def run_all_tests(self):
        """Run all integration tests."""
        print(f"\n{Fore.BLUE}{'='*60}{Style.RESET_ALL}")
        print(f"{Fore.BLUE}🧪 Running BiLat Integration Tests for Data Persistence{Style.RESET_ALL}")
        print(f"{Fore.BLUE}{'='*60}{Style.RESET_ALL}")
        
        try:
            await self.setup()
            
            # Run all test suites
            await self.test_startup_crud()
            await self.test_vc_crud()
            await self.test_match_operations()
            await self.test_database_persistence()
            await self.test_error_scenarios()
            await self.test_concurrent_operations()
            await self.test_matching_algorithm()
            
            # Print summary
            total_tests = self.test_results["passed"] + self.test_results["failed"]
            success_rate = (self.test_results["passed"] / total_tests * 100) if total_tests > 0 else 0
            
            print(f"\n{Fore.BLUE}{'='*60}{Style.RESET_ALL}")
            print(f"{Fore.BLUE}📊 Test Summary{Style.RESET_ALL}")
            print(f"{Fore.BLUE}{'='*60}{Style.RESET_ALL}")
            print(f"{Fore.GREEN}✅ Passed: {self.test_results['passed']}{Style.RESET_ALL}")
            print(f"{Fore.RED}❌ Failed: {self.test_results['failed']}{Style.RESET_ALL}")
            print(f"{Fore.CYAN}📈 Success Rate: {success_rate:.1f}%{Style.RESET_ALL}")
            
            if self.test_results["errors"]:
                print(f"\n{Fore.RED}Failed Tests:{Style.RESET_ALL}")
                for error in self.test_results["errors"]:
                    print(f"  • {error['test']}: {error['error']}")
            
            # Save results to file
            results_file = Path("integration_test_results.json")
            with open(results_file, "w") as f:
                json.dump({
                    "timestamp": datetime.now().isoformat(),
                    "results": self.test_results,
                    "success_rate": success_rate
                }, f, indent=2)
            print(f"\n{Fore.CYAN}📄 Results saved to {results_file}{Style.RESET_ALL}")
            
        finally:
            await self.teardown()
        
        return self.test_results["failed"] == 0


async def main():
    """Main entry point."""
    # Check if API is running
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{BASE_URL}/health")
            if response.status_code != 200:
                print(f"{Fore.RED}❌ API is not running at {BASE_URL}{Style.RESET_ALL}")
                print(f"{Fore.YELLOW}Please start the API first:{Style.RESET_ALL}")
                print(f"  python -m uvicorn test_api_minimal:app --reload")
                return False
    except Exception:
        print(f"{Fore.RED}❌ Cannot connect to API at {BASE_URL}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}Please start the API first:{Style.RESET_ALL}")
        print(f"  python -m uvicorn test_api_minimal:app --reload")
        return False
    
    # Run tests
    runner = IntegrationTestRunner()
    success = await runner.run_all_tests()
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)