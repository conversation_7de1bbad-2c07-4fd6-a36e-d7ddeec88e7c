# Celery Implementation Guide for VC Matching Platform

## Overview

This document outlines the Celery-based background task processing implementation for the VC Matching Platform. The implementation addresses key requirements identified in the codebase analysis:

- AI analysis operations (OpenAI API calls)
- Data enrichment and web scraping
- Notification delivery
- Scheduled tasks (6-hour discovery cycle)
- Batch processing and matching

## Architecture

### Queue Structure

The implementation uses 5 dedicated queues for different task types:

1. **ai_analysis** - AI-powered analysis tasks (OpenAI API calls)
2. **data_enrichment** - Web scraping and data collection
3. **notifications** - Email and notification delivery
4. **scheduled** - Periodic and scheduled tasks
5. **default** - General purpose tasks

### Key Components

```
src/
├── workers/
│   ├── __init__.py          # Celery app configuration
│   └── tasks/
│       ├── ai_tasks.py      # AI analysis tasks
│       ├── data_tasks.py    # Data enrichment tasks
│       ├── notification_tasks.py  # Notification tasks
│       └── scheduled_tasks.py     # Periodic tasks
├── api/
│   └── endpoints/
│       └── tasks.py         # Task management API endpoints
```

## Implementation Details

### 1. AI Analysis Tasks

**Purpose**: Move expensive OpenAI API calls to background processing

**Tasks**:
- `analyze_startup_task` - Analyzes startup with AI
- `analyze_vc_task` - Analyzes VC investment thesis
- `batch_match_task` - Processes batch matching with progress tracking
- `generate_match_insights_task` - Generates detailed match explanations

**Benefits**:
- Non-blocking API responses
- Automatic retry on failures
- Progress tracking for long operations
- Better handling of API rate limits

### 2. Data Enrichment Tasks

**Purpose**: Implement web scraping and data collection (Phase 2 roadmap item)

**Tasks**:
- `scrape_vc_website_task` - Scrapes VC websites for portfolio data
- `enrich_startup_data_task` - Enriches startup data from multiple sources
- `update_funding_data_task` - Updates funding information

**Features**:
- Extracts portfolio companies from VC websites
- Identifies investment themes and focus areas
- Collects news and updates about startups
- Stores enriched data in entity metadata

### 3. Notification Tasks

**Purpose**: Handle email notifications and communications

**Tasks**:
- `send_match_notification_task` - Notifies about new matches
- `send_weekly_digest_task` - Sends weekly summary emails
- `send_analysis_complete_task` - Notifies when AI analysis is done

**Features**:
- Template-based email generation
- Separate queue for reliable delivery
- Tracking of sent notifications

### 4. Scheduled Tasks

**Purpose**: Implement the "6-hour discovery cycle" and maintenance

**Tasks**:
- `refresh_ai_insights` - Re-analyzes entities every 6 hours
- `cleanup_stale_data` - Daily cleanup of old data
- `generate_analytics_report` - Daily analytics generation
- `update_match_scores` - Periodic score updates

**Schedule**:
```python
beat_schedule = {
    'refresh-ai-insights': {
        'schedule': 21600.0,  # Every 6 hours
    },
    'cleanup-stale-data': {
        'schedule': 86400.0,  # Daily
    },
    'generate-analytics': {
        'schedule': 86400.0,  # Daily
    },
}
```

## API Integration

### New Endpoints

```python
POST /api/tasks/analyze/startup/{startup_id}
POST /api/tasks/analyze/vc/{vc_id}
POST /api/tasks/batch-match
POST /api/tasks/enrich/vc/{vc_id}
POST /api/tasks/enrich/startup/{startup_id}
GET  /api/tasks/{task_id}
DELETE /api/tasks/{task_id}
GET  /api/tasks/queue/stats
```

### Example Usage

```python
# Start batch matching
response = client.post("/api/tasks/batch-match", json={
    "startup_ids": ["startup1", "startup2"],
    "vc_ids": ["vc1", "vc2"],
    "use_ai": true,
    "threshold": 0.7
})
task_id = response.json()["task_id"]

# Check progress
status = client.get(f"/api/tasks/{task_id}")
# Returns: {
#   "state": "PROCESSING",
#   "progress": {"current": 2, "total": 4, "percentage": 50}
# }
```

## Deployment

### Docker Compose Configuration

The `docker-compose.celery.yml` provides:

- Separate workers for each queue
- Celery Beat for scheduled tasks
- Flower for monitoring
- Resource limits and auto-restart

### Running Workers

```bash
# Development
celery -A celery_worker worker --loglevel=info

# Production with Docker Compose
docker-compose -f docker-compose.yml -f docker-compose.celery.yml up

# Monitor with Flower
# Access at http://localhost:5555
```

### Worker Configuration

- **AI Analysis Worker**: 2 concurrent tasks (OpenAI rate limits)
- **Data Enrichment Worker**: 4 concurrent tasks
- **Notification Worker**: 2 concurrent tasks
- **Default Worker**: 2 concurrent tasks

## Monitoring and Observability

### Flower Dashboard

Access at `http://localhost:5555` to monitor:
- Task execution in real-time
- Worker status and load
- Task success/failure rates
- Queue depths

### Task Status Tracking

```python
# Built-in task status function
status = get_task_status(task_id)
# Returns current state, progress, and results
```

### Metrics to Track

1. Task execution times
2. Queue depths
3. Success/failure rates
4. Worker utilization
5. API call counts

## Migration Path

### Phase 1: Core Infrastructure (Week 1)
1. Deploy Celery workers
2. Move AI analysis to background tasks
3. Implement task status API

### Phase 2: Enhanced Features (Week 2-3)
1. Add web scraping tasks
2. Implement notification system
3. Set up scheduled tasks

### Phase 3: Production Optimization (Week 4+)
1. Fine-tune worker concurrency
2. Implement advanced monitoring
3. Add task result caching

## Best Practices

1. **Idempotent Tasks**: All tasks are designed to be safely retried
2. **Progress Tracking**: Long-running tasks update their progress
3. **Error Handling**: Automatic retries with exponential backoff
4. **Resource Limits**: Time limits prevent stuck tasks
5. **Task Routing**: Dedicated queues for different workloads

## Configuration

### Environment Variables

```bash
# Required
REDIS_URL=redis://localhost:6379/0
DATABASE_URL=postgresql://user:pass@localhost/db
OPENAI_API_KEY=your-api-key

# Optional
CELERY_WORKER_CONCURRENCY=4
CELERY_TASK_TIME_LIMIT=300
FLOWER_BASIC_AUTH=admin:password
```

### Celery Settings

Key configurations in `src/workers/__init__.py`:
- Task serialization: JSON
- Result persistence: 1 hour
- Max retries: 3
- Soft time limit: 4 minutes
- Hard time limit: 5 minutes

## Troubleshooting

### Common Issues

1. **Tasks not processing**: Check Redis connection and worker logs
2. **High memory usage**: Adjust worker concurrency
3. **Slow task execution**: Check for blocking I/O in tasks
4. **Lost tasks**: Ensure `task_acks_late=True` is set

### Debug Commands

```bash
# Check active tasks
celery -A celery_worker inspect active

# Check scheduled tasks
celery -A celery_worker inspect scheduled

# Purge all tasks
celery -A celery_worker purge
```

## Future Enhancements

1. **Advanced Scheduling**: Cron-based schedules for complex timing
2. **Task Chaining**: Link related tasks for complex workflows
3. **Priority Queues**: Prioritize urgent tasks
4. **Result Storage**: Store results in S3 for large data
5. **WebSocket Updates**: Real-time progress updates to frontend