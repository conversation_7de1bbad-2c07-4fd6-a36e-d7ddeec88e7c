# Celery Setup Guide

## Overview
Celery is configured for asynchronous task processing in the VC Matching Platform. It handles:
- AI analysis tasks
- Data enrichment and web scraping
- Email notifications
- Scheduled maintenance tasks
- Batch processing

## Quick Start

### Option 1: Docker Compose (Recommended)
```bash
# Start all services including Celery
docker-compose up -d

# View logs
docker-compose logs -f celery
docker-compose logs -f celery-beat
docker-compose logs -f flower

# Access Flower monitoring UI
open http://localhost:5555
```

### Option 2: Local Development
```bash
# Start Redis first
redis-server

# In separate terminals:

# Terminal 1: Start Celery worker
./scripts/start_celery_worker.sh

# Terminal 2: Start Celery Beat (scheduler)
./scripts/start_celery_beat.sh

# Terminal 3: Start Flower (monitoring)
./scripts/start_celery_flower.sh
```

## Configuration

### Environment Variables
```bash
# Required
REDIS_URL=redis://localhost:6379/0
DATABASE_URL=postgresql://user:pass@localhost:5432/vc_matching_platform

# Optional
LOG_LEVEL=INFO
WORKER_NAME=worker1
CONCURRENCY=4
FLOWER_PORT=5555
```

### Celery Configuration
Located in `src/workers/__init__.py`:
- Task serialization: JSON
- Result backend: Redis
- Task time limits: 5 min hard, 4 min soft
- Worker prefetch: 4 tasks
- Max tasks per child: 1000

## Task Queues

1. **default** - General purpose tasks
2. **ai_analysis** - AI/LLM analysis tasks
3. **data_enrichment** - Web scraping and data tasks
4. **notifications** - Email and notification tasks
5. **scheduled** - Periodic/scheduled tasks

## Available Tasks

### AI Tasks (`src.workers.tasks.ai_tasks`)
- `analyze_startup_task` - Analyze startup with AI
- `analyze_vc_task` - Analyze VC with AI
- `batch_match_task` - Batch matching analysis

### Data Tasks (`src.workers.tasks.data_tasks`)
- `scrape_vc_website_task` - Scrape VC website
- `enrich_startup_data_task` - Enrich startup data
- `batch_data_enrichment_task` - Batch enrichment

### Notification Tasks (`src.workers.tasks.notification_tasks`)
- `send_match_notification` - Send match email
- `send_batch_notifications` - Send batch emails

### Scheduled Tasks (`src.workers.tasks.scheduled_tasks`)
- `refresh_ai_insights` - Refresh AI analysis (6 hours)
- `cleanup_stale_data` - Clean old data (daily)
- `generate_analytics_report` - Generate reports (daily)
- `daily_match_generation` - Generate matches (daily)
- `cleanup_old_tasks` - Clean task results (hourly)

## Testing Celery

### Run Test Script
```bash
# Test if Celery is working
python test_celery_tasks.py
```

### Manual Task Testing
```python
from src.workers.tasks.ai_tasks import analyze_startup_task

# Send task asynchronously
result = analyze_startup_task.delay("startup-id")

# Get task ID
print(result.id)

# Wait for result (blocking)
result.get(timeout=30)
```

### Check Worker Status
```python
from src.workers.celery_app import celery_app

# Get worker stats
stats = celery_app.control.inspect().stats()

# Get active tasks
active = celery_app.control.inspect().active()

# Get registered tasks
registered = celery_app.control.inspect().registered()
```

## Monitoring

### Flower Web UI
Access at http://localhost:5555
- Real-time task monitoring
- Worker status
- Task history
- Performance graphs

### Health Check API
```bash
# Check Celery health
curl http://localhost:8000/api/v1/health/celery

# Check all dependencies
curl http://localhost:8000/api/v1/health/dependencies
```

### Logs
```bash
# Docker logs
docker-compose logs celery
docker-compose logs celery-beat

# Local logs (if running locally)
# Logs go to stdout by default
```

## Troubleshooting

### Workers Not Found
```bash
# Check Redis connection
redis-cli ping

# Check worker processes
ps aux | grep celery

# Restart workers
docker-compose restart celery
```

### Tasks Not Executing
1. Check worker logs for errors
2. Verify Redis is running
3. Check task registration
4. Verify queue routing

### Memory Issues
- Adjust `--max-tasks-per-child` in worker script
- Reduce concurrency with `CONCURRENCY=2`
- Monitor memory usage in Flower

### Task Timeouts
- Increase time limits in configuration
- Break large tasks into smaller chunks
- Use task chains for complex workflows

## Production Considerations

1. **Scaling**
   - Run multiple workers: `docker-compose up --scale celery=3`
   - Use different queues for priority
   - Consider Redis Sentinel for HA

2. **Monitoring**
   - Set up alerts for failed tasks
   - Monitor queue lengths
   - Track task execution times

3. **Security**
   - Secure Flower with authentication
   - Use SSL for Redis in production
   - Rotate task result data regularly

4. **Performance**
   - Tune worker concurrency based on load
   - Use task routing for optimization
   - Consider using Redis Cluster for scale