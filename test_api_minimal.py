#!/usr/bin/env python3
"""
Minimal API test that focuses on database connectivity.
This bypasses complex imports and tests core functionality.
"""

import sys
import asyncio
from pathlib import Path

# Add project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from src.database.setup import get_async_db, get_async_engine
from src.core.config import settings
from sqlalchemy import text


# Create minimal FastAPI app
app = FastAPI(
    title="VC Matching Platform API",
    version="1.0.0",
    description="Minimal test version"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "VC Matching Platform API is running!"}


@app.get("/health")
async def health_check():
    """Health check endpoint with database connectivity test."""
    try:
        # Test database connection
        async for db in get_async_db():
            result = await db.execute(text("SELECT 1"))
            result.scalar()
            
            # Get table count
            result = await db.execute(text("""
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
            """))
            table_count = result.scalar()
            
            return {
                "status": "healthy",
                "database": "connected",
                "tables": table_count,
                "message": "Database connection successful!"
            }
            
    except Exception as e:
        return {
            "status": "unhealthy", 
            "database": "disconnected",
            "error": str(e)
        }


@app.get("/startups/count")
async def get_startup_count():
    """Get count of startups in database."""
    try:
        async for db in get_async_db():
            result = await db.execute(text("SELECT COUNT(*) FROM startups"))
            count = result.scalar()
            return {"startup_count": count}
    except Exception as e:
        return {"error": str(e)}


@app.get("/vcs/count") 
async def get_vc_count():
    """Get count of VCs in database."""
    try:
        async for db in get_async_db():
            result = await db.execute(text("SELECT COUNT(*) FROM vcs"))
            count = result.scalar()
            return {"vc_count": count}
    except Exception as e:
        return {"error": str(e)}


async def test_minimal_api():
    """Test the minimal API functionality."""
    print("🧪 Testing Minimal API with Database Connection")
    print("=" * 50)
    
    try:
        # Test app creation
        print(f"✅ FastAPI app created: {app.title}")
        
        # Test database engine
        engine = get_async_engine()
        print("✅ Database engine created")
        
        # Test health endpoint logic
        async for db in get_async_db():
            result = await db.execute(text("SELECT 1"))
            print("✅ Database query successful")
            
            result = await db.execute(text("SELECT COUNT(*) FROM startups"))
            startup_count = result.scalar()
            print(f"✅ Startups count: {startup_count}")
            
            result = await db.execute(text("SELECT COUNT(*) FROM vcs"))
            vc_count = result.scalar()
            print(f"✅ VCs count: {vc_count}")
            
            break
        
        print("\n🎉 MINIMAL API WITH DATABASE IS WORKING!")
        print("You can now start the API server with:")
        print("python -m uvicorn test_api_minimal:app --reload --host 0.0.0.0 --port 8000")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    result = asyncio.run(test_minimal_api())
    sys.exit(0 if result else 1)