# VC Matching Platform - Complete Setup Guide

This guide provides comprehensive instructions for setting up and running the VC Matching Platform locally.

## 🚀 Quick Start (One Command)

The easiest way to get started:

```bash
./run.sh
```

This interactive script will:
- ✅ Check all prerequisites
- ✅ Set up environment configuration
- ✅ Generate secure secret keys
- ✅ Prompt for your OpenAI API key
- ✅ Start all services
- ✅ Display service URLs

## 📋 Prerequisites

### For Docker Mode (Recommended):
- Docker Desktop
- OpenAI API key

### For Local/Hybrid Mode:
- Python 3.9+
- Docker Desktop (for hybrid mode)
- PostgreSQL 14+ (for local mode only)
- Redis 7+ (for local mode only)
- OpenAI API key

## 🎯 Setup Modes

### 1. Docker Mode (Recommended for Production)
```bash
./run.sh docker
```

**What it does:**
- Runs everything in Docker containers
- No local Python setup required
- Includes PostgreSQL, Redis, API, Celery workers, and Flower
- Perfect for production-like environment

**Services:**
- API Server: http://localhost:8000
- Flower Monitor: http://localhost:5555
- PostgreSQL: localhost:5432
- Redis: localhost:6379

### 2. Hybrid Mode (Recommended for Development)
```bash
./run.sh hybrid
```

**What it does:**
- Runs PostgreSQL and Redis in Docker
- Runs the Python app locally with hot reload
- Best of both worlds for development
- Fast iteration with reliable services

**Benefits:**
- Hot reload for code changes
- Easy debugging
- Reliable database and Redis
- No local PostgreSQL/Redis installation needed

### 3. Local Mode (Advanced)
```bash
./run.sh local
```

**What it does:**
- Runs everything locally
- Requires local PostgreSQL and Redis
- Full control over all services
- Best for advanced development scenarios

**Requirements:**
- Local PostgreSQL server running
- Local Redis server running
- Python 3.9+ environment

## 🔧 Environment Configuration

The script automatically handles environment setup:

1. **Copies template**: Uses `.env.staging` or `.env.example`
2. **Generates secrets**: Creates secure secret keys
3. **Prompts for API key**: Asks for your OpenAI API key
4. **Updates configuration**: Adjusts settings for the chosen mode

### Manual Environment Setup:
```bash
# Copy template
cp .env.staging .env

# Edit the file
nano .env

# Required: Add your OpenAI API key
OPENAI_API_KEY=sk-your-actual-key-here
```

## 🛠️ Advanced Usage

### Reset Environment:
```bash
./run.sh docker --reset
```

### Direct Script Usage:
```bash
# Use the main script directly
./scripts/setup-and-run.sh hybrid

# Show all options
./scripts/setup-and-run.sh --help
```

### Manual Service Management:

**Start services manually:**
```bash
# Docker mode
docker-compose up -d

# Local services (after hybrid/local setup)
uvicorn src.api.main:app --reload &
celery -A src.workers.celery_app worker --loglevel=info &
celery -A src.workers.celery_app beat --loglevel=info &
```

**Stop services:**
```bash
# If using run.sh
./stop-services.sh

# Docker services
docker-compose down

# Manual cleanup
pkill -f uvicorn
pkill -f celery
```

## 🧪 Testing Your Setup

### Health Check:
```bash
curl http://localhost:8000/health
```

### API Documentation:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

### Run Tests:
```bash
# Install test dependencies
pip install -r requirements-test.txt

# Run tests
pytest tests/ --cov=src --cov-report=html

# View coverage report
open htmlcov/index.html
```

## 🔍 Troubleshooting

### Common Issues:

**1. Docker not running:**
```bash
# Start Docker Desktop
# Then run: ./run.sh docker
```

**2. Port conflicts:**
```bash
# Check what's using the port
lsof -i :8000

# Kill the process or use different ports
```

**3. Permission errors:**
```bash
# Make scripts executable
chmod +x run.sh
chmod +x scripts/setup-and-run.sh
```

**4. Python version issues:**
```bash
# Check Python version
python3 --version

# Use specific Python version
python3.9 -m venv venv
```

**5. Database connection issues:**
```bash
# Check if PostgreSQL is running
docker-compose ps db

# View database logs
docker-compose logs db
```

### Log Files:

**Docker mode:**
```bash
docker-compose logs -f api
docker-compose logs -f celery
```

**Local/Hybrid mode:**
```bash
tail -f logs/api.log
tail -f logs/celery-worker.log
tail -f logs/celery-beat.log
```

## 🎉 Success!

Once everything is running, you should see:

```
✅ VC Matching Platform is running in [mode] mode!

🌐 Service URLs:
  API Documentation: http://localhost:8000/docs
  API ReDoc:         http://localhost:8000/redoc
  Flower Monitor:    http://localhost:5555
  Database:          localhost:5432
  Redis:             localhost:6379
```

## 📚 Next Steps

1. **Explore the API**: Visit http://localhost:8000/docs
2. **Run tests**: `pytest tests/`
3. **Check monitoring**: Visit http://localhost:5555 for Celery tasks
4. **Read the docs**: Check out the other documentation files
5. **Start developing**: The API is ready for your enhancements!

## 🆘 Getting Help

If you encounter issues:

1. Check the logs (see troubleshooting section)
2. Verify prerequisites are installed
3. Try resetting with `./run.sh docker --reset`
4. Check the GitHub issues or create a new one

Happy coding! 🚀
