# Deployment Guide - VC Matching Platform

## Overview

This guide covers deploying the VC Matching Platform using Docker Compose. The platform consists of:
- FastAPI application server
- PostgreSQL database
- Redis cache
- Celery workers for background tasks
- Celery Beat for scheduled tasks
- Flower for task monitoring
- Nginx reverse proxy
- Prometheus & Grafana for monitoring (staging only)

## Prerequisites

- Docker 20.10+
- Docker Compose 1.29+
- 4GB+ RAM available
- OpenAI API key (for AI features)

## Quick Start - Development

```bash
# Clone the repository
git clone <repository-url>
cd vc-matching-platform

# Copy environment template
cp .env.example .env

# Edit .env with your settings
# Minimum required: OPENAI_API_KEY

# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Access the application
# API Docs: http://localhost:8000/api/v1/docs
# Flower: http://localhost:5555
```

## Staging Deployment

### 1. Prepare Environment

```bash
# Copy staging environment template
cp .env.staging .env

# Edit .env with staging values
# Required:
# - OPENAI_API_KEY
# - SECRET_KEY (generate a secure one)
# - Database passwords
```

### 2. Deploy with <PERSON><PERSON><PERSON>

```bash
# Make deploy script executable
chmod +x scripts/deploy-staging.sh

# Run deployment
./scripts/deploy-staging.sh
```

### 3. Manual Deployment

```bash
# Build and start services
docker-compose -f docker-compose.yml -f docker-compose.staging.yml up -d --build

# Check service health
docker-compose -f docker-compose.yml -f docker-compose.staging.yml ps

# Run database migrations
docker-compose exec api alembic upgrade head
```

## Service URLs

### Development
- API Documentation: http://localhost:8000/api/v1/docs
- API (Swagger UI): http://localhost:8000/api/v1/docs
- API (ReDoc): http://localhost:8000/api/v1/redoc
- Flower (Celery): http://localhost:5555
- PostgreSQL: localhost:5432
- Redis: localhost:6379

### Staging (with Nginx)
- API Documentation: http://localhost/api/v1/docs
- Flower: http://localhost/flower/
- Prometheus: http://localhost:9090
- Grafana: http://localhost:3000 (admin/admin)

## Configuration

### Environment Variables

Key environment variables:

```bash
# Application
ENVIRONMENT=staging|production|development
DEBUG=true|false
SECRET_KEY=your-secret-key
LOG_LEVEL=DEBUG|INFO|WARNING|ERROR

# Database
DATABASE_URL=********************************/dbname
USE_ASYNC_DB=true|false

# Redis
REDIS_URL=redis://host:6379/0

# OpenAI
OPENAI_API_KEY=your-api-key

# Authentication
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Rate Limiting
RATE_LIMIT_ENABLED=true|false
RATE_LIMIT_PER_MINUTE=100
```

### Docker Compose Files

- `docker-compose.yml` - Base configuration for all environments
- `docker-compose.staging.yml` - Staging-specific overrides
- `docker-compose.prod.yml` - Production overrides (to be created)

## Common Operations

### Database Management

```bash
# Run migrations
docker-compose exec api alembic upgrade head

# Create new migration
docker-compose exec api alembic revision --autogenerate -m "Description"

# Rollback migration
docker-compose exec api alembic downgrade -1

# Access PostgreSQL
docker-compose exec db psql -U postgres -d vc_matching_platform
```

### Monitoring & Logs

```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f api
docker-compose logs -f celery

# Monitor Celery tasks
# Open http://localhost:5555 (Flower)

# Check service health
curl http://localhost/health
```

### Scaling

```bash
# Scale Celery workers
docker-compose up -d --scale celery=3

# Scale API workers (modify docker-compose.yml)
# Change gunicorn --workers value
```

### Backup & Restore

```bash
# Backup database
docker-compose exec db pg_dump -U postgres vc_matching_platform > backup.sql

# Restore database
docker-compose exec -T db psql -U postgres vc_matching_platform < backup.sql

# Backup Redis
docker-compose exec redis redis-cli SAVE
docker cp $(docker-compose ps -q redis):/data/dump.rdb ./redis-backup.rdb
```

## Troubleshooting

### Services Won't Start

```bash
# Check logs
docker-compose logs [service-name]

# Verify environment variables
docker-compose config

# Check port conflicts
netstat -tulpn | grep -E '(8000|5432|6379|5555)'
```

### Database Connection Issues

```bash
# Test database connection
docker-compose exec api python -c "from src.database.setup import engine; print(engine.url)"

# Verify migrations
docker-compose exec api alembic current
```

### Celery Issues

```bash
# Check Celery connectivity
docker-compose exec celery celery -A src.workers.celery_app inspect ping

# List active tasks
docker-compose exec celery celery -A src.workers.celery_app inspect active

# Purge all tasks
docker-compose exec celery celery -A src.workers.celery_app purge
```

### Performance Issues

1. Check resource usage:
```bash
docker stats
```

2. Increase resources in docker-compose.yml:
```yaml
services:
  api:
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
```

3. Optimize PostgreSQL:
```bash
# Add to postgres environment
POSTGRES_SHARED_BUFFERS=256MB
POSTGRES_EFFECTIVE_CACHE_SIZE=1GB
```

## Security Considerations

### Production Checklist

- [ ] Change all default passwords
- [ ] Generate secure SECRET_KEY
- [ ] Enable HTTPS (use Let's Encrypt)
- [ ] Restrict database access
- [ ] Enable firewall rules
- [ ] Regular security updates
- [ ] Enable log aggregation
- [ ] Set up monitoring alerts
- [ ] Configure backup strategy
- [ ] Rate limiting enabled
- [ ] CORS properly configured

### SSL/TLS Setup

For production, add SSL:

1. Obtain certificates (Let's Encrypt recommended)
2. Update nginx configuration
3. Redirect HTTP to HTTPS
4. Enable HSTS headers

## Monitoring

### Prometheus Metrics

Available at http://localhost:9090 in staging

Key metrics to monitor:
- Request rate and latency
- Error rates
- Database connection pool
- Celery task queue length
- Redis memory usage

### Grafana Dashboards

Access at http://localhost:3000 (admin/admin)

Import dashboards for:
- FastAPI metrics
- PostgreSQL monitoring
- Redis monitoring
- Celery/Flower metrics

## Maintenance

### Regular Tasks

1. **Daily**
   - Check error logs
   - Monitor disk usage
   - Verify backups

2. **Weekly**
   - Review performance metrics
   - Check for security updates
   - Clean up old logs

3. **Monthly**
   - Update dependencies
   - Review and optimize queries
   - Audit security settings

### Updates

```bash
# Update application
git pull origin main
docker-compose build --no-cache api celery celery-beat
docker-compose up -d

# Update dependencies
# Edit requirements.txt, then:
docker-compose build --no-cache
```

## Support

For issues:
1. Check logs: `docker-compose logs [service]`
2. Review this guide's troubleshooting section
3. Check GitHub issues
4. Contact support team