#!/usr/bin/env python3
"""Quick test to debug match creation."""

from fastapi.testclient import Test<PERSON>lient
from src.api.main import app

client = TestClient(app)

# Register and login
user_data = {
    "email": "<EMAIL>",
    "username": "testuser",
    "password": "testpass123",
    "full_name": "Test User"
}
response = client.post("/api/v1/auth/register", json=user_data)
print(f"Register: {response.status_code}")

login_response = client.post(
    "/api/v1/auth/login",
    data={
        "username": "testuser",
        "password": "testpass123",
        "grant_type": "password"
    },
    headers={"Content-Type": "application/x-www-form-urlencoded"}
)
print(f"Login: {login_response.status_code}")
token = login_response.json()["access_token"]
headers = {"Authorization": f"Bearer {token}"}

# Create startup
startup_data = {
    "name": "Test Startup",
    "sector": "AI/ML",
    "stage": "Series A",
    "description": "Test description"
}
response = client.post("/api/v1/startups", json=startup_data, headers=headers)
print(f"Create startup: {response.status_code}")
startup_id = response.json()["id"]

# Create VC
vc_data = {
    "firm_name": "Test VC",
    "website": "https://test.vc",
    "sectors": ["AI/ML"],
    "stages": ["Series A"]
}
response = client.post("/api/v1/vcs", json=vc_data, headers=headers)
print(f"Create VC: {response.status_code}")
vc_id = response.json()["id"]

# Create match
match_data = {
    "startup_id": startup_id,
    "vc_id": vc_id
}
response = client.post("/api/v1/matches", json=match_data, headers=headers)
print(f"Create match: {response.status_code}")
if response.status_code != 201:
    print(f"Error: {response.json()}")