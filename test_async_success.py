#!/usr/bin/env python3
"""Simple test to demonstrate async mode working without greenlet errors."""

import os
os.environ["USE_ASYNC_DB"] = "true"

import asyncio
import logging
from uuid import uuid4

from src.database.async_setup import AsyncSessionLocal
from src.database.repositories.async_startup_repository import AsyncStartupRepository
from src.core.models.startup import Startup as StartupDomainModel

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def main():
    """Demonstrate async operations working without greenlet errors."""
    logger.info("=" * 60)
    logger.info("ASYNC MODE SUCCESS DEMONSTRATION")
    logger.info("=" * 60)
    
    async with AsyncSessionLocal() as session:
        repo = AsyncStartupRepository(session)
        
        # Create multiple startups
        logger.info("\n1. Creating startups asynchronously...")
        startups = []
        for i in range(5):
            startup = StartupDomainModel(
                id=uuid4(),
                name=f"Async Startup {i}",
                sector="Technology",
                stage="Series A",
                description=f"Async test startup {i}",
                website=f"https://startup{i}.com",
                team_size=10 + i,
                monthly_revenue=50000.0 * (i + 1)
            )
            saved = await repo.save(startup)
            startups.append(saved)
            logger.info(f"   ✅ Created: {saved.name}")
        
        # Read operations
        logger.info("\n2. Reading startups asynchronously...")
        for startup in startups[:3]:
            found = await repo.find_by_id(startup.id)
            if found:
                logger.info(f"   ✅ Found: {found.name}")
        
        # Search operations
        logger.info("\n3. Searching startups...")
        results = await repo.search("Async")
        logger.info(f"   ✅ Found {len(results)} startups matching 'Async'")
        
        # Find by sector
        logger.info("\n4. Finding by sector...")
        tech_startups = await repo.find_by_sector("Technology")
        logger.info(f"   ✅ Found {len(tech_startups)} Technology startups")
        
        # Concurrent operations
        logger.info("\n5. Running concurrent operations...")
        async def concurrent_op(index):
            startup = await repo.find_by_id(startups[index % len(startups)].id)
            return startup.name if startup else None
        
        tasks = [concurrent_op(i) for i in range(10)]
        results = await asyncio.gather(*tasks)
        logger.info(f"   ✅ Completed {len(results)} concurrent reads")
        
        # Cleanup
        logger.info("\n6. Cleaning up...")
        for startup in startups:
            await repo.delete(startup.id)
        logger.info("   ✅ All test data cleaned up")
        
        logger.info("\n" + "=" * 60)
        logger.info("✅ SUCCESS: All async operations completed!")
        logger.info("✅ NO GREENLET ERRORS!")
        logger.info("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())