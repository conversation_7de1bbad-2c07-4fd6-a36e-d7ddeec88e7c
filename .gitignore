# Python
venv/
env/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.egg-info/
dist/
build/
*.egg

# Environment
.env
.env.*
!.env.example
!.env.staging

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
coverage.xml
*.cover

# Database
*.db
*.sqlite
*.sqlite3

# IDE
.DS_Store
.idea/
.vscode/
*.swp
*.swo
*~

# Logs
*.log
logs/

# Docker
docker-compose.override.yml

# Temporary files
*.tmp
*.bak
.cache/

# OS
Thumbs.db

# Project specific
postgres_data/
postgres_data_staging/
redis_data/
redis_data_staging/
nginx_logs/
prometheus_data/
grafana_data/

# Playwright
playwright-report/
test-results/

# Celery
celerybeat-schedule
celerybeat.pid