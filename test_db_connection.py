#!/usr/bin/env python3
"""Test database connection and basic operations."""

import sys
from pathlib import Path

# Add project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

from src.database.setup import get_engine, init_db
from src.core.config import settings
from sqlalchemy import text


def test_connection():
    """Test database connection."""
    print(f"Testing connection to: {settings.database_url}")
    
    try:
        engine = get_engine()
        
        # Test basic connection
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            print("✅ Database connection successful!")
            
            # Check PostgreSQL version
            result = conn.execute(text("SELECT version()"))
            version = result.scalar()
            print(f"✅ PostgreSQL version: {version}")
            
            # Initialize tables
            print("\nInitializing database tables...")
            init_db(engine)
            print("✅ Database tables created successfully!")
            
            # Check tables
            result = conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                ORDER BY table_name
            """))
            tables = [row[0] for row in result]
            print(f"\n✅ Tables created: {tables}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        print("\nMake sure PostgreSQL is running and the database exists:")
        print("  1. Install PostgreSQL if not installed")
        print("  2. Start PostgreSQL service")
        print("  3. Create database: createdb vc_matching_platform")
        print("  4. Update DATABASE_URL in .env file")
        sys.exit(1)


if __name__ == "__main__":
    test_connection()