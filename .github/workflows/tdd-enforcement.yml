name: TDD Enforcement

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  tdd-compliance:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
      with:
        fetch-depth: 0  # Full history for git analysis
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Cache pip packages
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements*.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-test.txt
    
    - name: Check TDD compliance
      run: |
        python scripts/check_tdd_compliance.py
      continue-on-error: true
    
    - name: Run tests with coverage
      run: |
        PYTHONPATH=. pytest tests/ -v --cov=src --cov-report=xml --cov-report=term-missing --cov-fail-under=80
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: true
    
    - name: Generate coverage badge
      run: |
        COVERAGE=$(python -c "import xml.etree.ElementTree as ET; print(round(float(ET.parse('coverage.xml').getroot().get('line-rate')) * 100, 1))")
        echo "COVERAGE=$COVERAGE" >> $GITHUB_ENV
        echo "Coverage: $COVERAGE%"
    
    - name: Create coverage comment
      if: github.event_name == 'pull_request'
      uses: py-cov-action/python-coverage-comment-action@v3
      with:
        GITHUB_TOKEN: ${{ github.token }}
        MINIMUM_GREEN: 80
        MINIMUM_ORANGE: 60
    
    - name: Check for test-first violations
      run: |
        # Check if implementation files were added without tests
        git diff --name-only origin/main..HEAD | grep "^src/" | while read impl_file; do
          test_file=$(echo $impl_file | sed 's|src/|tests/|' | sed 's|\.py$|_test.py|')
          if [ ! -f "$test_file" ]; then
            echo "❌ TDD Violation: No test for $impl_file"
            exit 1
          fi
        done
    
    - name: Enforce coverage threshold
      run: |
        if [ "$COVERAGE" -lt 80 ]; then
          echo "❌ Coverage $COVERAGE% is below 80% threshold"
          exit 1
        fi
        echo "✅ Coverage $COVERAGE% meets threshold"

  test-quality:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-test.txt
        pip install pytest-timeout pytest-xdist
    
    - name: Check test speed
      run: |
        # Ensure unit tests are fast
        PYTHONPATH=. pytest tests/unit/ -v --timeout=1 --timeout-method=thread
    
    - name: Check test independence
      run: |
        # Run tests in random order to ensure independence
        PYTHONPATH=. pytest tests/ -v --random-order
    
    - name: Parallel test execution
      run: |
        # Ensure tests can run in parallel
        PYTHONPATH=. pytest tests/ -v -n auto

  code-quality:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install linting tools
      run: |
        pip install ruff mypy black isort
    
    - name: Run ruff
      run: ruff check src/ tests/
    
    - name: Run mypy
      run: mypy src/ tests/ --ignore-missing-imports
    
    - name: Check formatting
      run: |
        black --check src/ tests/
        isort --check-only src/ tests/