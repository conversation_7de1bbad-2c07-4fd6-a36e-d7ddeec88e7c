name: Dependency Check

on:
  schedule:
    # Run every Monday at 9am UTC
    - cron: '0 9 * * 1'
  workflow_dispatch:

jobs:
  check-dependencies:
    name: Check for Outdated Dependencies
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'

    - name: Install pip-audit
      run: |
        python -m pip install --upgrade pip
        pip install pip-audit

    - name: Run pip-audit
      run: |
        pip-audit --desc -f json > pip-audit-report.json || true
        cat pip-audit-report.json

    - name: Check for outdated packages
      run: |
        pip list --outdated --format=json > outdated-packages.json || true
        cat outdated-packages.json

    - name: Create issue for vulnerabilities
      if: failure()
      uses: actions/github-script@v6
      with:
        script: |
          const title = 'Security: Dependency vulnerabilities detected';
          const body = 'Automated scan found dependency vulnerabilities. Check the workflow run for details.';
          
          github.rest.issues.create({
            owner: context.repo.owner,
            repo: context.repo.repo,
            title: title,
            body: body,
            labels: ['security', 'dependencies']
          });