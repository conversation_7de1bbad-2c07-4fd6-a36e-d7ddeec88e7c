name: Release

on:
  push:
    tags:
      - 'v*'

permissions:
  contents: write
  packages: write

jobs:
  release:
    name: Create Release
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Generate changelog
      id: changelog
      run: |
        # Get the previous tag
        PREV_TAG=$(git describe --tags --abbrev=0 HEAD^ 2>/dev/null || echo "")
        
        # Generate changelog
        if [ -z "$PREV_TAG" ]; then
          CHANGELOG=$(git log --pretty=format:"- %s" --reverse)
        else
          CHANGELOG=$(git log --pretty=format:"- %s" "$PREV_TAG"..HEAD --reverse)
        fi
        
        # Save to file for release body
        echo "# Changelog" > CHANGELOG.md
        echo "" >> CHANGELOG.md
        echo "$CHANGELOG" >> CHANGELOG.md
        
        # Set output
        echo "changelog<<EOF" >> $GITHUB_OUTPUT
        echo "$CHANGELOG" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT

    - name: Create Release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref }}
        release_name: Release ${{ github.ref }}
        body: |
          ## 🚀 Release ${{ github.ref_name }}
          
          ### 📋 Changes
          ${{ steps.changelog.outputs.changelog }}
          
          ### 🐳 Docker Image
          ```bash
          docker pull ghcr.io/${{ github.repository }}:${{ github.ref_name }}
          ```
          
          ### 📖 Documentation
          - [Deployment Guide](https://github.com/${{ github.repository }}/blob/main/DEPLOYMENT.md)
          - [API Documentation](https://github.com/${{ github.repository }}/blob/main/README.md#-key-api-endpoints)
          
          ### 🔧 Quick Start
          ```bash
          # Clone the repository
          git clone https://github.com/${{ github.repository }}.git
          cd vc-matching-platform
          
          # Deploy with Docker
          cp .env.staging .env
          # Edit .env with your OPENAI_API_KEY
          ./scripts/deploy-staging.sh
          ```
        draft: false
        prerelease: false