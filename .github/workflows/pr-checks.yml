name: PR Checks

on:
  pull_request:
    types: [opened, synchronize, reopened]

jobs:
  check-files:
    name: Check Required Files
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Check for test files
      run: |
        # Check if tests were added/modified for new code
        echo "Checking for test coverage..."
        
        # Get list of changed Python files
        CHANGED_PY_FILES=$(git diff --name-only origin/main...HEAD | grep "\.py$" | grep -v "__pycache__" | grep -v "test_" || true)
        
        if [ ! -z "$CHANGED_PY_FILES" ]; then
          echo "Changed Python files:"
          echo "$CHANGED_PY_FILES"
          
          # Check if corresponding test files exist
          for file in $CHANGED_PY_FILES; do
            if [[ $file == src/* ]]; then
              TEST_FILE=$(echo $file | sed 's/src/tests\/unit/' | sed 's/\.py$/\_test.py/')
              if [ ! -f "$TEST_FILE" ]; then
                echo "⚠️  Warning: No test file found for $file"
                echo "   Expected: $TEST_FILE"
              fi
            fi
          done
        fi

    - name: Check for documentation
      run: |
        # Check if README was updated for significant changes
        if git diff --name-only origin/main...HEAD | grep -E "(requirements\.txt|docker-compose\.yml|Dockerfile)" > /dev/null; then
          if ! git diff --name-only origin/main...HEAD | grep -E "(README\.md|DEPLOYMENT\.md)" > /dev/null; then
            echo "⚠️  Warning: Infrastructure changes detected but no documentation updates"
          fi
        fi

  size-check:
    name: PR Size Check
    runs-on: ubuntu-latest
    
    steps:
    - name: Check PR size
      uses: actions/github-script@v6
      with:
        script: |
          const pr = context.payload.pull_request;
          const additions = pr.additions;
          const deletions = pr.deletions;
          const total = additions + deletions;
          
          let label = 'size/XS';
          let comment = '';
          
          if (total < 10) {
            label = 'size/XS';
          } else if (total < 100) {
            label = 'size/S';
          } else if (total < 500) {
            label = 'size/M';
          } else if (total < 1000) {
            label = 'size/L';
            comment = '⚠️ This PR is quite large. Consider breaking it into smaller PRs.';
          } else {
            label = 'size/XL';
            comment = '🚨 This PR is very large! Please break it into smaller, reviewable chunks.';
          }
          
          // Add label
          await github.rest.issues.addLabels({
            owner: context.repo.owner,
            repo: context.repo.repo,
            issue_number: pr.number,
            labels: [label]
          });
          
          // Add comment if needed
          if (comment) {
            await github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: pr.number,
              body: comment
            });
          }

  auto-assign:
    name: Auto Assign Reviewers
    runs-on: ubuntu-latest
    
    steps:
    - name: Auto assign reviewers
      uses: actions/github-script@v6
      with:
        script: |
          // Add reviewers based on changed files
          const pr = context.payload.pull_request;
          const files = await github.rest.pulls.listFiles({
            owner: context.repo.owner,
            repo: context.repo.repo,
            pull_number: pr.number
          });
          
          const reviewers = new Set();
          
          // Add reviewers based on file patterns
          for (const file of files.data) {
            if (file.filename.includes('database/')) {
              // Database changes need DB expert review
              console.log('Database changes detected');
            }
            if (file.filename.includes('api/')) {
              // API changes need API expert review
              console.log('API changes detected');
            }
            if (file.filename.includes('.github/workflows/')) {
              // CI/CD changes need DevOps review
              console.log('CI/CD changes detected');
            }
          }
          
          // Note: Add actual GitHub usernames here
          // if (reviewers.size > 0) {
          //   await github.rest.pulls.requestReviewers({
          //     owner: context.repo.owner,
          //     repo: context.repo.repo,
          //     pull_number: pr.number,
          //     reviewers: Array.from(reviewers)
          //   });
          // }