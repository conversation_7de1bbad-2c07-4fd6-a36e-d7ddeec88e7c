# TDD Workflow Guide for VC-Startup Matching Platform

## Overview
This guide ensures strict TDD compliance through automated gates, proper agent utilization, and context preservation strategies. Current coverage: **39%** → Target: **80%+**

## 1. TDD Workflow with Gates/Checkpoints

### RED-GREEN-REFACTOR Cycle with Enforcement Gates

```mermaid
graph TD
    A[New Feature Request] --> B{Gate 1: Test First Check}
    B -->|No Test| C[BLOCKED: Write Test First]
    B -->|Test Written| D[Run Test]
    D --> E{Gate 2: Test Must Fail}
    E -->|Test Passes| F[BLOCKED: Test Invalid]
    E -->|Test Fails| G[Write Implementation]
    G --> H[Run Test]
    H --> I{Gate 3: Test Must Pass}
    I -->|Test Fails| G
    I -->|Test Passes| J[Refactor]
    J --> K[Run All Tests]
    K --> L{Gate 4: All Tests Green}
    L -->|Any Fail| J
    L -->|All Pass| M[Coverage Check]
    M --> N{Gate 5: Coverage ≥80%}
    N -->|Below 80%| O[Add More Tests]
    N -->|Above 80%| P[Code Review]
```

### Implementation Checklist per Feature

```python
# TDD Checkpoint Template
class TDDCheckpoint:
    """Automated TDD compliance verification"""
    
    def __init__(self, feature_name: str):
        self.feature = feature_name
        self.checkpoints = {
            "test_written_first": False,
            "test_initially_failed": False,
            "minimal_code_written": False,
            "test_now_passes": False,
            "refactored": False,
            "coverage_above_80": False,
            "all_tests_green": False
        }
    
    def verify_compliance(self):
        """Automated gate enforcement"""
        if not all(self.checkpoints.values()):
            raise TDDViolation(f"Feature {self.feature} failed TDD compliance")
```

## 2. Agent Assignment Matrix

### Primary Development Flow

| Phase | Primary Agent | Supporting Agents | Key Responsibilities |
|-------|--------------|-------------------|---------------------|
| **1. Domain Modeling** | `ddd_expert` | `tdd_specialist` | - Define entities, value objects<br>- Create domain services<br>- Establish bounded contexts |
| **2. Test Writing** | `tdd_specialist` | `ddd_expert` | - Write failing tests first<br>- Define test fixtures<br>- Create test scenarios |
| **3. API Design** | `fastapi_architect` | `tdd_specialist` | - Design endpoints<br>- Create schemas<br>- Implement middleware |
| **4. AI Integration** | `ai_langchain_expert` | `tdd_specialist` | - LLM prompts<br>- Chain design<br>- Response parsing |
| **5. Data Layer** | `database_performance_expert` | `ddd_expert` | - Repository pattern<br>- Query optimization<br>- Migrations |
| **6. Web Scraping** | `web_scraping_expert` | `background_tasks_expert` | - Scraper implementation<br>- Rate limiting<br>- Data extraction |
| **7. Background Tasks** | `background_tasks_expert` | `database_performance_expert` | - Celery tasks<br>- Queue management<br>- Scheduling |
| **8. Deployment** | `devops_deployment_expert` | `fastapi_architect` | - Docker config<br>- CI/CD pipeline<br>- Environment setup |

### Agent Collaboration Patterns

```python
# Example: Adding a new matching algorithm feature
workflow = {
    "step_1": {
        "agent": "tdd_specialist",
        "action": "Write failing test for new matching criteria",
        "output": "test_advanced_matching.py"
    },
    "step_2": {
        "agent": "ddd_expert",
        "action": "Design domain model changes",
        "context_from": "step_1",
        "output": "Updated Match entity"
    },
    "step_3": {
        "agent": "ai_langchain_expert",
        "action": "Create LLM chain for similarity scoring",
        "context_from": ["step_1", "step_2"],
        "output": "AI scoring implementation"
    }
}
```

## 3. Context Preservation Strategy

### A. Test Context File

```python
# tests/context/test_context.py
from dataclasses import dataclass
from typing import List, Dict
import json

@dataclass
class TestContext:
    """Preserves context across TDD cycles"""
    feature_name: str
    failing_tests: List[str]
    implementation_notes: Dict[str, str]
    coverage_before: float
    coverage_target: float
    
    def save(self):
        """Persist context for agent handoffs"""
        with open(f"tests/context/{self.feature_name}.json", "w") as f:
            json.dump(self.__dict__, f, indent=2)
    
    @classmethod
    def load(cls, feature_name: str):
        """Restore context for continuation"""
        with open(f"tests/context/{feature_name}.json", "r") as f:
            return cls(**json.load(f))
```

### B. Agent Handoff Protocol

```markdown
# Agent Handoff Template
## From: [Current Agent]
## To: [Next Agent]
## Feature: [Feature Name]

### Current State:
- [ ] Tests written and failing
- [ ] Implementation started/completed
- [ ] Coverage at X%

### Context:
- Key decisions: [...]
- Blockers: [...]
- Next steps: [...]

### Artifacts:
- Test files: [paths]
- Implementation files: [paths]
- Related issues: [links]
```

## 4. Automated TDD Compliance Checks

### Pre-commit Hook

```python
#!/usr/bin/env python3
# .git/hooks/pre-commit
import subprocess
import sys
import json

def check_tdd_compliance():
    """Enforce TDD gates before commit"""
    
    # 1. Check for new implementation files without tests
    result = subprocess.run(
        ["git", "diff", "--cached", "--name-only"],
        capture_output=True, text=True
    )
    
    files = result.stdout.strip().split('\n')
    impl_files = [f for f in files if f.startswith('src/') and f.endswith('.py')]
    test_files = [f for f in files if f.startswith('tests/') and f.endswith('.py')]
    
    # 2. Ensure tests exist for implementations
    for impl_file in impl_files:
        expected_test = impl_file.replace('src/', 'tests/').replace('.py', '_test.py')
        if expected_test not in test_files:
            print(f"❌ TDD Violation: No test for {impl_file}")
            return False
    
    # 3. Run tests and check coverage
    coverage_result = subprocess.run(
        ["pytest", "--cov=src", "--cov-report=json", "--quiet"],
        capture_output=True
    )
    
    if coverage_result.returncode != 0:
        print("❌ Tests failing - cannot commit")
        return False
    
    # 4. Check coverage threshold
    with open("coverage.json", "r") as f:
        coverage_data = json.load(f)
        total_coverage = coverage_data["totals"]["percent_covered"]
        
    if total_coverage < 80:
        print(f"❌ Coverage {total_coverage:.1f}% is below 80% threshold")
        return False
    
    print(f"✅ TDD compliance verified - Coverage: {total_coverage:.1f}%")
    return True

if __name__ == "__main__":
    if not check_tdd_compliance():
        sys.exit(1)
```

### GitHub Actions Workflow

```yaml
# .github/workflows/tdd-enforcement.yml
name: TDD Enforcement

on: [push, pull_request]

jobs:
  tdd-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      
      - name: Install dependencies
        run: |
          pip install -r requirements.txt
          pip install -r requirements-test.txt
      
      - name: Check test-first compliance
        run: |
          python scripts/check_tdd_compliance.py
      
      - name: Run tests with coverage
        run: |
          PYTHONPATH=. pytest tests/ --cov=src --cov-report=xml --cov-fail-under=80
      
      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml
          fail_ci_if_error: true
```

## 5. Practical Example: Adding Investment Thesis Matching

### Step 1: TDD Specialist Creates Failing Tests

```python
# tests/unit/test_investment_thesis_matching.py
import pytest
from src.core.models.vc import VC, InvestmentThesis
from src.core.models.startup import Startup
from src.core.services.thesis_matcher import ThesisMatcher

class TestInvestmentThesisMatching:
    """Test investment thesis matching feature"""
    
    def test_thesis_matches_startup_in_target_sector(self):
        # Arrange
        thesis = InvestmentThesis(
            sectors=["B2B SaaS", "FinTech"],
            stages=["Seed", "Series A"],
            check_size_range=(500_000, 2_000_000),
            geographic_focus=["US", "EU"]
        )
        vc = VC(name="AI Ventures", thesis=thesis)
        startup = Startup(
            name="PayTech Inc",
            sector="FinTech",
            stage="Seed",
            seeking_amount=1_000_000,
            location="US"
        )
        
        # Act
        matcher = ThesisMatcher()
        match = matcher.evaluate_thesis_fit(vc, startup)
        
        # Assert
        assert match.is_match is True
        assert match.score >= 0.8
        assert "sector alignment" in match.reasons
        assert "stage match" in match.reasons
        assert "check size fit" in match.reasons
```

### Step 2: DDD Expert Designs Domain Model

```python
# Context from TDD Specialist
# src/core/models/vc.py
from dataclasses import dataclass
from typing import List, Tuple, Optional

@dataclass
class InvestmentThesis:
    """Value object representing VC investment preferences"""
    sectors: List[str]
    stages: List[str]
    check_size_range: Tuple[int, int]
    geographic_focus: List[str]
    excluded_sectors: List[str] = None
    
    def __post_init__(self):
        self.excluded_sectors = self.excluded_sectors or []
        
    def matches_sector(self, sector: str) -> bool:
        """Check if sector aligns with thesis"""
        return (sector in self.sectors and 
                sector not in self.excluded_sectors)
```

### Step 3: Implement Minimal Code to Pass

```python
# src/core/services/thesis_matcher.py
from dataclasses import dataclass
from typing import List
from src.core.models.vc import VC
from src.core.models.startup import Startup

@dataclass
class ThesisMatch:
    is_match: bool
    score: float
    reasons: List[str]

class ThesisMatcher:
    """Service for matching VC thesis with startups"""
    
    def evaluate_thesis_fit(self, vc: VC, startup: Startup) -> ThesisMatch:
        reasons = []
        score = 0.0
        
        # Sector alignment
        if vc.thesis.matches_sector(startup.sector):
            score += 0.3
            reasons.append("sector alignment")
        
        # Stage match
        if startup.stage in vc.thesis.stages:
            score += 0.3
            reasons.append("stage match")
        
        # Check size fit
        min_check, max_check = vc.thesis.check_size_range
        if min_check <= startup.seeking_amount <= max_check:
            score += 0.2
            reasons.append("check size fit")
        
        # Geographic fit
        if startup.location in vc.thesis.geographic_focus:
            score += 0.2
            reasons.append("geographic match")
        
        return ThesisMatch(
            is_match=score >= 0.8,
            score=score,
            reasons=reasons
        )
```

### Step 4: Context Preservation

```json
{
  "feature_name": "investment_thesis_matching",
  "failing_tests": [
    "test_thesis_matches_startup_in_target_sector",
    "test_thesis_excludes_blacklisted_sectors",
    "test_partial_match_scoring"
  ],
  "implementation_notes": {
    "decisions": "Using value object for InvestmentThesis to ensure immutability",
    "next_steps": "Add AI-powered thesis extraction from VC websites",
    "blockers": "Need to define standard sector taxonomy"
  },
  "coverage_before": 39.0,
  "coverage_target": 85.0
}
```

### Step 5: Automated Verification

```bash
# Run TDD compliance check
python scripts/tdd_checkpoint.py --feature investment_thesis_matching

✅ Test written first: tests/unit/test_investment_thesis_matching.py
✅ Test initially failed: All 3 tests failed
✅ Minimal implementation: 45 lines added
✅ Tests now pass: 3/3 passing
✅ Refactored: Extracted ThesisMatch as value object
✅ Coverage improved: 39% → 52%
⚠️  Coverage below target: Need 28% more for 80% threshold
```

## 6. Recovery Procedures

### When TDD is Violated

1. **Implementation without test:**
   ```bash
   # Rollback and create test first
   git stash
   python scripts/generate_test_skeleton.py src/core/services/new_service.py
   # Write test, commit, then unstash
   ```

2. **Coverage drops below threshold:**
   ```bash
   # Identify uncovered code
   pytest --cov=src --cov-report=term-missing
   # Generate coverage report
   python scripts/suggest_missing_tests.py
   ```

3. **Test written after implementation:**
   ```bash
   # Mark as technical debt
   python scripts/mark_tdd_debt.py --file src/module.py --reason "legacy code"
   # Schedule refactoring
   ```

## 7. Metrics and Monitoring

### TDD Health Dashboard

```python
# scripts/tdd_metrics.py
def generate_tdd_report():
    metrics = {
        "coverage_trend": get_coverage_history(),
        "tdd_violations": count_violations_this_week(),
        "test_to_code_ratio": calculate_test_ratio(),
        "avg_test_first_compliance": get_compliance_rate(),
        "refactoring_frequency": count_refactorings()
    }
    
    return TDDHealthReport(metrics)
```

### Weekly TDD Review Checklist

- [ ] Coverage increased from last week
- [ ] All new features have tests first
- [ ] No TDD violations in commits
- [ ] Test-to-code ratio > 1.5
- [ ] All agents following TDD protocol
- [ ] Context files updated
- [ ] Technical debt documented

## Conclusion

This workflow ensures systematic TDD compliance through:
1. **Automated gates** preventing non-TDD code
2. **Clear agent responsibilities** for each phase
3. **Context preservation** across handoffs
4. **Continuous monitoring** of TDD health
5. **Practical examples** demonstrating the process

Remember: **No code without a failing test first!**