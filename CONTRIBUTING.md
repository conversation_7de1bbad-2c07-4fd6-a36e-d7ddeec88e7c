# Contributing to VC-Startup Matching Platform

First off, thank you for considering contributing to the VC-Startup Matching Platform! It's people like you that make this platform a great tool for the startup ecosystem.

## Code of Conduct

By participating in this project, you are expected to uphold our Code of Conduct:
- Be respectful and inclusive
- Welcome newcomers and help them get started
- Focus on what is best for the community
- Show empathy towards other community members

## How Can I Contribute?

### Reporting Bugs

Before creating bug reports, please check existing issues to avoid duplicates. When you create a bug report, include as many details as possible using the issue template.

**Great Bug Reports** tend to have:
- A quick summary and/or background
- Steps to reproduce
- What you expected would happen
- What actually happens
- Notes (possibly including why you think this might be happening)

### Suggesting Enhancements

Enhancement suggestions are tracked as GitHub issues. When creating an enhancement suggestion, include:
- A clear and descriptive title
- A detailed description of the proposed enhancement
- Explain why this enhancement would be useful
- List any alternative solutions you've considered

### Pull Requests

1. **Fork the repository** and create your branch from `main`
2. **Write tests** - We follow TDD. Write tests first!
3. **Follow the style guide** - Use Black for formatting
4. **Ensure tests pass** - Run `pytest` before submitting
5. **Update documentation** - Keep README and docs current
6. **Create a Pull Request** - Use the PR template

## Development Setup

### Prerequisites
- Python 3.9+
- Docker & Docker Compose
- PostgreSQL 14+
- Redis 7+

### Local Development

1. **Clone your fork**
   ```bash
   git clone https://github.com/your-username/vc-matching-platform.git
   cd vc-matching-platform
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   pip install -r requirements-dev.txt  # Development dependencies
   ```

4. **Set up pre-commit hooks**
   ```bash
   pre-commit install
   ```

5. **Start services**
   ```bash
   docker-compose up -d db redis
   ```

6. **Run migrations**
   ```bash
   alembic upgrade head
   ```

7. **Run tests**
   ```bash
   pytest
   ```

## Style Guide

### Python Style Guide

We use several tools to maintain code quality:

- **Black** - Code formatting
- **isort** - Import sorting
- **Flake8** - Linting
- **MyPy** - Type checking

Run all checks:
```bash
black src tests
isort src tests
flake8 src tests
mypy src
```

### Commit Messages

Follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

- `feat:` - New feature
- `fix:` - Bug fix
- `docs:` - Documentation changes
- `style:` - Code style changes (formatting, etc)
- `refactor:` - Code refactoring
- `test:` - Test additions or corrections
- `chore:` - Maintenance tasks

Examples:
```
feat: add LinkedIn scraping capability
fix: resolve database connection timeout
docs: update API endpoint documentation
test: add unit tests for match service
```

### Test Guidelines

1. **Test First** - Write tests before implementation (TDD)
2. **Aim for 80%+ coverage** - Check with `pytest --cov`
3. **Test structure**:
   ```python
   def test_should_do_something_when_condition():
       # Arrange
       # Act
       # Assert
   ```
4. **Use fixtures** for common test data
5. **Mock external services** (OpenAI, web scraping, etc.)

### API Design Guidelines

1. **RESTful principles** - Use proper HTTP methods
2. **Consistent naming** - Use kebab-case for URLs
3. **Proper status codes** - 200, 201, 400, 401, 404, 500
4. **Request/Response schemas** - Always use Pydantic models
5. **API documentation** - Update OpenAPI specs

## Project Structure

```
src/
├── api/              # FastAPI endpoints
├── core/             # Business logic & domain models
├── database/         # Database models & repositories
├── workers/          # Celery background tasks
└── scrapers/         # Web scraping modules

tests/
├── unit/            # Unit tests (mirror src structure)
└── integration/     # Integration tests
```

## Testing Specific Components

### Testing New Endpoints
```python
# tests/unit/api/test_your_endpoint.py
def test_create_resource(client, db_session):
    response = client.post("/api/v1/resource", json={...})
    assert response.status_code == 201
```

### Testing Services
```python
# tests/unit/test_your_service.py
def test_service_method():
    service = YourService(mock_repo)
    result = service.do_something()
    assert result == expected
```

### Testing Scrapers
```python
# tests/unit/test_scraper.py
@pytest.mark.asyncio
async def test_scraper(mock_httpx):
    scraper = YourScraper()
    result = await scraper.scrape("https://example.com")
    assert result.success
```

## Deployment

Changes to `main` branch trigger automatic deployment to staging. Production deployments require manual approval.

### Environment Variables
Never commit `.env` files. Use `.env.example` as reference.

Required variables:
- `OPENAI_API_KEY` - For AI features
- `DATABASE_URL` - PostgreSQL connection
- `REDIS_URL` - Redis connection
- `SECRET_KEY` - JWT signing

## Getting Help

- **Documentation**: Check README.md and docs/
- **Issues**: Search existing issues or create new one
- **Discussions**: Use GitHub Discussions for questions

## Recognition

Contributors will be recognized in:
- README.md contributors section
- Release notes
- Special thanks in documentation

Thank you for contributing! 🎉