version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: vc_matching_platform
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Main API Application
  api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      DATABASE_URL: **************************************/vc_matching_platform
      REDIS_URL: redis://redis:6379
      SECRET_KEY: ${SECRET_KEY:-your-secret-key-here-change-in-production}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      DEBUG: ${DEBUG:-false}
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./alembic:/app/alembic
      - ./src:/app/src
    command: >
      sh -c "
        echo 'Waiting for database...' &&
        python -m alembic upgrade head &&
        echo 'Starting API server...' &&
        gunicorn src.api.main:app --workers 4 --worker-class uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000 --access-logfile - --error-logfile - --log-level info
      "

  # Celery Worker
  celery:
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      DATABASE_URL: **************************************/vc_matching_platform
      REDIS_URL: redis://redis:6379
      SECRET_KEY: ${SECRET_KEY:-your-secret-key-here-change-in-production}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: celery -A src.workers.celery_app worker --loglevel=info
    volumes:
      - ./src:/app/src

  # Celery Beat Scheduler
  celery-beat:
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      DATABASE_URL: **************************************/vc_matching_platform
      REDIS_URL: redis://redis:6379
      SECRET_KEY: ${SECRET_KEY:-your-secret-key-here-change-in-production}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: celery -A src.workers.celery_app beat --loglevel=info --schedule=/tmp/celerybeat-schedule
    volumes:
      - ./src:/app/src

  # Flower - Celery Monitoring
  flower:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "5555:5555"
    environment:
      DATABASE_URL: **************************************/vc_matching_platform
      REDIS_URL: redis://redis:6379
      SECRET_KEY: ${SECRET_KEY:-your-secret-key-here-change-in-production}
    depends_on:
      - redis
      - celery
    command: celery -A src.workers.celery_app flower --port=5555
    volumes:
      - ./src:/app/src

  # Nginx Reverse Proxy (optional, for production)
  # nginx:
  #   image: nginx:alpine
  #   ports:
  #     - "80:80"
  #     - "443:443"
  #   volumes:
  #     - ./nginx.conf:/etc/nginx/nginx.conf:ro
  #     - ./ssl:/etc/nginx/ssl:ro
  #   depends_on:
  #     - api

volumes:
  postgres_data: