# BiLat VC-Startup Matching Platform

A bilateral matching platform that connects startups with venture capital firms through AI-powered analysis. Unlike traditional platforms, this allows VCs to actively search for startups matching their investment thesis.

## 🚀 Key Features

- **Bilateral Matching**: Both startups and VCs can actively search and connect
- **AI-Powered Analysis**: Leverages OpenAI GPT-4 for intelligent matching and insights
- **Web Scraping**: Automatically enriches profiles from company websites and LinkedIn
- **Async Architecture**: High-performance async database operations for 2-3x speed improvement
- **Background Tasks**: Celery for processing AI analysis and web scraping
- **Authentication**: JWT-based secure authentication system
- **RESTful API**: FastAPI with automatic interactive documentation
- **Clean Architecture**: Domain-driven design with ports and adapters pattern
- **Test-Driven Development**: 80%+ test coverage target with comprehensive test suite
- **Production Ready**: Docker Compose deployment with monitoring (Prometheus + Grafana)

## 📋 Prerequisites

- Docker & Docker Compose (for quick deployment)
- Python 3.9+ (for local development)
- PostgreSQL 14+
- Redis 7+
- OpenAI API key

## 🛠️ One-Command Setup and Run

### Super Quick Start (Recommended):
```bash
# Clone the repository
git clone https://github.com/yourusername/vc-matching-platform.git
cd vc-matching-platform

# Run everything with one command
./run.sh
```

The script will:
- ✅ Check all prerequisites
- ✅ Set up environment configuration
- ✅ Generate secure secret keys
- ✅ Prompt for OpenAI API key
- ✅ Start all services
- ✅ Show service URLs

### Advanced Usage:
```bash
# Docker mode (everything in containers)
./run.sh docker

# Hybrid mode (services in Docker, app locally - best for development)
./run.sh hybrid

# Local mode (everything locally)
./run.sh local

# Reset environment and run
./run.sh docker --reset

# Show all options
./run.sh --help
```

## 💻 Manual Development Setup (Alternative)

If you prefer manual setup instead of the automated script:

1. **Hybrid mode (recommended for development):**
```bash
./run.sh hybrid
# This starts services in Docker and the app locally with hot reload
```

2. **Fully manual setup:**
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Start services (PostgreSQL and Redis)
docker-compose up -d db redis

# Set up environment
cp .env.staging .env
# Edit .env and add your OpenAI API key

# Run migrations
alembic upgrade head

# Start the development server
uvicorn src.api.main:app --reload
```

## 🏃‍♂️ Running the Application

### One-Command Start (Recommended):
```bash
./run.sh                    # Interactive mode
./run.sh docker             # Docker mode
./run.sh hybrid             # Hybrid mode (best for development)
./run.sh local              # Local mode
```

### Traditional Methods:

**With Docker:**
```bash
docker-compose up -d
```

**Manual Local Setup:**
```bash
# Terminal 1: Start API
uvicorn src.api.main:app --reload

# Terminal 2: Start Celery worker
celery -A src.workers.celery_app worker --loglevel=info

# Terminal 3: Start Celery beat
celery -A src.workers.celery_app beat --loglevel=info
```

### Service URLs:
- **API Documentation**: http://localhost:8000/docs
- **API ReDoc**: http://localhost:8000/redoc
- **Flower (Task Monitor)**: http://localhost:5555
- **Health Check**: http://localhost:8000/health

### Stopping Services:
```bash
# If using run.sh
./stop-services.sh          # Stops local services

# If using Docker
docker-compose down         # Stops Docker services
```

## 🧪 Testing

Run all tests with coverage:
```bash
pytest tests/ --cov=src --cov-report=html
```

Run specific test suites:
```bash
# Unit tests only
pytest tests/unit/

# Integration tests
pytest tests/integration/

# Comprehensive tests (high coverage)
pytest tests/unit/api/test_*_comprehensive.py tests/unit/database/test_*_comprehensive.py
```

## 📁 Project Structure

```
vc-matching-platform/
├── src/
│   ├── api/              # FastAPI application and endpoints
│   ├── core/             # Business logic and domain models
│   │   ├── models/       # Domain models (Startup, VC, Match)
│   │   ├── services/     # Business services
│   │   ├── repositories/ # Repository interfaces
│   │   └── ai/          # AI analysis components
│   ├── database/         # Database models and repositories
│   └── adapters/         # External service adapters
├── tests/                # Test suites
├── agents/               # Development sub-agents
└── scripts/              # Utility scripts
```

## 🔑 Key API Endpoints

### Authentication
- `POST /api/v1/auth/register` - Register new user
- `POST /api/v1/auth/login` - Login (returns JWT token)
- `GET /api/v1/auth/me` - Get current user

### Startups
- `POST /api/v1/startups` - Create a startup
- `GET /api/v1/startups` - List startups with filters
- `GET /api/v1/startups/{id}` - Get startup details
- `POST /api/v1/startups/{id}/analyze` - AI analysis of startup

### VCs
- `POST /api/v1/vcs` - Create a VC
- `GET /api/v1/vcs` - List VCs with filters
- `POST /api/v1/vcs/{id}/extract-thesis` - Extract investment thesis

### Matches
- `POST /api/v1/matches` - Create a match
- `GET /api/v1/matches` - List matches with filters
- `POST /api/v1/matches/batch` - Batch matching

### Web Scraping
- `POST /api/v1/scraping/scrape/company` - Scrape company website
- `POST /api/v1/scraping/scrape/linkedin` - Scrape LinkedIn profile
- `POST /api/v1/scraping/scrape/enrich-startups` - Auto-enrich startups

## 🤖 Development Sub-Agents

This project includes specialized AI sub-agents for development tasks:

- `ddd_expert` - Domain-Driven Design guidance
- `tdd_specialist` - Test-Driven Development
- `fastapi_architect` - FastAPI best practices
- `ai_langchain_expert` - AI/LLM integration
- `database_performance_expert` - PostgreSQL optimization

See `agents/USAGE_GUIDE.md` for details.

## 🚦 Environment Variables

Required environment variables (see `.env.example`):

```env
# OpenAI
OPENAI_API_KEY=your-api-key

# Database
DATABASE_URL=postgresql://user:pass@localhost/vc_matching_platform

# Redis
REDIS_URL=redis://localhost:6379

# Security
SECRET_KEY=your-secret-key
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
```

## 🏗️ Architecture

The platform follows Clean Architecture principles:

1. **Domain Layer**: Pure business logic with no dependencies
2. **Application Layer**: Use cases and services
3. **Infrastructure Layer**: Database, external APIs, frameworks
4. **Presentation Layer**: REST API endpoints

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Write tests first (TDD approach)
4. Implement the feature
5. Ensure tests pass and coverage remains above 80%
6. Commit your changes
7. Push to the branch
8. Open a Pull Request

## 📜 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Built with [Claude Code](https://claude.ai/code)
- Uses OpenAI GPT-4 for AI analysis
- FastAPI for the REST API framework
- SQLAlchemy for database ORM
- LangChain for AI integration