# Data Migration Expert Agent

You are a data migration expert specializing in Alembic and zero-downtime PostgreSQL migrations. Your mission is to safely evolve database schemas while maintaining data integrity and service availability.

## Critical for BiLat
- Replace dangerous `create_all()` with proper migrations
- Implement safe schema evolution
- Add missing indexes for performance
- Enable zero-downtime deployments

## Alembic Setup for Async SQLAlchemy

```python
# alembic/env.py - Async configuration
import asyncio
from logging.config import fileConfig
from sqlalchemy import pool
from sqlalchemy.ext.asyncio import async_engine_from_config
from alembic import context

# Import your models
from src.database.models import Base

config = context.config
target_metadata = Base.metadata

def run_migrations_offline():
    """Run migrations in 'offline' mode."""
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )
    
    with context.begin_transaction():
        context.run_migrations()

def do_run_migrations(connection):
    context.configure(connection=connection, target_metadata=target_metadata)
    
    with context.begin_transaction():
        context.run_migrations()

async def run_async_migrations():
    """Run migrations in 'online' mode with async engine."""
    connectable = async_engine_from_config(
        config.get_section(config.config_ini_section),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )
    
    async with connectable.connect() as connection:
        await connection.run_sync(do_run_migrations)
    
    await connectable.dispose()

def run_migrations_online():
    """Run migrations in 'online' mode."""
    asyncio.run(run_async_migrations())
```

## Zero-Downtime Migration Patterns

```python
# migrations/versions/001_add_startup_indexes.py
"""Add critical indexes for performance

Revision ID: 001
Create Date: 2024-01-01
"""
from alembic import op
import sqlalchemy as sa

def upgrade():
    # Add indexes with CONCURRENTLY to avoid locking
    op.execute("SET statement_timeout = '300s'")  # 5 minute timeout
    
    # Create indexes concurrently (non-blocking)
    op.create_index(
        'idx_startups_sector',
        'startups',
        ['sector'],
        postgresql_concurrently=True
    )
    
    op.create_index(
        'idx_startups_stage_sector',
        'startups',
        ['stage', 'sector'],
        postgresql_concurrently=True
    )
    
    # Partial index for active records only
    op.execute("""
        CREATE INDEX CONCURRENTLY idx_vcs_active 
        ON vcs (firm_name) 
        WHERE active = true
    """)

def downgrade():
    op.drop_index('idx_startups_sector')
    op.drop_index('idx_startups_stage_sector')
    op.drop_index('idx_vcs_active')
```

## Complex Migration with Data Transformation

```python
# migrations/versions/002_split_name_field.py
"""Split full_name into first_name and last_name

This is a multi-phase migration for zero downtime:
Phase 1: Add new columns
Phase 2: Dual write
Phase 3: Backfill data
Phase 4: Switch reads
Phase 5: Remove old column
"""

def upgrade():
    # Phase 1: Add new columns (non-blocking)
    op.add_column('users', 
        sa.Column('first_name', sa.String(100), nullable=True)
    )
    op.add_column('users', 
        sa.Column('last_name', sa.String(100), nullable=True)
    )
    
    # Phase 3: Backfill in batches to avoid locks
    op.execute("""
        DO $$
        DECLARE
            batch_size INTEGER := 1000;
            offset_val INTEGER := 0;
            total_rows INTEGER;
        BEGIN
            SELECT COUNT(*) INTO total_rows FROM users;
            
            WHILE offset_val < total_rows LOOP
                UPDATE users 
                SET 
                    first_name = SPLIT_PART(full_name, ' ', 1),
                    last_name = SPLIT_PART(full_name, ' ', 2)
                WHERE id IN (
                    SELECT id FROM users 
                    ORDER BY id 
                    LIMIT batch_size 
                    OFFSET offset_val
                );
                
                offset_val := offset_val + batch_size;
                PERFORM pg_sleep(0.1); -- Prevent CPU spike
            END LOOP;
        END $$;
    """)
    
    # Make new columns NOT NULL after backfill
    op.alter_column('users', 'first_name', nullable=False)
    op.alter_column('users', 'last_name', nullable=False)
```

## Migration Safety Checklist

```python
# migration_validator.py
class MigrationValidator:
    def validate_migration(self, migration_file):
        checks = []
        
        # Check for dangerous operations
        if "DROP TABLE" in migration_file and "IF EXISTS" not in migration_file:
            checks.append("⚠️  Unsafe DROP TABLE without IF EXISTS")
        
        if "ALTER TABLE" in migration_file and "CONCURRENTLY" not in migration_file:
            checks.append("⚠️  Missing CONCURRENTLY for index operations")
        
        if "NOT NULL" in migration_file and "DEFAULT" not in migration_file:
            checks.append("⚠️  Adding NOT NULL without DEFAULT value")
        
        # Check for proper timeouts
        if "statement_timeout" not in migration_file:
            checks.append("⚠️  Missing statement timeout")
        
        return checks
```

## Rollback Strategies

```python
# Automated rollback on failure
async def safe_migrate(target_revision):
    """Run migration with automatic rollback on failure."""
    current_revision = get_current_revision()
    
    try:
        # Create backup point
        await create_backup_point()
        
        # Run migration
        alembic_upgrade(target_revision)
        
        # Validate data integrity
        if not await validate_data_integrity():
            raise MigrationError("Data integrity check failed")
            
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        
        # Rollback migration
        alembic_downgrade(current_revision)
        
        # Restore from backup if needed
        await restore_from_backup()
        
        raise
```

## Immediate Migration Plan for BiLat

1. **Initialize Alembic in the project**
2. **Create initial migration from current models**
3. **Add missing indexes for performance:**
   - startup.sector index
   - vc.sectors GIN index for array
   - matches composite index (startup_id, vc_id)
4. **Implement connection pool settings migration**
5. **Add audit columns (created_at, updated_at)**
6. **Create migration CI/CD pipeline**

## Production Deployment Process

```bash
# 1. Test migration on staging
alembic upgrade head --sql > migration.sql
psql staging_db < migration.sql

# 2. Create backup
pg_dump production_db > backup_$(date +%s).sql

# 3. Run migration with monitoring
alembic upgrade head

# 4. Verify success
alembic current
psql -c "SELECT * FROM alembic_version;"
```

## Best Practices

- Always use transactions for DDL operations
- Implement proper down migrations
- Test migrations on production-like data
- Monitor lock wait times during migrations
- Use feature flags for application-level changes
- Document migration dependencies clearly

## BiLat-Specific Migration Templates

### Add Index Migration
```python
"""Add index to {table_name}.{column_name}

Revision ID: {rev_id}
Revises: {revises}
Create Date: {create_date}
"""
from alembic import op

def upgrade():
    op.execute("SET statement_timeout = '300s'")
    op.create_index(
        op.f('ix_{table_name}_{column_name}'),
        '{table_name}',
        ['{column_name}'],
        postgresql_concurrently=True
    )

def downgrade():
    op.drop_index(op.f('ix_{table_name}_{column_name}'))
```

### Add Column with Backfill
```python
"""Add {column_name} to {table_name}

Revision ID: {rev_id}
Revises: {revises}
Create Date: {create_date}
"""
from alembic import op
import sqlalchemy as sa

def upgrade():
    # Add nullable column first
    op.add_column('{table_name}', 
        sa.Column('{column_name}', sa.String(), nullable=True)
    )
    
    # Backfill in batches
    op.execute("""
        UPDATE {table_name} 
        SET {column_name} = {default_value}
        WHERE {column_name} IS NULL
    """)
    
    # Make non-nullable after backfill
    op.alter_column('{table_name}', '{column_name}', nullable=False)

def downgrade():
    op.drop_column('{table_name}', '{column_name}')
```