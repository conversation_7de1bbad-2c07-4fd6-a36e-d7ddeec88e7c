# Sub-Agent Usage Guide

This guide explains how to leverage specialized sub-agents in the VC-Startup Matching Platform project.

## Available Agents

1. **Domain-Driven Design Expert** (`ddd_expert`)
   - Use for: Creating domain models, value objects, aggregates
   - Example: "Create a domain model for investment rounds"

2. **TDD Specialist** (`tdd_specialist`)
   - Use for: Writing tests first, test structure advice
   - Example: "Write tests for the new matching algorithm"

3. **FastAPI Architecture** (`fastapi_architect`)
   - Use for: API endpoint design, dependency injection, error handling
   - Example: "Create RESTful endpoints for startup CRUD operations"

4. **AI/LangChain Integration** (`ai_langchain_expert`)
   - Use for: AI features, prompt engineering, LLM integration
   - Example: "Implement AI-powered thesis extraction from VC websites"

5. **Web Scraping Expert** (`web_scraping_expert`)
   - Use for: Automated data collection, anti-detection strategies
   - Example: "Scrape startup data from Crunchbase"

6. **Background Tasks Expert** (`background_tasks_expert`)
   - Use for: Celery tasks, async processing, job queues
   - Example: "Set up periodic VC discovery tasks"

7. **Database Performance** (`database_performance_expert`)
   - Use for: Query optimization, caching strategies, schema design
   - Example: "Optimize the startup search queries"

8. **DevOps & Deployment** (`devops_deployment_expert`)
   - Use for: Containerization, CI/CD, deployment strategies
   - Example: "Create Docker setup for the application"

9. **Test Coverage Expert** (`test_coverage_expert`)
   - Use for: Coverage analysis, strategic testing, mocking patterns
   - Example: "Analyze why coverage dropped and create improvement plan"

10. **Refactoring Expert** (`refactoring_expert`)
    - Use for: Code cleanup, removing dead code, improving structure
    - Example: "Safely remove old deprecated files and improve code organization"

## How to Use with Claude Code

### Option 1: Direct Agent Invocation
When starting a task, specify which agent to use:

```
"Using the DDD Expert agent, help me create a proper domain model for VC investment preferences"
```

### Option 2: Task-Based Selection
Let Claude Code automatically select the appropriate agent:

```
"I need to write tests for the new matching engine feature"
# Claude Code will use the TDD Specialist agent
```

### Option 3: Multi-Agent Workflow
For complex features, use multiple agents in sequence:

```
1. "Start with DDD Expert to design the domain model for funding rounds"
2. "Now use TDD Specialist to write tests for this model"
3. "Finally, use FastAPI Architecture agent to create the API endpoints"
```

## Programmatic Usage

```python
from agents import agent_loader

# Get a specific agent
ddd_prompt = agent_loader.get_agent("ddd_expert")

# List all available agents
available = agent_loader.list_agents()

# Get suggested agent for a task
suggested = agent_loader.get_agent_for_task("create API endpoints")
# Returns: "fastapi_architect"
```

## Best Practices

1. **Start with Domain Models**: Use DDD Expert first for new features
2. **Test-First Development**: Always use TDD Specialist before implementation
3. **Layer Separation**: Use appropriate agents for each layer (domain, API, infrastructure)
4. **Performance Last**: Optimize with Database Performance agent after features work

## Example Workflow

Creating a new "Investment Round" feature:

1. **DDD Expert**: Design the InvestmentRound aggregate and value objects
2. **TDD Specialist**: Write comprehensive tests for the domain logic
3. **Database Performance**: Design efficient schema for round data
4. **FastAPI Architecture**: Create RESTful endpoints for round management
5. **AI/LangChain**: Add AI analysis of round terms
6. **Background Tasks**: Set up notifications for round updates
7. **DevOps**: Update Docker configuration for new dependencies

## Integration with VS Code

The agent prompts are stored in `agents/prompts/` and can be:
- Referenced during development
- Used as context for Claude Code
- Extended with project-specific patterns

Remember: Each agent has specialized knowledge. Use them for their strengths to get the best code quality and architecture.