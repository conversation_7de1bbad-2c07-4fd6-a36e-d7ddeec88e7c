"""Agent loader utilities for specialized sub-agents."""

from pathlib import Path
from typing import Dict, Optional

class AgentLoader:
    """Load and manage specialized agent prompts."""
    
    def __init__(self):
        self.agents_dir = Path(__file__).parent / "prompts"
        self._cache: Dict[str, str] = {}
        
    def get_agent(self, agent_name: str) -> Optional[str]:
        """Load an agent prompt by name.
        
        Args:
            agent_name: Name of the agent (e.g., 'ddd_expert', 'tdd_specialist')
            
        Returns:
            The agent prompt content or None if not found
        """
        if agent_name in self._cache:
            return self._cache[agent_name]
            
        agent_file = self.agents_dir / f"{agent_name}.md"
        if not agent_file.exists():
            return None
            
        content = agent_file.read_text()
        self._cache[agent_name] = content
        return content
    
    def list_agents(self) -> list[str]:
        """List all available agent names."""
        return [
            f.stem for f in self.agents_dir.glob("*.md")
        ]
    
    def get_agent_for_task(self, task_description: str) -> Optional[str]:
        """Suggest the best agent for a given task.
        
        Args:
            task_description: Description of the task to perform
            
        Returns:
            The name of the suggested agent or None
        """
        task_lower = task_description.lower()
        
        # Simple keyword matching for now
        if any(word in task_lower for word in ["domain", "model", "ddd", "aggregate"]):
            return "ddd_expert"
        elif any(word in task_lower for word in ["test", "tdd", "pytest"]):
            return "tdd_specialist"
        elif any(word in task_lower for word in ["api", "endpoint", "fastapi", "route"]):
            return "fastapi_architect"
        elif any(word in task_lower for word in ["ai", "langchain", "llm", "gpt"]):
            return "ai_langchain_expert"
        elif any(word in task_lower for word in ["scrape", "crawl", "playwright"]):
            return "web_scraping_expert"
        elif any(word in task_lower for word in ["celery", "background", "async task"]):
            return "background_tasks_expert"
        elif any(word in task_lower for word in ["database", "performance", "optimize", "cache"]):
            return "database_performance_expert"
        elif any(word in task_lower for word in ["deploy", "docker", "container", "cicd"]):
            return "devops_deployment_expert"
            
        return None


# Singleton instance
agent_loader = AgentLoader()