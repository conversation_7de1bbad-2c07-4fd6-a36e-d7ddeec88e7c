#!/usr/bin/env python3
"""Test script to verify Celery tasks are working."""

import os
import sys
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.workers.celery_app import celery_app
from src.workers.tasks.ai_tasks import analyze_startup_task
from src.workers.tasks.match_tasks import daily_match_generation
from src.workers.tasks.scheduled_tasks import cleanup_old_tasks


def test_simple_task():
    """Test a simple Celery task."""
    print("\n1. Testing simple task execution...")
    
    # Create a simple test task
    @celery_app.task
    def add_numbers(x, y):
        return x + y
    
    # Send task asynchronously
    result = add_numbers.delay(4, 6)
    print(f"   Task ID: {result.id}")
    
    try:
        # Wait for result (max 5 seconds)
        value = result.get(timeout=5)
        print(f"   Result: {value}")
        print("   ✅ Simple task execution: SUCCESS")
        return True
    except Exception as e:
        print(f"   ❌ Simple task execution: FAILED - {e}")
        return False


def test_ai_task():
    """Test AI analysis task."""
    print("\n2. Testing AI analysis task...")
    
    # Send analyze startup task (will fail without real startup ID)
    result = analyze_startup_task.delay("test-startup-id")
    print(f"   Task ID: {result.id}")
    
    # Check if task was accepted
    if result.id:
        print("   ✅ AI task accepted: SUCCESS")
        # Don't wait for result as it will fail without real data
        return True
    else:
        print("   ❌ AI task accepted: FAILED")
        return False


def test_scheduled_task():
    """Test scheduled task."""
    print("\n3. Testing scheduled task...")
    
    # Send cleanup task
    result = cleanup_old_tasks.delay()
    print(f"   Task ID: {result.id}")
    
    try:
        # Wait for result
        value = result.get(timeout=5)
        print(f"   Result: {value}")
        print("   ✅ Scheduled task execution: SUCCESS")
        return True
    except Exception as e:
        print(f"   ❌ Scheduled task execution: FAILED - {e}")
        return False


def check_celery_status():
    """Check Celery worker status."""
    print("\n4. Checking Celery status...")
    
    try:
        # Get worker stats
        stats = celery_app.control.inspect().stats()
        
        if stats:
            print(f"   Active workers: {len(stats)}")
            for worker, info in stats.items():
                print(f"   - {worker}")
            print("   ✅ Celery workers: RUNNING")
            return True
        else:
            print("   ❌ Celery workers: NOT FOUND")
            return False
    except Exception as e:
        print(f"   ❌ Celery status check: FAILED - {e}")
        return False


def check_registered_tasks():
    """Check registered Celery tasks."""
    print("\n5. Checking registered tasks...")
    
    try:
        registered = celery_app.control.inspect().registered()
        
        if registered:
            total_tasks = sum(len(tasks) for tasks in registered.values())
            print(f"   Total registered tasks: {total_tasks}")
            
            # Show some task names
            for worker, tasks in registered.items():
                print(f"   Worker: {worker}")
                for task in tasks[:5]:  # Show first 5 tasks
                    print(f"     - {task}")
                if len(tasks) > 5:
                    print(f"     ... and {len(tasks) - 5} more")
            
            print("   ✅ Task registration: SUCCESS")
            return True
        else:
            print("   ❌ Task registration: NO TASKS FOUND")
            return False
    except Exception as e:
        print(f"   ❌ Task registration check: FAILED - {e}")
        return False


def main():
    """Run all Celery tests."""
    print("=" * 60)
    print("CELERY TASK TESTING")
    print("=" * 60)
    print(f"Started at: {datetime.now()}")
    print(f"Redis URL: {os.getenv('REDIS_URL', 'redis://localhost:6379/0')}")
    
    # Check if Celery is running first
    if not check_celery_status():
        print("\n⚠️  No Celery workers found!")
        print("Please start Celery workers first:")
        print("  ./scripts/start_celery_worker.sh")
        print("  or")
        print("  docker-compose up celery")
        return
    
    # Run tests
    results = []
    results.append(("Simple Task", test_simple_task()))
    results.append(("AI Task", test_ai_task()))
    results.append(("Scheduled Task", test_scheduled_task()))
    results.append(("Task Registration", check_registered_tasks()))
    
    # Summary
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{name:.<40} {status}")
    
    print(f"\nTotal: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All Celery tests passed!")
    else:
        print(f"\n⚠️  {total - passed} tests failed")


if __name__ == "__main__":
    main()