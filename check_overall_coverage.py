#!/usr/bin/env python3
"""Quick check of overall test coverage."""

import subprocess
import re

# Run pytest with coverage
cmd = ["python3", "-m", "pytest", "tests/unit/", "--cov=src", "--cov-report=term", "-q"]
result = subprocess.run(cmd, capture_output=True, text=True, env={'PYTHONPATH': '.'})

# Extract coverage percentage from output
output = result.stdout + result.stderr
total_match = re.search(r'TOTAL\s+\d+\s+\d+\s+\d+\s+(\d+)%', output)

if total_match:
    coverage = int(total_match.group(1))
    print(f"Current overall test coverage: {coverage}%")
    
    if coverage >= 80:
        print("✅ Congratulations! We've achieved the 80% coverage goal!")
    else:
        print(f"📊 Progress: {coverage}% / 80% (need {80 - coverage}% more)")
        
    # Show which areas still need work
    if coverage < 80:
        print("\nAreas that still need test coverage:")
        lines = output.split('\n')
        for line in lines:
            if 'src/' in line and '%' in line:
                parts = line.split()
                if len(parts) >= 5:
                    try:
                        file_coverage = int(parts[-1].rstrip('%'))
                        if file_coverage < 50:  # Show files with less than 50% coverage
                            filename = parts[0]
                            print(f"  - {filename}: {file_coverage}%")
                    except:
                        pass
else:
    print("Unable to determine coverage percentage. Check test output for details.")
    print("\nTest failures detected:" if "FAILED" in output else "Tests passed but coverage not calculated.")