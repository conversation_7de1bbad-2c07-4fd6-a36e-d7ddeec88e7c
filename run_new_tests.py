#!/usr/bin/env python3
"""Run the new tests directly without loading the full app."""

import sys
import os

# Add the src directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set environment variables
os.environ['ENVIRONMENT'] = 'test'
os.environ['REDIS_URL'] = 'redis://localhost:6379/1'
os.environ['DATABASE_URL'] = 'postgresql://thecostcokid@localhost/vc_matching_platform_test'

# Run validation tests
print("🧪 Running Validation Service Tests")
print("=" * 60)
import pytest
result = pytest.main([
    'tests/unit/services/test_validation_service.py',
    '-v',
    '--tb=short',
    '--no-header'
])

print("\n🧪 Running Email Service Tests")
print("=" * 60)
result = pytest.main([
    'tests/unit/services/test_email_service.py',
    '-v',
    '--tb=short',
    '--no-header'
])

print("\n🧪 Running Scheduled Tasks Tests")
print("=" * 60)
result = pytest.main([
    'tests/unit/workers/test_scheduled_tasks.py',
    '-v',
    '--tb=short',
    '--no-header'
])

print("\n✅ Test execution complete!")