#!/usr/bin/env python3
"""Quick test to verify the API is working correctly."""

import asyncio
from fastapi.testclient import TestClient
from src.api.main import app

client = TestClient(app)

def test_health_check():
    """Test the health check endpoint."""
    response = client.get("/api/v1/health")
    print(f"Health check: {response.status_code}")
    print(f"Response: {response.json()}")
    assert response.status_code == 200

def test_create_startup():
    """Test creating a startup."""
    startup_data = {
        "name": "TechCo AI",
        "sector": "B2B SaaS",
        "stage": "Seed",
        "description": "AI-powered sales automation platform",
        "website": "https://techco.ai",
        "team_size": 10,
        "monthly_revenue": 50000
    }
    
    response = client.post("/api/v1/startups", json=startup_data)
    print(f"\nCreate startup: {response.status_code}")
    if response.status_code == 201:
        print(f"Created startup: {response.json()}")
    else:
        print(f"Error: {response.text}")
    
    return response.json() if response.status_code == 201 else None

def test_create_vc():
    """Test creating a VC."""
    vc_data = {
        "firm_name": "AI Ventures",
        "website": "https://aiventures.com",
        "thesis": "We invest in early-stage B2B SaaS companies leveraging AI",
        "check_size_min": 1000000,
        "check_size_max": 5000000,
        "sectors": ["B2B SaaS", "AI/ML"],
        "stages": ["Seed", "Series A"]
    }
    
    response = client.post("/api/v1/vcs", json=vc_data)
    print(f"\nCreate VC: {response.status_code}")
    if response.status_code == 201:
        print(f"Created VC: {response.json()}")
    else:
        print(f"Error: {response.text}")
    
    return response.json() if response.status_code == 201 else None

def test_matching():
    """Test the matching endpoint."""
    # Create a startup and VC first
    startup = test_create_startup()
    vc = test_create_vc()
    
    if startup and vc:
        # Test single match
        match_data = {
            "startup_id": startup["id"],
            "vc_id": vc["id"]
        }
        
        response = client.post("/api/v1/matches", json=match_data)
        print(f"\nCreate match: {response.status_code}")
        if response.status_code == 201:
            print(f"Match result: {response.json()}")
        else:
            print(f"Error: {response.text}")

def test_api_docs():
    """Test that API docs are accessible."""
    response = client.get("/api/v1/docs")
    print(f"\nAPI docs available: {response.status_code == 200}")

if __name__ == "__main__":
    print("Testing VC-Startup Matching Platform API...")
    print("=" * 50)
    
    test_health_check()
    test_api_docs()
    test_matching()
    
    print("\n" + "=" * 50)
    print("API testing complete!")