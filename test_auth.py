#!/usr/bin/env python3
"""Test authentication endpoints."""

import requests
import json
import sys

BASE_URL = "http://localhost:8000/api/v1"

def test_register():
    """Test user registration."""
    print("Testing registration...")
    
    user_data = {
        "email": "<EMAIL>",
        "username": "testuser",
        "password": "SecurePass123!",
        "full_name": "Test User"
    }
    
    response = requests.post(f"{BASE_URL}/auth/register", json=user_data)
    
    if response.status_code == 200:
        print("✅ Registration successful!")
        return response.json()
    else:
        print(f"❌ Registration failed: {response.status_code}")
        print(response.json())
        return None


def test_login():
    """Test user login."""
    print("\nTesting login...")
    
    login_data = {
        "username": "<EMAIL>",  # Can use email or username
        "password": "SecurePass123!"
    }
    
    response = requests.post(
        f"{BASE_URL}/auth/login",
        data=login_data,
        headers={"Content-Type": "application/x-www-form-urlencoded"}
    )
    
    if response.status_code == 200:
        print("✅ Login successful!")
        return response.json()
    else:
        print(f"❌ Login failed: {response.status_code}")
        print(response.json())
        return None


def test_get_profile(token):
    """Test getting user profile."""
    print("\nTesting get profile...")
    
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    response = requests.get(f"{BASE_URL}/auth/me", headers=headers)
    
    if response.status_code == 200:
        print("✅ Profile retrieved successfully!")
        print(json.dumps(response.json(), indent=2))
    else:
        print(f"❌ Get profile failed: {response.status_code}")
        print(response.json())


def test_change_password(token):
    """Test changing password."""
    print("\nTesting change password...")
    
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    password_data = {
        "old_password": "SecurePass123!",
        "new_password": "NewSecurePass456!"
    }
    
    response = requests.put(
        f"{BASE_URL}/auth/change-password",
        json=password_data,
        headers=headers
    )
    
    if response.status_code == 200:
        print("✅ Password changed successfully!")
    else:
        print(f"❌ Change password failed: {response.status_code}")
        print(response.json())


def main():
    """Run all authentication tests."""
    print("🔐 Testing Authentication System")
    print("=" * 40)
    
    # Test registration
    user = test_register()
    if not user:
        print("\nRegistration failed, attempting login with existing user...")
    
    # Test login
    login_result = test_login()
    if not login_result:
        print("\n❌ Authentication tests failed!")
        return 1
    
    token = login_result["access_token"]
    print(f"\n🔑 Access token: {token[:20]}...")
    
    # Test authenticated endpoints
    test_get_profile(token)
    test_change_password(token)
    
    print("\n✅ All authentication tests completed!")
    return 0


if __name__ == "__main__":
    sys.exit(main())