#!/usr/bin/env python3
"""Test script to validate async mode functionality."""

import os
import asyncio
import logging
from uuid import uuid4
import httpx
import json

# Enable async mode
os.environ["USE_ASYNC_DB"] = "true"

from src.database.async_setup import AsyncSessionLocal, async_engine
from src.database.repositories.async_startup_repository import AsyncStartupRepository
from src.database.repositories.async_vc_repository import AsyncVCRepository
from src.database.repositories.async_match_repository import AsyncMatchRepository
from src.core.models.startup import Startup as StartupDomainModel
from src.core.models.vc import VC as VCDomainModel
from src.core.models.match import Match as MatchDomainModel
from src.core.schemas.match import MatchStatus, MatchType

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_repository_operations():
    """Test all async repository operations."""
    logger.info("Testing async repository operations...")
    
    async with AsyncSessionLocal() as session:
        # Test Startup Repository
        startup_repo = AsyncStartupRepository(session)
        
        logger.info("1. Creating startup...")
        startup = StartupDomainModel(
            id=uuid4(),
            name="Async Test Startup",
            sector="AI/ML",
            stage="Series A",
            description="AI-powered analytics platform",
            website="https://asynctest.com",
            team_size=25,
            monthly_revenue=250000.0
        )
        saved_startup = await startup_repo.save(startup)
        logger.info(f"   ✅ Created: {saved_startup.name}")
        
        # Test VC Repository
        vc_repo = AsyncVCRepository(session)
        
        logger.info("2. Creating VC...")
        vc = VCDomainModel(
            id=uuid4(),
            firm_name="Async Capital Partners",
            website="https://asynccapital.com",
            thesis="We invest in AI-first companies",
            sectors=["AI/ML", "B2B SaaS"],
            stages=["Series A", "Series B"],
            check_size_min=2000000,
            check_size_max=10000000
        )
        saved_vc = await vc_repo.save(vc)
        logger.info(f"   ✅ Created: {saved_vc.firm_name}")
        
        # Test Match Repository
        match_repo = AsyncMatchRepository(session)
        
        logger.info("3. Creating match...")
        match = MatchDomainModel(
            id=uuid4(),
            startup=saved_startup,
            vc=saved_vc,
            score=0.85,
            reasons=["Strong AI focus", "Right funding stage", "B2B SaaS alignment"],
            status=MatchStatus.PENDING,
            match_type=MatchType.AI_ENHANCED
        )
        saved_match = await match_repo.create(match)
        logger.info(f"   ✅ Created match with score: {saved_match.score}")
        
        # Test queries
        logger.info("4. Testing queries...")
        
        # Find by sector
        ai_startups = await startup_repo.find_by_sector("AI/ML")
        logger.info(f"   ✅ Found {len(ai_startups)} AI/ML startups")
        
        # Find VCs by stage
        series_a_vcs = await vc_repo.find_by_stages(["Series A"])
        logger.info(f"   ✅ Found {len(series_a_vcs)} Series A VCs")
        
        # Find matches
        startup_matches = await match_repo.find_by_startup(saved_startup.id)
        logger.info(f"   ✅ Found {len(startup_matches)} matches for startup")
        
        # Cleanup
        logger.info("5. Cleaning up...")
        await match_repo.delete(saved_match.id)
        await startup_repo.delete(saved_startup.id)
        await vc_repo.delete(saved_vc.id)
        logger.info("   ✅ Cleanup complete")


async def test_api_endpoints():
    """Test API endpoints with async mode enabled."""
    logger.info("\nTesting API endpoints in async mode...")
    
    # Start the API server in a subprocess would be needed for full test
    # For now, we'll just show what the requests would look like
    
    base_url = "http://localhost:8000/api/v1"
    
    logger.info("API tests would include:")
    logger.info("1. GET /startups/debug/repository-type - Check async mode")
    logger.info("2. POST /startups - Create with async repository")
    logger.info("3. GET /startups - List with async repository")
    logger.info("4. GET /startups/{id} - Get with async repository")
    
    # Example of how to make async HTTP requests
    async with httpx.AsyncClient() as client:
        try:
            # This would work if the API server was running
            # response = await client.get(f"{base_url}/startups/debug/repository-type")
            # logger.info(f"Repository type: {response.json()}")
            pass
        except Exception as e:
            logger.info(f"Note: API server not running. Would test endpoints here.")


async def test_concurrent_operations():
    """Test concurrent async operations."""
    logger.info("\nTesting concurrent operations...")
    
    async with AsyncSessionLocal() as session:
        startup_repo = AsyncStartupRepository(session)
        
        async def create_and_delete_startup(index: int):
            startup = StartupDomainModel(
                id=uuid4(),
                name=f"Concurrent Startup {index}",
                sector="Technology",
                stage="Seed",
                description=f"Test startup {index}",
                website=f"https://startup{index}.com",
                team_size=10,
                monthly_revenue=50000.0
            )
            created = await startup_repo.save(startup)
            # Simulate some work
            found = await startup_repo.find_by_id(created.id)
            # Clean up
            await startup_repo.delete(created.id)
            return created.name
        
        # Run 20 concurrent operations
        tasks = [create_and_delete_startup(i) for i in range(20)]
        results = await asyncio.gather(*tasks)
        
        logger.info(f"   ✅ Completed {len(results)} concurrent operations")


async def verify_no_greenlet_errors():
    """Verify that greenlet errors are resolved."""
    logger.info("\nVerifying no greenlet errors...")
    
    try:
        # This was previously causing greenlet errors
        async with AsyncSessionLocal() as session:
            startup_repo = AsyncStartupRepository(session)
            vc_repo = AsyncVCRepository(session)
            match_repo = AsyncMatchRepository(session)
            
            # Complex operation that would trigger greenlet error in mixed mode
            startup = await startup_repo.find_by_id(uuid4())  # Not found
            vcs = await vc_repo.find_all()
            matches = await match_repo.find_with_filters(min_score=0.7)
            
        logger.info("   ✅ No greenlet errors! Async mode working correctly")
        
    except Exception as e:
        if "greenlet" in str(e):
            logger.error(f"   ❌ Greenlet error still present: {e}")
        else:
            logger.error(f"   ❌ Other error: {e}")


async def main():
    """Run all async mode tests."""
    logger.info("=" * 60)
    logger.info("ASYNC MODE VALIDATION")
    logger.info("=" * 60)
    logger.info(f"USE_ASYNC_DB = {os.environ.get('USE_ASYNC_DB', 'false')}")
    
    try:
        # Test repository operations
        await test_repository_operations()
        
        # Test concurrent operations
        await test_concurrent_operations()
        
        # Verify no greenlet errors
        await verify_no_greenlet_errors()
        
        # API endpoint tests (informational)
        await test_api_endpoints()
        
        logger.info("\n" + "=" * 60)
        logger.info("✅ ALL ASYNC MODE TESTS PASSED!")
        logger.info("=" * 60)
        
    except Exception as e:
        logger.error(f"\n❌ Test failed: {e}")
        raise
    finally:
        # Cleanup
        await async_engine.dispose()


if __name__ == "__main__":
    asyncio.run(main())