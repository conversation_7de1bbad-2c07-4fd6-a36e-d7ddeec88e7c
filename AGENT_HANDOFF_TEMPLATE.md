# Agent Handoff Template

## Purpose
This template ensures seamless context transfer between specialized agents during TDD development.

---

## HANDOFF RECORD

**From Agent:** [Current Agent Name]  
**To Agent:** [Next Agent Name]  
**Feature:** [Feature Being Developed]  
**Date:** [ISO Date]  
**TDD Phase:** [ ] Red [ ] Green [ ] Refactor

## CURRENT STATE

### TDD Checkpoints
- [ ] Tests written first
- [ ] Tests initially failed  
- [ ] Minimal implementation added
- [ ] Tests now pass
- [ ] Code refactored
- [ ] Coverage ≥ 80%
- [ ] All project tests green

### Files Modified
**Test Files:**
- `tests/unit/test_[feature].py` - [Description of tests]
- `tests/integration/test_[feature]_integration.py` - [If applicable]

**Implementation Files:**
- `src/core/models/[model].py` - [Changes made]
- `src/core/services/[service].py` - [Changes made]
- `src/api/endpoints/[endpoint].py` - [If applicable]

### Coverage Metrics
- Before: [X]%
- Current: [Y]%
- Target: 80%

## KEY DECISIONS

### Architectural Choices
1. [Decision 1] - Rationale: [Why this approach]
2. [Decision 2] - Rationale: [Why this approach]

### Domain Model Changes
```python
# Example of key model changes
@dataclass
class NewEntity:
    # What was added and why
```

### Test Strategy
- Unit tests focus on: [What behaviors]
- Integration tests cover: [What interactions]
- Edge cases identified: [List edge cases]

## BLOCKERS & ISSUES

### Current Blockers
1. **[Blocker Name]**
   - Description: [What's blocking]
   - Proposed solution: [How to resolve]
   - Requires: [What's needed]

### Technical Debt
1. **[Debt Item]**
   - Impact: [How it affects the system]
   - Priority: [High/Medium/Low]
   - Proposed fix: [How to address]

## NEXT STEPS

### Immediate Tasks
1. [ ] [Task 1] - Assigned to: [Next Agent]
2. [ ] [Task 2] - Assigned to: [Next Agent]
3. [ ] [Task 3] - Assigned to: [Next Agent]

### Context for Next Agent
**What you need to know:**
- [Critical context point 1]
- [Critical context point 2]
- [Gotchas or tricky parts]

**Where to start:**
```bash
# Commands to run first
cd /Users/<USER>/Documents/BiLat/vc-matching-platform
PYTHONPATH=. pytest tests/unit/test_[feature].py -v

# Check current coverage
PYTHONPATH=. pytest --cov=src --cov-report=term-missing
```

## AGENT-SPECIFIC NOTES

### For FastAPI Architect
- Endpoints needing implementation: [List]
- Schema changes required: [List]
- Middleware considerations: [Any special requirements]

### For AI/LangChain Expert  
- LLM integration points: [Where AI is needed]
- Prompt templates needed: [What prompts]
- Chain design considerations: [Special requirements]

### For Database Performance Expert
- New indexes needed: [List]
- Query optimization opportunities: [List]
- Migration requirements: [What needs migrating]

### For Background Tasks Expert
- Async tasks identified: [List]
- Queue requirements: [What queues]
- Scheduling needs: [Cron jobs, etc.]

## VERIFICATION CHECKLIST

Before accepting this handoff, verify:
- [ ] All tests in listed test files pass
- [ ] Coverage report shows improvement
- [ ] No regression in existing tests
- [ ] Context makes sense for your expertise
- [ ] Clear next steps defined

## EXAMPLES & REFERENCES

### Code Examples
```python
# Example of pattern established
def test_example_pattern():
    # This is the testing pattern we're following
    pass

# Example of implementation pattern
class ServicePattern:
    # This is the service pattern we're using
    pass
```

### Related Documentation
- [Link to design decision]
- [Link to API spec]
- [Link to domain model diagram]

---

## HANDOFF ACCEPTANCE

**Receiving Agent:** [Name]  
**Accepted Date:** [ISO Date]  
**Notes:** [Any clarifications needed]

---

## Usage Instructions

1. Copy this template when handing off between agents
2. Fill in all sections completely
3. Save as `handoffs/[feature]_[from]_to_[to]_[date].md`
4. Reference in your Task invocation: 
   ```
   Task: "Using [agent], continue from handoff file handoffs/[filename].md"
   ```