#!/usr/bin/env python3
"""
Test API endpoints for data persistence.
"""

import asyncio
import aiohttp
import json


async def test_api_persistence():
    """Test that we can save and retrieve data through the API."""
    
    print("\n🧪 Testing API Data Persistence\n")
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        # Test 1: Health check
        print("1️⃣ Testing health endpoint...")
        async with session.get(f"{base_url}/health") as resp:
            data = await resp.json()
            print(f"✅ Health check: {data['status']}")
            print(f"   Database: {data['database']}")
        
        # Test 2: Create a startup
        print("\n2️⃣ Creating a startup...")
        startup_data = {
            "name": "AI Assistant Startup",
            "sector": "AI/ML",
            "stage": "Seed",
            "description": "Building the next generation of AI assistants",
            "website": "https://ai-assistant.example.com",
            "team_size": 5,
            "monthly_revenue": 10000.0
        }
        
        async with session.post(f"{base_url}/api/v1/startups", json=startup_data) as resp:
            if resp.status == 201:
                startup = await resp.json()
                startup_id = startup["id"]
                print(f"✅ Created startup with ID: {startup_id}")
                print(f"   Name: {startup['name']}")
                print(f"   Sector: {startup['sector']}")
            else:
                error = await resp.text()
                print(f"❌ Failed to create startup: {error}")
                return False
        
        # Test 3: Retrieve the startup
        print("\n3️⃣ Retrieving the startup...")
        async with session.get(f"{base_url}/api/v1/startups/{startup_id}") as resp:
            if resp.status == 200:
                startup = await resp.json()
                print(f"✅ Retrieved startup: {startup['name']}")
                print(f"   Team size: {startup['team_size']}")
            else:
                print("❌ Failed to retrieve startup")
                return False
        
        # Test 4: Create a VC
        print("\n4️⃣ Creating a VC...")
        vc_data = {
            "firm_name": "Future Ventures",
            "sectors": ["AI/ML", "B2B SaaS"],
            "stages": ["Seed", "Series A"],
            "thesis": "We invest in AI-first companies",
            "website": "https://future-ventures.example.com"
        }
        
        async with session.post(f"{base_url}/api/v1/vcs", json=vc_data) as resp:
            if resp.status == 201:
                vc = await resp.json()
                vc_id = vc["id"]
                print(f"✅ Created VC with ID: {vc_id}")
                print(f"   Firm: {vc['firm_name']}")
                print(f"   Sectors: {', '.join(vc['sectors'])}")
            else:
                error = await resp.text()
                print(f"❌ Failed to create VC: {error}")
                return False
        
        # Test 5: Create a match
        print("\n5️⃣ Creating a match between startup and VC...")
        match_data = {
            "startup_id": startup_id,
            "vc_id": vc_id,
            "score": 0.92,
            "reasons": ["Sector alignment", "Stage match", "Strong founding team"]
        }
        
        async with session.post(f"{base_url}/api/v1/matches", json=match_data) as resp:
            if resp.status == 201:
                match = await resp.json()
                print(f"✅ Created match with score: {match['score']}")
                print(f"   Reasons: {', '.join(match['reasons'])}")
            else:
                error = await resp.text()
                print(f"❌ Failed to create match: {error}")
                return False
        
        # Test 6: List all startups
        print("\n6️⃣ Listing all startups...")
        async with session.get(f"{base_url}/api/v1/startups") as resp:
            if resp.status == 200:
                startups = await resp.json()
                print(f"✅ Found {len(startups)} startups in database")
                for s in startups[-3:]:  # Show last 3
                    print(f"   - {s['name']} ({s['sector']})")
            else:
                print("❌ Failed to list startups")
        
        # Test 7: List all VCs
        print("\n7️⃣ Listing all VCs...")
        async with session.get(f"{base_url}/api/v1/vcs") as resp:
            if resp.status == 200:
                vcs = await resp.json()
                print(f"✅ Found {len(vcs)} VCs in database")
                for v in vcs[-3:]:  # Show last 3
                    print(f"   - {v['firm_name']}")
            else:
                print("❌ Failed to list VCs")
        
        # Test 8: Update the startup
        print("\n8️⃣ Updating the startup...")
        update_data = {"team_size": 8}
        async with session.put(f"{base_url}/api/v1/startups/{startup_id}", json=update_data) as resp:
            if resp.status == 200:
                updated = await resp.json()
                print(f"✅ Updated startup team size to: {updated['team_size']}")
            else:
                print("❌ Failed to update startup")
        
        # Test 9: Count startups
        print("\n9️⃣ Counting startups...")
        async with session.get(f"{base_url}/startups/count") as resp:
            if resp.status == 200:
                data = await resp.json()
                print(f"✅ Total startups in database: {data['count']}")
            else:
                print("❌ Failed to count startups")
        
        print("\n🎉 All API persistence tests passed!")
        print("✅ Data persistence through API is working correctly")
        
        return True


if __name__ == "__main__":
    print("\n⚠️  Make sure the API server is running!")
    print("Run: python test_api_with_endpoints.py")
    print("\nStarting tests in 3 seconds...\n")
    asyncio.sleep(3)
    
    success = asyncio.run(test_api_persistence())
    if success:
        print("\n✅ API data persistence verified!")
    else:
        print("\n❌ API data persistence test failed")