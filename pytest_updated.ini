[pytest]
# pytest configuration file

# Test discovery patterns
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Test paths
testpaths = tests

# Output options
addopts = 
    -ra
    --strict-markers
    --cov=src
    --cov-report=term-missing
    --cov-report=html
    --cov-fail-under=50
    --maxfail=5
    --tb=short
    -p no:warnings

# Markers for test categorization
markers =
    unit: Unit tests (no external dependencies)
    integration: Integration tests (may use external services)
    slow: Slow running tests
    api: API endpoint tests
    ai: AI service tests
    auth: Authentication tests
    db: Database tests

# Async configuration
asyncio_mode = auto

# Coverage configuration
[coverage:run]
source = src
omit = 
    */tests/*
    */venv/*
    */__init__.py
    */migrations/*
    */*_old.py
    */*_refactored.py
    */examples.py
    src/core/ai/examples.py
    src/core/ai/config.py
    src/core/ai/exceptions.py
    src/core/ai/streaming.py
    src/core/ai/rate_limiter.py
    src/database/setup.py

[coverage:report]
precision = 2
show_missing = True
skip_covered = False
exclude_lines =
    # Have to re-enable the standard pragma
    pragma: no cover
    
    # Don't complain about missing debug-only code:
    def __repr__
    if self\.debug
    
    # Don't complain if tests don't hit defensive assertion code:
    raise AssertionError
    raise NotImplementedError
    
    # Don't complain if non-runnable code isn't run:
    if 0:
    if __name__ == .__main__.:
    
    # Don't complain about abstract methods
    @(abc\.)?abstractmethod

[coverage:html]
directory = htmlcov