# Development dependencies
# Install with: pip install -r requirements-dev.txt

# Testing
pytest==8.4.1
pytest-asyncio==1.1.0
pytest-cov==4.1.0
pytest-mock==3.12.0
pytest-timeout==2.2.0
pytest-xdist==3.5.0
httpx==0.25.2
faker==20.1.0

# Code Quality
black==23.12.1
isort==5.13.2
flake8==7.0.0
mypy==1.8.0
pre-commit==3.6.0
bandit==1.7.6

# Linting
pylint==3.0.3
pydocstyle==6.3.0

# Type stubs
types-redis==********
types-requests==*********

# Documentation
mkdocs==1.5.3
mkdocs-material==9.5.3
mkdocstrings==0.24.0
mkdocstrings-python==1.7.5

# Development tools
ipython==8.19.0
ipdb==0.13.13
rich==13.7.0

# Security scanning
pip-audit==2.6.2
safety==3.0.1

# Performance profiling
py-spy==0.3.14
memory-profiler==0.61.0

# API testing
locust==2.20.0