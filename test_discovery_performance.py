#!/usr/bin/env python3
"""Test discovery performance after optimizations."""

import os
import sys
import time
from uuid import uuid4
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.database.models.startup import Startup
from src.database.models.vc import VC
from src.database.repositories.startup_repository import PostgresStartupRepository
from src.database.repositories.vc_repository import PostgresVCRepository

# Database setup
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://vcmatch:vcmatch123@localhost/vcmatch")
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def test_discovery_performance():
    """Test the optimized discovery queries."""
    print("Testing Discovery Performance...")
    
    session = SessionLocal()
    try:
        # Create test VCs
        vc_repo = PostgresVCRepository(session)
        
        # Sample VC focused on AI/B2B SaaS
        test_vc = VC(
            id=uuid4(),
            firm_name="Test AI Ventures",
            website="https://test-ai.vc",
            thesis="We invest in B2B AI/ML companies building enterprise software",
            sectors=["AI/ML", "B2B SaaS", "Enterprise"],
            stages=["Seed", "Series A"],
            check_size_min=1000000,
            check_size_max=10000000,
            partners=["John Doe", "Jane Smith"],
            portfolio_companies=["AI Corp", "ML Systems"]
        )
        
        # Add to database if not exists
        existing = session.query(VC).filter_by(firm_name="Test AI Ventures").first()
        if not existing:
            session.add(test_vc)
            session.commit()
            print(f"Created test VC: {test_vc.firm_name}")
        else:
            test_vc = existing
            print(f"Using existing VC: {test_vc.firm_name}")
        
        # Create test startups
        startup_repo = PostgresStartupRepository(session)
        
        test_startups = [
            Startup(
                id=uuid4(),
                name=f"AI Startup {i}",
                description=f"B2B AI platform for enterprise automation using machine learning",
                sector="AI/ML",
                stage="Seed",
                website=f"https://ai-startup-{i}.com",
                team_size=10 + i,
                monthly_revenue=50000 * i
            )
            for i in range(1, 6)
        ]
        
        # Add startups if they don't exist
        for startup in test_startups:
            existing = session.query(Startup).filter_by(name=startup.name).first()
            if not existing:
                session.add(startup)
        
        session.commit()
        print(f"Created {len(test_startups)} test startups")

        # Test optimized discovery query
        print("\nTesting optimized discovery query...")
        start_time = time.time()
        
        # Extract keywords from thesis
        thesis_keywords = ["AI", "ML", "B2B", "enterprise"]
        
        # Run the optimized query
        discovered_startups = startup_repo.find_for_vc_discovery(
            vc_sectors=test_vc.sectors,
            vc_stages=test_vc.stages,
            min_funding=test_vc.check_size_min,
            max_funding=test_vc.check_size_max,
            thesis_keywords=thesis_keywords,
            limit=20
        )
        
        query_time = (time.time() - start_time) * 1000  # Convert to ms
        
        print(f"\nQuery completed in {query_time:.2f}ms")
        print(f"Found {len(discovered_startups)} matching startups")
        
        for startup in discovered_startups[:5]:
            print(f"  - {startup.name}: {startup.sector}, {startup.stage}, ${startup.monthly_revenue:,}/mo")
        
        # Test query plan to verify index usage
        print("\nChecking query execution plan...")
        
        # Get the actual SQL query
        explain_query = text("""
            EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON)
            SELECT * FROM startups
            WHERE sector = ANY(:sectors)
            AND stage = ANY(:stages)
            AND monthly_revenue >= 0
            ORDER BY 
                CASE 
                    WHEN stage = 'Pre-seed' THEN 1
                    WHEN stage = 'Seed' THEN 2
                    WHEN stage = 'Series A' THEN 3
                    WHEN stage = 'Series B' THEN 4
                    WHEN stage = 'Series C' THEN 5
                    WHEN stage = 'Growth' THEN 6
                    ELSE 0
                END DESC,
                monthly_revenue DESC
            LIMIT 20
        """)
        
        result = session.execute(
            explain_query,
            {"sectors": test_vc.sectors, "stages": test_vc.stages}
        )
        
        # Check if indexes are being used
        plan = result.scalar()
        if "Index Scan" in str(plan) or "Bitmap Index Scan" in str(plan):
            print("✓ Query is using indexes!")
        else:
            print("✗ Query might not be using indexes optimally")
        
        # Performance expectations
        print(f"\nPerformance Analysis:")
        print(f"  Query Time: {query_time:.2f}ms")
        print(f"  Target: <100ms")
        print(f"  Status: {'✓ PASS' if query_time < 100 else '✗ FAIL'}")
        
        return query_time < 100
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        session.close()

if __name__ == "__main__":
    print("VC-Startup Discovery Performance Test")
    print("=" * 50)
    
    success = test_discovery_performance()
    
    print("\n" + "=" * 50)
    print(f"Test {'PASSED' if success else 'FAILED'}")
    sys.exit(0 if success else 1)